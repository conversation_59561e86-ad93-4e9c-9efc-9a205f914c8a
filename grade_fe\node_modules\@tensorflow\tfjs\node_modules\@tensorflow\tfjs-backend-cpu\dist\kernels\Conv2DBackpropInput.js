/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, Conv2DBackpropInput, TensorBuffer, util } from '@tensorflow/tfjs-core';
import { assertNotComplex } from '../cpu_util';
export function conv2DBackpropInput(args) {
    const { inputs, backend, attrs } = args;
    const { dy, filter } = inputs;
    const { inputShape, strides, pad, dataFormat, dimRoundingMode } = attrs;
    assertNotComplex([dy, filter], 'conv2dBackpropInput');
    const filterStrides = util.computeStrides(filter.shape);
    const dyStrides = util.computeStrides(dy.shape);
    let $dataFormat = backend_util.convertConv2DDataFormat(dataFormat);
    const convInfo = backend_util.computeConv2DInfo(inputShape, filter.shape, strides, 1 /* dilations */, pad, dimRoundingMode, false, $dataFormat);
    const dx = new TensorBuffer(convInfo.inShape, 'float32');
    const dxValues = dx.values;
    const dyValues = backend.data.get(dy.dataId).values;
    const fltValues = backend.data.get(filter.dataId).values;
    const [fltS0, fltS1, fltS2] = filterStrides;
    const { batchSize, filterHeight, filterWidth, inChannels, inHeight, inWidth, outChannels, outHeight, outWidth, strideHeight, strideWidth } = convInfo;
    $dataFormat = convInfo.dataFormat;
    const topPad = filterHeight - 1 - convInfo.padInfo.top;
    const leftPad = filterWidth - 1 - convInfo.padInfo.left;
    const isChannelsLast = $dataFormat === 'channelsLast';
    const xBatchStride = dx.strides[0];
    const xRowStride = isChannelsLast ? dx.strides[1] : dx.strides[2];
    const xColStride = isChannelsLast ? dx.strides[2] : 1;
    const xChannelStride = isChannelsLast ? 1 : dx.strides[1];
    const yBatchStride = dyStrides[0];
    const yRowStride = isChannelsLast ? dyStrides[1] : dyStrides[2];
    const yColStride = isChannelsLast ? dyStrides[2] : 1;
    const yChannelStride = isChannelsLast ? 1 : dyStrides[1];
    for (let b = 0; b < batchSize; ++b) {
        for (let d1 = 0; d1 < inChannels; ++d1) {
            for (let xR = 0; xR < inHeight; ++xR) {
                const xRCorner = xR - topPad;
                const xRMin = Math.max(0, Math.ceil(xRCorner / strideHeight));
                const yRMax = Math.min(outHeight, (filterHeight + xRCorner) / strideHeight);
                for (let xC = 0; xC < inWidth; ++xC) {
                    const xCCorner = xC - leftPad;
                    const xCMin = Math.max(0, Math.ceil(xCCorner / strideWidth));
                    const yCMax = Math.min(outWidth, (filterWidth + xCCorner) / strideWidth);
                    let dotProd = 0;
                    for (let yR = xRMin; yR < yRMax; ++yR) {
                        const wR = yR * strideHeight - xRCorner;
                        for (let yC = xCMin; yC < yCMax; ++yC) {
                            const wC = yC * strideWidth - xCCorner;
                            const dyOffset = yBatchStride * b + yRowStride * yR + yColStride * yC;
                            const fltOffset = fltS0 * (filterHeight - 1 - wR) +
                                fltS1 * (filterWidth - 1 - wC) + fltS2 * d1;
                            for (let d2 = 0; d2 < outChannels; ++d2) {
                                const pixel = dyValues[dyOffset + yChannelStride * d2];
                                const weight = fltValues[fltOffset + d2];
                                dotProd += pixel * weight;
                            }
                        }
                    }
                    const dxOffset = xBatchStride * b + xRowStride * xR +
                        xColStride * xC + xChannelStride * d1;
                    dxValues[dxOffset] = dotProd;
                }
            }
        }
    }
    return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);
}
export const conv2DBackpropInputConfig = {
    kernelName: Conv2DBackpropInput,
    backendName: 'cpu',
    kernelFunc: conv2DBackpropInput
};
//# sourceMappingURL=data:application/json;base64,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
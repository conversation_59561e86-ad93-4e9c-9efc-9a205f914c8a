/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { EluGrad, env } from '@tensorflow/tfjs-core';
import { BinaryOpProgram } from '../binaryop_gpu';
import { BinaryOpPackedProgram } from '../binaryop_packed_gpu';
const ELU_DER = `return (b >= 1.0) ? a : a * (b + 1.0);`;
const ELU_DER_PACKED = `
  vec4 bGTEZero = vec4(greaterThanEqual(b, vec4(0.)));
  return (bGTEZero * a) + ((vec4(1.0) - bGTEZero) * (a * (b + vec4(1.0))));
`;
export const eluGrad = (args) => {
    const { inputs, backend } = args;
    const { dy, y } = inputs;
    const program = env().getBool('WEBGL_PACK_BINARY_OPERATIONS') ?
        new BinaryOpPackedProgram(ELU_DER_PACKED, dy.shape, y.shape) :
        new BinaryOpProgram(ELU_DER, dy.shape, y.shape);
    return backend.runWebGLProgram(program, [dy, y], dy.dtype);
};
export const eluGradConfig = {
    kernelName: EluGrad,
    backendName: 'webgl',
    kernelFunc: eluGrad
};
//# sourceMappingURL=data:application/json;base64,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
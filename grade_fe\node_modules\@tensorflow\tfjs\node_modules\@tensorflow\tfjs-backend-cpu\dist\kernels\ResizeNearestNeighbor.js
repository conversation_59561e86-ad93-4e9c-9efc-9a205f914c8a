/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { ResizeNearestNeighbor, util } from '@tensorflow/tfjs-core';
import { assertNotComplex } from '../cpu_util';
export function resizeNearestNeighbor(args) {
    const { inputs, backend, attrs } = args;
    const { images } = inputs;
    const { alignCorners, halfPixelCenters, size } = attrs;
    assertNotComplex(images, 'resizeNearestNeighbor');
    const imagesStrides = util.computeStrides(images.shape);
    const [newHeight, newWidth] = size;
    const [batch, oldHeight, oldWidth, numChannels] = images.shape;
    const xValues = backend.data.get(images.dataId).values;
    const output = new Float32Array(batch * newHeight * newWidth * numChannels);
    const effectiveInputSize = [
        (alignCorners && newHeight > 1) ? oldHeight - 1 : oldHeight,
        (alignCorners && newWidth > 1) ? oldWidth - 1 : oldWidth
    ];
    const effectiveOutputSize = [
        (alignCorners && newHeight > 1) ? newHeight - 1 : newHeight,
        (alignCorners && newWidth > 1) ? newWidth - 1 : newWidth
    ];
    const effectiveRowSizeRatio = effectiveInputSize[0] / effectiveOutputSize[0];
    const effectiveColSizeRatio = effectiveInputSize[1] / effectiveOutputSize[1];
    let outputOffset = 0;
    for (let b = 0; b < batch; b++) {
        const batchOffset = b * imagesStrides[0];
        for (let r = 0; r < newHeight; r++) {
            const sourceFracRow = halfPixelCenters ?
                effectiveRowSizeRatio * (r + 0.5) :
                effectiveRowSizeRatio * r;
            let sourceNearestRow = Math.min(oldHeight - 1, alignCorners ? Math.round(sourceFracRow) : Math.floor(sourceFracRow));
            if (halfPixelCenters) {
                sourceNearestRow = Math.max(0, sourceNearestRow);
            }
            const rowOffset = batchOffset + sourceNearestRow * imagesStrides[1];
            for (let c = 0; c < newWidth; c++) {
                const sourceFracCol = halfPixelCenters ?
                    effectiveColSizeRatio * (c + 0.5) :
                    effectiveColSizeRatio * c;
                let sourceNearestCol = Math.min(oldWidth - 1, alignCorners ? Math.round(sourceFracCol) :
                    Math.floor(sourceFracCol));
                if (halfPixelCenters) {
                    sourceNearestCol = Math.max(0, sourceNearestCol);
                }
                const colOffset = rowOffset + sourceNearestCol * imagesStrides[2];
                for (let d = 0; d < numChannels; d++) {
                    // Begin shader.
                    // Compute the fractional index of the source.
                    const newVal = xValues[colOffset + d];
                    output[outputOffset++] = newVal;
                }
            }
        }
    }
    return backend.makeTensorInfo([batch, newHeight, newWidth, numChannels], images.dtype, output);
}
export const resizeNearestNeighborConfig = {
    kernelName: ResizeNearestNeighbor,
    backendName: 'cpu',
    kernelFunc: resizeNearestNeighbor
};
//# sourceMappingURL=data:application/json;base64,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
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, util } from '@tensorflow/tfjs-core';
import { add } from '../kernels/Add';
import { complex } from '../kernels/Complex';
import { concat } from '../kernels/Concat';
import { identity } from '../kernels/Identity';
import { imag } from '../kernels/Imag';
import { multiply } from '../kernels/Multiply';
import { real } from '../kernels/Real';
import { realDivConfig } from '../kernels/RealDiv';
import { slice } from '../kernels/Slice';
import { sub } from '../kernels/Sub';
/**
 * Calculate FFT of inner most elements of batch tensor.
 */
export function fftBatch(input, inverse, cpuBackend) {
    const inputShape = input.shape;
    const batch = inputShape[0];
    const innerDim = inputShape[1];
    const inputVals = cpuBackend.data.get(input.dataId);
    const real2D = inputVals.complexTensorInfos.real;
    const imag2D = inputVals.complexTensorInfos.imag;
    // Collects real and imaginary values separately.
    const resultShape = [batch, innerDim];
    const resultSize = util.sizeFromShape(resultShape);
    const resultReal = util.getTypedArrayFromDType('float32', resultSize);
    const resultImag = util.getTypedArrayFromDType('float32', resultSize);
    for (let b = 0; b < batch; b++) {
        // TODO: Support slice ops for complex type.
        const r = slice({
            inputs: { x: real2D },
            backend: cpuBackend,
            attrs: { begin: [b, 0], size: [1, innerDim] }
        });
        const i = slice({
            inputs: { x: imag2D },
            backend: cpuBackend,
            attrs: { begin: [b, 0], size: [1, innerDim] }
        });
        const input = complex({ inputs: { real: r, imag: i }, backend: cpuBackend });
        // Run FFT by batch element.
        const { real, imag } = fftImpl(input, inverse, cpuBackend);
        const res = backend_util.mergeRealAndImagArrays(real, imag);
        for (let d = 0; d < innerDim; d++) {
            const c = backend_util.getComplexWithIndex(res, d);
            resultReal[b * innerDim + d] = c.real;
            resultImag[b * innerDim + d] = c.imag;
        }
        cpuBackend.disposeIntermediateTensorInfo(r);
        cpuBackend.disposeIntermediateTensorInfo(i);
        cpuBackend.disposeIntermediateTensorInfo(input);
    }
    const $realInfo = cpuBackend.makeTensorInfo(resultShape, 'float32', resultReal);
    const $imagInfo = cpuBackend.makeTensorInfo(resultShape, 'float32', resultImag);
    const result = complex({ inputs: { real: $realInfo, imag: $imagInfo }, backend: cpuBackend });
    cpuBackend.disposeIntermediateTensorInfo($realInfo);
    cpuBackend.disposeIntermediateTensorInfo($imagInfo);
    return result;
}
export function fftImpl(input, inverse, cpuBackend) {
    const inputSize = util.sizeFromShape(input.shape);
    const inputVals = cpuBackend.data.get(input.dataId);
    const realVals = cpuBackend.data.get(inputVals.complexTensorInfos.real.dataId).values;
    const imagVals = cpuBackend.data.get(inputVals.complexTensorInfos.imag.dataId).values;
    if (isExponentOf2(inputSize)) {
        const result = fftRadix2(realVals, imagVals, inputSize, inverse, cpuBackend);
        const resultShape = [input.shape[0], input.shape[1]];
        if (inverse) {
            const realInfo = cpuBackend.makeTensorInfo(resultShape, 'float32', result.real);
            const imagInfo = cpuBackend.makeTensorInfo(resultShape, 'float32', result.imag);
            const sizeInfo = cpuBackend.makeTensorInfo([], 'float32', util.createScalarValue(inputSize, 'float32'));
            const sizeInfoCopy = identity({ inputs: { x: sizeInfo }, backend: cpuBackend });
            const divRealInfo = realDivConfig.kernelFunc({ inputs: { a: realInfo, b: sizeInfo }, backend: cpuBackend });
            const divImagInfo = realDivConfig.kernelFunc({ inputs: { a: imagInfo, b: sizeInfoCopy }, backend: cpuBackend });
            const divRealVals = cpuBackend.data.get(divRealInfo.dataId).values;
            const divImagVals = cpuBackend.data.get(divImagInfo.dataId).values;
            cpuBackend.disposeIntermediateTensorInfo(realInfo);
            cpuBackend.disposeIntermediateTensorInfo(imagInfo);
            cpuBackend.disposeIntermediateTensorInfo(sizeInfo);
            cpuBackend.disposeIntermediateTensorInfo(sizeInfoCopy);
            cpuBackend.disposeIntermediateTensorInfo(divRealInfo);
            cpuBackend.disposeIntermediateTensorInfo(divImagInfo);
            return { real: divRealVals, imag: divImagVals };
        }
        return result;
    }
    else {
        const data = backend_util.mergeRealAndImagArrays(realVals, imagVals);
        const rawOutput = fourierTransformByMatmul(data, inputSize, inverse);
        return backend_util.splitRealAndImagArrays(rawOutput);
    }
}
function isExponentOf2(size) {
    return (size & size - 1) === 0;
}
// FFT using Cooley-Tukey algorithm on radix 2 dimensional input.
function fftRadix2(realVals, imagVals, size, inverse, cpuBackend) {
    if (size === 1) {
        return { real: realVals, imag: imagVals };
    }
    const data = backend_util.mergeRealAndImagArrays(realVals, imagVals);
    const half = size / 2;
    const evenComplex = backend_util.complexWithEvenIndex(data);
    const evenRealVals = evenComplex.real;
    const evenImagVals = evenComplex.imag;
    const evenShape = [evenRealVals.length];
    const evenRealInfo = cpuBackend.makeTensorInfo(evenShape, 'float32', evenRealVals);
    const evenImagInfo = cpuBackend.makeTensorInfo(evenShape, 'float32', evenImagVals);
    const evenTensorInfo = complex({ inputs: { real: evenRealInfo, imag: evenImagInfo }, backend: cpuBackend });
    const oddComplex = backend_util.complexWithOddIndex(data);
    const oddRealVals = oddComplex.real;
    const oddImagVals = oddComplex.imag;
    const oddShape = [oddRealVals.length];
    const oddRealInfo = cpuBackend.makeTensorInfo(oddShape, 'float32', oddRealVals);
    const oddImagInfo = cpuBackend.makeTensorInfo(oddShape, 'float32', oddImagVals);
    const oddTensorInfo = complex({ inputs: { real: oddRealInfo, imag: oddImagInfo }, backend: cpuBackend });
    // Recursive call for half part of original input.
    const $evenComplex = fftRadix2(evenRealVals, evenImagVals, half, inverse, cpuBackend);
    const $evenRealVals = $evenComplex.real;
    const $evenImagVals = $evenComplex.imag;
    const $evenShape = [$evenRealVals.length];
    const $evenRealInfo = cpuBackend.makeTensorInfo($evenShape, 'float32', $evenRealVals);
    const $evenImagInfo = cpuBackend.makeTensorInfo($evenShape, 'float32', $evenImagVals);
    const $evenTensorInfo = complex({
        inputs: { real: $evenRealInfo, imag: $evenImagInfo },
        backend: cpuBackend
    });
    const $oddComplex = fftRadix2(oddRealVals, oddImagVals, half, inverse, cpuBackend);
    const $oddRealVals = $oddComplex.real;
    const $oddImagVals = $oddComplex.imag;
    const $oddShape = [$oddRealVals.length];
    const $oddRealInfo = cpuBackend.makeTensorInfo($oddShape, 'float32', $oddRealVals);
    const $oddImagInfo = cpuBackend.makeTensorInfo($oddShape, 'float32', $oddImagVals);
    const $oddTensorInfo = complex({ inputs: { real: $oddRealInfo, imag: $oddImagInfo }, backend: cpuBackend });
    const e = backend_util.exponents(size, inverse);
    const eShape = [e.real.length];
    const eRealInfo = cpuBackend.makeTensorInfo(eShape, 'float32', e.real);
    const eImagInfo = cpuBackend.makeTensorInfo(eShape, 'float32', e.imag);
    const complexInfo = complex({ inputs: { real: eRealInfo, imag: eImagInfo }, backend: cpuBackend });
    const exponentInfo = multiply({ inputs: { a: complexInfo, b: $oddTensorInfo }, backend: cpuBackend });
    const addPart = add({
        inputs: { a: $evenTensorInfo, b: exponentInfo },
        backend: cpuBackend
    });
    const subPart = sub({
        inputs: { a: $evenTensorInfo, b: exponentInfo },
        backend: cpuBackend
    });
    const addPartReal = real({ inputs: { input: addPart }, backend: cpuBackend });
    const subPartReal = real({ inputs: { input: subPart }, backend: cpuBackend });
    const addPartImag = imag({ inputs: { input: addPart }, backend: cpuBackend });
    const subPartImag = imag({ inputs: { input: subPart }, backend: cpuBackend });
    const $real = concat({
        inputs: [addPartReal, subPartReal],
        backend: cpuBackend,
        attrs: { axis: 0 }
    });
    const $imag = concat({
        inputs: [addPartImag, subPartImag],
        backend: cpuBackend,
        attrs: { axis: 0 }
    });
    const $realVals = cpuBackend.data.get($real.dataId).values;
    const $imagVals = cpuBackend.data.get($imag.dataId).values;
    cpuBackend.disposeIntermediateTensorInfo(evenRealInfo);
    cpuBackend.disposeIntermediateTensorInfo(evenImagInfo);
    cpuBackend.disposeIntermediateTensorInfo(evenTensorInfo);
    cpuBackend.disposeIntermediateTensorInfo(oddRealInfo);
    cpuBackend.disposeIntermediateTensorInfo(oddImagInfo);
    cpuBackend.disposeIntermediateTensorInfo(oddTensorInfo);
    cpuBackend.disposeIntermediateTensorInfo($evenRealInfo);
    cpuBackend.disposeIntermediateTensorInfo($evenImagInfo);
    cpuBackend.disposeIntermediateTensorInfo($evenTensorInfo);
    cpuBackend.disposeIntermediateTensorInfo($oddRealInfo);
    cpuBackend.disposeIntermediateTensorInfo($oddImagInfo);
    cpuBackend.disposeIntermediateTensorInfo($oddTensorInfo);
    cpuBackend.disposeIntermediateTensorInfo(eRealInfo);
    cpuBackend.disposeIntermediateTensorInfo(eImagInfo);
    cpuBackend.disposeIntermediateTensorInfo(complexInfo);
    cpuBackend.disposeIntermediateTensorInfo(exponentInfo);
    cpuBackend.disposeIntermediateTensorInfo(addPart);
    cpuBackend.disposeIntermediateTensorInfo(subPart);
    cpuBackend.disposeIntermediateTensorInfo(addPartReal);
    cpuBackend.disposeIntermediateTensorInfo(addPartImag);
    cpuBackend.disposeIntermediateTensorInfo(subPartReal);
    cpuBackend.disposeIntermediateTensorInfo(subPartImag);
    cpuBackend.disposeIntermediateTensorInfo($real);
    cpuBackend.disposeIntermediateTensorInfo($imag);
    return { real: $realVals, imag: $imagVals };
}
// Calculate fourier transform by multplying sinusoid matrix.
function fourierTransformByMatmul(data, size, inverse) {
    const ret = new Float32Array(size * 2);
    // TODO: Use matmul instead once it supports complex64 type.
    for (let r = 0; r < size; r++) {
        let real = 0.0;
        let imag = 0.0;
        for (let c = 0; c < size; c++) {
            const e = backend_util.exponent(r * c, size, inverse);
            const term = backend_util.getComplexWithIndex(data, c);
            real += term.real * e.real - term.imag * e.imag;
            imag += term.real * e.imag + term.imag * e.real;
        }
        if (inverse) {
            real /= size;
            imag /= size;
        }
        backend_util.assignToTypedArray(ret, real, imag, r);
    }
    return ret;
}
//# sourceMappingURL=data:application/json;base64,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
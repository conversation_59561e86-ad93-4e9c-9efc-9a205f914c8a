/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the License);
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { ComplexAbs, util } from '@tensorflow/tfjs-core';
export const complexAbs = (args) => {
    const { x } = args.inputs;
    const cpuBackend = args.backend;
    const resultValues = new Float32Array(util.sizeFromShape(x.shape));
    const complexVals = cpuBackend.data.get(x.dataId);
    const real = complexVals.complexTensorInfos.real;
    const imag = complexVals.complexTensorInfos.imag;
    const realVals = cpuBackend.data.get(real.dataId).values;
    const imagVals = cpuBackend.data.get(imag.dataId).values;
    for (let i = 0; i < realVals.length; i++) {
        const real = realVals[i];
        const imag = imagVals[i];
        resultValues[i] = Math.hypot(real, imag);
    }
    return cpuBackend.makeOutput(resultValues, x.shape, 'float32');
};
export const complexAbsConfig = {
    kernelName: ComplexAbs,
    backendName: 'cpu',
    kernelFunc: complexAbs,
};
//# sourceMappingURL=data:application/json;base64,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
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, Dilation2DBackpropFilter, util } from '@tensorflow/tfjs-core';
export const dilation2DBackpropFilterConfig = {
    kernelName: Dilation2DBackpropFilter,
    backendName: 'cpu',
    kernelFunc: ({ inputs, backend, attrs }) => {
        const { x, filter, dy } = inputs;
        const { strides, pad, dilations } = attrs;
        const cpuBackend = backend;
        const $x = util.toNestedArray(x.shape, cpuBackend.data.get(x.dataId).values);
        const $filter = util.toNestedArray(filter.shape, cpuBackend.data.get(filter.dataId).values);
        const { batchSize, inHeight, inWidth, inChannels, outHeight, outWidth, padInfo, strideHeight, strideWidth, filterHeight, filterWidth, dilationHeight, dilationWidth, outShape } = backend_util.computeDilation2DInfo(x.shape, filter.shape, strides, pad, 'NHWC' /* dataFormat */, dilations);
        util.assert(dy.rank === outShape.length, () => `Error in ${Dilation2DBackpropFilter}, dy ` +
            `must have the same rank as output ${outShape.length}, but got ` +
            `${dy.rank}`);
        const $dy = util.toNestedArray(outShape, cpuBackend.data.get(dy.dataId).values);
        // The computed filter gradients has the same dimensions as the filter:
        // [filterHeight, filterWidth, depth]
        const gradients = util.makeZerosNestedTypedArray(filter.shape, filter.dtype);
        // In the case of multiple argmax branches, we only back-propagate along the
        // last branch, i.e., the one with largest value of `h * filter_cols + w`,
        // similarly to the max-pooling backward routines.
        // This implementation follows the TF c++ implementation:
        // https://github.com/tensorflow/tensorflow/blob/d9a3a849edc198e90172bc58eb293de457f9d986/tensorflow/core/kernels/dilation_ops.cc
        for (let b = 0; b < batchSize; ++b) {
            for (let hOut = 0; hOut < outHeight; ++hOut) {
                const hBeg = hOut * strideHeight - padInfo.top;
                for (let wOut = 0; wOut < outWidth; ++wOut) {
                    const wBeg = wOut * strideWidth - padInfo.left;
                    for (let d = 0; d < inChannels; ++d) {
                        let curVal = Number.MIN_SAFE_INTEGER;
                        let hMax = 0;
                        let wMax = 0;
                        for (let h = 0; h < filterHeight; ++h) {
                            const hIn = hBeg + h * dilationHeight;
                            if (hIn >= 0 && hIn < inHeight) {
                                for (let w = 0; w < filterWidth; ++w) {
                                    const wIn = wBeg + w * dilationWidth;
                                    if (wIn >= 0 && wIn < inWidth) {
                                        const val = $x[b][hIn][wIn][d] + $filter[h][w][d];
                                        if (val > curVal) {
                                            curVal = val;
                                            hMax = h;
                                            wMax = w;
                                        }
                                    }
                                }
                            }
                        }
                        gradients[hMax][wMax][d] += $dy[b][hOut][wOut][d];
                    }
                }
            }
        }
        const dataId = cpuBackend.write(util.toTypedArray(gradients, x.dtype), filter.shape, filter.dtype);
        return { dataId, shape: filter.shape, dtype: filter.dtype };
    }
};
//# sourceMappingURL=data:application/json;base64,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
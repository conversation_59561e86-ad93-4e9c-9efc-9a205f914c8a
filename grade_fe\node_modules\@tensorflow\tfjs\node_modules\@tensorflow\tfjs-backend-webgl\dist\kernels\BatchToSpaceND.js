/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, BatchToSpaceND, util } from '@tensorflow/tfjs-core';
import { reshape } from './Reshape';
import { slice } from './Slice';
import { transpose } from './Transpose';
export const batchToSpaceND = (args) => {
    const { inputs, backend, attrs } = args;
    const { x } = inputs;
    const { blockShape, crops } = attrs;
    util.assert(x.shape.length <= 4, () => 'batchToSpaceND for rank > 4 with a WebGL backend not ' +
        'implemented yet');
    const prod = blockShape.reduce((a, b) => a * b);
    const reshaped = backend_util.getReshaped(x.shape, blockShape, prod);
    const permuted = backend_util.getPermuted(reshaped.length, blockShape.length);
    const reshapedPermuted = backend_util.getReshapedPermuted(x.shape, blockShape, prod);
    const sliceBeginCoords = backend_util.getSliceBeginCoords(crops, blockShape.length);
    const sliceSize = backend_util.getSliceSize(reshapedPermuted, crops, blockShape.length);
    const toDispose = [];
    const reshapedIntermediate = reshape({ inputs: { x }, backend, attrs: { shape: reshaped } });
    const transposedIntermediate = transpose({ inputs: { x: reshapedIntermediate }, backend, attrs: { perm: permuted } });
    const reshapedIntermediate2 = reshape({
        inputs: { x: transposedIntermediate },
        backend,
        attrs: { shape: reshapedPermuted }
    });
    const sliced = slice({
        inputs: { x: reshapedIntermediate2 },
        backend,
        attrs: { begin: sliceBeginCoords, size: sliceSize }
    });
    toDispose.push(reshapedIntermediate);
    toDispose.push(transposedIntermediate);
    toDispose.push(reshapedIntermediate2);
    toDispose.forEach(t => backend.disposeIntermediateTensorInfo(t));
    return sliced;
};
export const batchToSpaceNDConfig = {
    kernelName: BatchToSpaceND,
    backendName: 'webgl',
    kernelFunc: batchToSpaceND
};
//# sourceMappingURL=data:application/json;base64,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
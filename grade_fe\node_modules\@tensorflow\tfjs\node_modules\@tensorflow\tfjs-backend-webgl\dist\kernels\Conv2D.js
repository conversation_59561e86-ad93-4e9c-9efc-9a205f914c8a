/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, Conv2D, env } from '@tensorflow/tfjs-core';
import { Conv2DProgram } from '../conv_gpu';
import { Conv2DPackedProgram } from '../conv_packed_gpu';
import { conv2dByMatMul, conv2dWithIm2Row } from './Conv2D_impl';
import { reshape } from './Reshape';
export function conv2d(args) {
    const { inputs, backend, attrs } = args;
    const { x, filter } = inputs;
    const { strides, pad, dataFormat, dilations, dimRoundingMode } = attrs;
    const $dataFormat = backend_util.convertConv2DDataFormat(dataFormat);
    const convInfo = backend_util.computeConv2DInfo(x.shape, filter.shape, strides, dilations, pad, dimRoundingMode, false /* depthwise */, $dataFormat);
    let out;
    if (convInfo.filterHeight === 1 && convInfo.filterWidth === 1 &&
        convInfo.dilationHeight === 1 && convInfo.dilationWidth === 1 &&
        convInfo.strideHeight === 1 && convInfo.strideWidth === 1 &&
        (convInfo.padInfo.type === 'SAME' || convInfo.padInfo.type === 'VALID')) {
        out = conv2dByMatMul({ x, filter, convInfo, backend });
    }
    else if (convInfo.strideWidth <= 2 && $dataFormat === 'channelsLast'
        && env().getBool('WEBGL_EXP_CONV')) {
        const program = new Conv2DPackedProgram(convInfo);
        const customValues = [
            [convInfo.padInfo.top, convInfo.padInfo.left],
            [convInfo.strideHeight, convInfo.strideWidth],
            [convInfo.dilationHeight, convInfo.dilationWidth],
            [convInfo.inHeight, convInfo.inWidth]
        ];
        out =
            backend.runWebGLProgram(program, [x, filter], 'float32', customValues);
    }
    else if (env().getBool('WEBGL_CONV_IM2COL')) {
        out = conv2dWithIm2Row({ x, filter, convInfo, backend });
    }
    else {
        const program = new Conv2DProgram(convInfo);
        out = backend.runWebGLProgram(program, [x, filter], 'float32');
    }
    const outReshaped = reshape({ inputs: { x: out }, backend, attrs: { shape: convInfo.outShape } });
    backend.disposeIntermediateTensorInfo(out);
    return outReshaped;
}
export const conv2DConfig = {
    kernelName: Conv2D,
    backendName: 'webgl',
    kernelFunc: conv2d,
};
//# sourceMappingURL=data:application/json;base64,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
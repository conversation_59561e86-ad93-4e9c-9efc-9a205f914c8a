/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, Conv3DBackpropFilterV2, TensorBuffer, util } from '@tensorflow/tfjs-core';
import { assertNotComplex } from '../cpu_util';
export function conv3DBackpropFilterV2(args) {
    const { inputs, backend, attrs } = args;
    const { x, dy } = inputs;
    const { strides, pad, filterShape } = attrs;
    assertNotComplex([x, dy], 'conv3dBackpropFilterV2');
    const xStrides = util.computeStrides(x.shape);
    const dyStrides = util.computeStrides(dy.shape);
    const convInfo = backend_util.computeConv3DInfo(x.shape, filterShape, strides, 1 /* dilations */, pad);
    const strideDepth = convInfo.strideDepth;
    const strideHeight = convInfo.strideHeight;
    const strideWidth = convInfo.strideWidth;
    const filterDepth = convInfo.filterDepth;
    const filterHeight = convInfo.filterHeight;
    const filterWidth = convInfo.filterWidth;
    const dw = new TensorBuffer(convInfo.filterShape, 'float32');
    const dwValues = dw.values;
    const [dwS0, dwS1, dwS2, dwS3] = dw.strides;
    const dyValues = backend.data.get(dy.dataId).values;
    const [dyS0, dyS1, dyS2, dyS3] = dyStrides;
    const xValues = backend.data.get(x.dataId).values;
    const [xS0, xS1, xS2, xS3] = xStrides;
    const frontPad = convInfo.padInfo.front;
    const leftPad = convInfo.padInfo.left;
    const topPad = convInfo.padInfo.top;
    for (let wF = 0; wF < filterDepth; ++wF) {
        const yFMin = Math.max(0, Math.ceil((frontPad - wF) / strideDepth));
        const yFMax = Math.min(convInfo.outDepth, (convInfo.inDepth + frontPad - wF) / strideDepth);
        const wOffset1 = wF * dwS0;
        for (let wR = 0; wR < filterHeight; ++wR) {
            const yRMin = Math.max(0, Math.ceil((topPad - wR) / strideHeight));
            const yRMax = Math.min(convInfo.outHeight, (convInfo.inHeight + topPad - wR) / strideHeight);
            const wOffset2 = wR * dwS1 + wOffset1;
            for (let wC = 0; wC < filterWidth; ++wC) {
                const yCMin = Math.max(0, Math.ceil((leftPad - wC) / strideWidth));
                const yCMax = Math.min(convInfo.outWidth, (convInfo.inWidth + leftPad - wC) / strideWidth);
                const wOffset3 = wC * dwS2 + wOffset2;
                for (let d1 = 0; d1 < convInfo.inChannels; ++d1) {
                    const wOffset4 = d1 * dwS3 + wOffset3;
                    for (let d2 = 0; d2 < convInfo.outChannels; ++d2) {
                        let dotProd = 0;
                        for (let b = 0; b < convInfo.batchSize; ++b) {
                            const xOffset1 = b * xS0;
                            const yOffset1 = b * dyS0;
                            for (let yF = yFMin; yF < yFMax; ++yF) {
                                const xF = wF + yF * strideDepth - frontPad;
                                const xOffset2 = xF * xS1 + xOffset1;
                                const yOffset2 = yF * dyS1 + yOffset1;
                                for (let yR = yRMin; yR < yRMax; ++yR) {
                                    const xR = wR + yR * strideHeight - topPad;
                                    const xOffset3 = xR * xS2 + xOffset2;
                                    const yOffset3 = yR * dyS2 + yOffset2;
                                    for (let yC = yCMin; yC < yCMax; ++yC) {
                                        const xC = wC + yC * strideWidth - leftPad;
                                        const xOffset4 = xC * xS3 + xOffset3;
                                        const yOffset4 = yC * dyS3 + yOffset3;
                                        dotProd += xValues[xOffset4 + d1] * dyValues[yOffset4 + d2];
                                    }
                                }
                            }
                        }
                        dwValues[wOffset4 + d2] = dotProd;
                    }
                }
            }
        }
    }
    return backend.makeTensorInfo(dw.shape, dw.dtype, dw.values);
}
export const conv3DBackpropFilterV2Config = {
    kernelName: Conv3DBackpropFilterV2,
    backendName: 'cpu',
    kernelFunc: conv3DBackpropFilterV2
};
//# sourceMappingURL=data:application/json;base64,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
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, env, util } from '@tensorflow/tfjs-core';
import { ConcatProgram } from '../concat_gpu';
import { ConcatPackedProgram } from '../concat_packed_gpu';
import { concatImplCPU } from '../kernel_utils/shared';
import { complex } from './Complex';
import { imag } from './Imag';
import { real } from './Real';
import { reshape } from './Reshape';
export function concatImpl(inputs, axis, backend) {
    const dtype = inputs[0].dtype;
    if (dtype === 'complex64') {
        const reals = inputs.map((t) => real({ inputs: { input: t }, backend }));
        const imags = inputs.map((t) => imag({ inputs: { input: t }, backend }));
        const realConcated = concatImpl(reals, axis, backend);
        const imagConcated = concatImpl(imags, axis, backend);
        const result = complex({ inputs: { real: realConcated, imag: imagConcated }, backend });
        reals.forEach(r => backend.disposeIntermediateTensorInfo(r));
        imags.forEach(i => backend.disposeIntermediateTensorInfo(i));
        backend.disposeIntermediateTensorInfo(realConcated);
        backend.disposeIntermediateTensorInfo(imagConcated);
        return result;
    }
    let runOnCpu = backend.shouldExecuteOnCPU(inputs);
    // Run on cpu if dtype is string. For string, the backend represents it
    // as Uint8Array[], where each Uint8Array is a character. Given that the
    // computation is only on the outer array, uploading the whole data onto
    // gpu is wasteful. Also, currently webgl doesn't have a design to
    // upload and retrieve Uint8Array[] between cpu and gpu. Therefore, we
    // just run the kernel on cpu if dtype is string.
    if (dtype === 'string') {
        runOnCpu = true;
    }
    if (runOnCpu) {
        // Any concat of n-dimensional tensors across any axis can be reduced to
        // a concatenation of two-dimensional tensors across the axis 1 by first
        // partitioning the axes of the original tensors into those less than the
        // axis to be concatenated and the rest. Then reshape the tensors
        // into a two-dimensional tensor by collapsing these two sets of axes and
        // concatenate the resulting matrices across the axis 1, finally reshaping
        // the result to have the proper shape.
        const tensors2D = inputs.map(t => {
            const innerSize = util.sizeFromShape(t.shape.slice(axis));
            const shape = [-1, innerSize];
            return reshape({ inputs: { x: t }, backend, attrs: { shape } });
        });
        const inputsValShapes = tensors2D.map(t => {
            return { vals: backend.readSync(t.dataId), shape: t.shape };
        });
        // Concats 2d tensors along axis=1.
        const outShape = backend_util.computeOutShape(tensors2D.map(t => t.shape), 1 /* axis */);
        const simplyConcat = tensors2D[0].shape[0] === 1;
        const outVals = concatImplCPU(inputsValShapes, outShape, dtype, simplyConcat);
        const finalOutShape = backend_util.computeOutShape(inputs.map(t => t.shape), axis);
        const outInfo = backend.makeTensorInfo(finalOutShape, dtype, outVals);
        tensors2D.forEach(t => backend.disposeIntermediateTensorInfo(t));
        return outInfo;
    }
    const maxTexturesInShader = env().getNumber('WEBGL_MAX_TEXTURES_IN_SHADER');
    if (inputs.length > maxTexturesInShader) {
        const reducedInputs = [];
        for (let i = 0; i < inputs.length; i += maxTexturesInShader) {
            const subArray = inputs.slice(i, i + maxTexturesInShader);
            reducedInputs.push(concatImpl(subArray, axis, backend));
        }
        const result = concatImpl(reducedInputs, axis, backend);
        for (const i of reducedInputs) {
            backend.disposeIntermediateTensorInfo(i);
        }
        return result;
    }
    if (env().getBool('WEBGL_PACK_ARRAY_OPERATIONS') &&
        inputs[0].shape.length > 1) {
        const program = new ConcatPackedProgram(inputs.map(t => t.shape), axis);
        return backend.runWebGLProgram(program, inputs, dtype);
    }
    const { tensors2D, outShape } = computeTensors2D(inputs, axis, backend);
    const program = new ConcatProgram(tensors2D.map(t => t.shape));
    const result = backend.runWebGLProgram(program, tensors2D, dtype);
    tensors2D.forEach(r => backend.disposeIntermediateTensorInfo(r));
    const reshapedResult = reshape({ inputs: { x: result }, attrs: { shape: outShape }, backend });
    backend.disposeIntermediateTensorInfo(result);
    return reshapedResult;
}
function computeTensors2D(inputs, axis, backend) {
    // Any concat of n-dimensional tensors across any axis can be reduced to
    // a concatenation of two-dimensional tensors across the axis 1 by first
    // partitioning the axes of the original tensors into those less than the
    // axis to be concatenated and the rest. Then reshape the tensors
    // into a two-dimensional tensor by collapsing these two sets of axes and
    // concatenate the resulting matrices across the axis 1, finally reshaping
    // the result to have the proper shape.
    const outShape = backend_util.computeOutShape(inputs.map(t => t.shape), axis);
    const tensors2D = inputs.map(x => reshape({
        inputs: { x },
        attrs: { shape: [-1, util.sizeFromShape(x.shape.slice(axis))] },
        backend
    }));
    return { tensors2D, outShape };
}
//# sourceMappingURL=data:application/json;base64,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
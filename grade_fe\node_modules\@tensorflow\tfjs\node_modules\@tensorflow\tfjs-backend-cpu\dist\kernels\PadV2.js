/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { PadV2, util } from '@tensorflow/tfjs-core';
import { assertNotComplex } from '../cpu_util';
export function padV2(args) {
    const { inputs, backend, attrs } = args;
    const { x } = inputs;
    const { paddings, constantValue } = attrs;
    assertNotComplex(x, 'pad');
    const outShape = paddings.map((p, i) => p[0] /* beforePad */ + x.shape[i] + p[1] /* afterPad */);
    const start = paddings.map(p => p[0]);
    const xVals = backend.data.get(x.dataId).values;
    const xSize = util.sizeFromShape(x.shape);
    const xRank = x.shape.length;
    const xStrides = util.computeStrides(x.shape);
    const resultSize = util.sizeFromShape(outShape);
    const resultRank = outShape.length;
    const resultStrides = util.computeStrides(outShape);
    const resVals = util.getTypedArrayFromDType(x.dtype, resultSize);
    if (constantValue !== 0) {
        resVals.fill(constantValue);
    }
    for (let i = 0; i < xSize; i++) {
        const coords = util.indexToLoc(i, xRank, xStrides);
        const outCoords = coords.map((c, i) => c + start[i]);
        const outIndex = util.locToIndex(outCoords, resultRank, resultStrides);
        resVals[outIndex] = xVals[i];
    }
    const outId = backend.write(resVals, outShape, x.dtype);
    return { dataId: outId, shape: outShape, dtype: x.dtype };
}
export const padV2Config = {
    kernelName: PadV2,
    backendName: 'cpu',
    kernelFunc: padV2
};
//# sourceMappingURL=data:application/json;base64,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
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { Pack, util } from '@tensorflow/tfjs-core';
import { concat } from './Concat';
import { expandDims } from './ExpandDims';
export function pack(args) {
    const { inputs, backend, attrs } = args;
    const { axis } = attrs;
    if (inputs.length === 1) {
        return expandDims({ inputs: { input: inputs[0] }, backend, attrs: { dim: axis } });
    }
    const shape = inputs[0].shape;
    const dtype = inputs[0].dtype;
    inputs.forEach(t => {
        util.assertShapesMatch(shape, t.shape, 'All tensors passed to stack must have matching shapes');
        util.assert(dtype === t.dtype, () => 'All tensors passed to stack must have matching dtypes');
    });
    const intermediateTensorInfos = [];
    const expandedTensors = inputs.map(t => {
        const expandedT = expandDims({ inputs: { input: t }, backend, attrs: { dim: axis } });
        intermediateTensorInfos.push(expandedT);
        return expandedT;
    });
    const result = concat({ inputs: expandedTensors, backend, attrs: { axis } });
    intermediateTensorInfos.forEach(t => backend.disposeIntermediateTensorInfo(t));
    return result;
}
export const packConfig = {
    kernelName: Pack,
    backendName: 'webgl',
    kernelFunc: pack
};
//# sourceMappingURL=data:application/json;base64,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
{"name": "@tensorflow/tfjs-backend-cpu", "version": "3.21.0", "description": "Vanilla JavaScript backend for TensorFlow.js", "private": false, "main": "dist/tf-backend-cpu.node.js", "jsdelivr": "dist/tf-backend-cpu.min.js", "unpkg": "dist/tf-backend-cpu.min.js", "types": "dist/index.d.ts", "jsnext:main": "dist/index.js", "module": "dist/index.js", "miniprogram": "dist/miniprogram", "engines": {"yarn": ">= 1.3.2"}, "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs.git", "directory": "tfjs-backend-cpu"}, "license": "Apache-2.0", "devDependencies": {"@bazel/bazelisk": "^1.12.0", "@bazel/ibazel": "^0.16.2"}, "scripts": {"build-ci": "yarn build", "build": "bazel build :tfjs-backend-cpu_pkg", "bundle": "bazel build :tfjs-backend-cpu_pkg", "bundle-ci": "yarn bundle", "build-npm": "bazel build :tfjs-backend-cpu_pkg", "link-local": "yalc link", "publish-npm": "bazel run :tfjs-backend-cpu_pkg.publish", "coverage": "bazel coverage :tfjs-backend-cpu_test", "test": "bazel test :tests --test_output=streamed", "test-debug": "bazel run :tfjs-backend-cpu_test --config=debug", "test-dev": "ibazel run :tests --test-output=streamed", "test-ci": "yarn test"}, "dependencies": {"@types/seedrandom": "^2.4.28", "seedrandom": "^3.0.5"}, "peerDependencies": {"@tensorflow/tfjs-core": "3.21.0"}, "browser": {"util": false, "crypto": false}, "sideEffects": ["./dist/register_all_kernels.js", "./dist/base.js", "./dist/index.js", "./src/register_all_kernels.mjs", "./src/base.mjs", "./src/index.mjs"], "resolutions": {"minimist": "1.2.6"}}
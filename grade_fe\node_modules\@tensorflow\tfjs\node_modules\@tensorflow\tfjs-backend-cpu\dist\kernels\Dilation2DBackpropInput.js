/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, Dilation2DBackpropInput, util } from '@tensorflow/tfjs-core';
export const dilation2DBackpropInputConfig = {
    kernelName: Dilation2DBackpropInput,
    backendName: 'cpu',
    kernelFunc: ({ inputs, backend, attrs }) => {
        const { x, filter, dy } = inputs;
        const { strides, pad, dilations } = attrs;
        const cpuBackend = backend;
        const $x = util.toNestedArray(x.shape, cpuBackend.data.get(x.dataId).values);
        const $filter = util.toNestedArray(filter.shape, cpuBackend.data.get(filter.dataId).values);
        const { batchSize, inHeight, inWidth, inChannels, outHeight, outWidth, padInfo, strideHeight, strideWidth, filterHeight, filterWidth, dilationHeight, dilationWidth, outShape } = backend_util.computeDilation2DInfo(x.shape, filter.shape, strides, pad, 'NHWC' /* dataFormat */, dilations);
        util.assert(dy.rank === outShape.length, () => `Error in ${Dilation2DBackpropInput}, dy ` +
            `must have the same rank as output ${outShape.length}, but got ` +
            `${dy.rank}`);
        const $dy = util.toNestedArray(outShape, cpuBackend.data.get(dy.dataId).values);
        // The computed gradients has the same dimensions as the input:
        // [batch, inputHeight, inputCols, inChannel]
        const gradients = util.makeZerosNestedTypedArray(x.shape, x.dtype);
        // In the case of multiple argmax branches, we only back-propagate along the
        // last branch, i.e., the one with largest value of `h * filter_cols + w`,
        // similarly to the max-pooling backward routines.
        // This implementation follows the TF c++ implementation:
        // https://github.com/tensorflow/tensorflow/blob/d9a3a849edc198e90172bc58eb293de457f9d986/tensorflow/core/kernels/dilation_ops.cc
        for (let b = 0; b < batchSize; ++b) {
            for (let hOut = 0; hOut < outHeight; ++hOut) {
                const hBeg = hOut * strideHeight - padInfo.top;
                for (let wOut = 0; wOut < outWidth; ++wOut) {
                    const wBeg = wOut * strideWidth - padInfo.left;
                    for (let d = 0; d < inChannels; ++d) {
                        let curVal = Number.MIN_SAFE_INTEGER;
                        let hInMax = (hBeg < 0) ? 0 : hBeg;
                        let wInMax = (wBeg < 0) ? 0 : wBeg;
                        for (let h = 0; h < filterHeight; ++h) {
                            const hIn = hBeg + h * dilationHeight;
                            if (hIn >= 0 && hIn < inHeight) {
                                for (let w = 0; w < filterWidth; ++w) {
                                    const wIn = wBeg + w * dilationWidth;
                                    if (wIn >= 0 && wIn < inWidth) {
                                        const val = $x[b][hIn][wIn][d] + $filter[h][w][d];
                                        if (val > curVal) {
                                            curVal = val;
                                            hInMax = hIn;
                                            wInMax = wIn;
                                        }
                                    }
                                }
                            }
                        }
                        gradients[b][hInMax][wInMax][d] += $dy[b][hOut][wOut][d];
                    }
                }
            }
        }
        const dataId = cpuBackend.write(util.toTypedArray(gradients, x.dtype), x.shape, x.dtype);
        return { dataId, shape: x.shape, dtype: x.dtype };
    }
};
//# sourceMappingURL=data:application/json;base64,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
/**
 * @license
 * Copyright 2022 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@tensorflow/tfjs-core")):"function"==typeof define&&define.amd?define(["exports","@tensorflow/tfjs-core"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).tf=e.tf||{},e.tf)}(this,(function(e,t){"use strict";function r(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,t}var n=r(t);t.env().registerFlag("KEEP_INTERMEDIATE_TENSORS",(function(){return!1}),(function(e){e&&console.warn("Keep intermediate tensors is ON. This will print the values of all intermediate tensors during model inference. Not all models support this mode. For details, check e2e/benchmarks/ model_config.js. This significantly impacts performance.")}));var a,s,o=function(e,t){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},o(e,t)};function i(e,t,r,n){return new(r||(r=Promise))((function(a,s){function o(e){try{u(n.next(e))}catch(e){s(e)}}function i(e){try{u(n.throw(e))}catch(e){s(e)}}function u(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(o,i)}u((n=n.apply(e,t||[])).next())}))}function u(e,t){var r,n,a,s,o={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return s={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function i(s){return function(i){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,n&&(a=2&s[0]?n.return:s[0]?n.throw||((a=n.return)&&a.call(n),0):n.next)&&!(a=a.call(n,s[1])).done)return a;switch(n=0,a&&(s=[2&s[0],a.value]),s[0]){case 0:case 1:a=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,n=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(a=o.trys,(a=a.length>0&&a[a.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!a||s[1]>a[0]&&s[1]<a[3])){o.label=s[1];break}if(6===s[0]&&o.label<a[1]){o.label=a[1],a=s;break}if(a&&o.label<a[2]){o.label=a[2],o.ops.push(s);break}a[2]&&o.ops.pop(),o.trys.pop();continue}s=t.call(e,o)}catch(e){s=[6,e],n=0}finally{r=a=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,i])}}}function p(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function l(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,a,s=r.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(n=s.next()).done;)o.push(n.value)}catch(e){a={error:e}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(a)throw a.error}}return o}function c(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(l(arguments[t]));return e}!function(e){e[e.DT_INVALID=0]="DT_INVALID",e[e.DT_FLOAT=1]="DT_FLOAT",e[e.DT_DOUBLE=2]="DT_DOUBLE",e[e.DT_INT32=3]="DT_INT32",e[e.DT_UINT8=4]="DT_UINT8",e[e.DT_INT16=5]="DT_INT16",e[e.DT_INT8=6]="DT_INT8",e[e.DT_STRING=7]="DT_STRING",e[e.DT_COMPLEX64=8]="DT_COMPLEX64",e[e.DT_INT64=9]="DT_INT64",e[e.DT_BOOL=10]="DT_BOOL",e[e.DT_QINT8=11]="DT_QINT8",e[e.DT_QUINT8=12]="DT_QUINT8",e[e.DT_QINT32=13]="DT_QINT32",e[e.DT_BFLOAT16=14]="DT_BFLOAT16",e[e.DT_QINT16=15]="DT_QINT16",e[e.DT_QUINT16=16]="DT_QUINT16",e[e.DT_UINT16=17]="DT_UINT16",e[e.DT_COMPLEX128=18]="DT_COMPLEX128",e[e.DT_HALF=19]="DT_HALF",e[e.DT_RESOURCE=20]="DT_RESOURCE",e[e.DT_VARIANT=21]="DT_VARIANT",e[e.DT_UINT32=22]="DT_UINT32",e[e.DT_UINT64=23]="DT_UINT64",e[e.DT_FLOAT_REF=101]="DT_FLOAT_REF",e[e.DT_DOUBLE_REF=102]="DT_DOUBLE_REF",e[e.DT_INT32_REF=103]="DT_INT32_REF",e[e.DT_UINT8_REF=104]="DT_UINT8_REF",e[e.DT_INT16_REF=105]="DT_INT16_REF",e[e.DT_INT8_REF=106]="DT_INT8_REF",e[e.DT_STRING_REF=107]="DT_STRING_REF",e[e.DT_COMPLEX64_REF=108]="DT_COMPLEX64_REF",e[e.DT_INT64_REF=109]="DT_INT64_REF",e[e.DT_BOOL_REF=110]="DT_BOOL_REF",e[e.DT_QINT8_REF=111]="DT_QINT8_REF",e[e.DT_QUINT8_REF=112]="DT_QUINT8_REF",e[e.DT_QINT32_REF=113]="DT_QINT32_REF",e[e.DT_BFLOAT16_REF=114]="DT_BFLOAT16_REF",e[e.DT_QINT16_REF=115]="DT_QINT16_REF",e[e.DT_QUINT16_REF=116]="DT_QUINT16_REF",e[e.DT_UINT16_REF=117]="DT_UINT16_REF",e[e.DT_COMPLEX128_REF=118]="DT_COMPLEX128_REF",e[e.DT_HALF_REF=119]="DT_HALF_REF",e[e.DT_RESOURCE_REF=120]="DT_RESOURCE_REF",e[e.DT_VARIANT_REF=121]="DT_VARIANT_REF",e[e.DT_UINT32_REF=122]="DT_UINT32_REF",e[e.DT_UINT64_REF=123]="DT_UINT64_REF"}(a||(a={})),function(e){var t;(t=e.CheckpointFormatVersion||(e.CheckpointFormatVersion={}))[t.LEGACY=0]="LEGACY",t[t.V1=1]="V1",t[t.V2=2]="V2"}(s||(s={}));var d={};function h(e){return d[e]}function f(e,r,n,a,s){var o=r.inputParams[e];if(o&&void 0!==o.inputIndexStart){var i=o.inputIndexStart,u=0===o.inputIndexEnd?void 0:void 0===o.inputIndexEnd?i+1:o.inputIndexEnd;if("tensor"===o.type)return m(r.inputNames[o.inputIndexStart],n,a,s);if("tensors"===o.type)return r.inputNames.slice(i,u).map((function(e){return m(e,n,a,s)}));var p=m(r.inputNames.slice(i)[0],n,a,s),l=p.dataSync();return"number"===o.type?l[0]:t.util.toNestedArray(p.shape,l)}var c=r.attrParams[e];return c&&c.value}function m(e,t,r,n){var a=l(v(e),2),s=a[0],o=a[1];if(null!=n){var i=n.getHashTableHandleByName(s);if(null!=i)return i}var u=r.currentContextIds.find((function(e){return!!t[g(s,e)]}));return void 0!==u?t[g(s,u)][o]:void 0}function y(e,t){var r=l(v(e),3),n=r[0],a=r[1],s=r[2];return[g(n,t&&t.currentContextId),a,s]}function g(e,t){return t?e+"-"+t:e}function v(e){var t=e.split(":");if(1===t.length)return[e,0,void 0];var r=t[0],n=3===t.length?t[1]:void 0;return[r,Number(t[t.length-1]),n]}function b(e,t,r){var n=f("pad",e,t,r);if("explicit"===n){n=f("explicitPaddings",e,t,r);for(var a=[[0,0],[0,0],[0,0],[0,0]],s=0;s<4;s++)a[s][0]=n[2*s],a[s][1]=n[2*s+1];return a}return n}function x(e){return e.kept?e:t.clone(e)}var N={__proto__:null,json:[{tfOpName:"Add",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"AddV2",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"AddN",category:"arithmetic",inputs:[{start:0,end:0,name:"tensors",type:"tensors"}]},{tfOpName:"BiasAdd",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0}]},{tfOpName:"Sub",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"RealDiv",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Div",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"DivNoNan",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"FloorDiv",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Mul",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Maximum",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Minimum",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Pow",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"SquaredDifference",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Mod",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"FloorMod",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]}]},w={__proto__:null,json:[{tfOpName:"Abs",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Acos",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Asin",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Atan",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Atan2",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"y",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Ceil",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"ClipByValue",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"clipValueMin",type:"number"},{start:2,name:"clipValueMax",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Complex",category:"basic_math",inputs:[{start:0,name:"real",type:"tensor"},{start:1,name:"imag",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"ComplexAbs",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Cos",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Cosh",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Elu",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Exp",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Floor",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Log",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Imag",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"Tout",name:"outputType",type:"dtype",notSupported:!0}]},{tfOpName:"Neg",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Real",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"Tout",name:"outputType",type:"dtype",notSupported:!0}]},{tfOpName:"Prelu",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"alpha",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Relu",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Relu6",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Selu",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Sigmoid",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Sin",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Sinh",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Sqrt",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Rsqrt",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Square",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Tan",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Tanh",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Sign",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Round",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Expm1",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Log1p",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Reciprocal",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Softplus",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Asinh",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Acosh",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Atanh",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Erf",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Prod",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axes",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool",notSupported:!0},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"LeakyRelu",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"alpha",name:"alpha",type:"number",defaultValue:.2},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"IsNan",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]}]},k={__proto__:null,json:[{tfOpName:"EmptyTensorList",category:"control",inputs:[{start:0,name:"elementShape",type:"shape"},{start:1,name:"maxNumElements",type:"number"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"LoopCond",category:"control",inputs:[{start:0,name:"pred",type:"tensor"}]},{tfOpName:"Switch",category:"control",inputs:[{start:0,name:"data",type:"tensor"},{start:1,name:"pred",type:"tensor"}]},{tfOpName:"Merge",category:"control",inputs:[{start:0,end:0,name:"tensors",type:"tensors"}]},{tfOpName:"Enter",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"frame_name",name:"frameName",type:"string"},{tfName:"is_constant",name:"isConstant",type:"bool"}]},{tfOpName:"Exit",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"NextIteration",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"TensorArrayV3",category:"control",inputs:[{start:0,name:"size",type:"number"}],attrs:[{tfName:"dtype",name:"dtype",type:"dtype"},{tfName:"element_shape",name:"elementShape",type:"shape"},{tfName:"dynamic_size",name:"dynamicSize",type:"bool"},{tfName:"clear_after_read",name:"clearAfterRead",type:"bool"},{tfName:"identical_element_shapes",name:"identicalElementShapes",type:"bool"},{tfName:"tensor_array_name",name:"name",type:"string"}]},{tfOpName:"TensorArrayWriteV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"index",type:"number"},{start:2,name:"tensor",type:"tensor"},{start:3,name:"flowIn",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"TensorArrayReadV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"index",type:"number"},{start:2,name:"flowIn",type:"number"}],attrs:[{tfName:"dtype",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"TensorArrayGatherV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"indices",type:"number[]"},{start:2,name:"flowIn",type:"number"}],attrs:[{tfName:"dtype",name:"dtype",type:"dtype"},{tfName:"element_shape",name:"elementShape",type:"shape"}]},{tfOpName:"TensorArrayScatterV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"indices",type:"number[]"},{start:2,name:"tensor",type:"tensor"},{start:3,name:"flowIn",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"TensorArrayConcatV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"flowIn",type:"number"}],attrs:[{tfName:"dtype",name:"dtype",type:"dtype"},{tfName:"element_shape_except0",name:"elementShapeExcept0",type:"shape",notSupported:!0}]},{tfOpName:"TensorArraySplitV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"tensor",type:"tensor"},{start:2,name:"lengths",type:"number[]"},{start:3,name:"flowIn",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"TensorArraySizeV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"flowIn",type:"number"}]},{tfOpName:"TensorArrayCloseV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"}]},{tfOpName:"StatelessIf",category:"control",inputs:[{start:0,name:"cond",type:"tensor"},{start:1,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"then_branch",name:"thenBranch",type:"func"},{tfName:"else_branch",name:"elseBranch",type:"func"}]},{tfOpName:"If",category:"control",inputs:[{start:0,name:"cond",type:"tensor"},{start:1,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"then_branch",name:"thenBranch",type:"func"},{tfName:"else_branch",name:"elseBranch",type:"func"}]},{tfOpName:"StatelessWhile",category:"control",inputs:[{start:0,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"cond",name:"cond",type:"func"},{tfName:"body",name:"body",type:"func"}]},{tfOpName:"While",category:"control",inputs:[{start:0,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"cond",name:"cond",type:"func"},{tfName:"body",name:"body",type:"func"}]},{tfOpName:"TensorListScatter",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"},{start:1,name:"indices",type:"number[]"},{start:2,name:"elementShape",type:"shape"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListScatterV2",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"},{start:1,name:"indices",type:"number[]"},{start:2,name:"elementShape",type:"shape"},{start:3,name:"numElements",type:"number"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListGather",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"indices",type:"number[]"},{start:2,name:"elementShape",type:"shape"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListGetItem",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"index",type:"number"},{start:2,name:"elementShape",type:"shape"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListSetItem",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"index",type:"number"},{start:2,name:"tensor",type:"tensor"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListReserve",category:"control",inputs:[{start:0,name:"elementShape",type:"shape"},{start:1,name:"numElements",type:"number"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListFromTensor",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"},{start:1,name:"elementShape",type:"shape"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListStack",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"elementShape",type:"shape"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"},{tfName:"num_elements",name:"numElements",type:"dtype"}]},{tfOpName:"TensorListSplit",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"},{start:1,name:"elementShape",type:"shape"},{start:2,name:"lengths",type:"number[]"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListConcat",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"}],attrs:[{tfName:"element_shape",name:"elementShape",type:"shape"},{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListConcatV2",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"}],attrs:[{tfName:"element_shape",name:"elementShape",type:"shape"},{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListPopBack",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"elementShape",type:"shape"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListPushBack",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"tensor",type:"tensor"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListLength",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"}]},{tfOpName:"TensorListResize",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"size",type:"number"}]}]},T={__proto__:null,json:[{tfOpName:"AvgPool",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0},{tfName:"ksize",name:"kernelSize",type:"number[]"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"MaxPool",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0},{tfName:"ksize",name:"kernelSize",type:"number[]"},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[],notSupported:!0},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"MaxPoolWithArgmax",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"ksize",name:"kernelSize",type:"number[]"},{tfName:"include_batch_in_index",name:"includeBatchInIndex",type:"bool"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"AvgPool3D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0},{tfName:"ksize",name:"kernelSize",type:"number[]"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"MaxPool3D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0},{tfName:"ksize",name:"kernelSize",type:"number[]"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Conv1D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"}],attrs:[{tfName:"stride",name:"stride",type:"number"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NWC"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"dilation",name:"dilation",type:"number",defaultValue:1}]},{tfOpName:"Conv2D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"useCudnnOnGpu",name:"useCudnnOnGpu",type:"bool"},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NHWC"},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[]},{tfName:"dilations",name:"dilations",type:"number[]"}]},{tfOpName:"_FusedConv2D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"},{start:2,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"num_args",name:"numArgs",type:"number"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[]},{tfName:"use_cudnn_on_gpu",name:"useCudnnOnGpu",type:"bool",defaultValue:!0},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NHWC"},{tfName:"dilations",name:"dilations",type:"number[]",defaultValue:[1,1,1,1]},{tfName:"fused_ops",name:"fusedOps",type:"string[]",defaultValue:[]},{tfName:"epsilon",name:"epsilon",type:"number",defaultValue:1e-4},{tfName:"leakyrelu_alpha",name:"leakyreluAlpha",type:"number",defaultValue:.2}]},{tfOpName:"Conv2DBackpropInput",category:"convolution",inputs:[{start:2,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"},{start:0,name:"outputShape",type:"number[]"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[]},{tfName:"dilations",name:"dilations",type:"number[]",notSupported:!0}]},{tfOpName:"DepthwiseConv2d",category:"convolution",inputs:[{start:0,name:"input",type:"tensor"},{start:1,name:"filter",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NHWC"},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[]},{tfName:"dilations",name:"dilations",type:"number[]"}]},{tfOpName:"DepthwiseConv2dNative",category:"convolution",inputs:[{start:0,name:"input",type:"tensor"},{start:1,name:"filter",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NHWC"},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[]},{tfName:"dilations",name:"dilations",type:"number[]"}]},{tfOpName:"FusedDepthwiseConv2dNative",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"},{start:2,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"num_args",name:"numArgs",type:"number"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NHWC"},{tfName:"dilations",name:"dilations",type:"number[]",defaultValue:[1,1,1,1]},{tfName:"fused_ops",name:"fusedOps",type:"string[]",defaultValue:[]},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[]}]},{tfOpName:"Conv3D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NHWC"},{tfName:"dilations",name:"dilations",type:"number[]"}]},{tfOpName:"Dilation2D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"rates",name:"dilations",type:"number[]"},{tfName:"padding",name:"pad",type:"string"}]}]},_={__proto__:null,json:[{tfOpName:"Fill",category:"creation",inputs:[{start:0,name:"shape",type:"number[]"},{start:1,name:"value",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"LinSpace",category:"creation",inputs:[{start:0,name:"start",type:"number"},{start:1,name:"stop",type:"number"},{start:2,name:"num",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"OneHot",category:"creation",inputs:[{start:0,name:"indices",type:"tensor"},{start:1,name:"depth",type:"number"},{start:2,name:"onValue",type:"number",defaultValue:1},{start:3,name:"offValue",type:"number",defaultValue:0}],attrs:[{tfName:"axis",name:"axis",type:"number",notSupported:!0},{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"Ones",category:"creation",inputs:[{start:0,name:"shape",type:"number[]"}],attrs:[{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"OnesLike",category:"creation",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"dtype",name:"dtype",type:"dtype"}]},{tfOpName:"RandomStandardNormal",category:"creation",inputs:[{start:0,name:"shape",type:"number[]"}],attrs:[{tfName:"seed",name:"seed",type:"number",defaultValue:0},{tfName:"seed2",name:"seed2",type:"number",defaultValue:0,notSupported:!0},{tfName:"dtype",name:"dtype",type:"dtype"},{tfName:"T",name:"T",type:"number",notSupported:!0}]},{tfOpName:"RandomUniform",category:"creation",inputs:[{start:0,name:"shape",type:"number[]"}],attrs:[{tfName:"minval",name:"minval",type:"number",defaultValue:0},{tfName:"maxval",name:"maxval",type:"number",defaultValue:1},{tfName:"dtype",name:"dtype",type:"dtype"},{tfName:"seed",name:"seed",type:"number",defaultValue:0},{tfName:"seed2",name:"seed2",type:"number",defaultValue:0,notSupported:!0},{tfName:"T",name:"T",type:"number",notSupported:!0}]},{tfOpName:"Range",category:"creation",inputs:[{start:0,name:"start",type:"number"},{start:1,name:"stop",type:"number"},{start:2,name:"step",type:"number",defaultValue:0}],attrs:[{tfName:"Tidx",name:"dtype",type:"dtype"}]},{tfOpName:"TruncatedNormal",category:"creation",inputs:[{start:0,name:"shape",type:"number[]"}],attrs:[{tfName:"means",name:"mean",type:"number",defaultValue:0},{tfName:"stddev",name:"stdDev",type:"number",defaultValue:1},{tfName:"seed",name:"seed",type:"number"},{tfName:"seed2",name:"seed2",type:"number",defaultValue:0,notSupported:!0},{tfName:"dtype",name:"dtype",type:"dtype"},{tfName:"T",name:"T",type:"number",notSupported:!0}]},{tfOpName:"Zeros",category:"creation",inputs:[{start:0,name:"shape",type:"number[]"}],attrs:[{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"ZerosLike",category:"creation",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"Multinomial",category:"creation",inputs:[{start:0,name:"logits",type:"tensor"},{start:1,name:"numSamples",type:"number"}],attrs:[{tfName:"seed",name:"seed",type:"number"},{tfName:"seed2",name:"seed2",type:"number"},{tfName:"T",name:"dtype",type:"dtype"},{tfName:"output_dtype",name:"output_dtype",type:"dtype"}]}]},S={__proto__:null,json:[{tfOpName:"NonMaxSuppressionV2",category:"dynamic",inputs:[{start:0,name:"boxes",type:"tensor"},{start:1,name:"scores",type:"tensor"},{start:2,name:"maxOutputSize",type:"number"},{start:3,name:"iouThreshold",type:"number"}]},{tfOpName:"NonMaxSuppressionV3",category:"dynamic",inputs:[{start:0,name:"boxes",type:"tensor"},{start:1,name:"scores",type:"tensor"},{start:2,name:"maxOutputSize",type:"number"},{start:3,name:"iouThreshold",type:"number"},{start:4,name:"scoreThreshold",type:"number"}]},{tfOpName:"NonMaxSuppressionV4",category:"dynamic",inputs:[{start:0,name:"boxes",type:"tensor"},{start:1,name:"scores",type:"tensor"},{start:2,name:"maxOutputSize",type:"number"},{start:3,name:"iouThreshold",type:"number"},{start:4,name:"scoreThreshold",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"T_threshold",name:"threshold",type:"dtype",notSupported:!0},{tfName:"pad_to_max_output_size",name:"padToMaxOutputSize",type:"bool"}]},{tfOpName:"NonMaxSuppressionV5",category:"dynamic",inputs:[{start:0,name:"boxes",type:"tensor"},{start:1,name:"scores",type:"tensor"},{start:2,name:"maxOutputSize",type:"number"},{start:3,name:"iouThreshold",type:"number"},{start:4,name:"scoreThreshold",type:"number"},{start:5,name:"softNmsSigma",type:"number"}]},{tfOpName:"Where",category:"dynamic",inputs:[{start:0,name:"condition",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"ListDiff",category:"dynamic",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"y",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]}]},E={__proto__:null,json:[{tfOpName:"LowerBound",category:"evaluation",inputs:[{start:0,name:"sortedSequence",type:"tensor"},{start:1,name:"values",type:"tensor"}]},{tfOpName:"TopKV2",category:"evaluation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"k",type:"number"}],attrs:[{tfName:"sorted",name:"sorted",type:"bool"}]},{tfOpName:"UpperBound",category:"evaluation",inputs:[{start:0,name:"sortedSequence",type:"tensor"},{start:1,name:"values",type:"tensor"}]},{tfOpName:"Unique",category:"evaluation",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"UniqueV2",category:"evaluation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number"}]}]},I={__proto__:null,json:[{tfOpName:"PlaceholderWithDefault",category:"graph",inputs:[{start:0,name:"default",type:"tensor"}],attrs:[{tfName:"shape",name:"shape",type:"shape"},{tfName:"dtype",name:"dtype",type:"dtype"}]},{tfOpName:"Placeholder",category:"graph",attrs:[{tfName:"shape",name:"shape",type:"shape"},{tfName:"dtype",name:"dtype",type:"dtype"}]},{tfOpName:"Const",category:"graph"},{tfOpName:"Identity",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"IdentityN",category:"graph",inputs:[{start:0,end:0,name:"x",type:"tensors"}]},{tfOpName:"Snapshot",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"Rank",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"Size",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"Shape",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"ShapeN",category:"graph",inputs:[{start:0,end:0,name:"x",type:"tensors"}]},{tfOpName:"Print",category:"graph",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"data",type:"tensors"}],attrs:[{tfName:"message",name:"message",type:"string"},{tfName:"first_n",name:"firstN",type:"number",notSupported:!0},{tfName:"summarize",name:"summarize",type:"number",defaultValue:3}]},{tfOpName:"NoOp",category:"graph",inputs:[]},{tfOpName:"StopGradient",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"FakeQuantWithMinMaxVars",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"min",name:"min",type:"number"},{tfName:"max",name:"max",type:"number"}]}]},O={__proto__:null,json:[{tfOpName:"HashTable",category:"hash_table",inputs:[],attrs:[{tfName:"shared_name",name:"sharedName",type:"string"},{tfName:"use_node_name_sharing",name:"useNodeNameSharing",type:"bool"},{tfName:"key_dtype",name:"keyDType",type:"dtype"},{tfName:"value_dtype",name:"valueDType",type:"dtype"}]},{tfOpName:"HashTableV2",category:"hash_table",inputs:[],attrs:[{tfName:"shared_name",name:"sharedName",type:"string"},{tfName:"use_node_name_sharing",name:"useNodeNameSharing",type:"bool"},{tfName:"key_dtype",name:"keyDType",type:"dtype"},{tfName:"value_dtype",name:"valueDType",type:"dtype"}]},{tfOpName:"LookupTableImport",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"},{start:1,name:"keys",type:"tensor"},{start:2,name:"values",type:"tensor"}],attrs:[{tfName:"Tin",name:"tIn",type:"dtype",notSupported:!0},{tfName:"Tout",name:"tOut",type:"dtype",notSupported:!0}]},{tfOpName:"LookupTableImportV2",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"},{start:1,name:"keys",type:"tensor"},{start:2,name:"values",type:"tensor"}],attrs:[{tfName:"Tin",name:"tIn",type:"dtype",notSupported:!0},{tfName:"Tout",name:"tOut",type:"dtype",notSupported:!0}]},{tfOpName:"LookupTableFind",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"},{start:1,name:"keys",type:"tensor"},{start:2,name:"defaultValue",type:"tensor"}],attrs:[{tfName:"Tin",name:"tIn",type:"dtype",notSupported:!0},{tfName:"Tout",name:"tOut",type:"dtype",notSupported:!0}]},{tfOpName:"LookupTableFindV2",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"},{start:1,name:"keys",type:"tensor"},{start:2,name:"defaultValue",type:"tensor"}],attrs:[{tfName:"Tin",name:"tIn",type:"dtype",notSupported:!0},{tfName:"Tout",name:"tOut",type:"dtype",notSupported:!0}]},{tfOpName:"LookupTableSize",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"}]},{tfOpName:"LookupTableSizeV2",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"}]}]},D={__proto__:null,json:[{tfOpName:"ResizeBilinear",category:"image",inputs:[{start:0,name:"images",type:"tensor"},{start:1,name:"size",type:"number[]"}],attrs:[{tfName:"align_corners",name:"alignCorners",type:"bool"},{tfName:"half_pixel_centers",name:"halfPixelCenters",type:"bool"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"ResizeNearestNeighbor",category:"image",inputs:[{start:0,name:"images",type:"tensor"},{start:1,name:"size",type:"number[]"}],attrs:[{tfName:"align_corners",name:"alignCorners",type:"bool"},{tfName:"half_pixel_centers",name:"halfPixelCenters",type:"bool"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"CropAndResize",category:"image",inputs:[{start:0,name:"image",type:"tensor"},{start:1,name:"boxes",type:"tensor"},{start:2,name:"boxInd",type:"tensor"},{start:3,name:"cropSize",type:"number[]"}],attrs:[{tfName:"method",name:"method",type:"string"},{tfName:"extrapolation_value",name:"extrapolationValue",type:"number"}]},{tfOpName:"ImageProjectiveTransformV3",category:"image",inputs:[{start:0,name:"images",type:"tensor"},{start:1,name:"transforms",type:"tensor"},{start:2,name:"outputShape",type:"number[]"},{start:3,name:"fillValue",type:"number"}],attrs:[{tfName:"interpolation",name:"interpolation",type:"string"},{tfName:"fill_mode",name:"fillMode",type:"string"}]}]},A={__proto__:null,json:[{tfOpName:"Equal",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"NotEqual",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Greater",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"GreaterEqual",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Less",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"LessEqual",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"LogicalAnd",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"LogicalNot",category:"logical",inputs:[{start:0,name:"a",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"LogicalOr",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Select",category:"logical",inputs:[{start:0,name:"condition",type:"tensor"},{start:1,name:"a",type:"tensor"},{start:2,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"SelectV2",category:"logical",inputs:[{start:0,name:"condition",type:"tensor"},{start:1,name:"a",type:"tensor"},{start:2,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]}]},M={__proto__:null,json:[{tfOpName:"_FusedMatMul",category:"matrices",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"},{start:2,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"num_args",name:"numArgs",type:"number"},{tfName:"fused_ops",name:"fusedOps",type:"string[]",defaultValue:[]},{tfName:"epsilon",name:"epsilon",type:"number",defaultValue:1e-4},{tfName:"transpose_a",name:"transposeA",type:"bool",defaultValue:!1},{tfName:"transpose_b",name:"transposeB",type:"bool",defaultValue:!1},{tfName:"leakyrelu_alpha",name:"leakyreluAlpha",type:"number",defaultValue:.2},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"MatMul",category:"matrices",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"transpose_a",name:"transposeA",type:"bool",defaultValue:!1},{tfName:"transpose_b",name:"transposeB",type:"bool",defaultValue:!1},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"BatchMatMul",category:"matrices",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"adj_x",name:"transposeA",type:"bool",defaultValue:!1},{tfName:"adj_y",name:"transposeB",type:"bool",defaultValue:!1},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"BatchMatMulV2",category:"matrices",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"adj_x",name:"transposeA",type:"bool",defaultValue:!1},{tfName:"adj_y",name:"transposeB",type:"bool",defaultValue:!1},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Transpose",category:"matrices",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"perm",type:"number[]"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Einsum",category:"matrices",inputs:[{start:0,end:0,name:"tensors",type:"tensors"}],attrs:[{tfName:"equation",name:"equation",type:"string"},{tfName:"N",name:"n",type:"number",defaultValue:2},{tfName:"T",name:"dtype",type:"dtype"}]}]},C={__proto__:null,json:[{tfOpName:"EuclideanNorm",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool",defaultValue:!1}]},{tfOpName:"FusedBatchNorm",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"scale",type:"tensor"},{start:2,name:"offset",type:"tensor"},{start:3,name:"mean",type:"tensor"},{start:4,name:"variance",type:"tensor"}],attrs:[{tfName:"epsilon",name:"epsilon",type:"number",defaultValue:.001},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0}]},{tfOpName:"FusedBatchNormV2",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"scale",type:"tensor"},{start:2,name:"offset",type:"tensor"},{start:3,name:"mean",type:"tensor"},{start:4,name:"variance",type:"tensor"}],attrs:[{tfName:"epsilon",name:"epsilon",type:"number",defaultValue:.001},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0}]},{tfOpName:"FusedBatchNormV3",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"scale",type:"tensor"},{start:2,name:"offset",type:"tensor"},{start:3,name:"mean",type:"tensor"},{start:4,name:"variance",type:"tensor"}],attrs:[{tfName:"epsilon",name:"epsilon",type:"number",defaultValue:.001},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0}]},{tfOpName:"LRN",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"depth_radius",name:"radius",type:"number",defaultValue:5},{tfName:"bias",name:"bias",type:"number",defaultValue:1},{tfName:"alpha",name:"alpha",type:"number",defaultValue:1},{tfName:"beta",name:"beta",type:"number",defaultValue:.5}]},{tfOpName:"Softmax",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"LogSoftmax",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"SparseToDense",category:"normalization",inputs:[{start:0,name:"sparseIndices",type:"tensor"},{start:1,name:"outputShape",type:"number[]"},{start:2,name:"sparseValues",type:"tensor"},{start:3,name:"defaultValue",type:"tensor"}],attrs:[{tfName:"validate_indices",name:"validateIndices",type:"bool",defaultValue:!0,notSupported:!0}]}]},F={__proto__:null,json:[{tfOpName:"Bincount",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"size",type:"number"},{start:2,name:"weights",type:"tensor"}]},{tfOpName:"DenseBincount",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"size",type:"number"},{start:2,name:"weights",type:"tensor"}],attrs:[{tfName:"binary_output",name:"binaryOutput",type:"bool"}]},{tfOpName:"Max",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"}]},{tfOpName:"Mean",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"}]},{tfOpName:"Min",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"}]},{tfOpName:"Sum",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"}]},{tfOpName:"All",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"}]},{tfOpName:"Any",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"}]},{tfOpName:"ArgMax",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number"}]},{tfOpName:"ArgMin",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number"}]},{tfOpName:"Prod",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"}]},{tfOpName:"Cumprod",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number"}],attrs:[{tfName:"exclusive",name:"exclusive",type:"bool"},{tfName:"reverse",name:"reverse",type:"bool"}]},{tfOpName:"Cumsum",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number"}],attrs:[{tfName:"exclusive",name:"exclusive",type:"bool"},{tfName:"reverse",name:"reverse",type:"bool"}]}]},V={__proto__:null,json:[{tfOpName:"ConcatV2",category:"slice_join",inputs:[{start:0,end:-1,name:"tensors",type:"tensors"},{start:-1,name:"axis",type:"number"}],attrs:[{tfName:"N",name:"n",type:"number",defaultValue:2}]},{tfOpName:"Concat",category:"slice_join",inputs:[{start:1,end:0,name:"tensors",type:"tensors"},{start:0,name:"axis",type:"number"}],attrs:[{tfName:"N",name:"n",type:"number",defaultValue:2}]},{tfOpName:"GatherV2",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"indices",type:"tensor"},{start:2,name:"axis",type:"number",defaultValue:0}],attrs:[{tfName:"batch_dims",name:"batchDims",type:"number",defaultValue:0}]},{tfOpName:"Gather",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"indices",type:"tensor"}],attrs:[{tfName:"validate_indices",name:"validateIndices",type:"bool",notSupported:!0}]},{tfOpName:"Reverse",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"dims",type:"bool[]"}]},{tfOpName:"ReverseV2",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}]},{tfOpName:"Slice",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"begin",type:"number[]"},{start:2,name:"size",type:"number[]"}]},{tfOpName:"StridedSlice",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"begin",type:"number[]"},{start:2,name:"end",type:"number[]"},{start:3,name:"strides",type:"number[]"}],attrs:[{tfName:"begin_mask",name:"beginMask",type:"number",defaultValue:0},{tfName:"end_mask",name:"endMask",type:"number",defaultValue:0},{tfName:"new_axis_mask",name:"newAxisMask",type:"number",defaultValue:0},{tfName:"ellipsis_mask",name:"ellipsisMask",type:"number",defaultValue:0},{tfName:"shrink_axis_mask",name:"shrinkAxisMask",type:"number",defaultValue:0}]},{tfOpName:"Pack",category:"slice_join",inputs:[{start:0,end:0,name:"tensors",type:"tensors"}],attrs:[{tfName:"axis",name:"axis",type:"number",defaultValue:0}]},{tfOpName:"Unpack",category:"slice_join",inputs:[{start:0,name:"tensor",type:"tensor"}],attrs:[{tfName:"axis",name:"axis",type:"number",defaultValue:0},{tfName:"num",name:"num",type:"number",defaultValue:0,notSupported:!0}]},{tfOpName:"Tile",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"reps",type:"number[]"}]},{tfOpName:"Split",category:"slice_join",inputs:[{start:0,name:"axis",type:"number",defaultValue:0},{start:1,name:"x",type:"tensor"}],attrs:[{tfName:"num_split",name:"numOrSizeSplits",type:"number",defaultValue:1}]},{tfOpName:"SplitV",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"numOrSizeSplits",type:"number[]"},{start:2,name:"axis",type:"number",defaultValue:0}]},{tfOpName:"ScatterNd",category:"slice_join",inputs:[{start:0,name:"indices",type:"tensor"},{start:1,name:"values",type:"tensor"},{start:2,name:"shape",type:"number[]"}]},{tfOpName:"GatherNd",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"indices",type:"tensor"}]},{tfOpName:"SparseToDense",category:"slice_join",inputs:[{start:0,name:"sparseIndices",type:"tensor"},{start:1,name:"outputShape",type:"number[]"},{start:2,name:"sparseValues",type:"tensor"},{start:3,name:"defaultValue",type:"tensor"}],attrs:[{tfName:"validate_indices",name:"validateIndices",type:"bool",defaultValue:!1,notSupported:!0}]}]},R={__proto__:null,json:[{tfOpName:"SparseFillEmptyRows",category:"sparse",inputs:[{start:0,name:"indices",type:"tensor"},{start:1,name:"values",type:"tensor"},{start:2,name:"denseShape",type:"tensor"},{start:3,name:"defaultValue",type:"tensor"}]},{tfOpName:"SparseReshape",category:"sparse",inputs:[{start:0,name:"inputIndices",type:"tensor"},{start:1,name:"inputShape",type:"tensor"},{start:2,name:"newShape",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"SparseSegmentMean",category:"sparse",inputs:[{start:0,name:"data",type:"tensor"},{start:1,name:"indices",type:"tensor"},{start:2,name:"segmentIds",type:"tensor"}]},{tfOpName:"SparseSegmentSum",category:"sparse",inputs:[{start:0,name:"data",type:"tensor"},{start:1,name:"indices",type:"tensor"},{start:2,name:"segmentIds",type:"tensor"}]}]},z={__proto__:null,json:[{tfOpName:"FFT",category:"spectral",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"IFFT",category:"spectral",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"RFFT",category:"spectral",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"fft_length",type:"number",notSupported:!0}]},{tfOpName:"IRFFT",category:"spectral",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"fft_length",type:"number",notSupported:!0}]}]},L={__proto__:null,json:[{tfOpName:"StringNGrams",category:"string",inputs:[{start:0,name:"data",type:"tensor"},{start:1,name:"dataSplits",type:"tensor"}],attrs:[{tfName:"separator",name:"separator",type:"string"},{tfName:"ngram_widths",name:"nGramWidths",type:"number[]"},{tfName:"left_pad",name:"leftPad",type:"string"},{tfName:"right_pad",name:"rightPad",type:"string"},{tfName:"pad_width",name:"padWidth",type:"number"},{tfName:"preserve_short_sequences",name:"preserveShortSequences",type:"bool"}],outputs:["ngrams","ngrams_splits"]},{tfOpName:"StringSplit",category:"string",inputs:[{start:0,name:"input",type:"tensor"},{start:1,name:"delimiter",type:"tensor"}],attrs:[{tfName:"skip_empty",name:"skipEmpty",type:"bool"}],outputs:["indices","values","shape"]},{tfOpName:"StringToHashBucketFast",category:"string",inputs:[{start:0,name:"input",type:"tensor"}],attrs:[{tfName:"num_buckets",name:"numBuckets",type:"number"}]}]},P={__proto__:null,json:[{tfOpName:"Cast",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"SrcT",name:"sdtype",type:"dtype",notSupported:!0},{tfName:"DstT",name:"dtype",type:"dtype"}]},{tfOpName:"ExpandDims",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number"}]},{tfOpName:"MirrorPad",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"padding",type:"number[]"}],attrs:[{tfName:"mode",name:"mode",type:"string"}]},{tfOpName:"Pad",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"padding",type:"number[]"}],attrs:[{tfName:"constant_value",name:"constantValue",type:"number",defaultValue:0}]},{tfOpName:"PadV2",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"padding",type:"number[]"},{start:2,name:"constantValue",type:"number",defaultValue:0}]},{tfOpName:"Reshape",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"shape",type:"number[]"}]},{tfOpName:"Squeeze",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"axis",tfDeprecatedName:"squeeze_dims",name:"axis",type:"number[]"}]},{tfOpName:"SpaceToBatchND",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"blockShape",type:"number[]"},{start:2,name:"paddings",type:"number[]"}]},{tfOpName:"BatchToSpaceND",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"blockShape",type:"number[]"},{start:2,name:"crops",type:"number[]"}]},{tfOpName:"DepthToSpace",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"block_size",name:"blockSize",type:"number"},{tfName:"data_format",name:"dataFormat",type:"string"}]},{tfOpName:"BroadcastTo",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"shape",type:"number[]"}],attrs:[]},{tfOpName:"BroadcastArgs",category:"transformation",inputs:[{start:0,name:"s0",type:"tensor"},{start:1,name:"s1",type:"tensor"}],attrs:[]}]},B=function(){function e(){var e=[N,w,k,T,_,S,E,I,O,D,A,M,C,F,V,R,z,L,P],t=[].concat.apply([],c(e.map((function(e){return e.json}))));this.opMappers=t.reduce((function(e,t){return e[t.tfOpName]=t,e}),{})}return Object.defineProperty(e,"Instance",{get:function(){return this._instance||(this._instance=new this)},enumerable:!0,configurable:!0}),e.prototype.transformGraph=function(e,t){var r=this;void 0===t&&(t={});var n=e.node,a=[],s=[],o=[],i=n.reduce((function(e,t){return e[t.name]=r.mapNode(t),t.op.startsWith("Placeholder")?a.push(e[t.name]):"Const"===t.op?s.push(e[t.name]):null!=t.input&&0!==t.input.length||o.push(e[t.name]),e}),{}),u=[],p=[],c={},d={};null!=t&&(c=this.mapSignatureEntries(t.inputs),d=this.mapSignatureEntries(t.outputs));var h=Object.keys(i);h.forEach((function(e){var t=i[e];t.inputNames.forEach((function(e,r){var n=l(y(e),3),a=n[0],s=n[2],o=i[a];if(null!=o.outputs){var u=o.outputs.indexOf(s);if(-1!==u){var p=a+":"+u;t.inputNames[r]=p}}t.inputs.push(o),o.children.push(t)}))})),0===Object.keys(d).length?h.forEach((function(e){var t=i[e];0===t.children.length&&p.push(t)})):Object.keys(d).forEach((function(e){var t=l(y(e),1)[0],r=i[t];null!=r&&(r.signatureKey=d[e],p.push(r))})),Object.keys(c).length>0?Object.keys(c).forEach((function(e){var t=l(y(e),1)[0],r=i[t];r&&(r.signatureKey=c[e],u.push(r))})):u=a;var f={};null!=e.library&&null!=e.library.function&&(f=e.library.function.reduce((function(e,t){return e[t.signature.name]=r.mapFunction(t),e}),{}));var m={nodes:i,inputs:u,outputs:p,weights:s,placeholders:a,signature:t,functions:f};return o.length>0&&(m.initNodes=o),m},e.prototype.mapSignatureEntries=function(e){return Object.keys(e||{}).reduce((function(t,r){return t[e[r].name]=r,t}),{})},e.prototype.mapNode=function(e){var t=h(e.op)||this.opMappers[e.op]||{};null==e.attr&&(e.attr={});var r={name:e.name,op:e.op,category:t.category,inputNames:(e.input||[]).map((function(e){return e.startsWith("^")?e.slice(1):e})),inputs:[],children:[],inputParams:{},attrParams:{},rawAttrs:e.attr,outputs:t.outputs};return null!=t.inputs&&(r.inputParams=t.inputs.reduce((function(e,t){return e[t.name]={type:t.type,inputIndexStart:t.start,inputIndexEnd:t.end},e}),{})),null!=t.attrs&&(r.attrParams=t.attrs.reduce((function(t,r){var n=r.type,a=void 0;switch(r.type){case"string":void 0===(a=q(e.attr,r.tfName,r.defaultValue))&&r.tfDeprecatedName&&(a=q(e.attr,r.tfDeprecatedName,r.defaultValue));break;case"string[]":void 0===(a=J(e.attr,r.tfName,r.defaultValue))&&r.tfDeprecatedName&&(a=J(e.attr,r.tfDeprecatedName,r.defaultValue));break;case"number":void 0===(a=U(e.attr,r.tfName,r.defaultValue||0))&&r.tfDeprecatedName&&(a=U(e.attr,r.tfDeprecatedName,r.defaultValue));break;case"number[]":void 0===(a=X(e.attr,r.tfName,r.defaultValue))&&r.tfDeprecatedName&&(a=X(e.attr,r.tfDeprecatedName,r.defaultValue));break;case"bool":void 0===(a=j(e.attr,r.tfName,r.defaultValue))&&r.tfDeprecatedName&&(a=j(e.attr,r.tfDeprecatedName,r.defaultValue));break;case"bool[]":void 0===(a=ee(e.attr,r.tfName,r.defaultValue))&&r.tfDeprecatedName&&(a=ee(e.attr,r.tfDeprecatedName,r.defaultValue));break;case"shape":void 0===(a=Y(e.attr,r.tfName,r.defaultValue))&&r.tfDeprecatedName&&(a=Y(e.attr,r.tfDeprecatedName,r.defaultValue));break;case"shape[]":void 0===(a=$(e.attr,r.tfName,r.defaultValue))&&r.tfDeprecatedName&&(a=$(e.attr,r.tfDeprecatedName,r.defaultValue));break;case"dtype":void 0===(a=G(e.attr,r.tfName,r.defaultValue))&&r.tfDeprecatedName&&(a=G(e.attr,r.tfDeprecatedName,r.defaultValue));break;case"dtype[]":void 0===(a=Z(e.attr,r.tfName,r.defaultValue))&&r.tfDeprecatedName&&(a=Z(e.attr,r.tfDeprecatedName,r.defaultValue));break;case"func":void 0===(a=H(e.attr,r.tfName,r.defaultValue))&&r.tfDeprecatedName&&(a=H(e.attr,r.tfDeprecatedName,r.defaultValue));break;case"tensor":case"tensors":break;default:throw new Error("Unsupported param type: "+r.type+" for op: "+e.op)}return t[r.name]={value:a,type:n},t}),{})),r},e.prototype.mapFunction=function(e){var t=this,r=e.nodeDef,n=[],a={};null!=r&&(a=r.reduce((function(e,r){return e[r.name]=t.mapNode(r),"Const"===r.op&&n.push(e[r.name]),e}),{}));var s=[],o=[];e.signature.inputArg.forEach((function(e){var t=l(y(e.name),1)[0],r={name:t,op:"Placeholder",inputs:[],inputNames:[],category:"graph",inputParams:{},attrParams:{dtype:{value:W(e.type),type:"dtype"}},children:[]};r.signatureKey=e.name,s.push(r),a[t]=r})),Object.keys(a).forEach((function(e){var t=a[e];t.inputNames.forEach((function(e,r){var n=l(y(e),3),s=n[0],o=n[2],i=a[s];if(null!=i.outputs){var u=i.outputs.indexOf(o);if(-1!==u){var p=s+":"+u;t.inputNames[r]=p}}t.inputs.push(i),i.children.push(t)}))}));var i=e.ret;e.signature.outputArg.forEach((function(e){var t=l(y(i[e.name]),2),r=t[0],n=t[1],s=a[r];null!=s&&(s.defaultOutput=n,o.push(s))}));var u=this.mapArgsToSignature(e);return{nodes:a,inputs:s,outputs:o,weights:n,placeholders:[],signature:u}},e.prototype.mapArgsToSignature=function(e){var t=this;return{methodName:e.signature.name,inputs:e.signature.inputArg.reduce((function(e,r){return e[r.name]=t.mapArgToTensorInfo(r),e}),{}),outputs:e.signature.outputArg.reduce((function(r,n){return r[n.name]=t.mapArgToTensorInfo(n,e.ret),r}),{})}},e.prototype.mapArgToTensorInfo=function(e,t){var r=e.name;return null!=t&&(r=t[r]),{name:r,dtype:e.type}},e}();function K(e,r){var n=Array.isArray(e)?String.fromCharCode.apply(null,e):function(e){var r=t.env().global;if("undefined"!=typeof r.atob)return r.atob(e);if("undefined"!=typeof Buffer)return new Buffer(e,"base64").toString();throw new Error("Unable to decode base64 in this environment. Missing built-in atob() or Buffer()")}(e);return r?n:n.toLowerCase()}function q(e,t,r,n){void 0===n&&(n=!1);var a=e[t];return null!=a?K(a.s,n):r}function j(e,t,r){var n=e[t];return n?n.b:r}function U(e,t,r){var n=e[t]||{},a=null!=n.i?n.i:null!=n.f?n.f:r;return"number"==typeof a?a:parseInt(a,10)}function W(e){switch("string"==typeof e&&(e=a[e]),e){case a.DT_FLOAT:case a.DT_HALF:return"float32";case a.DT_INT32:case a.DT_INT64:case a.DT_INT8:case a.DT_UINT8:return"int32";case a.DT_BOOL:return"bool";case a.DT_DOUBLE:return"float32";case a.DT_STRING:return"string";default:return null}}function H(e,t,r){var n=e[t];return n&&n.func?n.func.name:r}function G(e,t,r){var n=e[t];return n&&n.type?W(n.type):r}function Z(e,t,r){var n=e[t];return n&&n.list&&n.list.type?n.list.type.map((function(e){return W(e)})):r}function Q(e){if(!e.unknownRank)return null!=e.dim?e.dim.map((function(e){return"number"==typeof e.size?e.size:parseInt(e.size,10)})):[]}function Y(e,t,r){var n=e[t];return n&&n.shape?Q(n.shape):r}function X(e,t,r){var n=e[t];return n?((n.list.f&&n.list.f.length?n.list.f:n.list.i)||[]).map((function(e){return"number"==typeof e?e:parseInt(e,10)})):r}function J(e,t,r,n){void 0===n&&(n=!1);var a=e[t];return a&&a.list&&a.list.s?a.list.s.map((function(e){return K(e,n)})):r}function $(e,t,r){var n=e[t];return n&&n.list&&n.list.shape?n.list.shape.map((function(e){return Q(e)})):r}function ee(e,t,r){var n=e[t];return n&&n.list&&n.list.b?n.list.b:r}var te=function(){function e(e,t,r){var n=this;this.node=e,this.tensorMap=t,this.context=r,this.inputs=[],this.attrs={},this.inputs=e.inputNames.map((function(e){return n.getInput(e)})),null!=e.rawAttrs&&(this.attrs=Object.keys(e.rawAttrs).reduce((function(e,t){return e[t]=n.getAttr(t),e}),{}))}return e.prototype.getInput=function(e){return m(e,this.tensorMap,this.context)},e.prototype.getAttr=function(e,t){var r=this.node.rawAttrs[e];if(null!=r.tensor)return m(e,this.tensorMap,this.context);if(null!=r.i||null!=r.f)return U(this.node.rawAttrs,e,t);if(null!=r.s)return q(this.node.rawAttrs,e,t);if(null!=r.b)return j(this.node.rawAttrs,e,t);if(null!=r.shape)return Y(this.node.rawAttrs,e,t);if(null!=r.type)return G(this.node.rawAttrs,e,t);if(null!=r.list){if(null!=r.list.i||null!=r.list.f)return X(this.node.rawAttrs,e,t);if(null!=r.list.s)return J(this.node.rawAttrs,e,t);if(null!=r.list.shape)return $(this.node.rawAttrs,e,t);if(null!=r.list.b)return ee(this.node.rawAttrs,e,t);if(null!=r.list.type)return Z(this.node.rawAttrs,e,t)}return t},e}(),re=function(){function e(){}return e.prototype.refCount=function(e){return ne("refCount")},e.prototype.incRef=function(e){return ne("incRef")},e.prototype.timerAvailable=function(){return!0},e.prototype.time=function(e){return ne("time")},e.prototype.read=function(e){return ne("read")},e.prototype.readSync=function(e){return ne("readSync")},e.prototype.readToGPU=function(e,t){return ne("readToGPU")},e.prototype.numDataIds=function(){return ne("numDataIds")},e.prototype.disposeData=function(e,t){return ne("disposeData")},e.prototype.write=function(e,t,r){return ne("write")},e.prototype.move=function(e,t,r,n,a){return ne("move")},e.prototype.memory=function(){return ne("memory")},e.prototype.floatPrecision=function(){return ne("floatPrecision")},e.prototype.epsilon=function(){return 32===this.floatPrecision()?1e-7:1e-4},e.prototype.dispose=function(){return ne("dispose")},e}();function ne(e){throw new Error("'"+e+"' not yet implemented or not found in the registry. This kernel may not be supported by the tfjs backend you have chosen")}function ae(e,t){if(!e)throw new Error("string"==typeof t?t:t())}function se(e,t,r){void 0===r&&(r=""),ae(pe(e,t),(function(){return r+" Shapes "+e+" and "+t+" must match"}))}function oe(e){ae(null!=e,(function(){return"The input to the tensor constructor must be a non-null value."}))}function ie(e,t,r){if(void 0===t&&(t=[]),void 0===r&&(r=!1),null==t&&(t=[]),Array.isArray(e)||he(e)&&!r)for(var n=0;n<e.length;++n)ie(e[n],t,r);else t.push(e);return t}function ue(e){if(0===e.length)return 1;for(var t=e[0],r=1;r<e.length;r++)t*=e[r];return t}function pe(e,t){if(e===t)return!0;if(null==e||null==t)return!1;if(e.length!==t.length)return!1;for(var r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}function le(e){return e%1==0}function ce(e,t){return t<=e.length?e:e+" ".repeat(t-e.length)}function de(e,t){var r=t.length;return ae((e=null==e?t.map((function(e,t){return t})):[].concat(e)).every((function(e){return e>=-r&&e<r})),(function(){return"All values in axis param must be in range [-"+r+", "+r+") but got axis "+e})),ae(e.every((function(e){return le(e)})),(function(){return"All values in axis param must be integers but got axis "+e})),e.map((function(e){return e<0?r+e:e}))}function he(e){return e instanceof Float32Array||e instanceof Int32Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray}function fe(e){if("float32"===e||"int32"===e)return 4;if("complex64"===e)return 8;if("bool"===e)return 1;throw new Error("Unknown dtype "+e)}function me(e){return"string"==typeof e||e instanceof String}function ye(e){return Array.isArray(e)?ye(e[0]):e instanceof Float32Array?"float32":e instanceof Int32Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray?"int32":"number"==typeof e?"float32":me(e)?"string":function(e){return"boolean"==typeof e}(e)?"bool":"float32"}function ge(e){return!!(e&&e.constructor&&e.call&&e.apply)}function ve(e){var t=e.length;if(t<2)return[];var r=new Array(t-1);r[t-2]=e[t-1];for(var n=t-3;n>=0;--n)r[n]=r[n+1]*e[n+1];return r}function be(e,t,r,n){void 0===n&&(n=!1);var a=new Array;if(1===t.length)for(var s=t[0]*(n?2:1),o=0;o<s;o++)a[o]=r[e+o];else{s=t[0];var i=t.slice(1),u=i.reduce((function(e,t){return e*t}))*(n?2:1);for(o=0;o<s;o++)a[o]=be(e+o*u,i,r,n)}return a}function xe(e,t,r){if(void 0===r&&(r=!1),0===e.length)return t[0];var n=e.reduce((function(e,t){return e*t}))*(r?2:1);if(0===n)return[];if(n!==t.length)throw new Error("["+e+"] does not match the input size "+t.length+(r?" for a complex tensor":"")+".");return be(0,e,t,r)}function Ne(e,t){for(var r=we(e,t),n=0;n<r.length;n++)r[n]=1;return r}function we(e,t){if(null==t||"float32"===t||"complex64"===t)return new Float32Array(e);if("int32"===t)return new Int32Array(e);if("bool"===t)return new Uint8Array(e);throw new Error("Unknown data type "+t)}function ke(e){e.forEach((function(t){ae(Number.isInteger(t)&&t>=0,(function(){return"Tensor must have a shape comprised of positive integers but got shape ["+e+"]."}))}))}function Te(e){return e&&e.then&&"function"==typeof e.then}var _e="tfjsflags",Se=function(){function e(e){this.global=e,this.flags={},this.flagRegistry={},this.urlFlags={},this.getQueryParams=Ee,this.populateURLFlags()}return e.prototype.setPlatform=function(e,t){null!=this.platform&&(Oe().getBool("IS_TEST")||Oe().getBool("PROD")||console.warn("Platform "+this.platformName+" has already been set. Overwriting the platform with "+e+".")),this.platformName=e,this.platform=t},e.prototype.registerFlag=function(e,t,r){if(this.flagRegistry[e]={evaluationFn:t,setHook:r},null!=this.urlFlags[e]){var n=this.urlFlags[e];Oe().getBool("IS_TEST")||Oe().getBool("PROD")||console.warn("Setting feature override from URL "+e+": "+n+"."),this.set(e,n)}},e.prototype.getAsync=function(e){return i(this,void 0,void 0,(function(){var t,r;return u(this,(function(n){switch(n.label){case 0:return e in this.flags?[2,this.flags[e]]:(t=this.flags,r=e,[4,this.evaluateFlag(e)]);case 1:return t[r]=n.sent(),[2,this.flags[e]]}}))}))},e.prototype.get=function(e){if(e in this.flags)return this.flags[e];var t=this.evaluateFlag(e);if(Te(t))throw new Error("Flag "+e+" cannot be synchronously evaluated. Please use getAsync() instead.");return this.flags[e]=t,this.flags[e]},e.prototype.getNumber=function(e){return this.get(e)},e.prototype.getBool=function(e){return this.get(e)},e.prototype.getFlags=function(){return this.flags},Object.defineProperty(e.prototype,"features",{get:function(){return this.flags},enumerable:!0,configurable:!0}),e.prototype.set=function(e,t){if(null==this.flagRegistry[e])throw new Error("Cannot set flag "+e+" as it has not been registered.");this.flags[e]=t,null!=this.flagRegistry[e].setHook&&this.flagRegistry[e].setHook(t)},e.prototype.evaluateFlag=function(e){if(null==this.flagRegistry[e])throw new Error("Cannot evaluate flag '"+e+"': no evaluation function found.");return this.flagRegistry[e].evaluationFn()},e.prototype.setFlags=function(e){this.flags=Object.assign({},e)},e.prototype.reset=function(){this.flags={},this.urlFlags={},this.populateURLFlags()},e.prototype.populateURLFlags=function(){var e=this;if("undefined"!=typeof this.global&&"undefined"!=typeof this.global.location&&"undefined"!=typeof this.global.location.search){var t=this.getQueryParams(this.global.location.search);if(_e in t)t.tfjsflags.split(",").forEach((function(t){var r=l(t.split(":"),2),n=r[0],a=r[1];e.urlFlags[n]=function(e,t){if("true"===(t=t.toLowerCase())||"false"===t)return"true"===t;if(""+ +t===t)return+t;throw new Error("Could not parse value flag value "+t+" for flag "+e+".")}(n,a)}))}},e}();function Ee(e){var t={};return e.replace(/[?&]([^=?&]+)(?:=([^&]*))?/g,(function(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return Ie(t,r[0],r[1]),r.join("=")})),t}function Ie(e,t,r){e[decodeURIComponent(t)]=decodeURIComponent(r||"")}function Oe(){return Ae}var De,Ae=null;function Me(){if(null==De){var e=void 0;if("undefined"!=typeof window)e=window;else if("undefined"!=typeof global)e=global;else if("undefined"!=typeof process)e=process;else{if("undefined"==typeof self)throw new Error("Could not find a global object");e=self}De=e}return De}function Ce(e,t){var r,n=(null==(r=Me())._tfGlobals&&(r._tfGlobals=new Map),r._tfGlobals);if(n.has(e))return n.get(e);var a=t();return n.set(e,a),n.get(e)}var Fe="Cast",Ve="Einsum",Re="Identity",ze="Tile",Le="Transpose",Pe="_FusedMatMul",Be="FusedConv2D",Ke="FusedDepthwiseConv2D";function qe(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];Oe().getBool("IS_TEST")||Oe().getBool("PROD")||console.warn.apply(console,c(e))}var je=Ce("kernelRegistry",(function(){return new Map})),Ue=Ce("gradRegistry",(function(){return new Map}));function We(e,t){var r=function(e,t){return t+"_"+e}(e,t);return je.get(r)}function He(e){return Ue.get(e)}function Ge(e){for(var t=je.entries(),r=[];;){var n=t.next(),a=n.done,s=n.value;if(a)break;var o=l(s,2),i=o[0],u=o[1];l(i.split("_"),1)[0]===e&&r.push(u)}return r}var Ze=Ye,Qe=null;try{Qe=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(e){}function Ye(e,t,r){this.low=0|e,this.high=0|t,this.unsigned=!!r}function Xe(e){return!0===(e&&e.__isLong__)}Ye.prototype.__isLong__,Object.defineProperty(Ye.prototype,"__isLong__",{value:!0}),Ye.isLong=Xe;var Je={},$e={};function et(e,t){var r,n,a;return t?(a=0<=(e>>>=0)&&e<256)&&(n=$e[e])?n:(r=rt(e,(0|e)<0?-1:0,!0),a&&($e[e]=r),r):(a=-128<=(e|=0)&&e<128)&&(n=Je[e])?n:(r=rt(e,e<0?-1:0,!1),a&&(Je[e]=r),r)}function tt(e,t){if(isNaN(e))return t?ct:lt;if(t){if(e<0)return ct;if(e>=it)return yt}else{if(e<=-ut)return gt;if(e+1>=ut)return mt}return e<0?tt(-e,t).neg():rt(e%ot|0,e/ot|0,t)}function rt(e,t,r){return new Ye(e,t,r)}Ye.fromInt=et,Ye.fromNumber=tt,Ye.fromBits=rt;var nt=Math.pow;function at(e,t,r){if(0===e.length)throw Error("empty string");if("NaN"===e||"Infinity"===e||"+Infinity"===e||"-Infinity"===e)return lt;if("number"==typeof t?(r=t,t=!1):t=!!t,(r=r||10)<2||36<r)throw RangeError("radix");var n;if((n=e.indexOf("-"))>0)throw Error("interior hyphen");if(0===n)return at(e.substring(1),t,r).neg();for(var a=tt(nt(r,8)),s=lt,o=0;o<e.length;o+=8){var i=Math.min(8,e.length-o),u=parseInt(e.substring(o,o+i),r);if(i<8){var p=tt(nt(r,i));s=s.mul(p).add(tt(u))}else s=(s=s.mul(a)).add(tt(u))}return s.unsigned=t,s}function st(e,t){return"number"==typeof e?tt(e,t):"string"==typeof e?at(e,t):rt(e.low,e.high,"boolean"==typeof t?t:e.unsigned)}Ye.fromString=at,Ye.fromValue=st;var ot=4294967296,it=ot*ot,ut=it/2,pt=et(1<<24),lt=et(0);Ye.ZERO=lt;var ct=et(0,!0);Ye.UZERO=ct;var dt=et(1);Ye.ONE=dt;var ht=et(1,!0);Ye.UONE=ht;var ft=et(-1);Ye.NEG_ONE=ft;var mt=rt(-1,2147483647,!1);Ye.MAX_VALUE=mt;var yt=rt(-1,-1,!0);Ye.MAX_UNSIGNED_VALUE=yt;var gt=rt(0,-2147483648,!1);Ye.MIN_VALUE=gt;var vt=Ye.prototype;vt.toInt=function(){return this.unsigned?this.low>>>0:this.low},vt.toNumber=function(){return this.unsigned?(this.high>>>0)*ot+(this.low>>>0):this.high*ot+(this.low>>>0)},vt.toString=function(e){if((e=e||10)<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(gt)){var t=tt(e),r=this.div(t),n=r.mul(t).sub(this);return r.toString(e)+n.toInt().toString(e)}return"-"+this.neg().toString(e)}for(var a=tt(nt(e,6),this.unsigned),s=this,o="";;){var i=s.div(a),u=(s.sub(i.mul(a)).toInt()>>>0).toString(e);if((s=i).isZero())return u+o;for(;u.length<6;)u="0"+u;o=""+u+o}},vt.getHighBits=function(){return this.high},vt.getHighBitsUnsigned=function(){return this.high>>>0},vt.getLowBits=function(){return this.low},vt.getLowBitsUnsigned=function(){return this.low>>>0},vt.getNumBitsAbs=function(){if(this.isNegative())return this.eq(gt)?64:this.neg().getNumBitsAbs();for(var e=0!=this.high?this.high:this.low,t=31;t>0&&0==(e&1<<t);t--);return 0!=this.high?t+33:t+1},vt.isZero=function(){return 0===this.high&&0===this.low},vt.eqz=vt.isZero,vt.isNegative=function(){return!this.unsigned&&this.high<0},vt.isPositive=function(){return this.unsigned||this.high>=0},vt.isOdd=function(){return 1==(1&this.low)},vt.isEven=function(){return 0==(1&this.low)},vt.equals=function(e){return Xe(e)||(e=st(e)),(this.unsigned===e.unsigned||this.high>>>31!=1||e.high>>>31!=1)&&(this.high===e.high&&this.low===e.low)},vt.eq=vt.equals,vt.notEquals=function(e){return!this.eq(e)},vt.neq=vt.notEquals,vt.ne=vt.notEquals,vt.lessThan=function(e){return this.comp(e)<0},vt.lt=vt.lessThan,vt.lessThanOrEqual=function(e){return this.comp(e)<=0},vt.lte=vt.lessThanOrEqual,vt.le=vt.lessThanOrEqual,vt.greaterThan=function(e){return this.comp(e)>0},vt.gt=vt.greaterThan,vt.greaterThanOrEqual=function(e){return this.comp(e)>=0},vt.gte=vt.greaterThanOrEqual,vt.ge=vt.greaterThanOrEqual,vt.compare=function(e){if(Xe(e)||(e=st(e)),this.eq(e))return 0;var t=this.isNegative(),r=e.isNegative();return t&&!r?-1:!t&&r?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1},vt.comp=vt.compare,vt.negate=function(){return!this.unsigned&&this.eq(gt)?gt:this.not().add(dt)},vt.neg=vt.negate,vt.add=function(e){Xe(e)||(e=st(e));var t=this.high>>>16,r=65535&this.high,n=this.low>>>16,a=65535&this.low,s=e.high>>>16,o=65535&e.high,i=e.low>>>16,u=0,p=0,l=0,c=0;return l+=(c+=a+(65535&e.low))>>>16,p+=(l+=n+i)>>>16,u+=(p+=r+o)>>>16,u+=t+s,rt((l&=65535)<<16|(c&=65535),(u&=65535)<<16|(p&=65535),this.unsigned)},vt.subtract=function(e){return Xe(e)||(e=st(e)),this.add(e.neg())},vt.sub=vt.subtract,vt.multiply=function(e){if(this.isZero())return lt;if(Xe(e)||(e=st(e)),Qe)return rt(Qe.mul(this.low,this.high,e.low,e.high),Qe.get_high(),this.unsigned);if(e.isZero())return lt;if(this.eq(gt))return e.isOdd()?gt:lt;if(e.eq(gt))return this.isOdd()?gt:lt;if(this.isNegative())return e.isNegative()?this.neg().mul(e.neg()):this.neg().mul(e).neg();if(e.isNegative())return this.mul(e.neg()).neg();if(this.lt(pt)&&e.lt(pt))return tt(this.toNumber()*e.toNumber(),this.unsigned);var t=this.high>>>16,r=65535&this.high,n=this.low>>>16,a=65535&this.low,s=e.high>>>16,o=65535&e.high,i=e.low>>>16,u=65535&e.low,p=0,l=0,c=0,d=0;return c+=(d+=a*u)>>>16,l+=(c+=n*u)>>>16,c&=65535,l+=(c+=a*i)>>>16,p+=(l+=r*u)>>>16,l&=65535,p+=(l+=n*i)>>>16,l&=65535,p+=(l+=a*o)>>>16,p+=t*u+r*i+n*o+a*s,rt((c&=65535)<<16|(d&=65535),(p&=65535)<<16|(l&=65535),this.unsigned)},vt.mul=vt.multiply,vt.divide=function(e){if(Xe(e)||(e=st(e)),e.isZero())throw Error("division by zero");var t,r,n;if(Qe)return this.unsigned||-2147483648!==this.high||-1!==e.low||-1!==e.high?rt((this.unsigned?Qe.div_u:Qe.div_s)(this.low,this.high,e.low,e.high),Qe.get_high(),this.unsigned):this;if(this.isZero())return this.unsigned?ct:lt;if(this.unsigned){if(e.unsigned||(e=e.toUnsigned()),e.gt(this))return ct;if(e.gt(this.shru(1)))return ht;n=ct}else{if(this.eq(gt))return e.eq(dt)||e.eq(ft)?gt:e.eq(gt)?dt:(t=this.shr(1).div(e).shl(1)).eq(lt)?e.isNegative()?dt:ft:(r=this.sub(e.mul(t)),n=t.add(r.div(e)));if(e.eq(gt))return this.unsigned?ct:lt;if(this.isNegative())return e.isNegative()?this.neg().div(e.neg()):this.neg().div(e).neg();if(e.isNegative())return this.div(e.neg()).neg();n=lt}for(r=this;r.gte(e);){t=Math.max(1,Math.floor(r.toNumber()/e.toNumber()));for(var a=Math.ceil(Math.log(t)/Math.LN2),s=a<=48?1:nt(2,a-48),o=tt(t),i=o.mul(e);i.isNegative()||i.gt(r);)i=(o=tt(t-=s,this.unsigned)).mul(e);o.isZero()&&(o=dt),n=n.add(o),r=r.sub(i)}return n},vt.div=vt.divide,vt.modulo=function(e){return Xe(e)||(e=st(e)),Qe?rt((this.unsigned?Qe.rem_u:Qe.rem_s)(this.low,this.high,e.low,e.high),Qe.get_high(),this.unsigned):this.sub(this.div(e).mul(e))},vt.mod=vt.modulo,vt.rem=vt.modulo,vt.not=function(){return rt(~this.low,~this.high,this.unsigned)},vt.and=function(e){return Xe(e)||(e=st(e)),rt(this.low&e.low,this.high&e.high,this.unsigned)},vt.or=function(e){return Xe(e)||(e=st(e)),rt(this.low|e.low,this.high|e.high,this.unsigned)},vt.xor=function(e){return Xe(e)||(e=st(e)),rt(this.low^e.low,this.high^e.high,this.unsigned)},vt.shiftLeft=function(e){return Xe(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?rt(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):rt(0,this.low<<e-32,this.unsigned)},vt.shl=vt.shiftLeft,vt.shiftRight=function(e){return Xe(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?rt(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):rt(this.high>>e-32,this.high>=0?0:-1,this.unsigned)},vt.shr=vt.shiftRight,vt.shiftRightUnsigned=function(e){if(Xe(e)&&(e=e.toInt()),0===(e&=63))return this;var t=this.high;return e<32?rt(this.low>>>e|t<<32-e,t>>>e,this.unsigned):rt(32===e?t:t>>>e-32,0,this.unsigned)},vt.shru=vt.shiftRightUnsigned,vt.shr_u=vt.shiftRightUnsigned,vt.toSigned=function(){return this.unsigned?rt(this.low,this.high,!1):this},vt.toUnsigned=function(){return this.unsigned?this:rt(this.low,this.high,!0)},vt.toBytes=function(e){return e?this.toBytesLE():this.toBytesBE()},vt.toBytesLE=function(){var e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24,255&e,e>>>8&255,e>>>16&255,e>>>24]},vt.toBytesBE=function(){var e=this.high,t=this.low;return[e>>>24,e>>>16&255,e>>>8&255,255&e,t>>>24,t>>>16&255,t>>>8&255,255&t]},Ye.fromBytes=function(e,t,r){return r?Ye.fromBytesLE(e,t):Ye.fromBytesBE(e,t)},Ye.fromBytesLE=function(e,t){return new Ye(e[0]|e[1]<<8|e[2]<<16|e[3]<<24,e[4]|e[5]<<8|e[6]<<16|e[7]<<24,t)},Ye.fromBytesBE=function(e,t){return new Ye(e[4]<<24|e[5]<<16|e[6]<<8|e[7],e[0]<<24|e[1]<<16|e[2]<<8|e[3],t)};var bt=Ze||Object.assign(Object.create(null),Ze,{default:Ze});function xt(e){return bt.fromString(e,!0,16)}function Nt(e,t){if("string"===t)throw new Error("Cannot convert a string[] to a TypedArray");if(Array.isArray(e)&&(e=ie(e)),Oe().getBool("DEBUG")&&function(e,t){for(var r=0;r<e.length;r++){var n=e[r];if(isNaN(n)||!isFinite(n))throw Error("A tensor of type "+t+" being uploaded contains "+n+".")}}(e,t),function(e,t){return e instanceof Float32Array&&"float32"===t||e instanceof Int32Array&&"int32"===t||e instanceof Uint8Array&&"bool"===t}(e,t))return e;if(null==t||"float32"===t||"complex64"===t)return new Float32Array(e);if("int32"===t)return new Int32Array(e);if("bool"===t){for(var r=new Uint8Array(e.length),n=0;n<r.length;++n)0!==Math.round(e[n])&&(r[n]=1);return r}throw new Error("Unknown data type "+t)}function wt(){return Oe().platform.now()}function kt(e,t){return void 0===t&&(t="utf-8"),t=t||"utf-8",Oe().platform.decode(e,t)}xt("c3a5c85c97cb3127"),xt("b492b66fbe98f273"),xt("9ae16a3b2f90404f");var Tt=function(){function e(e,t){this.backendTimer=e,this.logger=t,null==t&&(this.logger=new _t)}return e.prototype.profileKernel=function(e,t,r){var n,a,s,o,i=function(){s=r()},u=wt();if(this.backendTimer.timerAvailable())o=this.backendTimer.time(i);else{i();try{for(var l=p(s),c=l.next();!c.done;c=l.next()){c.value.dataSync()}}catch(e){n={error:e}}finally{try{c&&!c.done&&(a=l.return)&&a.call(l)}finally{if(n)throw n.error}}o=Promise.resolve({kernelMs:wt()-u})}if(Oe().getBool("CHECK_COMPUTATION_FOR_ERRORS"))for(var d=function(t){var r=s[t];r.data().then((function(t){!function(e,t,r){if("float32"!==t)return!1;for(var n=0;n<e.length;n++){var a=e[n];if(isNaN(a)||!isFinite(a))return console.warn("Found "+a+" in the result of '"+r+"'"),!0}}(t,r.dtype,e)}))},h=0;h<s.length;h++)d(h);return{kernelName:e,outputs:s,inputs:t,timeMs:o.then((function(e){return e.kernelMs})),extraInfo:o.then((function(e){return null!=e.getExtraProfileInfo?e.getExtraProfileInfo():""}))}},e.prototype.logKernelProfile=function(e){var t=this,r=e.kernelName,n=e.outputs,a=e.timeMs,s=e.inputs,o=e.extraInfo;n.forEach((function(e){Promise.all([e.data(),a,o]).then((function(n){t.logger.logKernelProfile(r,e,n[0],n[1],s,n[2])}))}))},e}();var _t=function(){function e(){}return e.prototype.logKernelProfile=function(e,t,r,n,a,s){var o="number"==typeof n?ce(n+"ms",9):n.error,i=ce(e,25),u=t.rank,p=t.size,l=ce(t.shape.toString(),14),c="";for(var d in a){var h=a[d];if(null!=h){var f=h.shape||t.shape,m=f.length;c+=d+": "+m+"D "+(m>0?f:"")+" "}}console.log("%c"+i+"\t%c"+o+"\t%c"+u+"D "+l+"\t%c"+p+"\t%c"+c+"\t%c"+s,"font-weight:bold","color:red","color:blue","color: orange","color: green","color: steelblue")},e}();function St(e,t,r,n){var a=ve(t),s=function(e,t,r,n){var a=ue(t),s=n[n.length-1],o=new Array(s).fill(0),i=t.length,u="complex64"===r?Dt(e):e;if(i>1)for(var p=0;p<a/s;p++)for(var l=p*s,c=0;c<s;c++)o[c]=Math.max(o[c],Et(u[l+c],0,r).length);return o}(e,t,r,a),o=t.length,i=Ot(e,t,r,a,s),u=["Tensor"];return n&&(u.push("  dtype: "+r),u.push("  rank: "+o),u.push("  shape: ["+t+"]"),u.push("  values:")),u.push(i.map((function(e){return"    "+e})).join("\n")),u.join("\n")}function Et(e,t,r){return ce(Array.isArray(e)?parseFloat(e[0].toFixed(7))+" + "+parseFloat(e[1].toFixed(7))+"j":me(e)?"'"+e+"'":"bool"===r?It(e):parseFloat(e.toFixed(7)).toString(),t)}function It(e){return 0===e?"false":"true"}function Ot(e,t,r,n,a,s){void 0===s&&(s=!0);var o="complex64"===r?2:1,i=t[0],u=t.length;if(0===u)return"complex64"===r?[Et(Dt(e)[0],0,r)]:"bool"===r?[It(e[0])]:[e[0].toString()];if(1===u){if(i>20){var p=3*o,l=Array.from(e.slice(0,p)),d=Array.from(e.slice((i-3)*o,i*o));return"complex64"===r&&(l=Dt(l),d=Dt(d)),["["+l.map((function(e,t){return Et(e,a[t],r)})).join(", ")+", ..., "+d.map((function(e,t){return Et(e,a[i-3+t],r)})).join(", ")+"]"]}return["["+("complex64"===r?Dt(e):Array.from(e)).map((function(e,t){return Et(e,a[t],r)})).join(", ")+"]"]}var h=t.slice(1),f=n.slice(1),m=n[0]*o,y=[];if(i>20){for(var g=0;g<3;g++){var v=(b=g*m)+m;y.push.apply(y,c(Ot(e.slice(b,v),h,r,f,a,!1)))}y.push("...");for(g=i-3;g<i;g++){v=(b=g*m)+m;y.push.apply(y,c(Ot(e.slice(b,v),h,r,f,a,g===i-1)))}}else for(g=0;g<i;g++){var b;v=(b=g*m)+m;y.push.apply(y,c(Ot(e.slice(b,v),h,r,f,a,g===i-1)))}var x=2===u?",":"";y[0]="["+y[0]+x;for(g=1;g<y.length-1;g++)y[g]=" "+y[g]+x;var N=",\n";for(g=2;g<u;g++)N+="\n";return y[y.length-1]=" "+y[y.length-1]+"]"+(s?"":N),y}function Dt(e){for(var t=[],r=0;r<e.length;r+=2)t.push([e[r],e[r+1]]);return t}var At=function(){function e(e,t,r){var n=this;if(this.dtype=t,this.shape=e.slice(),this.size=ue(e),null!=r){var a=r.length;ae(a===this.size,(function(){return"Length of values '"+a+"' does not match the size inferred by the shape '"+n.size+"'."}))}if("complex64"===t)throw new Error("complex64 dtype TensorBuffers are not supported. Please create a TensorBuffer for the real and imaginary parts separately and call tf.complex(real, imag).");this.values=r||function(e,t){var r=null;if(null==e||"float32"===e)r=new Float32Array(t);else if("int32"===e)r=new Int32Array(t);else if("bool"===e)r=new Uint8Array(t);else{if("string"!==e)throw new Error("Unknown data type "+e);r=new Array(t)}return r}(t,this.size),this.strides=ve(e)}return e.prototype.set=function(e){for(var t=this,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];0===r.length&&(r=[0]),ae(r.length===this.rank,(function(){return"The number of provided coordinates ("+r.length+") must match the rank ("+t.rank+")"}));var a=this.locToIndex(r);this.values[a]=e},e.prototype.get=function(){for(var e,t,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];0===r.length&&(r=[0]);var a=0;try{for(var s=p(r),o=s.next();!o.done;o=s.next()){var i=o.value;if(i<0||i>=this.shape[a]){var u="Requested out of range element at "+r+".   Buffer shape="+this.shape;throw new Error(u)}a++}}catch(t){e={error:t}}finally{try{o&&!o.done&&(t=s.return)&&t.call(s)}finally{if(e)throw e.error}}for(var l=r[r.length-1],c=0;c<r.length-1;++c)l+=this.strides[c]*r[c];return this.values[l]},e.prototype.locToIndex=function(e){if(0===this.rank)return 0;if(1===this.rank)return e[0];for(var t=e[e.length-1],r=0;r<e.length-1;++r)t+=this.strides[r]*e[r];return t},e.prototype.indexToLoc=function(e){if(0===this.rank)return[];if(1===this.rank)return[e];for(var t=new Array(this.shape.length),r=0;r<t.length-1;++r)t[r]=Math.floor(e/this.strides[r]),e-=t[r]*this.strides[r];return t[t.length-1]=e,t},Object.defineProperty(e.prototype,"rank",{get:function(){return this.shape.length},enumerable:!0,configurable:!0}),e.prototype.toTensor=function(){return Mt().makeTensor(this.values,this.shape,this.dtype)},e}(),Mt=null,Ct=null;var Ft=function(){function e(e,t,r,n){this.kept=!1,this.isDisposedInternal=!1,this.shape=e.slice(),this.dtype=t||"float32",this.size=ue(e),this.strides=ve(e),this.dataId=r,this.id=n,this.rankType=this.rank<5?this.rank.toString():"higher"}return Object.defineProperty(e.prototype,"rank",{get:function(){return this.shape.length},enumerable:!0,configurable:!0}),e.prototype.buffer=function(){return i(this,void 0,void 0,(function(){var e;return u(this,(function(t){switch(t.label){case 0:return[4,this.data()];case 1:return e=t.sent(),[2,Ct.buffer(this.shape,this.dtype,e)]}}))}))},e.prototype.bufferSync=function(){return Ct.buffer(this.shape,this.dtype,this.dataSync())},e.prototype.array=function(){return i(this,void 0,void 0,(function(){var e;return u(this,(function(t){switch(t.label){case 0:return[4,this.data()];case 1:return e=t.sent(),[2,xe(this.shape,e,"complex64"===this.dtype)]}}))}))},e.prototype.arraySync=function(){return xe(this.shape,this.dataSync(),"complex64"===this.dtype)},e.prototype.data=function(){return i(this,void 0,void 0,(function(){var e,t;return u(this,(function(r){switch(r.label){case 0:return this.throwIfDisposed(),e=Mt().read(this.dataId),"string"!==this.dtype?[3,2]:[4,e];case 1:t=r.sent();try{return[2,t.map((function(e){return kt(e)}))]}catch(e){throw new Error("Failed to decode the string bytes into utf-8. To get the original bytes, call tensor.bytes().")}r.label=2;case 2:return[2,e]}}))}))},e.prototype.dataToGPU=function(e){return this.throwIfDisposed(),Mt().readToGPU(this.dataId,e)},e.prototype.dataSync=function(){this.throwIfDisposed();var e=Mt().readSync(this.dataId);if("string"===this.dtype)try{return e.map((function(e){return kt(e)}))}catch(e){throw new Error("Failed to decode the string bytes into utf-8. To get the original bytes, call tensor.bytes().")}return e},e.prototype.bytes=function(){return i(this,void 0,void 0,(function(){var e;return u(this,(function(t){switch(t.label){case 0:return this.throwIfDisposed(),[4,Mt().read(this.dataId)];case 1:return e=t.sent(),"string"===this.dtype?[2,e]:[2,new Uint8Array(e.buffer)]}}))}))},e.prototype.dispose=function(){this.isDisposed||(Mt().disposeTensor(this),this.isDisposedInternal=!0)},Object.defineProperty(e.prototype,"isDisposed",{get:function(){return this.isDisposedInternal},enumerable:!0,configurable:!0}),e.prototype.throwIfDisposed=function(){if(this.isDisposed)throw new Error("Tensor is disposed.")},e.prototype.print=function(e){return void 0===e&&(e=!1),Ct.print(this,e)},e.prototype.clone=function(){return this.throwIfDisposed(),Ct.clone(this)},e.prototype.toString=function(e){return void 0===e&&(e=!1),St(this.dataSync(),this.shape,this.dtype,e)},e.prototype.cast=function(e){return this.throwIfDisposed(),Ct.cast(this,e)},e.prototype.variable=function(e,t,r){return void 0===e&&(e=!0),this.throwIfDisposed(),Mt().makeVariable(this,e,t,r)},e}();Object.defineProperty(Ft,Symbol.hasInstance,{value:function(e){return!!e&&null!=e.data&&null!=e.dataSync&&null!=e.throwIfDisposed}}),Ce("Tensor",(function(){return Ft}));var Vt,Rt,zt,Lt,Pt,Bt=function(e){function t(t,r,n,a){var s=e.call(this,t.shape,t.dtype,t.dataId,a)||this;return s.trainable=r,s.name=n,s}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}(t,e),t.prototype.assign=function(e){if(e.dtype!==this.dtype)throw new Error("dtype of the new value ("+e.dtype+") and previous value ("+this.dtype+") must match");if(!pe(e.shape,this.shape))throw new Error("shape of the new value ("+e.shape+") and previous value ("+this.shape+") must match");Mt().disposeTensor(this),this.dataId=e.dataId,Mt().incRef(this,null)},t.prototype.dispose=function(){Mt().disposeVariable(this),this.isDisposedInternal=!0},t}(Ft);Object.defineProperty(Bt,Symbol.hasInstance,{value:function(e){return e instanceof Ft&&null!=e.assign&&e.assign instanceof Function}}),function(e){e.R0="R0",e.R1="R1",e.R2="R2",e.R3="R3",e.R4="R4",e.R5="R5",e.R6="R6"}(Vt||(Vt={})),function(e){e.float32="float32",e.int32="int32",e.bool="int32",e.complex64="complex64"}(Rt||(Rt={})),function(e){e.float32="float32",e.int32="int32",e.bool="bool",e.complex64="complex64"}(zt||(zt={})),function(e){e.float32="float32",e.int32="float32",e.bool="float32",e.complex64="complex64"}(Lt||(Lt={})),function(e){e.float32="complex64",e.int32="complex64",e.bool="complex64",e.complex64="complex64"}(Pt||(Pt={}));var Kt={float32:Lt,int32:Rt,bool:zt,complex64:Pt};function qt(e,t){if(e.dtype===t.dtype)return[e,t];var r=function(e,t){if("string"===e||"string"===t){if("string"===e&&"string"===t)return"string";throw new Error("Can not upcast "+e+" with "+t)}return Kt[e][t]}(e.dtype,t.dtype);return[e.cast(r),t.cast(r)]}function jt(e){var t=[];return Ut(e,t,new Set),t}function Ut(e,t,r){if(null!=e)if(e instanceof Ft)t.push(e);else if(n=e,Array.isArray(n)||"object"==typeof n){var n,a=e;for(var s in a){var o=a[s];r.has(o)||(r.add(o),Ut(o,t,r))}}}function Wt(e){return null!=e.kernelName}var Ht=function(){function e(){this.registeredVariables={},this.nextTapeNodeId=0,this.numBytes=0,this.numTensors=0,this.numStringTensors=0,this.numDataBuffers=0,this.gradientDepth=0,this.kernelDepth=0,this.scopeStack=[],this.numDataMovesStack=[],this.nextScopeId=0,this.tensorInfo=new WeakMap,this.profiling=!1,this.activeProfile={newBytes:0,newTensors:0,peakBytes:0,kernels:[],result:null,get kernelNames(){return Array.from(new Set(this.kernels.map((function(e){return e.name}))))}}}return e.prototype.dispose=function(){for(var e in this.registeredVariables)this.registeredVariables[e].dispose()},e}(),Gt=function(){function e(e){this.ENV=e,this.registry={},this.registryFactory={},this.pendingBackendInitId=0,this.state=new Ht}return e.prototype.ready=function(){return i(this,void 0,void 0,(function(){var e,t,r;return u(this,(function(n){switch(n.label){case 0:if(null!=this.pendingBackendInit)return[2,this.pendingBackendInit.then((function(){}))];if(null!=this.backendInstance)return[2];e=this.getSortedBackends(),t=0,n.label=1;case 1:return t<e.length?(r=e[t],[4,this.initializeBackend(r).success]):[3,5];case 2:return n.sent()?[4,this.setBackend(r)]:[3,4];case 3:return n.sent(),[2];case 4:return t++,[3,1];case 5:throw new Error("Could not initialize any backends, all backend initializations failed.")}}))}))},Object.defineProperty(e.prototype,"backend",{get:function(){if(null!=this.pendingBackendInit)throw new Error("Backend '"+this.backendName+"' has not yet been initialized. Make sure to await tf.ready() or await tf.setBackend() before calling other methods");if(null==this.backendInstance){var e=this.initializeBackendsAndReturnBest(),t=e.name;if(e.asyncInit)throw new Error("The highest priority backend '"+t+"' has not yet been initialized. Make sure to await tf.ready() or await tf.setBackend() before calling other methods");this.setBackend(t)}return this.backendInstance},enumerable:!0,configurable:!0}),e.prototype.backendNames=function(){return Object.keys(this.registryFactory)},e.prototype.findBackend=function(e){if(!(e in this.registry)){if(!(e in this.registryFactory))return null;if(this.initializeBackend(e).asyncInit)return null}return this.registry[e]},e.prototype.findBackendFactory=function(e){return e in this.registryFactory?this.registryFactory[e].factory:null},e.prototype.registerBackend=function(e,t,r){return void 0===r&&(r=1),e in this.registryFactory?(qe(e+" backend was already registered. Reusing existing backend factory."),!1):(this.registryFactory[e]={factory:t,priority:r},!0)},e.prototype.setBackend=function(e){return i(this,void 0,void 0,(function(){var t,r,n;return u(this,(function(a){switch(a.label){case 0:if(null==this.registryFactory[e])throw new Error("Backend name '"+e+"' not found in registry");return this.backendName=e,null!=this.registry[e]?[3,4]:(this.backendInstance=null,t=this.initializeBackend(e),r=t.success,t.asyncInit?[4,r]:[3,2]);case 1:return n=a.sent(),[3,3];case 2:n=r,a.label=3;case 3:if(!n)return[2,!1];a.label=4;case 4:return this.backendInstance=this.registry[e],this.setupRegisteredKernels(),this.profiler=new Tt(this.backendInstance),[2,!0]}}))}))},e.prototype.setupRegisteredKernels=function(){var e=this;Ge(this.backendName).forEach((function(t){null!=t.setupFunc&&t.setupFunc(e.backendInstance)}))},e.prototype.disposeRegisteredKernels=function(e){var t=this;Ge(e).forEach((function(r){null!=r.disposeFunc&&r.disposeFunc(t.registry[e])}))},e.prototype.initializeBackend=function(e){var t=this,r=this.registryFactory[e];if(null==r)throw new Error("Cannot initialize backend "+e+", no registration found.");try{var n=r.factory();if(!n||n instanceof re||"function"!=typeof n.then)return this.registry[e]=n,{success:!0,asyncInit:!1};var a=++this.pendingBackendInitId,s=n.then((function(r){return!(a<t.pendingBackendInitId)&&(t.registry[e]=r,t.pendingBackendInit=null,!0)})).catch((function(r){return a<t.pendingBackendInitId||(t.pendingBackendInit=null,qe("Initialization of backend "+e+" failed"),qe(r.stack||r.message)),!1}));return this.pendingBackendInit=s,{success:s,asyncInit:!0}}catch(t){return qe("Initialization of backend "+e+" failed"),qe(t.stack||t.message),{success:!1,asyncInit:!1}}},e.prototype.removeBackend=function(e){if(!(e in this.registryFactory))throw new Error(e+" backend not found in registry");this.backendName===e&&null!=this.pendingBackendInit&&this.pendingBackendInitId++,e in this.registry&&(this.disposeRegisteredKernels(e),this.registry[e].dispose(),delete this.registry[e]),delete this.registryFactory[e],this.backendName===e&&(this.pendingBackendInit=null,this.backendName=null,this.backendInstance=null)},e.prototype.getSortedBackends=function(){var e=this;if(0===Object.keys(this.registryFactory).length)throw new Error("No backend found in registry.");return Object.keys(this.registryFactory).sort((function(t,r){return e.registryFactory[r].priority-e.registryFactory[t].priority}))},e.prototype.initializeBackendsAndReturnBest=function(){for(var e=this.getSortedBackends(),t=0;t<e.length;t++){var r=e[t],n=this.initializeBackend(r),a=n.success,s=n.asyncInit;if(s||a)return{name:r,asyncInit:s}}throw new Error("Could not initialize any backends, all backend initializations failed.")},e.prototype.moveData=function(e,t){var r=this.state.tensorInfo.get(t),n=r.backend,a=this.readSync(t),s=n.refCount(t);n.disposeData(t,!0),r.backend=e,e.move(t,a,r.shape,r.dtype,s),this.shouldCheckForMemLeaks()&&this.state.numDataMovesStack[this.state.numDataMovesStack.length-1]++},e.prototype.tidy=function(e,t){var r,n=this,a=null;if(null==t){if("function"!=typeof e)throw new Error("Please provide a function to tidy()");t=e}else{if("string"!=typeof e&&!(e instanceof String))throw new Error("When calling with two arguments, the first argument to tidy() must be a string");if("function"!=typeof t)throw new Error("When calling with two arguments, the 2nd argument to tidy() must be a function");a=e}return this.scopedRun((function(){return n.startScope(a)}),(function(){return n.endScope(r)}),(function(){return(r=t())instanceof Promise&&console.error("Cannot return a Promise inside of tidy."),r}))},e.prototype.scopedRun=function(e,t,r){e();try{var n=r();return t(),n}catch(e){throw t(),e}},e.prototype.nextTensorId=function(){return e.nextTensorId++},e.prototype.nextVariableId=function(){return e.nextVariableId++},e.prototype.clone=function(e){var t=Zt.runKernel(Re,{x:e}),r={x:e};return this.addTapeNode(this.state.activeScope.name,r,[t],(function(e){return{x:function(){var t={x:e},r={dtype:"float32"};return Zt.runKernel(Fe,t,r)}}}),[],{}),t},e.prototype.runKernel=function(e,t,r){if(null==this.backendName&&this.backend,!(null!=We(e,this.backendName)))throw new Error("Kernel '"+e+"' not registered for backend '"+this.backendName+"'");return this.runKernelFunc({kernelName:e,inputs:t,attrs:r})},e.prototype.shouldCheckForMemLeaks=function(){return this.ENV.getBool("IS_TEST")},e.prototype.checkKernelForMemLeak=function(e,t,r){var n=this.backend.numDataIds(),a=0;r.forEach((function(e){a+="complex64"===e.dtype?3:1}));var s=this.state.numDataMovesStack[this.state.numDataMovesStack.length-1],o=n-t-a-s;if(o>0)throw new Error("Backend '"+this.backendName+"' has an internal memory leak ("+o+" data ids) after running '"+e+"'")},e.prototype.runKernelFunc=function(e){var t,r,n,a=this,s=[],o=this.isTapeOn(),i=this.state.numBytes,u=this.state.numTensors;this.shouldCheckForMemLeaks()&&this.state.numDataMovesStack.push(0),null==this.backendName&&this.backend;var p=Wt(e)?e.kernelName:null!=this.state.activeScope?this.state.activeScope.name:"";if(Wt(e)){var l=e.kernelName,c=e.inputs,d=e.attrs;null==this.backendName&&this.backend;var h=We(l,this.backendName);ae(null!=h,(function(){return"Cannot find registered kernel '"+l+"' for backend '"+a.backendName+"'"})),r=function(){var e=a.backend.numDataIds();n=h.kernelFunc({inputs:c,attrs:d,backend:a.backend});var t=Array.isArray(n)?n:[n];a.shouldCheckForMemLeaks()&&a.checkKernelForMemLeak(l,e,t);var r=t.map((function(e){return null!=e.rank?e:a.makeTensorFromTensorInfo(e)}));if(o){var i=a.getTensorsForGradient(l,c,r);s=a.saveTensorsForBackwardMode(i)}return r}}else{var f=e.forwardFunc,m=function(e){o&&(s=e.map((function(e){return a.keep(a.clone(e))})))};r=function(){var e=a.backend.numDataIds();n=a.tidy((function(){return f(a.backend,m)}));var t=Array.isArray(n)?n:[n];return a.shouldCheckForMemLeaks()&&a.checkKernelForMemLeak(p,e,t),t}}var y,g=e.inputs,v=e.attrs,b=Wt(e)?null:e.backwardsFunc;return this.scopedRun((function(){return a.state.kernelDepth++}),(function(){return a.state.kernelDepth--}),(function(){a.ENV.getBool("DEBUG")||a.state.profiling?(y=a.profiler.profileKernel(p,g,(function(){return r()})),a.ENV.getBool("DEBUG")&&a.profiler.logKernelProfile(y),t=y.outputs):t=r()})),o&&this.addTapeNode(p,g,t,b,s,v),this.state.profiling&&this.state.activeProfile.kernels.push({name:p,bytesAdded:this.state.numBytes-i,totalBytesSnapshot:this.state.numBytes,tensorsAdded:this.state.numTensors-u,totalTensorsSnapshot:this.state.numTensors,inputShapes:Object.keys(g).map((function(e){return null!=g[e]?g[e].shape:null})),outputShapes:t.map((function(e){return e.shape})),kernelTimeMs:y.timeMs,extraInfo:y.extraInfo}),Array.isArray(n)?t:t[0]},e.prototype.saveTensorsForBackwardMode=function(e){var t=this,r=e.map((function(e){return t.keep(t.clone(e))}));return r},e.prototype.getTensorsForGradient=function(e,t,r){var n=He(e);if(null!=n){var a=n.inputsToSave||[],s=n.outputsToSave||[],o=void 0;n.saveAllInputs?(ae(Array.isArray(t),(function(){return"saveAllInputs is true, expected inputs to be an array."})),o=Object.keys(t).map((function(e){return t[e]}))):o=a.map((function(e){return t[e]}));var i=r.filter((function(e,t){return s[t]}));return o.concat(i)}return[]},e.prototype.makeTensor=function(e,t,r,n){if(null==e)throw new Error("Values passed to engine.makeTensor() are null");r=r||"float32",n=n||this.backend;var a=e;"string"===r&&me(e[0])&&(a=e.map((function(e){return t=e,void 0===r&&(r="utf-8"),r=r||"utf-8",Oe().platform.encode(t,r);var t,r})));var s=n.write(a,t,r),o=new Ft(t,r,s,this.nextTensorId());if(this.trackTensor(o,n),"string"===r){var i=this.state.tensorInfo.get(s),u=function(e){if(null==e)return 0;var t=0;return e.forEach((function(e){return t+=e.length})),t}(a);this.state.numBytes+=u-i.bytes,i.bytes=u}return o},e.prototype.makeTensorFromDataId=function(e,t,r,n){var a={dataId:e,shape:t,dtype:r=r||"float32"};return this.makeTensorFromTensorInfo(a,n)},e.prototype.makeTensorFromTensorInfo=function(e,t){var r=e.dataId,n=e.shape,a=e.dtype,s=new Ft(n,a,r,this.nextTensorId());return this.trackTensor(s,t),s},e.prototype.makeVariable=function(e,t,r,n){void 0===t&&(t=!0),r=r||this.nextVariableId().toString(),null!=n&&n!==e.dtype&&(e=e.cast(n));var a=new Bt(e,t,r,this.nextTensorId());if(null!=this.state.registeredVariables[a.name])throw new Error("Variable with name "+a.name+" was already registered");return this.state.registeredVariables[a.name]=a,this.incRef(a,this.backend),a},e.prototype.trackTensor=function(e,t){this.state.numTensors++,"string"===e.dtype&&this.state.numStringTensors++;var r=0;"complex64"!==e.dtype&&"string"!==e.dtype&&(r=e.size*fe(e.dtype)),this.state.numBytes+=r,this.state.tensorInfo.has(e.dataId)||(this.state.numDataBuffers++,this.state.tensorInfo.set(e.dataId,{backend:t||this.backend,dtype:e.dtype,shape:e.shape,bytes:r})),e instanceof Bt||this.track(e)},e.prototype.incRef=function(e,t){this.trackTensor(e,t),this.backend.incRef(e.dataId)},e.prototype.removeDataId=function(e,t){this.state.tensorInfo.has(e)&&this.state.tensorInfo.get(e).backend===t&&(this.state.tensorInfo.delete(e),this.state.numDataBuffers--)},e.prototype.disposeTensor=function(e){if(this.state.tensorInfo.has(e.dataId)){var t=this.state.tensorInfo.get(e.dataId);if(this.state.numTensors--,"string"===e.dtype&&(this.state.numStringTensors--,this.state.numBytes-=t.bytes),"complex64"!==e.dtype&&"string"!==e.dtype){var r=e.size*fe(e.dtype);this.state.numBytes-=r}t.backend.disposeData(e.dataId)&&this.removeDataId(e.dataId,t.backend)}},e.prototype.disposeVariables=function(){for(var e in this.state.registeredVariables){var t=this.state.registeredVariables[e];this.disposeVariable(t)}},e.prototype.disposeVariable=function(e){this.disposeTensor(e),null!=this.state.registeredVariables[e.name]&&delete this.state.registeredVariables[e.name]},e.prototype.memory=function(){var e=this.backend.memory();return e.numTensors=this.state.numTensors,e.numDataBuffers=this.state.numDataBuffers,e.numBytes=this.state.numBytes,this.state.numStringTensors>0&&(e.unreliable=!0,null==e.reasons&&(e.reasons=[]),e.reasons.push("Memory usage by string tensors is approximate (2 bytes per character)")),e},e.prototype.profile=function(e){return i(this,void 0,void 0,(function(){var t,r,n,a,s,o,i,l,d,h,f;return u(this,(function(u){switch(u.label){case 0:return this.state.profiling=!0,t=this.state.numBytes,r=this.state.numTensors,this.state.activeProfile.kernels=[],n=this.state.activeProfile,[4,e()];case 1:n.result=u.sent(),this.state.profiling=!1,this.state.activeProfile.peakBytes=Math.max.apply(Math,c(this.state.activeProfile.kernels.map((function(e){return e.totalBytesSnapshot})))),this.state.activeProfile.newBytes=this.state.numBytes-t,this.state.activeProfile.newTensors=this.state.numTensors-r,u.label=2;case 2:u.trys.push([2,8,9,10]),a=p(this.state.activeProfile.kernels),s=a.next(),u.label=3;case 3:return s.done?[3,7]:(o=s.value,i=o,[4,o.kernelTimeMs]);case 4:return i.kernelTimeMs=u.sent(),l=o,[4,o.extraInfo];case 5:l.extraInfo=u.sent(),u.label=6;case 6:return s=a.next(),[3,3];case 7:return[3,10];case 8:return d=u.sent(),h={error:d},[3,10];case 9:try{s&&!s.done&&(f=a.return)&&f.call(a)}finally{if(h)throw h.error}return[7];case 10:return[2,this.state.activeProfile]}}))}))},e.prototype.isTapeOn=function(){return this.state.gradientDepth>0&&0===this.state.kernelDepth},e.prototype.addTapeNode=function(e,t,r,n,a,s){var o=this,i={id:this.state.nextTapeNodeId++,kernelName:e,inputs:t,outputs:r,saved:a},u=He(e);null!=u&&(n=u.gradFunc),null!=n&&(i.gradient=function(e){return e=e.map((function(e,t){if(null==e){var n=r[t],a=we(n.size,n.dtype);return o.makeTensor(a,n.shape,n.dtype)}return e})),n(e.length>1?e:e[0],a,s)}),this.state.activeTape.push(i)},e.prototype.keep=function(e){return e.kept=!0,e},e.prototype.startTape=function(){0===this.state.gradientDepth&&(this.state.activeTape=[]),this.state.gradientDepth++},e.prototype.endTape=function(){this.state.gradientDepth--},e.prototype.startScope=function(e){var t={track:[],name:"unnamed scope",id:this.state.nextScopeId++};e&&(t.name=e),this.state.scopeStack.push(t),this.state.activeScope=t},e.prototype.endScope=function(e){for(var t=this,r=jt(e),n=new Set(r.map((function(e){return e.id}))),a=0;a<this.state.activeScope.track.length;a++){var s=this.state.activeScope.track[a];s.kept||n.has(s.id)||s.dispose()}var o=this.state.scopeStack.pop();this.state.activeScope=0===this.state.scopeStack.length?null:this.state.scopeStack[this.state.scopeStack.length-1],r.forEach((function(e){e.kept||e.scopeId!==o.id||t.track(e)}))},e.prototype.gradients=function(e,t,r,n){var a=this;if(void 0===n&&(n=!1),ae(t.length>0,(function(){return"gradients() received an empty list of xs."})),null!=r&&"float32"!==r.dtype)throw new Error("dy must have 'float32' dtype, but has '"+r.dtype+"'");var s=this.scopedRun((function(){return a.startTape()}),(function(){return a.endTape()}),(function(){return a.tidy("forward",e)}));ae(s instanceof Ft,(function(){return"The result y returned by f() must be a tensor."}));var o=function(e,t,r){for(var n={},a={},s=0;s<t.length;s++)n[t[s].id]=!0;for(s=0;s<e.length;s++){var o=(f=e[s]).inputs;for(var i in o){for(var u=o[i],p=!1,l=0;l<t.length;l++)if(n[u.id]){f.outputs.forEach((function(e){return n[e.id]=!0})),p=!0,a[f.id]=!0;break}if(p)break}}var c={};c[r.id]=!0;var d={};for(s=e.length-1;s>=0;s--)for(o=(f=e[s]).inputs,l=0;l<f.outputs.length;l++)if(c[f.outputs[l].id]){for(var i in o)c[o[i].id]=!0,d[f.id]=!0;break}var h=[];for(s=0;s<e.length;s++){var f;if(a[(f=e[s]).id]&&d[f.id]){var m={};for(var i in f.inputs){var y=f.inputs[i];n[y.id]&&(m[i]=y)}var g=Object.assign({},f);g.inputs=m,g.outputs=f.outputs,h.push(g)}}return h}(this.state.activeTape,t,s);if(!n&&0===o.length&&t.length>0)throw new Error("Cannot compute gradient of y=f(x) with respect to x. Make sure that the f you passed encloses all operations that lead from x to y.");return this.tidy("backward",(function(){var e,n,i={};i[s.id]=null==r?(e=s.shape,n=Ne(ue(e),"float32"),Zt.makeTensor(n,e,"float32")):r,function(e,t,r,n){for(var a=function(a){var s=t[a],o=[];if(s.outputs.forEach((function(t){var r=e[t.id];null!=r?o.push(r):o.push(null)})),null==s.gradient)throw new Error("Cannot compute gradient: gradient function not found for "+s.kernelName+".");var i=s.gradient(o),u=function(t){if(!(t in i))throw new Error("Cannot backprop through input "+t+". Available gradients found: "+Object.keys(i)+".");var a=r((function(){return i[t]()}));if("float32"!==a.dtype)throw new Error("Error in gradient for op "+s.kernelName+". The gradient of input "+t+" must have 'float32' dtype, but has '"+a.dtype+"'");var o=s.inputs[t];if(!pe(a.shape,o.shape))throw new Error("Error in gradient for op "+s.kernelName+". The gradient of input '"+t+"' has shape '"+a.shape+"', which does not match the shape of the input '"+o.shape+"'");if(null==e[o.id])e[o.id]=a;else{var u=e[o.id];e[o.id]=n(u,a),u.dispose()}};for(var p in s.inputs)u(p)},s=t.length-1;s>=0;s--)a(s)}(i,o,(function(e){return a.tidy(e)}),Qt);var u=t.map((function(e){return i[e.id]}));return 0===a.state.gradientDepth&&(a.state.activeTape.forEach((function(e){var t,r;try{for(var n=p(e.saved),a=n.next();!a.done;a=n.next()){a.value.dispose()}}catch(e){t={error:e}}finally{try{a&&!a.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}})),a.state.activeTape=null),{value:s,grads:u}}))},e.prototype.customGrad=function(e){var t=this;return ae(ge(e),(function(){return"The f passed in customGrad(f) must be a function."})),function(){for(var r,n=[],a=0;a<arguments.length;a++)n[a]=arguments[a];ae(n.every((function(e){return e instanceof Ft})),(function(){return"The args passed in customGrad(f)(x1, x2,...) must all be tensors"}));var s={};n.forEach((function(e,t){s[t]=e}));var o=function(t,a){return ae((r=e.apply(void 0,c(n,[a]))).value instanceof Ft,(function(){return"The function f passed in customGrad(f) must return an object where `obj.value` is a tensor"})),ae(ge(r.gradFunc),(function(){return"The function f passed in customGrad(f) must return an object where `obj.gradFunc` is a function."})),r.value},i=function(e,t){var a=r.gradFunc(e,t),s=Array.isArray(a)?a:[a];ae(s.length===n.length,(function(){return"The function f passed in customGrad(f) must return an object where `obj.gradFunc` is a function that returns the same number of tensors as inputs passed to f(...)."})),ae(s.every((function(e){return e instanceof Ft})),(function(){return"The function f passed in customGrad(f) must return an object where `obj.gradFunc` is a function that returns a list of only tensors."}));var o={};return s.forEach((function(e,t){o[t]=function(){return e}})),o};return t.runKernelFunc({forwardFunc:o,backwardsFunc:i,inputs:s})}},e.prototype.readSync=function(e){return this.state.tensorInfo.get(e).backend.readSync(e)},e.prototype.read=function(e){return this.state.tensorInfo.get(e).backend.read(e)},e.prototype.readToGPU=function(e,t){return this.state.tensorInfo.get(e).backend.readToGPU(e,t)},e.prototype.time=function(e){return i(this,void 0,void 0,(function(){var t,r;return u(this,(function(n){switch(n.label){case 0:return t=wt(),[4,this.backend.time(e)];case 1:return(r=n.sent()).wallMs=wt()-t,[2,r]}}))}))},e.prototype.track=function(e){return null!=this.state.activeScope&&(e.scopeId=this.state.activeScope.id,this.state.activeScope.track.push(e)),e},Object.defineProperty(e.prototype,"registeredVariables",{get:function(){return this.state.registeredVariables},enumerable:!0,configurable:!0}),e.prototype.reset=function(){for(var e in this.pendingBackendInitId++,this.state.dispose(),this.ENV.reset(),this.state=new Ht,this.registry)this.disposeRegisteredKernels(e),this.registry[e].dispose(),delete this.registry[e];this.backendName=null,this.backendInstance=null,this.pendingBackendInit=null},e}();Gt.nextTensorId=0,Gt.nextVariableId=0;var Zt=function(){var e=Me();if(null==e._tfengine){var t=new Se(e);e._tfengine=new Gt(t)}return function(e){Ae=e}(e._tfengine.ENV),Mt=function(){return e._tfengine},e._tfengine}();function Qt(e,t){var r={a:e,b:t};return Zt.runKernel("Add",r)}function Yt(e,t){var r=e;if(he(e))return"string"===t?[]:[e.length];if(!Array.isArray(e))return[];for(var n=[];Array.isArray(r)||he(r)&&"string"!==t;)n.push(r.length),r=r[0];return Array.isArray(e)&&Oe().getBool("TENSORLIKE_CHECK_SHAPE_CONSISTENCY")&&Xt(e,n,[]),n}function Xt(e,t,r){if(r=r||[],Array.isArray(e)||he(e)){ae(t.length>0,(function(){return"Element arr["+r.join("][")+"] should be a primitive, but is an array of "+e.length+" elements"})),ae(e.length===t[0],(function(){return"Element arr["+r.join("][")+"] should have "+t[0]+" elements, but has "+e.length+" elements"}));for(var n=t.slice(1),a=0;a<e.length;++a)Xt(e[a],n,r.concat(a))}else ae(0===t.length,(function(){return"Element arr["+r.join("][")+"] is a primitive, but should be an array/TypedArray of "+t[0]+" elements"}))}function Jt(e,t,r,n){if("string_or_numeric"!==e){if(null==e)throw new Error("Expected dtype cannot be null.");if("numeric"!==e&&e!==t||"numeric"===e&&"string"===t)throw new Error("Argument '"+r+"' passed to '"+n+"' must be "+e+" tensor, but got "+t+" tensor")}}function $t(e,t,r,n){if(void 0===n&&(n="numeric"),e instanceof Ft)return Jt(n,e.dtype,t,r),e;var a=ye(e);if("string"!==a&&["bool","int32","float32"].indexOf(n)>=0&&(a=n),Jt(n,a,t,r),null==e||!he(e)&&!Array.isArray(e)&&"number"!=typeof e&&"boolean"!=typeof e&&"string"!=typeof e){var s=null==e?"null":e.constructor.name;throw new Error("Argument '"+t+"' passed to '"+r+"' must be a Tensor or TensorLike, but got '"+s+"'")}var o=Yt(e,a);he(e)||Array.isArray(e)||(e=[e]);var i="string"!==a?Nt(e,a):ie(e,[],!0);return Zt.makeTensor(i,o,a)}function er(e,t,r,n){if(void 0===n&&(n="numeric"),!Array.isArray(e))throw new Error("Argument "+t+" passed to "+r+" must be a `Tensor[]` or `TensorLike[]`");return e.map((function(e,a){return $t(e,t+"["+a+"]",r,n)}))}var tr="__op";function rr(e){var t=Object.keys(e);if(1!==t.length)throw new Error("Please provide an object with a single key (operation name) mapping to a function. Got an object with "+t.length+" keys.");var r=t[0],n=e[r];r.endsWith("_")&&(r=r.substring(0,r.length-1)),r+=tr;var a=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];Zt.startScope(r);try{var a=n.apply(void 0,c(e));return Te(a)&&console.error("Cannot return a Promise inside of tidy."),Zt.endScope(a),a}catch(e){throw Zt.endScope(null),e}};return Object.defineProperty(a,"name",{value:r,configurable:!0}),a}var nr=rr({abs_:function(e){var t=$t(e,"x","abs");if("complex64"===t.dtype){var r={x:t};return Zt.runKernel("ComplexAbs",r)}return r={x:t},Zt.runKernel("Abs",r)}});var ar=rr({acos_:function(e){var t={x:$t(e,"x","acos")};return Zt.runKernel("Acos",t)}});var sr=rr({acosh_:function(e){var t={x:$t(e,"x","acosh")};return Zt.runKernel("Acosh",t)}});var or=rr({add_:function(e,t){var r,n=$t(e,"a","add"),a=$t(t,"b","add"),s={a:n=(r=l(qt(n,a),2))[0],b:a=r[1]};return Zt.runKernel("Add",s)}});var ir=rr({addN_:function(e){ae(Array.isArray(e),(function(){return"The argument passed to tf.addN() must be a list of tensors"})),ae(e.length>=1,(function(){return"Must pass at least one tensor to tf.addN(), but got "+e.length}));var t=e.map((function(e,t){return $t(e,"tensors"+t,"addN")})),r=t[0];t.forEach((function(e){if(e.dtype!==r.dtype)throw new Error("All tensors passed to tf.addN() must have the same dtype")})),t.forEach((function(e){if(!pe(e.shape,r.shape))throw new Error("All tensors passed to tf.addN() must have the same shape")}));var n=t;return Zt.runKernel("AddN",n)}});var ur=rr({all_:function(e,t,r){void 0===t&&(t=null),void 0===r&&(r=!1);var n={x:$t(e,"x","all","bool")},a={axis:t,keepDims:r};return Zt.runKernel("All",n,a)}});var pr=rr({any_:function(e,t,r){void 0===t&&(t=null),void 0===r&&(r=!1);var n={x:$t(e,"x","any","bool")},a={axis:t,keepDims:r};return Zt.runKernel("Any",n,a)}});var lr=rr({argMax_:function(e,t){void 0===t&&(t=0);var r={x:$t(e,"x","argMax")},n={axis:t};return Zt.runKernel("ArgMax",r,n)}});var cr=rr({argMin_:function(e,t){void 0===t&&(t=0);var r={x:$t(e,"x","argMin")},n={axis:t};return Zt.runKernel("ArgMin",r,n)}});var dr=rr({asin_:function(e){var t={x:$t(e,"x","asin")};return Zt.runKernel("Asin",t)}});var hr=rr({asinh_:function(e){var t={x:$t(e,"x","asinh")};return Zt.runKernel("Asinh",t)}});var fr=rr({atan_:function(e){var t={x:$t(e,"x","atan")};return Zt.runKernel("Atan",t)}});var mr=rr({atan2_:function(e,t){var r,n=$t(e,"a","atan2"),a=$t(t,"b","atan2"),s={a:n=(r=l(qt(n,a),2))[0],b:a=r[1]};return Zt.runKernel("Atan2",s)}});var yr=rr({atanh_:function(e){var t={x:$t(e,"x","atanh")};return Zt.runKernel("Atanh",t)}});var gr=rr({cast_:function(e,t){var r=$t(e,"x","cast");if(!function(e){return"bool"===e||"complex64"===e||"float32"===e||"int32"===e||"string"===e}(t))throw new Error("Failed to cast to unknown dtype "+t);if("string"===t&&"string"!==r.dtype||"string"!==t&&"string"===r.dtype)throw new Error("Only strings can be casted to strings");var n={x:r},a={dtype:t};return Zt.runKernel(Fe,n,a)}});function vr(e,t,r,n,a,s,o,i){var u,p;void 0===o&&(o=!1),void 0===i&&(i="channelsLast");var c=l([-1,-1,-1,-1],4),d=c[0],h=c[1],f=c[2],m=c[3];if("channelsLast"===i)d=(u=l(e,4))[0],h=u[1],f=u[2],m=u[3];else{if("channelsFirst"!==i)throw new Error("Unknown dataFormat "+i);d=(p=l(e,4))[0],m=p[1],h=p[2],f=p[3]}var y,g=l(t,4),v=g[0],b=g[1],x=g[3],N=l(br(r),2),w=N[0],k=N[1],T=l(br(n),2),_=T[0],S=T[1],E=xr(v,_),I=xr(b,S),O=function(e,t,r,n,a,s,o,i,u){var p,l,c;if("number"==typeof e){p={top:e,bottom:e,left:e,right:e,type:0===e?"VALID":"NUMBER"};var d=function(e,t,r,n,a){null==n&&(n=function(e,t,r,n){void 0===n&&(n=1);var a=xr(t,n);return Math.floor((e[0]*(r-1)-r+a)/2)}(e,t,r));var s=e[0],o=e[1],i=Nr((s-t+2*n)/r+1,a),u=Nr((o-t+2*n)/r+1,a);return[i,u]}([t,r],s,n,e,i);l=d[0],c=d[1]}else if("same"===e){l=Math.ceil(t/n),c=Math.ceil(r/a);var h=Math.max(0,(l-1)*n+s-t),f=Math.max(0,(c-1)*a+o-r);p={top:m=Math.floor(h/2),bottom:y=h-m,left:g=Math.floor(f/2),right:v=f-g,type:"SAME"}}else if("valid"===e)p={top:0,bottom:0,left:0,right:0,type:"VALID"},l=Math.ceil((t-s+1)/n),c=Math.ceil((r-o+1)/a);else{if("object"!=typeof e)throw Error("Unknown padding parameter: "+e);var m,y,g,v;p={top:m="channelsLast"===u?e[1][0]:e[2][0],bottom:y="channelsLast"===u?e[1][1]:e[2][1],left:g="channelsLast"===u?e[2][0]:e[3][0],right:v="channelsLast"===u?e[2][1]:e[3][1],type:0===m&&0===y&&0===g&&0===v?"VALID":"EXPLICIT"},l=Nr((t-s+m+y)/n+1,i),c=Nr((r-o+g+v)/a+1,i)}return{padInfo:p,outHeight:l,outWidth:c}}(a,h,f,w,k,E,I,s,i),D=O.padInfo,A=O.outHeight,M=O.outWidth,C=o?x*m:x;return"channelsFirst"===i?y=[d,C,A,M]:"channelsLast"===i&&(y=[d,A,M,C]),{batchSize:d,dataFormat:i,inHeight:h,inWidth:f,inChannels:m,outHeight:A,outWidth:M,outChannels:C,padInfo:D,strideHeight:w,strideWidth:k,filterHeight:v,filterWidth:b,effectiveFilterHeight:E,effectiveFilterWidth:I,dilationHeight:_,dilationWidth:S,inShape:e,outShape:y,filterShape:t}}function br(e){return"number"==typeof e?[e,e,e]:2===e.length?[e[0],e[1],1]:e}function xr(e,t){return t<=1?e:e+(e-1)*(t-1)}function Nr(e,t){if(!t)return Math.trunc(e);switch(t){case"round":return Math.round(e);case"ceil":return Math.ceil(e);case"floor":return Math.floor(e);default:throw new Error("Unknown roundingMode "+t)}}function wr(e){var t=l(br(e),3),r=t[0],n=t[1],a=t[2];return 1===r&&1===n&&1===a}function kr(e,t){return wr(e)||wr(t)}function Tr(e,t,r){if(null!=r){if("string"==typeof t)throw Error("Error in "+e+": pad must be an integer when using dimRoundingMode "+r+" but got pad "+t+".");if("number"==typeof t)ae(le(t),(function(){return"Error in "+e+": pad must be an integer when using dimRoundingMode "+r+" but got pad "+t+"."}));else{if("object"!=typeof t)throw Error("Error in "+e+": Unknown padding parameter: "+t);t.forEach((function(t){t.forEach((function(t){ae(le(t),(function(){return"Error in "+e+": pad must be an integer when using dimRoundingMode "+r+" but got pad "+t+"."}))}))}))}}}var _r=rr({reshape_:function(e,t){var r={x:$t(e,"x","reshape","string_or_numeric")},n={shape:t};return Zt.runKernel("Reshape",r,n)}});var Sr=rr({avgPool_:function(e,t,r,n,a){var s=$t(e,"x","avgPool","float32");ae(kr(r,1),(function(){return"Error in avgPool: Either strides or dilations must be 1. Got strides "+r+" and dilations '1'"}));var o=s,i=!1;3===s.rank&&(i=!0,o=_r(s,[1,s.shape[0],s.shape[1],s.shape[2]])),ae(4===o.rank,(function(){return"Error in avgPool: x must be rank 4 but got rank "+o.rank+"."})),Tr("avgPool",n,a);var u={x:o},p={filterSize:t,strides:r,pad:n,dimRoundingMode:a},l=Zt.runKernel("AvgPool",u,p);return l=gr(l,s.dtype),i?_r(l,[l.shape[1],l.shape[2],l.shape[3]]):l}});var Er=rr({avgPool3d_:function(e,t,r,n,a,s){void 0===s&&(s="NDHWC");var o=$t(e,"x","avgPool3d","float32"),i=o,u=!1;4===o.rank&&(u=!0,i=_r(o,[1,o.shape[0],o.shape[1],o.shape[2],o.shape[3]])),ae(5===i.rank,(function(){return"Error in avgPool3d: x must be rank 5 but got rank "+i.rank+"."})),ae("NDHWC"===s,(function(){return"Error in avgPool3d: Only NDHWC is currently supported, but got dataFormat of "+s})),Tr("avgPool3d",n,a);var p={x:i},l={filterSize:t,strides:r,pad:n,dimRoundingMode:a,dataFormat:s},c=Zt.runKernel("AvgPool3D",p,l);return c=gr(c,i.dtype),u?_r(c,[c.shape[1],c.shape[2],c.shape[3],c.shape[4]]):c}});var Ir=rr({clone_:function(e){var t={x:$t(e,"x","clone","string_or_numeric")};return Zt.runKernel(Re,t)}});var Or=rr({concat_:function(e,t){void 0===t&&(t=0),ae(e.length>=1,(function(){return"Pass at least one tensor to concat"}));var r=er(e,"tensors","concat","string_or_numeric");if("complex64"===r[0].dtype&&r.forEach((function(e){if("complex64"!==e.dtype)throw new Error("Cannot concatenate complex64 tensors with a tensor\n          with dtype "+e.dtype+". ")})),1===r.length)return Ir(r[0]);var n=r,a={axis:t};return Zt.runKernel("Concat",n,a)}});var Dr=rr({matMul_:function(e,t,r,n){var a;void 0===r&&(r=!1),void 0===n&&(n=!1);var s=$t(e,"a","matMul"),o=$t(t,"b","matMul"),i={a:s=(a=l(qt(s,o),2))[0],b:o=a[1]},u={transposeA:r,transposeB:n};return Zt.runKernel("BatchMatMul",i,u)}});var Ar=rr({mul_:function(e,t){var r,n=$t(e,"a","mul"),a=$t(t,"b","mul"),s={a:n=(r=l(qt(n,a),2))[0],b:a=r[1]};return Zt.runKernel("Multiply",s)}});var Mr=rr({sigmoid_:function(e){var t={x:$t(e,"x","sigmoid","float32")};return Zt.runKernel("Sigmoid",t)}});var Cr=rr({slice_:function(e,t,r){var n=$t(e,"x","slice","string_or_numeric");if(0===n.rank)throw new Error("Slicing scalar is not possible");var a={x:n},s={begin:t,size:r};return Zt.runKernel("Slice",a,s)}});var Fr=rr({tanh_:function(e){var t={x:$t(e,"x","tanh","float32")};return Zt.runKernel("Tanh",t)}});var Vr=rr({basicLSTMCell_:function(e,t,r,n,a,s){var o=$t(e,"forgetBias","basicLSTMCell"),i=$t(t,"lstmKernel","basicLSTMCell"),u=$t(r,"lstmBias","basicLSTMCell"),p=$t(n,"data","basicLSTMCell"),l=$t(a,"c","basicLSTMCell"),c=$t(s,"h","basicLSTMCell"),d=Or([p,c],1),h=Dr(d,i),f=or(h,u),m=f.shape[0],y=f.shape[1]/4,g=[m,y],v=Cr(f,[0,0],g),b=Cr(f,[0,y],g),x=Cr(f,[0,2*y],g),N=Cr(f,[0,3*y],g),w=or(Ar(Mr(v),Fr(b)),Ar(l,Mr(or(o,x))));return[w,Ar(Fr(w),Mr(N))]}});var Rr=rr({batchToSpaceND_:function(e,t,r){var n=$t(e,"x","batchToSpaceND"),a=t.reduce((function(e,t){return e*t}));ae(n.rank>=1+t.length,(function(){return"input rank is "+n.rank+" but should be > than blockShape.length "+t.length})),ae(r.length===t.length,(function(){return"crops.length is "+r.length+" but should be equal to blockShape.length  "+t.length})),ae(n.shape[0]%a==0,(function(){return"input tensor batch is "+n.shape[0]+" but is not divisible by the product of the elements of blockShape "+t.join(" * ")+" === "+a}));var s={x:n},o={blockShape:t,crops:r};return Zt.runKernel("BatchToSpaceND",s,o)}});var zr=rr({batchNorm_:function(e,t,r,n,a,s){null==s&&(s=.001);var o,i,u=$t(e,"x","batchNorm"),p=$t(t,"mean","batchNorm"),l=$t(r,"variance","batchNorm");null!=a&&(o=$t(a,"scale","batchNorm")),null!=n&&(i=$t(n,"offset","batchNorm")),ae(p.rank===l.rank,(function(){return"Batch normalization gradient requires mean and variance to have equal ranks."})),ae(null==i||p.rank===i.rank,(function(){return"Batch normalization gradient requires mean and offset to have equal ranks."})),ae(null==o||p.rank===o.rank,(function(){return"Batch normalization gradient requires mean and scale to have equal ranks."}));var c=function(e){return 0===e.rank||1===e.rank?_r(e,[1,1,1,e.size]):2===e.rank?_r(e,[1,1,e.shape[0],e.shape[1]]):3===e.rank?_r(e,[1,e.shape[0],e.shape[1],e.shape[2]]):e}(u),d={x:c,scale:o,offset:i,mean:p,variance:l},h={varianceEpsilon:s},f=Zt.runKernel("FusedBatchNorm",d,h);return _r(f,u.shape)}});var Lr=rr({batchNorm2d_:function(e,t,r,n,a,s){var o,i,u=$t(e,"x","batchNorm"),p=$t(t,"mean","batchNorm"),l=$t(r,"variance","batchNorm");return null!=a&&(o=$t(a,"scale","batchNorm")),null!=n&&(i=$t(n,"offset","batchNorm")),ae(2===u.rank,(function(){return"Error in batchNorm2D: x must be rank 2 but got rank "+u.rank+"."})),ae(2===p.rank||1===p.rank,(function(){return"Error in batchNorm2D: mean must be rank 2 or rank 1 but got rank "+p.rank+"."})),ae(2===l.rank||1===l.rank,(function(){return"Error in batchNorm2D: variance must be rank 2 or rank 1 but got rank "+l.rank+"."})),null!=o&&ae(2===o.rank||1===o.rank,(function(){return"Error in batchNorm2D: scale must be rank 2 or rank 1 but got rank "+o.rank+"."})),null!=i&&ae(2===i.rank||1===i.rank,(function(){return"Error in batchNorm2D: offset must be rank 2 or rank 1 but got rank "+i.rank+"."})),zr(u,p,l,i,o,s)}});var Pr=rr({batchNorm3d_:function(e,t,r,n,a,s){var o,i,u=$t(e,"x","batchNorm"),p=$t(t,"mean","batchNorm"),l=$t(r,"variance","batchNorm");return null!=a&&(o=$t(a,"scale","batchNorm")),null!=n&&(i=$t(n,"offset","batchNorm")),ae(3===u.rank,(function(){return"Error in batchNorm3D: x must be rank 3 but got rank "+u.rank+"."})),ae(3===p.rank||1===p.rank,(function(){return"Error in batchNorm3D: mean must be rank 3 or rank 1 but got rank "+p.rank+"."})),ae(3===l.rank||1===l.rank,(function(){return"Error in batchNorm3D: variance must be rank 3 or rank 1 but got rank "+l.rank+"."})),null!=o&&ae(3===o.rank||1===o.rank,(function(){return"Error in batchNorm3D: scale must be rank 3 or rank 1 but got rank "+o.rank+"."})),null!=i&&ae(3===i.rank||1===i.rank,(function(){return"Error in batchNorm3D: offset must be rank 3 or rank 1 but got rank "+i.rank+"."})),zr(u,p,l,i,o,s)}});var Br=rr({batchNorm4d_:function(e,t,r,n,a,s){var o,i,u=$t(e,"x","batchNorm"),p=$t(t,"mean","batchNorm"),l=$t(r,"variance","batchNorm");return null!=a&&(o=$t(a,"scale","batchNorm")),null!=n&&(i=$t(n,"offset","batchNorm")),ae(4===u.rank,(function(){return"Error in batchNorm4D: x must be rank 4 but got rank "+u.rank+"."})),ae(4===p.rank||1===p.rank,(function(){return"Error in batchNorm4D: mean must be rank 4 or rank 1 but got rank "+p.rank+"."})),ae(4===l.rank||1===l.rank,(function(){return"Error in batchNorm4D: variance must be rank 4 or rank 1 but got rank "+l.rank+"."})),null!=o&&ae(4===o.rank||1===o.rank,(function(){return"Error in batchNorm4D: scale must be rank 4 or rank 1 but got rank "+o.rank+"."})),null!=i&&ae(4===i.rank||1===i.rank,(function(){return"Error in batchNorm4D: offset must be rank 4 or rank 1 but got rank "+i.rank+"."})),zr(u,p,l,i,o,s)}});var Kr=rr({bincount_:function(e,t,r){var n=$t(e,"x","bincount"),a=$t(t,"weights","bincount");ae("int32"===n.dtype,(function(){return"Error in bincount: input dtype must be int32, but got "+n.dtype})),ae(r>=0,(function(){return"size must be non-negative, but got "+r+"."})),ae(a.size===n.size||0===a.size,(function(){return"Error in bincount: weights must have the same size as input or0-length, but got input shape: "+n.shape+", weights shape: "+a.shape+"."}));var s={x:n,weights:a},o={size:r};return Zt.runKernel("Bincount",s,o)}});var qr=rr({broadcastArgs_:function(e,t){var r=$t(e,"s0","broadcastArgs","int32"),n=$t(t,"s1","broadcastArgs","int32");if(1!==r.rank)throw new Error("broadcastArgs(): first input must be a vector (rank=1). Has rank "+r.rank);if(1!==n.rank)throw new Error("broadcastArgs(): second input must be a vector (rank=1). Has rank "+n.rank);var a={s0:r,s1:n};return Zt.runKernel("BroadcastArgs",a)}});var jr=rr({broadcastTo_:function(e,t){var r=$t(e,"broadcastTo","x"),n=r.shape;if(t.some((function(e){return!(e>0)||e%1!=0})))throw new Error("broadcastTo(): Invalid broadcast shape ["+t+"].");if(t.length<r.rank)throw new Error("broadcastTo(): shape.length="+t.length+" < input.rank="+r.rank+".");if(t.length>r.rank){for(var a=r.shape.slice();a.length<t.length;)a.unshift(1);r=_r(r,a)}for(var s=r.shape,o=Array.from(t),i=t.length-1;i>=0;i--)if(s[i]===t[i])o[i]=1;else if(1!==r.shape[i])throw new Error("broadcastTo(): ["+n+"] cannot be broadcast to ["+t+"].");var u=o.map((function(e,t){return e>1?t:-1})).filter((function(e){return e>=0}));if(0===u.length)return Ir(r);var p={x:r},l={reps:o};return Zt.runKernel(ze,p,l)}});function Ur(e,t,r){return void 0===t&&(t="float32"),t=t||"float32",ke(e),new At(e,t,r)}var Wr=rr({ceil_:function(e){var t={x:$t(e,"x","ceil","float32")};return Zt.runKernel("Ceil",t)}});function Hr(e,t,r){var n={shape:e,value:t,dtype:r};return Zt.runKernel("Fill",{},n)}var Gr=rr({clipByValue_:function(e,t,r){var n=$t(e,"x","clipByValue");if(ae(t<=r,(function(){return"Error in clip: min ("+t+") must be less than or equal to max ("+r+")."})),t===r)return Hr(n.shape,t,n.dtype);var a={x:n},s={clipValueMin:t,clipValueMax:r};return Zt.runKernel("ClipByValue",a,s)}});var Zr=rr({complex_:function(e,t){var r=$t(e,"real","complex"),n=$t(t,"imag","complex");se(r.shape,n.shape,"real and imag shapes, "+r.shape+" and "+n.shape+", must match in call to tf.complex().");var a={real:r,imag:n};return Zt.runKernel("Complex",a)}});var Qr=rr({concat1d_:function(e){return Or(e,0)}});var Yr=rr({concat2d_:function(e,t){return Or(e,t)}});var Xr=rr({concat3d_:function(e,t){return Or(e,t)}});var Jr=rr({concat4d_:function(e,t){return Or(e,t)}});var $r=rr({conv2d_:function(e,t,r,n,a,s,o){void 0===a&&(a="NHWC"),void 0===s&&(s=[1,1]);var i=$t(e,"x","conv2d","float32"),u=$t(t,"filter","conv2d","float32"),p=i,l=!1;3===i.rank&&(l=!0,p=_r(i,[1,i.shape[0],i.shape[1],i.shape[2]])),ae(4===p.rank,(function(){return"Error in conv2d: input must be rank 4, but got rank "+p.rank+"."})),ae(4===u.rank,(function(){return"Error in conv2d: filter must be rank 4, but got rank "+u.rank+"."})),Tr("conv2d",n,o);var c="NHWC"===a?p.shape[3]:p.shape[1];ae(c===u.shape[2],(function(){return"Error in conv2d: depth of input ("+c+") must match input depth for filter "+u.shape[2]+"."})),ae(kr(r,s),(function(){return"Error in conv2D: Either strides or dilations must be 1. Got strides "+r+" and dilations '"+s+"'"}));var d={x:p,filter:u},h={strides:r,pad:n,dataFormat:a,dilations:s,dimRoundingMode:o},f=Zt.runKernel("Conv2D",d,h);return l?_r(f,[f.shape[1],f.shape[2],f.shape[3]]):f}});var en=rr({conv1d_:function(e,t,r,n,a,s,o){void 0===a&&(a="NWC"),void 0===s&&(s=1);var i=$t(e,"x","conv1d"),u=$t(t,"filter","conv1d"),p=i,l=!1;2===i.rank&&(l=!0,p=_r(i,[1,i.shape[0],i.shape[1]])),ae(3===p.rank,(function(){return"Error in conv1d: input must be rank 3, but got rank "+p.rank+"."})),ae(3===u.rank,(function(){return"Error in conv1d: filter must be rank 3, but got rank "+u.rank+"."})),Tr("conv1d",n,o),ae(p.shape[2]===u.shape[1],(function(){return"Error in conv1d: depth of input ("+p.shape[2]+") must match input depth for filter "+u.shape[1]+"."})),ae(kr(r,s),(function(){return"Error in conv1D: Either stride or dilation must be 1. Got stride "+r+" and dilation '"+s+"'"})),ae("NWC"===a,(function(){return"Error in conv1d: got dataFormat of "+a+" but only NWC is currently supported."}));var c=_r(u,[1,u.shape[0],u.shape[1],u.shape[2]]),d=_r(p,[p.shape[0],1,p.shape[1],p.shape[2]]),h=$r(d,c,[1,r],n,"NHWC",[1,s],o);return _r(h,l?[h.shape[2],h.shape[3]]:[h.shape[0],h.shape[2],h.shape[3]])}});var tn=rr({conv2DBackpropInput_:function(e,t,r,n,a,s,o){void 0===s&&(s="NHWC"),ae(e.length===t.rank,(function(){return"Length of inShape ("+e.length+") and rank of dy ("+t.rank+") must match"}));var i=e,u=t,p=!1;3===t.rank&&(p=!0,u=_r(t,[1,t.shape[0],t.shape[1],t.shape[2]]),i=[1,e[0],e[1],e[2]]),ae(4===i.length,(function(){return"Error in conv2dDerInput: inShape must be length 4, but got length "+i.length+"."})),ae(4===u.rank,(function(){return"Error in conv2dDerInput: dy must be rank 4, but got rank "+u.rank})),ae(4===r.rank,(function(){return"Error in conv2dDerInput: filter must be rank 4, but got rank "+r.rank}));var l="NHWC"===s?i[3]:i[1],c="NHWC"===s?u.shape[3]:u.shape[1];ae(l===r.shape[2],(function(){return"Error in conv2dDerInput: depth of input ("+l+") must match input depth for filter "+r.shape[2]+"."})),ae(c===r.shape[3],(function(){return"Error in conv2dDerInput: depth of output ("+c+") must match output depth for filter "+r.shape[3]+"."})),Tr("conv2dDerInput",a,o);var d={dy:u,filter:r},h={strides:n,pad:a,dataFormat:s,dimRoundingMode:o,inputShape:i},f=Zt.runKernel("Conv2DBackpropInput",d,h);return p?_r(f,[f.shape[1],f.shape[2],f.shape[3]]):f}});var rn=rr({conv2dTranspose_:function(e,t,r,n,a,s){var o=$t(e,"x","conv2dTranspose"),i=$t(t,"filter","conv2dTranspose");return tn(r,o,i,n,a,"NHWC",s)}});var nn=rr({conv3d_:function(e,t,r,n,a,s){void 0===a&&(a="NDHWC"),void 0===s&&(s=[1,1,1]);var o=$t(e,"x","conv3d"),i=$t(t,"filter","conv3d"),u=o,p=!1;4===o.rank&&(p=!0,u=_r(o,[1,o.shape[0],o.shape[1],o.shape[2],o.shape[3]])),ae(5===u.rank,(function(){return"Error in conv3d: input must be rank 5, but got rank "+u.rank+"."})),ae(5===i.rank,(function(){return"Error in conv3d: filter must be rank 5, but got rank "+i.rank+"."})),ae(u.shape[4]===i.shape[3],(function(){return"Error in conv3d: depth of input ("+u.shape[4]+") must match input depth for filter "+i.shape[3]+"."})),ae(kr(r,s),(function(){return"Error in conv3D: Either strides or dilations must be 1. Got strides "+r+" and dilations '"+s+"'"})),ae("NDHWC"===a,(function(){return"Error in conv3d: got dataFormat of "+a+" but only NDHWC is currently supported."}));var l={x:u,filter:i},c={strides:r,pad:n,dataFormat:a,dilations:s},d=Zt.runKernel("Conv3D",l,c);return p?_r(d,[d.shape[1],d.shape[2],d.shape[3],d.shape[4]]):d}});var an=rr({conv3DBackpropInput_:function(e,t,r,n,a){ae(e.length===t.rank,(function(){return"Length of inShape ("+e.length+") and rank of dy ("+t.rank+") must match"}));var s=e,o=t,i=!1;4===t.rank&&(i=!0,o=_r(t,[1,t.shape[0],t.shape[1],t.shape[2],t.shape[3]]),s=[1,e[0],e[1],e[2],e[3]]);var u=s[4],p=o.shape[4];ae(5===s.length,(function(){return"Error in conv3dDerInput: inShape must be length 5, but got length "+s.length+"."})),ae(5===o.rank,(function(){return"Error in conv3dDerInput: dy must be rank 5, but got rank "+o.rank})),ae(5===r.rank,(function(){return"Error in conv3dDerInput: filter must be rank 5, but got rank "+r.rank})),ae(u===r.shape[3],(function(){return"Error in conv3dDerInput: depth of input ("+u+") must match input depth for filter "+r.shape[3]+"."})),ae(p===r.shape[4],(function(){return"Error in conv3dDerInput: depth of output ("+p+") must match output depth for filter "+r.shape[4]+"."}));var l={dy:o,filter:r},c={pad:a,strides:n,inputShape:s},d=Zt.runKernel("Conv3DBackpropInputV2",l,c);return i?_r(d,[d.shape[1],d.shape[2],d.shape[3],d.shape[4]]):d}});var sn=rr({conv3dTranspose_:function(e,t,r,n,a){var s=$t(e,"x","conv3dTranspose"),o=$t(t,"filter","conv3dTranspose");return an(r,s,o,n,a)}});var on=rr({cos_:function(e){var t={x:$t(e,"x","cos","float32")};return Zt.runKernel("Cos",t)}});var un=rr({cosh_:function(e){var t={x:$t(e,"x","cosh","float32")};return Zt.runKernel("Cosh",t)}});var pn=rr({cumprod_:function(e,t,r,n){void 0===t&&(t=0),void 0===r&&(r=!1),void 0===n&&(n=!1);var a={x:$t(e,"x","cumprod")},s={axis:t,exclusive:r,reverse:n};return Zt.runKernel("Cumprod",a,s)}});var ln=rr({cumsum_:function(e,t,r,n){void 0===t&&(t=0),void 0===r&&(r=!1),void 0===n&&(n=!1);var a={x:$t(e,"x","cumsum")},s={axis:t,exclusive:r,reverse:n};return Zt.runKernel("Cumsum",a,s)}});var cn=rr({denseBincount_:function(e,t,r,n){void 0===n&&(n=!1);var a=$t(e,"x","denseBincount"),s=$t(t,"weights","denseBincount");ae("int32"===a.dtype,(function(){return"Error in denseBincount: input dtype must be int32, but got "+a.dtype})),ae(a.rank<=2,(function(){return"Error in denseBincount: input must be at most rank 2, but got rank "+a.rank+"."})),ae(r>=0,(function(){return"size must be non-negative, but got "+r+"."})),ae(s.size===a.size||0===s.size,(function(){return"Error in denseBincount: weights must have the same shape as x or 0-length, but got x shape: "+a.shape+", weights shape: "+s.shape+"."}));var o={x:a,weights:s},i={size:r,binaryOutput:n};return Zt.runKernel("DenseBincount",o,i)}});var dn=rr({depthToSpace_:function(e,t,r){void 0===r&&(r="NHWC");var n=$t(e,"x","depthToSpace","float32"),a="NHWC"===r?n.shape[1]:n.shape[2],s="NHWC"===r?n.shape[2]:n.shape[3],o="NHWC"===r?n.shape[3]:n.shape[1];ae(t>1,(function(){return"blockSize should be > 1 for depthToSpace, but was: "+t})),ae(a*t>=0,(function(){return"Negative dimension size caused by overflow when multiplying\n    "+a+" and "+t+"  for depthToSpace with input shape\n    "+n.shape})),ae(s*t>=0,(function(){return"Negative dimension size caused by overflow when multiplying\n    "+s+" and "+t+" for depthToSpace with input shape\n        "+n.shape})),ae(o%(t*t)==0,(function(){return"Dimension size must be evenly divisible by "+t*t+" but is "+o+" for depthToSpace with input shape "+n.shape}));var i={x:n},u={blockSize:t,dataFormat:r};return Zt.runKernel("DepthToSpace",i,u)}});var hn=rr({depthwiseConv2d_:function(e,t,r,n,a,s,o){void 0===a&&(a="NHWC"),void 0===s&&(s=[1,1]);var i=$t(e,"x","depthwiseConv2d","float32"),u=$t(t,"filter","depthwiseConv2d","float32"),p=i,l=!1;3===i.rank&&(l=!0,p=_r(i,[1,i.shape[0],i.shape[1],i.shape[2]])),ae(4===p.rank,(function(){return"Error in depthwiseConv2d: input must be rank 4, but got rank "+p.rank+"."})),ae(4===u.rank,(function(){return"Error in depthwiseConv2d: filter must be rank 4, but got rank "+u.rank+"."}));var c="NHWC"===a?p.shape[3]:p.shape[1];ae(c===u.shape[2],(function(){return"Error in depthwiseConv2d: number of input channels ("+c+") must match the inChannels dimension in filter "+u.shape[2]+"."})),Tr("depthwiseConv2d",n,o);var d={x:p,filter:u},h={strides:r,pad:n,dataFormat:a,dilations:s,dimRoundingMode:o},f=Zt.runKernel("DepthwiseConv2dNative",d,h);return l?_r(f,[f.shape[1],f.shape[2],f.shape[3]]):f}});var fn=rr({diag_:function(e){var t={x:$t(e,"x","diag")};return Zt.runKernel("Diag",t)}});var mn=rr({dilation2d_:function(e,t,r,n,a,s){void 0===a&&(a=[1,1]),void 0===s&&(s="NHWC");var o=$t(e,"x","dilation2d"),i=$t(t,"filter","dilation2d");ae(3===o.rank||4===o.rank,(function(){return"Error in dilation2d: input must be rank 3 or 4, but got rank "+o.rank+"."})),ae(3===i.rank,(function(){return"Error in dilation2d: filter must be rank 3, but got rank "+i.rank+"."})),ae("NHWC"===s,(function(){return"Error in dilation2d: Only NHWC is currently supported, but got dataFormat of "+s}));var u=o,p=!1;3===o.rank&&(u=_r(o,[1,o.shape[0],o.shape[1],o.shape[2]]),p=!0);var l={x:u,filter:i},c={strides:r,pad:n,dilations:a},d=Zt.runKernel("Dilation2D",l,c);return p?_r(d,[d.shape[1],d.shape[2],d.shape[3]]):d}});var yn=rr({floorDiv_:function(e,t){var r,n=$t(e,"a","floorDiv"),a=$t(t,"b","floorDiv"),s={a:n=(r=l(qt(n,a),2))[0],b:a=r[1]};return Zt.runKernel("FloorDiv",s)}});var gn=rr({div_:function(e,t){var r,n=$t(e,"a","div"),a=$t(t,"b","div");if(n=(r=l(qt(n,a),2))[0],a=r[1],"int32"===n.dtype&&"int32"===a.dtype)return yn(n,a);var s={a:n,b:a};return Zt.runKernel("RealDiv",s,{})}});function vn(e,t){for(var r=[],n=Math.max(e.length,t.length),a=0;a<n;a++){var s=e[e.length-a-1];null==s&&(s=1);var o=t[t.length-a-1];if(null==o&&(o=1),1===s)r.unshift(o);else if(1===o)r.unshift(s);else{if(s!==o)throw Error("Operands could not be broadcast together with shapes "+e+" and "+t+".");r.unshift(s)}}return r}var bn=rr({equal_:function(e,t){var r,n=$t(e,"a","equal","string_or_numeric"),a=$t(t,"b","equal","string_or_numeric");n=(r=l(qt(n,a),2))[0],a=r[1],vn(n.shape,a.shape);var s={a:n,b:a};return Zt.runKernel("Equal",s)}});var xn=rr({where_:function(e,t,r){var n=$t(t,"a","where"),a=$t(r,"b","where"),s=$t(e,"condition","where","bool"),o=vn(vn(s.shape,n.shape),a.shape),i={condition:jr(s,o),t:jr(n,o),e:jr(a,o)};return Zt.runKernel("Select",i)}});var Nn=rr({zerosLike_:function(e){var t={x:$t(e,"x","zerosLike")};return Zt.runKernel("ZerosLike",t)}});var wn=rr({divNoNan_:function(e,t){var r,n=$t(e,"a","div"),a=$t(t,"b","div");n=(r=l(qt(n,a),2))[0],a=r[1];var s=gn(n,a),o=Nn(s),i=bn(a,o);return xn(i,o,s)}});var kn=rr({dot_:function(e,t){var r=$t(e,"t1","dot"),n=$t(t,"t2","dot");ae(!(1!==r.rank&&2!==r.rank||1!==n.rank&&2!==n.rank),(function(){return"Error in dot: inputs must all be rank 1 or 2, but got ranks "+r.rank+" and "+n.rank+"."}));var a=1===r.rank?r.size:r.shape[1],s=1===n.rank?n.size:n.shape[0];if(ae(a===s,(function(){return"Error in dot: inner dimensions of inputs must match, but got "+a+" and "+s+"."})),1===r.rank&&1===n.rank){var o=_r(r,[1,-1]),i=_r(n,[-1,1]),u=Dr(o,i);return _r(u,[])}if(1===r.rank&&2===n.rank){o=_r(r,[1,-1]),i=_r(n,[n.shape[0],n.shape[1]]),u=Dr(o,i);return _r(u,[u.size])}if(2===r.rank&&1===n.rank){i=_r(n,[-1,1]),u=Dr(r,i);return _r(u,[u.size])}return i=_r(n,[n.shape[0],n.shape[1]]),u=Dr(r,i)}});var Tn=rr({einsum_:function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var n=t.map((function(e,t){return $t(e,"tensors"+t,"einsum")})),a={equation:e};return Zt.runKernel(Ve,n,a)}});var _n=rr({elu_:function(e){var t={x:$t(e,"x","elu","float32")};return Zt.runKernel("Elu",t)}});var Sn=rr({erf_:function(e){var t=$t(e,"x","erf");ae("int32"===t.dtype||"float32"===t.dtype,(function(){return"Input dtype must be `int32` or `float32`."})),"int32"===t.dtype&&(t=gr(t,"float32"));var r={x:t};return Zt.runKernel("Erf",r)}});function En(e,t){return function(e,t,r){for(var n=e.length+t.length,a=[],s=0,o=0,i=0;i<n;i++)-1===r.indexOf(i)?a.push(e[s++]):a.push(t[o++]);return a}(e,t.map((function(e){return 1})),t)}var In=rr({max_:function(e,t,r){void 0===t&&(t=null),void 0===r&&(r=!1);var n={x:$t(e,"x","max")},a={reductionIndices:t,keepDims:r};return Zt.runKernel("Max",n,a)}});var On=rr({min_:function(e,t,r){void 0===t&&(t=null),void 0===r&&(r=!1);var n={x:$t(e,"x","min")},a={axis:t,keepDims:r};return Zt.runKernel("Min",n,a)}});var Dn=rr({pow_:function(e,t){var r,n=$t(e,"base","pow"),a=$t(t,"exp","pow"),s={a:n=(r=l(qt(n,a),2))[0],b:a=r[1]};return Zt.runKernel("Pow",s)}});function An(e,t,r,n){if(null==n&&(n=ye(e)),"complex64"===n)throw new Error("Cannot construct a complex64 tensor directly. Please use tf.complex(real, imag).");if(!he(e)&&!Array.isArray(e)&&"number"!=typeof e&&"boolean"!=typeof e&&"string"!=typeof e)throw new Error("values passed to tensor(values) must be a number/boolean/string or an array of numbers/booleans/strings, or a TypedArray");if(null!=t){ke(t);var a=ue(t),s=ue(r);ae(a===s,(function(){return"Based on the provided shape, ["+t+"], the tensor should have "+a+" values but has "+s}));for(var o=0;o<r.length;++o){var i=r[o],u=o!==r.length-1||i!==ue(t.slice(o));ae(r[o]===t[o]||!u,(function(){return"Error creating a new Tensor. Inferred shape ("+r+") does not match the provided shape ("+t+"). "}))}}return he(e)||Array.isArray(e)||(e=[e]),t=t||r,e="string"!==n?Nt(e,n):ie(e,[],!0),Zt.makeTensor(e,t,n)}function Mn(e,t){if((he(e)&&"string"!==t||Array.isArray(e))&&"complex64"!==t)throw new Error("Error creating a new Scalar: value must be a primitive (number|boolean|string)");if("string"===t&&he(e)&&!(e instanceof Uint8Array))throw new Error("When making a scalar from encoded string, the value must be `Uint8Array`.");return An(e,[],[],t)}var Cn=rr({sqrt_:function(e){var t={x:$t(e,"x","sqrt","float32")};return Zt.runKernel("Sqrt",t)}});var Fn=rr({square_:function(e){var t=$t(e,"x","square");return Zt.runKernel("Square",{x:t},{})}});var Vn=rr({sum_:function(e,t,r){void 0===t&&(t=null),void 0===r&&(r=!1);var n=$t(e,"x","sum");"bool"===n.dtype&&(n=gr(n,"int32"));var a={x:n},s={axis:t,keepDims:r};return Zt.runKernel("Sum",a,s)}});function Rn(e,t,r){if(void 0===r&&(r=null),0===e.rank)return nr(e);if(1!==e.rank&&null===r)return Rn(_r(e,[-1]),t,r);if(1===e.rank||"number"==typeof r||Array.isArray(r)&&1===r.length){if(1===t)return Vn(nr(e),r);if(t===1/0)return In(nr(e),r);if(t===-1/0)return On(nr(e),r);if("euclidean"===t||2===t)return Cn(Vn(Dn(nr(e),Mn(2,"int32")),r));throw new Error("Error in norm: invalid ord value: "+t)}if(Array.isArray(r)&&2===r.length){if(1===t)return In(Vn(nr(e),r[0]),r[1]-1);if(t===1/0)return In(Vn(nr(e),r[1]),r[0]);if(t===-1/0)return On(Vn(nr(e),r[1]),r[0]);if("fro"===t||"euclidean"===t)return Cn(Vn(Fn(e),r));throw new Error("Error in norm: invalid ord value: "+t)}throw new Error("Error in norm: invalid axis: "+r)}var zn=rr({norm_:function(e,t,r,n){void 0===t&&(t="euclidean"),void 0===r&&(r=null),void 0===n&&(n=!1);var a=Rn(e=$t(e,"x","norm"),t,r),s=a.shape;if(n){var o=de(r,e.shape);s=En(a.shape,o)}return _r(a,s)}});var Ln=rr({euclideanNorm_:function(e,t,r){return void 0===t&&(t=null),void 0===r&&(r=!1),zn(e,"euclidean",t,r)}});var Pn=rr({exp_:function(e){var t={x:$t(e,"x","exp")};return Zt.runKernel("Exp",t)}});var Bn=rr({expandDims_:function(e,t){void 0===t&&(t=0);var r=$t(e,"x","expandDims","string_or_numeric");ae(t<=r.rank,(function(){return"Axis must be <= rank of the tensor"}));var n={input:r},a={dim:t};return Zt.runKernel("ExpandDims",n,a)}});var Kn=rr({expm1_:function(e){var t={x:$t(e,"x","expm1")};return Zt.runKernel("Expm1",t)}});var qn=rr({tile_:function(e,t){var r=$t(e,"x","tile","string_or_numeric");ae(r.rank===t.length,(function(){return"Error in transpose: rank of input "+r.rank+" must match length of reps "+t+"."}));var n={x:r},a={reps:t};return Zt.runKernel(ze,n,a)}});var jn=rr({eye_:function(e,t,r,n){void 0===n&&(n="float32"),null==t&&(t=e);for(var a=Ur([e,t],n),s=e<=t?e:t,o=0;o<s;++o)a.set(1,o,o);var i=_r(a.toTensor(),[e,t]);if(null==r)return i;if(1===r.length)return qn(Bn(i,0),[r[0],1,1]);if(2===r.length)return qn(Bn(Bn(i,0),0),[r[0],r[1],1,1]);if(3===r.length)return qn(Bn(Bn(Bn(i,0),0),0),[r[0],r[1],r[2],1,1]);throw new Error("eye() currently supports only 1D and 2D batchShapes, but received "+r.length+"D.")}});var Un=rr({floor_:function(e){var t={x:$t(e,"x","floor","float32")};return Zt.runKernel("Floor",t)}});var Wn=rr({gather_:function(e,t,r,n){void 0===r&&(r=0),void 0===n&&(n=0);var a={x:$t(e,"x","gather"),indices:$t(t,"indices","gather","int32")},s={axis:r,batchDims:n};return Zt.runKernel("GatherV2",a,s)}});var Hn=rr({greater_:function(e,t){var r,n=$t(e,"a","greater","string_or_numeric"),a=$t(t,"b","greater","string_or_numeric");n=(r=l(qt(n,a),2))[0],a=r[1],vn(n.shape,a.shape);var s={a:n,b:a};return Zt.runKernel("Greater",s)}});var Gn=rr({greaterEqual_:function(e,t){var r,n=$t(e,"a","greaterEqual","string_or_numeric"),a=$t(t,"b","greaterEqual","string_or_numeric");n=(r=l(qt(n,a),2))[0],a=r[1],vn(n.shape,a.shape);var s={a:n,b:a};return Zt.runKernel("GreaterEqual",s)}});var Zn=rr({imag_:function(e){var t={input:$t(e,"input","imag")};return Zt.runKernel("Imag",t)}});var Qn=rr({isFinite_:function(e){var t={x:$t(e,"x","isFinite")};return Zt.runKernel("IsFinite",t)}});var Yn=rr({isInf_:function(e){var t={x:$t(e,"x","isInf")};return Zt.runKernel("IsInf",t)}});var Xn=rr({isNaN_:function(e){var t={x:$t(e,"x","isNaN")};return Zt.runKernel("IsNan",t)}});var Jn=rr({leakyRelu_:function(e,t){void 0===t&&(t=.2);var r={x:$t(e,"x","leakyRelu")},n={alpha:t};return Zt.runKernel("LeakyRelu",r,n)}});var $n=rr({less_:function(e,t){var r,n=$t(e,"a","less","string_or_numeric"),a=$t(t,"b","less","string_or_numeric");n=(r=l(qt(n,a),2))[0],a=r[1],vn(n.shape,a.shape);var s={a:n,b:a};return Zt.runKernel("Less",s)}});var ea=rr({lessEqual_:function(e,t){var r,n=$t(e,"a","lessEqual","string_or_numeric"),a=$t(t,"b","lessEqual","string_or_numeric");n=(r=l(qt(n,a),2))[0],a=r[1],vn(n.shape,a.shape);var s={a:n,b:a};return Zt.runKernel("LessEqual",s)}});var ta=rr({localResponseNormalization_:function(e,t,r,n,a){void 0===t&&(t=5),void 0===r&&(r=1),void 0===n&&(n=1),void 0===a&&(a=.5);var s=$t(e,"x","localResponseNormalization");ae(4===s.rank||3===s.rank,(function(){return"Error in localResponseNormalization: x must be rank 3 or 4 but got\n               rank "+s.rank+"."})),ae(le(t),(function(){return"Error in localResponseNormalization: depthRadius must be an integer but got depthRadius "+t+"."}));var o=s,i=!1;3===s.rank&&(i=!0,o=_r(s,[1,s.shape[0],s.shape[1],s.shape[2]]));var u={x:o},p={depthRadius:t,bias:r,alpha:n,beta:a},l=Zt.runKernel("LRN",u,p);return i?_r(l,[l.shape[1],l.shape[2],l.shape[3]]):l}});var ra=rr({log_:function(e){var t={x:$t(e,"x","log","float32")};return Zt.runKernel("Log",t)}});var na=rr({log1p_:function(e){var t={x:$t(e,"x","log1p")};return Zt.runKernel("Log1p",t)}});function aa(e){return Zt.customGrad(e)}var sa=rr({neg_:function(e){var t={x:$t(e,"x","neg")};return Zt.runKernel("Neg",t)}});var oa=rr({softplus_:function(e){var t={x:$t(e,"x","softplus")};return Zt.runKernel("Softplus",t)}});var ia=rr({logSigmoid_:function(e){var t=$t(e,"x","logSigmoid"),r=aa((function(e){return{value:sa(oa(sa(e))),gradFunc:function(t){return Ar(t,Mr(sa(e)))}}}));return r(t)}});var ua=rr({sub_:function(e,t){var r,n=$t(e,"a","sub"),a=$t(t,"b","sub"),s={a:n=(r=l(qt(n,a),2))[0],b:a=r[1]};return Zt.runKernel("Sub",s)}});var pa=rr({logSoftmax_:function(e,t){void 0===t&&(t=-1);var r=$t(e,"logits","logSoftmax");if(-1===t&&(t=r.rank-1),t!==r.rank-1)throw Error("Log Softmax along a non-last dimension is not yet supported. Logits was rank "+r.rank+" and axis was "+t);var n=aa((function(e,r){var n=In(e,t,!0),a=ua(e,n),s=ua(gr(a,"float32"),ra(Vn(Pn(a),t,!0)));r([s]);return{value:s,gradFunc:function(e,r){var n=l(r,1)[0],a=Pn(n);return ua(e,Ar(Vn(e,t,!0),a))}}}));return n(r)}});var la=rr({logSumExp_:function(e,t,r){void 0===t&&(t=null),void 0===r&&(r=!1);var n=$t(e,"x","logSumExp"),a=de(t,n.shape),s=In(n,a,!0),o=ua(n,s),i=Pn(o),u=Vn(i,a),p=ra(u),l=or(_r(s,p.shape),p);if(r){var c=En(l.shape,a);return _r(l,c)}return l}});var ca=rr({logicalAnd_:function(e,t){var r=$t(e,"a","logicalAnd","bool"),n=$t(t,"b","logicalAnd","bool");vn(r.shape,n.shape);var a={a:r,b:n};return Zt.runKernel("LogicalAnd",a)}});var da=rr({logicalNot_:function(e){var t={x:$t(e,"x","logicalNot","bool")};return Zt.runKernel("LogicalNot",t)}});var ha=rr({logicalOr_:function(e,t){var r=$t(e,"a","logicalOr","bool"),n=$t(t,"b","logicalOr","bool");vn(r.shape,n.shape);var a={a:r,b:n};return Zt.runKernel("LogicalOr",a)}});var fa=rr({logicalXor_:function(e,t){var r=$t(e,"a","logicalXor","bool"),n=$t(t,"b","logicalXor","bool");return vn(r.shape,n.shape),ca(ha(e,t),da(ca(e,t)))}}),ma=2147483648;var ya=rr({searchSorted_:function(e,t,r){void 0===r&&(r="left");var n=$t(e,"sortedSequence","searchSorted"),a=$t(t,"values","searchSorted"),s=n.shape[n.shape.length-1],o=a.shape[a.shape.length-1],i=_r(n,[-1,s]),u=_r(a,[-1,o]);if(i.rank<2)throw new Error("Sorted input argument must be at least 2-dimensional");if(i.shape[0]!==u.shape[0])throw new Error("Leading dimension of 'sortedSequence' and 'values' must match.");if(ue(u.shape)>=ma)throw new Error("values tensor size must less than 2147483648");if(i.shape[1]>=ma)throw new Error("trailing dim_size must less than 2147483648 for int32 output type, was "+i.shape[1]);var p={sortedSequence:i,values:u},l={side:r};return Zt.runKernel("SearchSorted",p,l)}});var ga=rr({maxPool_:function(e,t,r,n,a){var s=$t(e,"x","maxPool"),o=s,i=!1;3===s.rank&&(i=!0,o=_r(s,[1,s.shape[0],s.shape[1],s.shape[2]])),ae(4===o.rank,(function(){return"Error in maxPool: input must be rank 4 but got rank "+o.rank+"."})),ae(kr(r,1),(function(){return"Error in maxPool: Either strides or dilations must be 1. Got strides "+r+" and dilations '1'"})),Tr("maxPool",n,a);var u={x:o},p={filterSize:t,strides:r,pad:n,dimRoundingMode:a},l=Zt.runKernel("MaxPool",u,p);return i?_r(l,[l.shape[1],l.shape[2],l.shape[3]]):l}});var va=rr({maxPool3d_:function(e,t,r,n,a,s){void 0===t&&(t=[1,1,1]),void 0===s&&(s="NDHWC");var o=$t(e,"x","maxPool3d"),i=o,u=!1;4===o.rank&&(u=!0,i=_r(o,[1,o.shape[0],o.shape[1],o.shape[2],o.shape[3]])),ae(5===i.rank,(function(){return"Error in maxPool3d: x must be rank 5 but got rank "+i.rank+"."})),ae("NDHWC"===s,(function(){return"Error in maxPool3d: Only NDHWC is currently supported, but got dataFormat of "+s})),Tr("maxPool3d",n,a);var p={x:i},l={filterSize:t,strides:r,pad:n,dimRoundingMode:a,dataFormat:s},c=Zt.runKernel("MaxPool3D",p,l);return u?_r(c,[c.shape[1],c.shape[2],c.shape[3],c.shape[4]]):c}});var ba=rr({maxPoolWithArgmax_:function(e,t,r,n,a){void 0===a&&(a=!1);var s={x:$t(e,"x","maxPoolWithArgmax")},o={filterSize:t,strides:r,pad:n,includeBatchInIndex:a},i=Zt.runKernel("MaxPoolWithArgmax",s,o);return{result:i[0],indexes:i[1]}}});var xa=rr({maximum_:function(e,t){var r,n=$t(e,"a","maximum"),a=$t(t,"b","maximum");n=(r=l(qt(n,a),2))[0],a=r[1],"bool"===n.dtype&&(n=gr(n,"int32"),a=gr(a,"int32")),vn(n.shape,a.shape);var s={a:n,b:a};return Zt.runKernel("Maximum",s)}});var Na=rr({mean_:function(e,t,r){void 0===t&&(t=null),void 0===r&&(r=!1);var n={x:$t(e,"x","mean")},a={axis:t,keepDims:r};return Zt.runKernel("Mean",n,a)}});function wa(e,t){if(void 0===t&&(t="float32"),"complex64"===t){var r=wa(e,"float32"),n=wa(e,"float32");return Zr(r,n)}var a=we(ue(e),t);return Zt.makeTensor(a,e,t)}function ka(e,t){if(void 0===t&&(t="float32"),"complex64"===t){var r=ka(e,"float32"),n=wa(e,"float32");return Zr(r,n)}var a=Ne(ue(e),t);return Zt.makeTensor(a,e,t)}var Ta=rr({minimum_:function(e,t){var r,n=$t(e,"a","minimum"),a=$t(t,"b","minimum");n=(r=l(qt(n,a),2))[0],a=r[1],"bool"===n.dtype&&(n=gr(n,"int32"),a=gr(a,"int32")),vn(n.shape,a.shape);var s={a:n,b:a};return Zt.runKernel("Minimum",s)}});var _a=rr({mirrorPad_:function(e,t,r){ae("reflect"===r||"symmetric"===r,(function(){return"Invalid mode. Mode must be either reflect or symmetric. Got "+r+"."}));var n=$t(e,"x","mirrorPad");if(0===n.rank)throw new Error("mirrorPad(scalar) is not defined. Pass non-scalar to mirrorPad");ae(t.length===n.rank,(function(){return"Padding doesn't match input. Must be "+n.rank+". Got "+t.length+"."}));for(var a="reflect"===r?1:0,s=function(e){ae(2===t[e].length,(function(){return"Invalid number of paddings. Must be length of 2 each."})),ae(t[e][0]>=0&&t[e][0]<=n.shape[e]-a&&t[e][1]>=0&&t[e][1]<=n.shape[e]-a,(function(){return"Padding in dimension "+e+" cannot be greater than or equal to "+(n.shape[e]-a)+" or less than 0 for input of shape "+n.shape}))},o=0;o<n.rank;o++)s(o);var i={paddings:t,mode:r},u={x:n};return Zt.runKernel("MirrorPad",u,i)}});var Sa=rr({mod_:function(e,t){var r,n=$t(e,"a","mod"),a=$t(t,"b","mod"),s={a:n=(r=l(qt(n,a),2))[0],b:a=r[1]};return Zt.runKernel("Mod",s)}});var Ea=rr({moments_:function(e,t,r){void 0===t&&(t=null),void 0===r&&(r=!1);var n=de(t,(e=$t(e,"x","moments")).shape),a=Na(e,n,r),s=a.shape;r||(s=En(a.shape,n));var o=Fn(ua(gr(e,"float32"),_r(a,s)));return{mean:a,variance:Na(o,n,r)}}});var Ia=rr({multiRNNCell_:function(e,t,r,n){for(var a=$t(t,"data","multiRNNCell"),s=er(r,"c","multiRNNCell"),o=er(n,"h","multiRNNCell"),i=a,u=[],p=0;p<e.length;p++){var l=e[p](i,s[p],o[p]);u.push(l[0]),u.push(l[1]),i=l[1]}var c=[],d=[];for(p=0;p<u.length;p+=2)c.push(u[p]),d.push(u[p+1]);return[c,d]}});var Oa=rr({multinomial_:function(e,t,r,n){void 0===n&&(n=!1);var a=$t(e,"logits","multinomial"),s=a.size,o=a.rank;if(s<2)throw new Error("Error in multinomial: you need at least 2 outcomes, but got "+s+".");if(o>2)throw new Error("Rank of probabilities must be 1 or 2, but is "+o);r=r||Math.random();var i={logits:1===o?_r(a,[1,-1]):a},u={numSamples:t,seed:r,normalized:n},p=Zt.runKernel("Multinomial",i,u);return 1===o?_r(p,[p.size]):p}});var Da=rr({notEqual_:function(e,t){var r,n=$t(e,"a","notEqual","string_or_numeric"),a=$t(t,"b","notEqual","string_or_numeric");n=(r=l(qt(n,a),2))[0],a=r[1],vn(n.shape,a.shape);var s={a:n,b:a};return Zt.runKernel("NotEqual",s)}});var Aa=rr({oneHot_:function(e,t,r,n,a){if(void 0===r&&(r=1),void 0===n&&(n=0),void 0===a&&(a="int32"),t<2)throw new Error("Error in oneHot: depth must be >=2, but it is "+t);var s={indices:$t(e,"indices","oneHot","int32")},o={dtype:a,depth:t,onValue:r,offValue:n};return Zt.runKernel("OneHot",s,o)}});var Ma=rr({onesLike_:function(e){var t={x:$t(e,"x","onesLike")};return Zt.runKernel("OnesLike",t)}});var Ca=rr({outerProduct_:function(e,t){var r=$t(e,"v1","outerProduct"),n=$t(t,"v2","outerProduct");ae(1===r.rank&&1===n.rank,(function(){return"Error in outerProduct: inputs must be rank 1, but got ranks "+r.rank+" and "+n.rank+"."}));var a=_r(r,[-1,1]),s=_r(n,[1,-1]);return Dr(a,s)}});var Fa=rr({pad_:function(e,t,r){void 0===r&&(r=0);var n=$t(e,"x","pad");if(0===n.rank)throw new Error("pad(scalar) is not defined. Pass non-scalar to pad");var a={paddings:t,constantValue:r},s={x:n};return Zt.runKernel("PadV2",s,a)}});var Va=rr({pad1d_:function(e,t,r){return void 0===r&&(r=0),ae(2===t.length,(function(){return"Invalid number of paddings. Must be length of 2."})),Fa(e,[t],r)}});var Ra=rr({pad2d_:function(e,t,r){return void 0===r&&(r=0),ae(2===t.length&&2===t[0].length&&2===t[1].length,(function(){return"Invalid number of paddings. Must be length of 2 each."})),Fa(e,t,r)}});var za=rr({pad3d_:function(e,t,r){return void 0===r&&(r=0),ae(3===t.length&&2===t[0].length&&2===t[1].length&&2===t[2].length,(function(){return"Invalid number of paddings. Must be length of 2 each."})),Fa(e,t,r)}});var La=rr({pad4d_:function(e,t,r){return void 0===r&&(r=0),ae(4===t.length&&2===t[0].length&&2===t[1].length&&2===t[2].length&&2===t[3].length,(function(){return"Invalid number of paddings. Must be length of 2 each."})),Fa(e,t,r)}});var Pa=rr({spaceToBatchND_:function(e,t,r){var n=$t(e,"x","spaceToBatchND");ae(n.rank>=1+t.length,(function(){return"input rank "+n.rank+" should be > than [blockShape] "+t.length})),ae(r.length===t.length,(function(){return"paddings.shape[0] "+r.length+" must be equal to [blockShape] "+t.length})),ae(n.shape.reduce((function(e,n,a){return a>0&&a<=t.length?e&&(n+r[a-1][0]+r[a-1][1])%t[a-1]==0:e}),!0),(function(){return"input spatial dimensions "+n.shape.slice(1)+" with paddings "+r.toString()+" must be divisible by blockShapes "+t.toString()}));var a={x:n},s={blockShape:t,paddings:r};return Zt.runKernel("SpaceToBatchND",a,s)}});var Ba=rr({pool_:function(e,t,r,n,a,s,o){null==a&&(a=[1,1]),null==s&&(s=1),0===n&&(n="valid");var i=$t(e,"x","maxPool"),u=i,p=!1;3===i.rank&&(p=!0,u=_r(i,[1,i.shape[0],i.shape[1],i.shape[2]])),ae(kr(s,a),(function(){return"Error in pool: Either strides or dilations must be 1. Got strides "+s+" and dilations '"+a+"'"}));var c,d=function(e,t,r,n,a,s,o){void 0===o&&(o="channelsLast");var i,u=l(br(t),2),p=u[0],c=u[1];if("channelsLast"===o)i=[p,c,e[3],e[3]];else{if("channelsFirst"!==o)throw new Error("Unknown dataFormat "+o);i=[p,c,e[1],e[1]]}return vr(e,i,r,n,a,s,!1,o)}(u.shape,t,s,a,n),h=[d.dilationHeight,d.dilationWidth];c="same"===n?function(e,t){var r=e.map((function(e,r){return e+(e-1)*(t[r]-1)})).map((function(e){return e-1})),n=r.map((function(e){return Math.floor(e/2)})),a=r.map((function(e,t){return e-n[t]}));return r.map((function(e,t){return[n[t],a[t]]}))}([d.filterHeight,d.filterWidth],h):[[0,0],[0,0]];var f=1===h[0]&&1===h[1],m=l(function(e,t,r){var n=r.map((function(e){return e[0]})),a=r.map((function(e){return e[1]})),s=e.concat(n,a),o=t.map((function(e,t){return(e-s[t]%e)%e})),i=a.map((function(e,t){return e+o[t]})),u=t.map((function(e,t){return[n[t],i[t]]})),p=t.map((function(e,t){return[0,o[t]]}));return[u,p]}([d.inHeight,d.inWidth],h,c),2),y=m[0],g=m[1],v=f?n:"valid",b=f?u:Pa(u,h,y),x=("avg"===r?function(){return Sr(b,t,s,v,o)}:function(){return ga(b,t,s,v,o)})(),N=f?x:Rr(x,h,g);return p?_r(N,[N.shape[1],N.shape[2],N.shape[3]]):N}});var Ka=rr({prelu_:function(e,t){var r={x:$t(e,"x","prelu"),alpha:$t(t,"alpha","prelu")};return Zt.runKernel("Prelu",r)}});var qa=rr({prod_:function(e,t,r){void 0===t&&(t=null),void 0===r&&(r=!1);var n=$t(e,"x","prod");"bool"===n.dtype&&(n=gr(n,"int32"));var a={x:n},s={axis:t,keepDims:r};return Zt.runKernel("Prod",a,s)}});var ja=rr({raggedGather_:function(e,t,r,n){var a={paramsNestedSplits:e.map((function(e,t){return $t(e,"tensors"+t,"raggedGather","int32")})),paramsDenseValues:$t(t,"paramsDenseValues","raggedGather"),indices:$t(r,"indices","raggedGather","int32")},s={outputRaggedRank:n},o=Zt.runKernel("RaggedGather",a,s);return{outputNestedSplits:o.slice(0,o.length-1),outputDenseValues:o[o.length-1]}}});var Ua=rr({raggedTensorToTensor_:function(e,t,r,n,a){var s=$t(e,"shape","raggedTensorToTensor","int32"),o=$t(t,"values","raggedTensorToTensor"),i={shape:s,values:o,defaultValue:$t(r,"defaultValue","raggedTensorToTensor",o.dtype),rowPartitionTensors:n.map((function(e,t){return $t(e,"tensors"+t,"raggedTensorToTensor","int32")}))},u={rowPartitionTypes:a};return Zt.runKernel("RaggedTensorToTensor",i,u)}});var Wa=rr({rand_:function(e,t,r){var n=ue(e),a=null;if(null==r||"float32"===r)a=new Float32Array(n);else if("int32"===r)a=new Int32Array(n);else{if("bool"!==r)throw new Error("Unknown data type "+r);a=new Uint8Array(n)}for(var s=0;s<n;s++)a[s]=t();return Zt.makeTensor(a,e,r)}}),Ha="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Ga(e){if(e.__esModule)return e;var t=Object.defineProperty({},"__esModule",{value:!0});return Object.keys(e).forEach((function(r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})})),t}function Za(e){var t={exports:{}};return e(t,t.exports),t.exports}var Qa=Za((function(e){!function(e,t,r){function n(e){var t,r=this,n=(t=4022871197,function(e){e=String(e);for(var r=0;r<e.length;r++){var n=.02519603282416938*(t+=e.charCodeAt(r));n-=t=n>>>0,t=(n*=t)>>>0,t+=4294967296*(n-=t)}return 2.3283064365386963e-10*(t>>>0)});r.next=function(){var e=2091639*r.s0+2.3283064365386963e-10*r.c;return r.s0=r.s1,r.s1=r.s2,r.s2=e-(r.c=0|e)},r.c=1,r.s0=n(" "),r.s1=n(" "),r.s2=n(" "),r.s0-=n(e),r.s0<0&&(r.s0+=1),r.s1-=n(e),r.s1<0&&(r.s1+=1),r.s2-=n(e),r.s2<0&&(r.s2+=1),n=null}function a(e,t){return t.c=e.c,t.s0=e.s0,t.s1=e.s1,t.s2=e.s2,t}function s(e,t){var r=new n(e),s=t&&t.state,o=r.next;return o.int32=function(){return 4294967296*r.next()|0},o.double=function(){return o()+11102230246251565e-32*(2097152*o()|0)},o.quick=o,s&&("object"==typeof s&&a(s,r),o.state=function(){return a(r,{})}),o}t&&t.exports?t.exports=s:r&&r.amd?r((function(){return s})):this.alea=s}(0,e,!1)})),Ya=Za((function(e){!function(e,t,r){function n(e){var t=this,r="";t.x=0,t.y=0,t.z=0,t.w=0,t.next=function(){var e=t.x^t.x<<11;return t.x=t.y,t.y=t.z,t.z=t.w,t.w^=t.w>>>19^e^e>>>8},e===(0|e)?t.x=e:r+=e;for(var n=0;n<r.length+64;n++)t.x^=0|r.charCodeAt(n),t.next()}function a(e,t){return t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t}function s(e,t){var r=new n(e),s=t&&t.state,o=function(){return(r.next()>>>0)/4294967296};return o.double=function(){do{var e=((r.next()>>>11)+(r.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},o.int32=r.next,o.quick=o,s&&("object"==typeof s&&a(s,r),o.state=function(){return a(r,{})}),o}t&&t.exports?t.exports=s:r&&r.amd?r((function(){return s})):this.xor128=s}(0,e,!1)})),Xa=Za((function(e){!function(e,t,r){function n(e){var t=this,r="";t.next=function(){var e=t.x^t.x>>>2;return t.x=t.y,t.y=t.z,t.z=t.w,t.w=t.v,(t.d=t.d+362437|0)+(t.v=t.v^t.v<<4^e^e<<1)|0},t.x=0,t.y=0,t.z=0,t.w=0,t.v=0,e===(0|e)?t.x=e:r+=e;for(var n=0;n<r.length+64;n++)t.x^=0|r.charCodeAt(n),n==r.length&&(t.d=t.x<<10^t.x>>>4),t.next()}function a(e,t){return t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t.v=e.v,t.d=e.d,t}function s(e,t){var r=new n(e),s=t&&t.state,o=function(){return(r.next()>>>0)/4294967296};return o.double=function(){do{var e=((r.next()>>>11)+(r.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},o.int32=r.next,o.quick=o,s&&("object"==typeof s&&a(s,r),o.state=function(){return a(r,{})}),o}t&&t.exports?t.exports=s:r&&r.amd?r((function(){return s})):this.xorwow=s}(0,e,!1)})),Ja=Za((function(e){!function(e,t,r){function n(e){var t=this;t.next=function(){var e,r,n=t.x,a=t.i;return e=n[a],r=(e^=e>>>7)^e<<24,r^=(e=n[a+1&7])^e>>>10,r^=(e=n[a+3&7])^e>>>3,r^=(e=n[a+4&7])^e<<7,e=n[a+7&7],r^=(e^=e<<13)^e<<9,n[a]=r,t.i=a+1&7,r},function(e,t){var r,n=[];if(t===(0|t))n[0]=t;else for(t=""+t,r=0;r<t.length;++r)n[7&r]=n[7&r]<<15^t.charCodeAt(r)+n[r+1&7]<<13;for(;n.length<8;)n.push(0);for(r=0;r<8&&0===n[r];++r);for(8==r&&(n[7]=-1),e.x=n,e.i=0,r=256;r>0;--r)e.next()}(t,e)}function a(e,t){return t.x=e.x.slice(),t.i=e.i,t}function s(e,t){null==e&&(e=+new Date);var r=new n(e),s=t&&t.state,o=function(){return(r.next()>>>0)/4294967296};return o.double=function(){do{var e=((r.next()>>>11)+(r.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},o.int32=r.next,o.quick=o,s&&(s.x&&a(s,r),o.state=function(){return a(r,{})}),o}t&&t.exports?t.exports=s:r&&r.amd?r((function(){return s})):this.xorshift7=s}(0,e,!1)})),$a=Za((function(e){!function(e,t,r){function n(e){var t=this;t.next=function(){var e,r,n=t.w,a=t.X,s=t.i;return t.w=n=n+1640531527|0,r=a[s+34&127],e=a[s=s+1&127],r^=r<<13,e^=e<<17,r^=r>>>15,e^=e>>>12,r=a[s]=r^e,t.i=s,r+(n^n>>>16)|0},function(e,t){var r,n,a,s,o,i=[],u=128;for(t===(0|t)?(n=t,t=null):(t+="\0",n=0,u=Math.max(u,t.length)),a=0,s=-32;s<u;++s)t&&(n^=t.charCodeAt((s+32)%t.length)),0===s&&(o=n),n^=n<<10,n^=n>>>15,n^=n<<4,n^=n>>>13,s>=0&&(o=o+1640531527|0,a=0==(r=i[127&s]^=n+o)?a+1:0);for(a>=128&&(i[127&(t&&t.length||0)]=-1),a=127,s=512;s>0;--s)n=i[a+34&127],r=i[a=a+1&127],n^=n<<13,r^=r<<17,n^=n>>>15,r^=r>>>12,i[a]=n^r;e.w=o,e.X=i,e.i=a}(t,e)}function a(e,t){return t.i=e.i,t.w=e.w,t.X=e.X.slice(),t}function s(e,t){null==e&&(e=+new Date);var r=new n(e),s=t&&t.state,o=function(){return(r.next()>>>0)/4294967296};return o.double=function(){do{var e=((r.next()>>>11)+(r.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},o.int32=r.next,o.quick=o,s&&(s.X&&a(s,r),o.state=function(){return a(r,{})}),o}t&&t.exports?t.exports=s:r&&r.amd?r((function(){return s})):this.xor4096=s}(0,e,!1)})),es=Za((function(e){!function(e,t,r){function n(e){var t=this,r="";t.next=function(){var e=t.b,r=t.c,n=t.d,a=t.a;return e=e<<25^e>>>7^r,r=r-n|0,n=n<<24^n>>>8^a,a=a-e|0,t.b=e=e<<20^e>>>12^r,t.c=r=r-n|0,t.d=n<<16^r>>>16^a,t.a=a-e|0},t.a=0,t.b=0,t.c=-1640531527,t.d=1367130551,e===Math.floor(e)?(t.a=e/4294967296|0,t.b=0|e):r+=e;for(var n=0;n<r.length+20;n++)t.b^=0|r.charCodeAt(n),t.next()}function a(e,t){return t.a=e.a,t.b=e.b,t.c=e.c,t.d=e.d,t}function s(e,t){var r=new n(e),s=t&&t.state,o=function(){return(r.next()>>>0)/4294967296};return o.double=function(){do{var e=((r.next()>>>11)+(r.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},o.int32=r.next,o.quick=o,s&&("object"==typeof s&&a(s,r),o.state=function(){return a(r,{})}),o}t&&t.exports?t.exports=s:r&&r.amd?r((function(){return s})):this.tychei=s}(0,e,!1)})),ts=Ga({__proto__:null,default:{}}),rs=Za((function(e){!function(t,r,n){var a,s=256,o=n.pow(s,6),i=n.pow(2,52),u=2*i,p=255;function l(e,p,l){var y=[],g=f(h((p=1==p?{entropy:!0}:p||{}).entropy?[e,m(r)]:null==e?function(){try{var e;return a&&(e=a.randomBytes)?e=e(s):(e=new Uint8Array(s),(t.crypto||t.msCrypto).getRandomValues(e)),m(e)}catch(e){var n=t.navigator,o=n&&n.plugins;return[+new Date,t,o,t.screen,m(r)]}}():e,3),y),v=new c(y),b=function(){for(var e=v.g(6),t=o,r=0;e<i;)e=(e+r)*s,t*=s,r=v.g(1);for(;e>=u;)e/=2,t/=2,r>>>=1;return(e+r)/t};return b.int32=function(){return 0|v.g(4)},b.quick=function(){return v.g(4)/4294967296},b.double=b,f(m(v.S),r),(p.pass||l||function(e,t,r,a){return a&&(a.S&&d(a,v),e.state=function(){return d(v,{})}),r?(n.random=e,t):e})(b,g,"global"in p?p.global:this==n,p.state)}function c(e){var t,r=e.length,n=this,a=0,o=n.i=n.j=0,i=n.S=[];for(r||(e=[r++]);a<s;)i[a]=a++;for(a=0;a<s;a++)i[a]=i[o=p&o+e[a%r]+(t=i[a])],i[o]=t;(n.g=function(e){for(var t,r=0,a=n.i,o=n.j,i=n.S;e--;)t=i[a=p&a+1],r=r*s+i[p&(i[a]=i[o=p&o+t])+(i[o]=t)];return n.i=a,n.j=o,r})(s)}function d(e,t){return t.i=e.i,t.j=e.j,t.S=e.S.slice(),t}function h(e,t){var r,n=[],a=typeof e;if(t&&"object"==a)for(r in e)try{n.push(h(e[r],t-1))}catch(e){}return n.length?n:"string"==a?e:e+"\0"}function f(e,t){for(var r,n=e+"",a=0;a<n.length;)t[p&a]=p&(r^=19*t[p&a])+n.charCodeAt(a++);return m(t)}function m(e){return String.fromCharCode.apply(0,e)}if(f(n.random(),r),e.exports){e.exports=l;try{a=ts}catch(e){}}else n.seedrandom=l}("undefined"!=typeof self?self:Ha,[],Math)}));rs.alea=Qa,rs.xor128=Ya,rs.xorwow=Xa,rs.xorshift7=Ja,rs.xor4096=$a,rs.tychei=es;var ns=rs,as=function(){function e(e,t,r,n,a){this.mean=e,this.stdDev=t,this.dtype=r,this.nextVal=NaN,this.truncated=n,this.truncated&&(this.upper=this.mean+2*this.stdDev,this.lower=this.mean-2*this.stdDev);var s=a||Math.random();this.random=ns.alea(s.toString())}return e.prototype.nextValue=function(){if(!isNaN(this.nextVal)){var e=this.nextVal;return this.nextVal=NaN,e}for(var t,r,n=!1;!n;){var a=void 0,s=void 0,o=void 0;do{o=(a=2*this.random()-1)*a+(s=2*this.random()-1)*s}while(o>=1||0===o);var i=Math.sqrt(-2*Math.log(o)/o);t=this.mean+this.stdDev*a*i,r=this.mean+this.stdDev*s*i,this.truncated&&!this.isValidTruncated(t)||(n=!0)}return this.truncated&&!this.isValidTruncated(r)||(this.nextVal=this.convertValue(r)),this.convertValue(t)},e.prototype.convertValue=function(e){return null==this.dtype||"float32"===this.dtype?e:Math.round(e)},e.prototype.isValidTruncated=function(e){return e<=this.upper&&e>=this.lower},e}(),ss=function(){function e(e,t,r,n){this.alpha=e,this.beta=1/t,this.dtype=r;var a=n||Math.random();this.randu=ns.alea(a.toString()),this.randn=new as(0,1,r,!1,this.randu()),this.d=e<1?e+2/3:e-1/3,this.c=1/Math.sqrt(9*this.d)}return e.prototype.nextValue=function(){for(var e,t,r,n,a,s;;){do{n=this.randn.nextValue(),s=1+this.c*n}while(s<=0);if(s*=s*s,t=1-.331*(e=n*n)*e,r=.5*e+this.d*(1-s+Math.log(s)),(a=this.randu())<t||Math.log(a)<r)break}return s=1/this.beta*this.d*s,this.alpha<1&&(s*=Math.pow(this.randu(),1/this.alpha)),this.convertValue(s)},e.prototype.convertValue=function(e){return"float32"===this.dtype?e:Math.round(e)},e}(),os=function(){function e(e,t,r,n){var a=this;if(void 0===e&&(e=0),void 0===t&&(t=1),this.canReturnFloat=function(){return null==a.dtype||"float32"===a.dtype},this.min=e,this.range=t-e,this.dtype=r,null==n&&(n=Math.random()),"number"==typeof n&&(n=n.toString()),!this.canReturnFloat()&&this.range<=1)throw new Error("The difference between "+e+" - "+t+" <= 1 and dtype is not float");this.random=ns.alea(n)}return e.prototype.convertValue=function(e){return this.canReturnFloat()?e:Math.round(e)},e.prototype.nextValue=function(){return this.convertValue(this.min+this.range*this.random())},e}();var is=rr({randomGamma_:function(e,t,r,n,a){if(void 0===r&&(r=1),void 0===n&&(n="float32"),null==r&&(r=1),null==n&&(n="float32"),"float32"!==n&&"int32"!==n)throw new Error("Unsupported data type "+n);for(var s=new ss(t,r,n,a),o=Ur(e,n),i=0;i<o.values.length;i++)o.values[i]=s.nextValue();return o.toTensor()}});var us=rr({randomNormal_:function(e,t,r,n,a){if(void 0===t&&(t=0),void 0===r&&(r=1),null!=n&&"bool"===n)throw new Error("Unsupported data type "+n);for(var s=new as(t,r,n,!1,a),o=Ur(e,n),i=0;i<o.values.length;i++)o.values[i]=s.nextValue();return o.toTensor()}});var ps=rr({randomStandardNormal_:function(e,t,r){if(null!=t&&"bool"===t)throw new Error("Unsupported data type "+t);return us(e,0,1,t,r)}});var ls=rr({randomUniform_:function(e,t,r,n,a){void 0===t&&(t=0),void 0===r&&(r=1),void 0===n&&(n="float32");for(var s=Ur(e,n),o=new os(t,r,null,a),i=0;i<s.values.length;i++)s.values[i]=o.nextValue();return s.toTensor()}});function cs(e,t,r,n){if(void 0===r&&(r=1),void 0===n&&(n="float32"),0===r)throw new Error("Cannot have a step of zero");var a={start:e,stop:t,step:r,dtype:n};return Zt.runKernel("Range",{},a)}var ds=rr({real_:function(e){var t={input:$t(e,"input","real")};return Zt.runKernel("Real",t)}});var hs=rr({reciprocal_:function(e){var t={x:$t(e,"x","reciprocal")};return Zt.runKernel("Reciprocal",t)}});var fs=rr({relu_:function(e){var t={x:$t(e,"x","relu")};return Zt.runKernel("Relu",t)}});var ms=rr({relu6_:function(e){var t={x:$t(e,"x","relu6")};return Zt.runKernel("Relu6",t)}});var ys=rr({reverse_:function(e,t){var r={x:$t(e,"x","reverse")},n={dims:t};return Zt.runKernel("Reverse",r,n)}});var gs=rr({reverse1d_:function(e){var t=$t(e,"x","reverse");return ae(1===t.rank,(function(){return"Error in reverse1D: x must be rank 1 but got rank "+t.rank+"."})),ys(t,0)}});var vs=rr({reverse2d_:function(e,t){var r=$t(e,"x","reverse");return ae(2===r.rank,(function(){return"Error in reverse2D: x must be rank 2 but got rank "+r.rank+"."})),ys(r,t)}});var bs=rr({reverse3d_:function(e,t){var r=$t(e,"x","reverse");return ae(3===r.rank,(function(){return"Error in reverse3D: x must be rank 3 but got rank "+r.rank+"."})),ys(r,t)}});var xs=rr({reverse4d_:function(e,t){var r=$t(e,"x","reverse");return ae(4===r.rank,(function(){return"Error in reverse4D: x must be rank 4 but got rank "+r.rank+"."})),ys(r,t)}});var Ns=rr({round_:function(e){var t={x:$t(e,"x","round")};return Zt.runKernel("Round",t)}});var ws=rr({rsqrt_:function(e){var t={x:$t(e,"x","rsqrt","float32")};return Zt.runKernel("Rsqrt",t)}});var ks=rr({selu_:function(e){var t={x:$t(e,"x","selu")};return Zt.runKernel("Selu",t)}});var Ts=rr({separableConv2d_:function(e,t,r,n,a,s,o){void 0===s&&(s=[1,1]),void 0===o&&(o="NHWC");var i=$t(e,"x","separableConv2d"),u=$t(t,"depthwiseFilter","separableConv2d"),p=$t(r,"pointwiseFilter","separableConv2d"),l=i,c=!1;if(3===i.rank&&(c=!0,l=_r(i,[1,i.shape[0],i.shape[1],i.shape[2]])),"NCHW"===o)throw new Error("separableConv2d currently does not support dataFormat NCHW; only NHWC is supported");ae(4===l.rank,(function(){return"Error in separableConv2d: input must be rank 4, but got rank "+l.rank+"."})),ae(4===u.rank,(function(){return"Error in separableConv2d: depthwise filter must be rank 4, but got rank "+u.rank+"."})),ae(4===p.rank,(function(){return"Error in separableConv2d: pointwise filter must be rank 4, but got rank "+u.rank+"."})),ae(1===p.shape[0],(function(){return"Error in separableConv2d: the first dimension of pointwise filter  must be 1, but got "+p.shape[0]+"."})),ae(1===p.shape[1],(function(){return"Error in separableConv2d: the second dimension of pointwise filter must be 1, but got "+p.shape[1]+"."}));var d=u.shape[2],h=u.shape[3];ae(p.shape[2]===d*h,(function(){return"Error in separableConv2d: the third dimension of pointwise filter must be "+d*h+", but got "+p.shape[2]+"."}));var f=hn(l,u,n,a,o,s),m=$r(f,p,1,"valid",o);return c?_r(m,[m.shape[1],m.shape[2],m.shape[3]]):m}});var _s=function(e,t){return i(this,void 0,void 0,(function(){var r,n,a,s,o,i,p,l,c,d;return u(this,(function(u){switch(u.label){case 0:return r=$t(e,"x","setdiff1d"),n=$t(t,"y","setdiff1d"),ae(r.dtype===n.dtype,(function(){return"x and y should have the same dtype, but got x ("+r.dtype+") and y ("+n.dtype+")."})),ae(1===r.rank,(function(){return"x should be 1D tensor, but got x ("+r.shape+")."})),ae(1===n.rank,(function(){return"y should be 1D tensor, but got y ("+n.shape+")."})),[4,r.data()];case 1:return a=u.sent(),[4,n.data()];case 2:for(s=u.sent(),o=new Set(s),i=0,c=0;c<a.length;c++)o.has(a[c])||i++;for(p=new At([i],r.dtype),l=new At([i],"int32"),c=0,d=0;c<a.length;c++)o.has(a[c])||(p.values[d]=a[c],l.values[d]=c,d++);return[2,[p.toTensor(),l.toTensor()]]}}))}))};var Ss=rr({sign_:function(e){var t={x:$t(e,"x","sign")};return Zt.runKernel("Sign",t)}});var Es=rr({sin_:function(e){var t={x:$t(e,"x","sin","float32")};return Zt.runKernel("Sin",t)}});var Is=rr({sinh_:function(e){var t={x:$t(e,"x","sinh")};return Zt.runKernel("Sinh",t)}});var Os=rr({slice1d_:function(e,t,r){var n=$t(e,"x","slice1d");return ae(1===n.rank,(function(){return"slice1d expects a rank-1 tensor, but got a rank-"+n.rank+" tensor"})),Cr(n,[t],[r])}});var Ds=rr({slice2d_:function(e,t,r){var n=$t(e,"x","slice2d");return ae(2===n.rank,(function(){return"slice2d expects a rank-2 tensor, but got a rank-"+n.rank+" tensor"})),Cr(n,t,r)}});var As=rr({slice3d_:function(e,t,r){var n=$t(e,"x","slice3d");return ae(3===n.rank,(function(){return"slice3d expects a rank-3 tensor, but got a rank-"+n.rank+" tensor"})),Cr(n,t,r)}});var Ms=rr({slice4d_:function(e,t,r){var n=$t(e,"x","slice4d");return ae(4===n.rank,(function(){return"slice4d expects a rank-4 tensor, but got a rank-"+n.rank+" tensor"})),Cr(n,t,r)}});var Cs=rr({softmax_:function(e,t){void 0===t&&(t=-1);var r=$t(e,"logits","softmax","float32");if(-1===t&&(t=r.rank-1),t!==r.rank-1)throw Error("Softmax along a non-last dimension is not yet supported. Logits was rank "+r.rank+" and dim was "+t);var n={logits:r},a={dim:t};return Zt.runKernel("Softmax",n,a)}});var Fs=rr({fft_:function(e){ae("complex64"===e.dtype,(function(){return"The dtype for tf.spectral.fft() must be complex64 but got "+e.dtype+"."}));var t={input:e};return Zt.runKernel("FFT",t)}});var Vs=rr({ifft_:function(e){ae("complex64"===e.dtype,(function(){return"The dtype for tf.spectral.ifft() must be complex64 but got "+e.dtype+"."}));var t={input:e};return Zt.runKernel("IFFT",t)}});var Rs=rr({irfft_:function(e){var t,r=e.shape[e.shape.length-1],n=e.size/r;if(r<=2){var a=_r(e,[n,r]);t=Vs(a)}else{var s=[n,2*(r-1)],o=_r(ds(e),[n,r]),i=_r(Zn(e),[n,r]),u=ys(Cr(o,[0,1],[n,r-2]),1),p=Ar(ys(Cr(i,[0,1],[n,r-2]),1),Mn(-1)),l=Or([o,u],1),c=Or([i,p],1);a=_r(Zr(l,c),[s[0],s[1]]);t=Vs(a)}if(t=ds(t),3===e.rank&&0!==e.shape[0]){var d=t,h=e.shape[0];t=_r(t,[h,t.shape[0]/h,t.shape[1]]),d.dispose()}return t}});var zs=rr({split_:function(e,t,r){void 0===r&&(r=0);var n={x:$t(e,"x","split")},a={numOrSizeSplits:t,axis:r};return Zt.runKernel("SplitV",n,a)}});var Ls=rr({rfft_:function(e,t){ae("float32"===e.dtype,(function(){return"The dtype for rfft() must be real value but got "+e.dtype}));var r,n=e.shape[e.shape.length-1],a=e.size/n;if(null!=t&&t<n){var s=e.shape.map((function(e){return 0})),o=e.shape.map((function(e){return e}));o[e.shape.length-1]=t,r=Cr(e,s,o),n=t}else if(null!=t&&t>n){var i=e.shape.map((function(e){return e}));i[e.shape.length-1]=t-n,r=Or([e,wa(i)],e.shape.length-1),n=t}else r=e;var u=Nn(r),p=_r(Zr(r,u),[a,n]),l=Fs(p),c=Math.floor(n/2)+1,d=ds(l),h=Zn(l),f=zs(d,[c,n-c],d.shape.length-1),m=zs(h,[c,n-c],h.shape.length-1),y=r.shape.slice();return y[r.shape.length-1]=c,_r(Zr(f[0],m[0]),y)}});var Ps=rr({squaredDifference_:function(e,t){var r,n=$t(e,"a","squaredDifference"),a=$t(t,"b","squaredDifference");n=(r=l(qt(n,a),2))[0],a=r[1],vn(n.shape,a.shape);var s={a:n,b:a};return Zt.runKernel("SquaredDifference",s,{})}});var Bs=rr({squeeze_:function(e,t){var r=$t(e,"x","squeeze","string_or_numeric");return _r(r,function(e,t){for(var r=[],n=[],a=null!=t&&Array.isArray(t)&&0===t.length,s=null==t||a?null:de(t,e).sort(),o=0,i=0;i<e.length;++i){if(null!=s){if(s[o]===i&&1!==e[i])throw new Error("Can't squeeze axis "+i+" since its dim '"+e[i]+"' is not 1");(null==s[o]||s[o]>i)&&1===e[i]&&(r.push(e[i]),n.push(i)),s[o]<=i&&o++}1!==e[i]&&(r.push(e[i]),n.push(i))}return{newShape:r,keptDims:n}}(r.shape,t).newShape)}});var Ks=rr({stack_:function(e,t){void 0===t&&(t=0);var r=er(e,"tensors","stack","string_or_numeric");ae(r.length>=1,(function(){return"Pass at least one tensor to tf.stack"})),r.length>0&&ae(t<=r[0].rank,(function(){return"Axis must be <= rank of the tensor"}));var n=r,a={axis:t};return Zt.runKernel("Pack",n,a)}});var qs=rr({step_:function(e,t){void 0===t&&(t=0);var r={x:$t(e,"x","step")},n={alpha:t};return Zt.runKernel("Step",r,n)}});var js=rr({stridedSlice_:function(e,t,r,n,a,s,o,i,u){void 0===a&&(a=0),void 0===s&&(s=0),void 0===o&&(o=0),void 0===i&&(i=0),void 0===u&&(u=0);var p={x:$t(e,"x","stridedSlice","string_or_numeric")},l={begin:t,end:r,strides:n,beginMask:a,endMask:s,ellipsisMask:o,newAxisMask:i,shrinkAxisMask:u};return Zt.runKernel("StridedSlice",p,l)}});var Us=rr({tan_:function(e){var t={x:$t(e,"x","tan","float32")};return Zt.runKernel("Tan",t)}});function Ws(e,t,r){return An(e,t,Yt(e,r),r)}function Hs(e,t){oe(e);var r=Yt(e,t);if(1!==r.length)throw new Error("tensor1d() requires values to be a flat/TypedArray");return An(e,null,r,t)}function Gs(e,t,r){if(oe(e),null!=t&&2!==t.length)throw new Error("tensor2d() requires shape to have two numbers");var n=Yt(e,r);if(2!==n.length&&1!==n.length)throw new Error("tensor2d() requires values to be number[][] or flat/TypedArray");if(1===n.length&&null==t)throw new Error("tensor2d() requires shape to be provided when `values` are a flat/TypedArray");return An(e,t,n,r)}var Zs=rr({topk_:function(e,t,r){void 0===t&&(t=1),void 0===r&&(r=!0);var n=$t(e,"x","topk");if(0===n.rank)throw new Error("topk() expects the input to be of rank 1 or higher");var a=n.shape[n.shape.length-1];if(t<0)throw new Error("'k' passed to topk() must be >= 0 but got "+t);if(t>a)throw new Error("'k' passed to topk() must be <= the last dimension ("+a+") but got "+t);var s={x:n},o={k:t,sorted:r},i=l(Zt.runKernel("TopK",s,o),2);return{values:i[0],indices:i[1]}}});var Qs=rr({truncatedNormal_:function(e,t,r,n,a){if(void 0===t&&(t=0),void 0===r&&(r=1),null!=n&&"bool"===n)throw new Error("Unsupported data type $ { dtype }");for(var s=new as(t,r,n,!0,a),o=Ur(e,n),i=0;i<o.values.length;i++)o.values[i]=s.nextValue();return o.toTensor()}});var Ys=rr({unique_:function(e,t){void 0===t&&(t=0);var r=$t(e,"x","unique","string_or_numeric");ae(r.rank>0,(function(){return"The input tensor must be at least 1D"}));var n={x:r},a={axis:t},s=l(Zt.runKernel("Unique",n,a),2);return{values:s[0],indices:s[1]}}});var Xs=rr({unsortedSegmentSum_:function(e,t,r){var n=$t(e,"x","unsortedSegmentSum"),a=$t(t,"segmentIds","unsortedSegmentSum","int32");ae(le(r),(function(){return"numSegments must be of dtype int"}));var s={x:n,segmentIds:a},o={numSegments:r};return Zt.runKernel("UnsortedSegmentSum",s,o)}});var Js=rr({unstack_:function(e,t){void 0===t&&(t=0);var r=$t(e,"x","unstack","string_or_numeric");ae(t>=-r.shape.length&&t<r.shape.length,(function(){return"Axis = "+t+" is not in [-"+r.shape.length+", "+r.shape.length+")"}));var n={value:r},a={axis:t};return Zt.runKernel("Unpack",n,a)}});var $s=function(e){return i(this,void 0,void 0,(function(){var t,r,n;return u(this,(function(a){switch(a.label){case 0:return[4,(t=$t(e,"condition","whereAsync","bool")).data()];case 1:return r=a.sent(),n=function(e,t){for(var r=[],n=0;n<t.length;n++)t[n]&&r.push(n);var a=Ur(e,"int32"),s=Ur([r.length,e.length],"int32");for(n=0;n<r.length;n++){var o=a.indexToLoc(r[n]),i=n*e.length;s.values.set(o,i)}return s.toTensor()}(t.shape,r),e!==t&&t.dispose(),[2,n]}}))}))};var eo=function(e,t,r){return i(this,void 0,void 0,(function(){var n,a,s,o,i,p,l,c,d,h,f,m,y;return u(this,(function(u){switch(u.label){case 0:for(n=$t(e,"tensor","boolMask"),a=$t(t,"mask","boolMask","bool"),s=null==r?0:r,o=a.rank,i=n.shape,ae(o>0,(function(){return"mask cannot be scalar"})),se(i.slice(s,s+o),a.shape,"mask's shape must match the first K dimensions of tensor's shape,"),p=1,l=s;l<s+o;l++)p*=i[l];return c=i.slice(0,s).concat([p],i.slice(s+o)),d=_r(n,c),h=_r(a,[-1]),[4,$s(h)];case 1:return f=u.sent(),m=Bs(f,[1]),y=Wn(d,m,s),e!==n&&n.dispose(),t!==a&&a.dispose(),m.dispose(),d.dispose(),h.dispose(),f.dispose(),[2,y]}}))}))};var to=rr({transpose_:function(e,t,r){var n=$t(e,"x","transpose");if(null==t&&(t=n.shape.map((function(e,t){return t})).reverse()),ae(n.rank===t.length,(function(){return"Error in transpose: rank of input "+n.rank+" must match length of perm "+t+"."})),t.forEach((function(e){ae(e>=0&&e<n.rank,(function(){return"All entries in 'perm' must be between 0 and "+(n.rank-1)+" but got "+t}))})),n.rank<=1)return n.clone();var a,s,o={x:n},i={perm:t};return"complex64"===n.dtype?(a=function(){var e=ds(n),t=Zn(n);return e=Zt.runKernel(Le,{x:e},i),t=Zt.runKernel(Le,{x:t},i),r&&(t=sa(t)),Zr(e,t)},Zt.tidy(a,s)):Zt.runKernel(Le,o,i)}});var ro=rr({movingAverage_:function(e,t,r,n,a){void 0===a&&(a=!0);var s,o,i=$t(e,"v","movingAverage"),u=$t(t,"x","movingAverage"),p=$t(r,"decay","movingAverage");o=u,ae((s=i).dtype===o.dtype,(function(){return"The dtypes of the first("+s.dtype+") and second("+o.dtype+") input must match"})),ae(pe(i.shape,u.shape),(function(){return"Shape mismatch in v and x"}));var l=Mn(1),c=ua(l,p),d=Ar(ua(u,i),c);if(a){ae(null!=n,(function(){return"When using zeroDebias: true, step is required."}));var h=$t(n,"step","movingAverage");d=gn(d,ua(l,Dn(p,h)))}return or(i,d)}});function no(e,t,r){if(t.rank<1)throw new Error("tf.scatterND() expects the indices to be rank 1 or higher, but the rank was "+t.rank+".");if(e.rank<1)throw new Error("tf.scatterND() expects the updates to be rank 1 or higher, but the rank was "+e.rank+".");if("int32"!==t.dtype)throw new Error("The dtype of 'indices' should be int32, but got dtype: "+t.dtype);if(r.length<1)throw new Error("Output rank must be greater or equal to 1, but got shape: "+r);if(0===r.length){if(0===t.size)throw new Error("Indices specified for empty output. indices shape: "+t.shape);if(0===e.size)throw new Error("Updates specified for empty output. updates shape: "+e.shape)}!function(e,t,r){var n=t.rank>1?t.shape[t.rank-1]:1,a=t.rank>1?t.rank-1:1,s="Must have updates.shape = indices.shape[:batchDim] + shape[sliceDim:], got updates.shape: "+r.shape+", indices.shape: "+t.shape+", shape: "+e+", sliceDim: "+n+", and batchDim: "+a+".";if(r.rank<a)throw new Error(s+" update.rank < "+a+". ");if(e.length<n+(r.rank-a))throw new Error(s+" Output shape length < "+(n+(r.rank-a)));if(r.rank!==a+e.length-n)throw new Error(s+" update.rank != "+(a+e.length-n));for(var o=0;o<a;++o)if(r.shape[o]!==t.shape[o])throw new Error(s+" updates.shape["+o+"] ("+r.shape[o]+") != indices.shape["+o+"] ("+t.shape[o]+").");for(o=0;o<r.rank-a;++o)if(r.shape[o+a]!==e[o+n])throw new Error(s+" updates.shape["+(o+a)+"] ("+r.shape[o+a]+") != shape["+(o+a)+"] ("+e[o+a]+")")}(r,t,e)}var ao=rr({scatterND_:function(e,t,r){var n=$t(e,"indices","scatterND","int32"),a=$t(t,"updates","scatterND");no(a,n,r);var s={indices:n,updates:a},o={shape:r};return Zt.runKernel("ScatterNd",s,o)}});var so=rr({sparseToDense_:function(e,t,r,n){void 0===n&&(n=0);var a=$t(e,"sparseIndices","sparseToDense","int32"),s=$t(t,"sparseValues","sparseToDense","string_or_numeric"),o=$t(n,"defaultValue","sparseToDense",s.dtype);!function(e,t,r,n){if("int32"!==e.dtype)throw new Error("tf.sparseToDense() expects the indices to be int32 type, but the dtype was "+e.dtype+".");if(e.rank>2)throw new Error("sparseIndices should be a scalar, vector, or matrix, but got shape "+e.shape+".");var a=e.rank>0?e.shape[0]:1,s=e.rank>1?e.shape[1]:1;if(r.length!==s)throw new Error("outputShape has incorrect number of elements:, "+r.length+", should be: "+s+".");var o=t.size;if(0!==t.rank&&(1!==t.rank||o!==a))throw new Error("sparseValues has incorrect shape "+t.shape+", should be [] or ["+a+"]");if(t.dtype!==n.dtype)throw new Error("sparseValues.dtype must match defaultValues.dtype")}(a,s,r,o);var i={sparseIndices:a,sparseValues:s,defaultValue:o},u={outputShape:r};return Zt.runKernel("SparseToDense",i,u)}});var oo=rr({gatherND_:function(e,t){var r=$t(t,"indices","gatherND","int32"),n={params:$t(e,"x","gatherND","string_or_numeric"),indices:r};return Zt.runKernel("GatherNd",n)}});var io=rr({dropout_:function(e,t,r,n){var a=$t(e,"x","dropout");if(ae("float32"===a.dtype,(function(){return"x has to be a floating point tensor since it's going to be scaled, but got a "+a.dtype+" tensor instead."})),ae(t>=0&&t<1,(function(){return"rate must be a float in the range [0, 1), but got "+t+"."})),0===t)return e instanceof Ft?a.clone():a;var s=function(e,t){if(null==t)return e.shape.slice();if(pe(e.shape,t))return t;if(e.shape.length===t.length){for(var r=[],n=0;n<e.shape.length;n++)null==t[n]&&null!=e.shape[n]?r.push(e.shape[n]):r.push(t[n]);return r}return t}(a,r),o=1-t,i=gn(Un(or(ls(s,0,1,"float32",n),o)),o);return Ar(a,i)}});function uo(e){return Math.floor(Math.pow(2,Math.ceil(Math.log(e)/Math.log(2))))}function po(e,t,r){for(var n=1-e%2,a=new Float32Array(e),s=0;s<e;++s){var o=2*Math.PI*s/(e+n-1);a[s]=t-r*Math.cos(o)}return Hs(a,"float32")}var lo=function(e,t,r){return void 0===r&&(r=1),i(this,void 0,void 0,(function(){var n,a,s,o,i,p,c,d,h,f,m,y,g,v;return u(this,(function(u){switch(u.label){case 0:return n=$t(e,"predictions","inTopK"),a=$t(t,"targets","inTopK"),ae(n.rank>1,(function(){return"inTopK() expects the predictions to be of rank 2 or higher, but got "+n.rank})),ae(n.rank-1===a.rank,(function(){return"predictions rank should be 1 larger than targets rank, but got predictions rank "+n.rank+" and targets rank "+a.rank})),se(n.shape.slice(0,n.shape.length-1),a.shape,"predictions's shape should be align with the targets' shape, except the last dimension."),s=n.shape[n.shape.length-1],ae(r>0&&r<=s,(function(){return"'k' passed to inTopK() must be > 0 && <= the predictions last dimension ("+s+"), but got "+r})),[4,n.data()];case 1:return o=u.sent(),[4,a.data()];case 2:for(i=u.sent(),p=l([o.length/s,s],2),c=p[0],d=p[1],h=function(e,t){var r=null;if(null==e||"float32"===e)r=new Float32Array(t);else if("int32"===e)r=new Int32Array(t);else{if("bool"!==e)throw new Error("Unknown data type "+e);r=new Uint8Array(t)}return r}("bool",c),f=0;f<c;f++){for(m=f*d,y=o.subarray(m,m+d),g=[],v=0;v<y.length;v++)g.push({value:y[v],index:v});for(g.sort((function(e,t){return t.value-e.value})),h[f]=0,v=0;v<r;v++)if(g[v].index===i[f]){h[f]=1;break}}return e!==n&&n.dispose(),t!==a&&a.dispose(),[2,Ws(h,a.shape,"bool")]}}))}))};var co=rr({conv2DBackpropFilter_:function(e,t,r,n,a,s,o){void 0===s&&(s="NHWC");var i=e;3===e.rank&&(i=_r(e,[1,e.shape[0],e.shape[1],e.shape[2]]));var u=t;3===u.rank&&(u=_r(t,[1,t.shape[0],t.shape[1],t.shape[2]])),ae(4===i.rank,(function(){return"Error in conv2dDerFilter: input must be rank 4, but got shape "+i.shape+"."})),ae(4===u.rank,(function(){return"Error in conv2dDerFilter: dy must be rank 4, but got shape "+u.shape+"."})),ae(4===r.length,(function(){return"Error in conv2dDerFilter: filterShape must be length 4, but got "+r+"."}));var p="NHWC"===s?i.shape[3]:i.shape[1],l="NHWC"===s?u.shape[3]:u.shape[1];ae(p===r[2],(function(){return"Error in conv2dDerFilter: depth of input "+p+") must match input depth in filter ("+r[2]+"."})),ae(l===r[3],(function(){return"Error in conv2dDerFilter: depth of dy ("+l+") must match output depth for filter ("+r[3]+")."})),Tr("conv2dDerFilter",a,o);var c={x:i,dy:u},d={strides:n,pad:a,dataFormat:s,dimRoundingMode:o,filterShape:r};return Zt.runKernel("Conv2DBackpropFilter",c,d)}});function ho(e,t,r){if(null==r||"linear"===r)return e;if("relu"===r)return Ar(e,qs(t));throw new Error("Cannot compute gradient for fused activation "+r+".")}function fo(e,t){var r=t,n=function(e,t){for(var r=[],n=0;n<t.length;n++){var a=e[e.length-n-1],s=t.length-n-1,o=t[s];(null==a||1===a&&o>1)&&r.unshift(s)}return r}(e.shape,t.shape);return n.length>0&&(r=Vn(r,n)),_r(r,e.shape)}function mo(e,t,r,n){if("linear"===t)return e;if("relu"===t)return fs(e);if("elu"===t)return _n(e);if("relu6"===t)return ms(e);if("prelu"===t)return Ka(e,r);if("leakyrelu"===t)return Jn(e,n);if("sigmoid"===t)return Mr(e);throw new Error("Unknown fused activation "+t+".")}var yo=function(e,t){return!(e>0)||"linear"===t};var go=rr({fusedConv2d_:function(e){var t,r=e.x,n=e.filter,a=e.strides,s=e.pad,o=e.dataFormat,i=void 0===o?"NHWC":o,u=e.dilations,p=void 0===u?[1,1]:u,c=e.dimRoundingMode,d=e.bias,h=e.activation,f=void 0===h?"linear":h,m=e.preluActivationWeights,y=e.leakyreluAlpha;if(f=f||"linear",!1===yo(Zt.state.gradientDepth,f)){ae("NHWC"===i,(function(){return"Error in fused conv2d: got dataFormat of "+i+" but only NHWC is currently supported for the case of gradient depth is 0 and the activation is not linear."}));var g=$r(r,n,a,s,i,p,c);return null!=d&&(g=or(g,d)),mo(g,f,m,y)}var v=$t(r,"x","conv2d","float32"),b=$t(n,"filter","conv2d","float32"),x=v,N=!1;3===v.rank&&(N=!0,x=_r(v,[1,v.shape[0],v.shape[1],v.shape[2]])),ae(4===x.rank,(function(){return"Error in fused conv2d: input must be rank 4, but got rank "+x.rank+"."})),ae(4===b.rank,(function(){return"Error in fused conv2d: filter must be rank 4, but got rank "+b.rank+"."})),Tr("fused conv2d",s,c);var w="NHWC"===i?x.shape[3]:x.shape[1];ae(b.shape[2]===w,(function(){return"Error in conv2d: depth of input ("+w+") must match input depth for filter "+b.shape[2]+"."})),ae(kr(a,p),(function(){return"Error in conv2D: Either strides or dilations must be 1. Got strides "+a+" and dilations '"+p+"'"}));var k,T,_=vr(x.shape,b.shape,a,p,s,c);if(null!=d&&(t=l(qt(k=$t(d,"bias","fused conv2d"),v),1),k=t[0],"NHWC"===i?vn(_.outShape,k.shape):(ae(k.shape.length<=1,(function(){return"Error in fused conv2d: only supports scalar or 1-D Tensor bias for NCHW format but got the bias of rank-"+k.shape.length+"."})),ae(0===k.shape.length||k.shape[0]===_.outChannels||1===k.shape[0],(function(){return"Error in fused conv2d: bias shape ("+k.shape+") is not compatible with the number of output channels ("+_.outChannels+")"})))),null!=m){var S=m.shape;if(ae(S.length<=1||3===S.length,(function(){return"Error in fused conv2d: only supports scalar, 1-D Tensor or 3-D Tensor PReLU activation weights but got a tensor of rank-"+S.length+"."})),1===S.length)ae(1===S[0]||S[0]===_.outChannels,(function(){return"Error in fused conv2d: PReLU activation weights ("+S+") is not compatible with the number of output channels ("+_.outChannels+")."}));else if(3===S.length)try{vn(S,_.outShape)}catch(e){var E="Error in fused conv2d: PReLU activation weights ("+S+") is not compatible with the output shape of the conv2d ("+_.outShape+").";throw Error(E)}T=$t(m,"prelu weights","fused conv2d")}var I=function(e,t){ae("NHWC"===i,(function(){return"Error in gradient of fused conv2D: got dataFormat of "+i+" but only NHWC is currently supported."}));var r=l(t,4),n=r[0],o=r[1],u=r[2],c=r[3],d=ho(e,u,f);ae(wr(p),(function(){return"Error in gradient of fused conv2D: dilation rates greater than 1 are not yet supported in gradients. Got dilations '"+p+"'"}));var h=[tn(o.shape,d,n,a,s),co(o,d,n.shape,a,s)];if(null!=c){var m=fo(c,d);h.push(m)}return h},O={x:x,filter:b,bias:k,preluActivationWeights:T},D={strides:a,pad:s,dataFormat:i,dilations:p,dimRoundingMode:c,activation:f,leakyreluAlpha:y};if(null==d){var A=aa((function(e,t,r){var n=Zt.runKernel(Be,O,D);return r([t,e,n]),N&&(n=_r(n,[n.shape[1],n.shape[2],n.shape[3]])),{value:n,gradFunc:I}}));return A(x,b)}var M=aa((function(e,t,r,n){var a=Zt.runKernel(Be,O,D);return n([t,e,a,r]),N&&(a=_r(a,[a.shape[1],a.shape[2],a.shape[3]])),{value:a,gradFunc:I}}));return M(x,b,k)}});var vo=rr({depthwiseConv2dNativeBackpropFilter_:function(e,t,r,n,a,s,o){void 0===s&&(s=[1,1]);var i=e;3===e.rank&&(i=_r(e,[1,e.shape[0],e.shape[1],e.shape[2]]));var u=t;3===u.rank&&(u=_r(t,[1,t.shape[0],t.shape[1],t.shape[2]]));var p={x:i,dy:u},l={strides:n,pad:a,dimRoundingMode:o,dilations:s,filterShape:r};return Zt.runKernel("DepthwiseConv2dNativeBackpropFilter",p,l)}});var bo=rr({depthwiseConv2dNativeBackpropInput_:function(e,t,r,n,a,s,o){void 0===s&&(s=[1,1]);var i=t,u=!1;3===t.rank&&(u=!0,i=_r(t,[1,t.shape[0],t.shape[1],t.shape[2]]));var p={dy:i,filter:r},l={strides:n,pad:a,dimRoundingMode:o,dilations:s,inputShape:e},c=Zt.runKernel("DepthwiseConv2dNativeBackpropInput",p,l);return u?_r(c,[c.shape[1],c.shape[2],c.shape[3]]):c}});var xo=rr({fusedDepthwiseConv2d_:function(e){var t,r=e.x,n=e.filter,a=e.strides,s=e.pad,o=e.dataFormat,i=void 0===o?"NHWC":o,u=e.dilations,p=void 0===u?[1,1]:u,c=e.dimRoundingMode,d=e.bias,h=e.activation,f=void 0===h?"linear":h,m=e.preluActivationWeights,y=e.leakyreluAlpha;if(!1===yo(Zt.state.gradientDepth,f)){var g=hn(r,n,a,s,i,p,c);return null!=d&&(g=or(g,d)),mo(g,f,m,y)}var v=$t(r,"x","depthwiseConv2d","float32"),b=$t(n,"filter","depthwiseConv2d","float32"),x=v,N=!1;3===v.rank&&(N=!0,x=_r(v,[1,v.shape[0],v.shape[1],v.shape[2]])),ae(4===x.rank,(function(){return"Error in fused depthwiseConv2d: input must be rank 4, but got rank "+x.rank+"."})),ae(4===b.rank,(function(){return"Error in fused depthwiseConv2d: filter must be rank 4, but got rank "+b.rank+"."})),ae(x.shape[3]===b.shape[2],(function(){return"Error in fused depthwiseConv2d: number of input channels ("+x.shape[3]+") must match the inChannels dimension in filter "+b.shape[2]+"."})),null==p&&(p=[1,1]),ae(kr(a,p),(function(){return"Error in fused depthwiseConv2d: Either strides or dilations must be 1. Got strides "+a+" and dilations '"+p+"'"})),Tr("fused depthwiseConv2d",s,c);var w,k,T=vr(x.shape,b.shape,a,p,s,c,!0);null!=d&&(t=l(qt(w=$t(d,"bias","fused conv2d"),v),1),w=t[0],vn(T.outShape,w.shape)),null!=m&&(k=$t(m,"prelu weights","fused depthwiseConv2d"));var _=function(e,t){ae(wr(p),(function(){return"Error in gradient of fused depthwiseConv2d: dilation rates greater than 1 are not yet supported. Got dilations '"+p+"'"}));var r=l(t,4),n=r[0],o=r[1],i=r[2],u=r[3],d=ho(e,i,f),h=bo(o.shape,d,n,a,s,p,c),m=vo(o,d,n.shape,a,s,p,c);return null!=u?[h,m,fo(w,d)]:[h,m]},S={x:x,filter:b,bias:w,preluActivationWeights:k},E={strides:a,pad:s,dataFormat:i,dilations:p,dimRoundingMode:c,activation:f,leakyreluAlpha:y};if(null==d){var I=aa((function(e,t,r){var n=Zt.runKernel(Ke,S,E);return r([t,e,n]),N&&(n=_r(n,[n.shape[1],n.shape[2],n.shape[3]])),{value:n,gradFunc:_}}));return I(x,b)}var O=aa((function(e,t,r,n){var a=Zt.runKernel(Ke,S,E);return n([t,e,a,r]),N&&(a=_r(a,[a.shape[1],a.shape[2],a.shape[3]])),{value:a,gradFunc:_}}));return O(x,b,w)}});var No={__proto__:null,conv2d:go,depthwiseConv2d:xo,matMul:rr({fusedMatMul_:function(e){var t,r=e.a,n=e.b,a=e.transposeA,s=void 0!==a&&a,o=e.transposeB,i=void 0!==o&&o,u=e.bias,p=e.activation,c=void 0===p?"linear":p,d=e.preluActivationWeights,h=e.leakyreluAlpha,f=void 0===h?.2:h;if(!1===yo(Zt.state.gradientDepth,c)){var m=Dr(r,n,s,i);return null!=u&&(m=or(m,u)),mo(m,c,d,f)}var y=$t(r,"a","fused matMul"),g=$t(n,"b","fused matMul");t=l(qt(y,g),2),y=t[0],g=t[1];var v=s?y.shape[y.rank-2]:y.shape[y.rank-1],b=i?g.shape[g.rank-1]:g.shape[g.rank-2],x=s?y.shape[y.rank-1]:y.shape[y.rank-2],N=i?g.shape[g.rank-2]:g.shape[g.rank-1],w=y.shape.slice(0,-2),k=g.shape.slice(0,-2),T=ue(w),_=ue(k);ae(v===b,(function(){return"Error in fused matMul: inner shapes ("+v+") and ("+b+") of Tensors with shapes "+y.shape+" and "+g.shape+" and transposeA="+s+" and transposeB="+i+" must match."}));var S,E,I=vn(y.shape.slice(0,-2),g.shape.slice(0,-2)).concat([x,N]),O=_r(y,s?[T,v,x]:[T,x,v]),D=_r(g,i?[_,N,b]:[_,b,N]);null!=u&&(S=l(qt(S=$t(u,"bias","fused matMul"),y),1)[0],vn(I,S.shape)),null!=d&&(E=$t(d,"prelu weights","fused matMul"));var A=function(e,t){var r,n,a=l(t,4),o=a[0],p=a[1],d=a[2],h=a[3],f=ho(_r(e,d.shape),d,c);return s||i?!s&&i?(r=Dr(f,p,!1,!1),n=Dr(f,o,!0,!1)):s&&!i?(r=Dr(p,f,!1,!0),n=Dr(o,f,!1,!1)):(r=Dr(p,f,!0,!0),n=Dr(f,o,!0,!0)):(r=Dr(f,p,!1,!0),n=Dr(o,f,!0,!1)),null!=u?[r,n,fo(h,f)]:[r,n]},M={a:O,b:D,bias:S,preluActivationWeights:E},C={transposeA:s,transposeB:i,activation:c,leakyreluAlpha:f};if(null==u){var F=aa((function(e,t,r){var n=Zt.runKernel(Pe,M,C);return r([e,t,n]),{value:_r(n,I),gradFunc:A}}));return F(O,D)}var V=aa((function(e,t,r,n){var a=Zt.runKernel(Pe,M,C);return n([e,t,a,r]),{value:_r(a,I),gradFunc:A}}));return V(O,D,S)}})};var wo=rr({hammingWindow_:function(e){return po(e,.54,.46)}});var ko=rr({hannWindow_:function(e){return po(e,.5,.5)}});var To=rr({frame_:function(e,t,r,n,a){void 0===n&&(n=!1),void 0===a&&(a=0);for(var s=0,o=[];s+t<=e.size;)o.push(Cr(e,s,t)),s+=r;if(n)for(;s<e.size;){var i=s+t-e.size,u=Or([Cr(e,s,t-i),Hr([i],a)]);o.push(u),s+=r}return 0===o.length?Gs([],[0,t]):_r(Or(o),[o.length,t])}});var _o=rr({stft_:function(e,t,r,n,a){void 0===a&&(a=ko),null==n&&(n=uo(t));var s=To(e,t,r),o=Ar(s,a(t));return Ls(o,n)}});var So=rr({cropAndResize_:function(e,t,r,n,a,s){void 0===a&&(a="bilinear"),void 0===s&&(s=0);var o=$t(e,"image","cropAndResize"),i=$t(t,"boxes","cropAndResize","float32"),u=$t(r,"boxInd","cropAndResize","int32"),p=i.shape[0];ae(4===o.rank,(function(){return"Error in cropAndResize: image must be rank 4,but got rank "+o.rank+"."})),ae(2===i.rank&&4===i.shape[1],(function(){return"Error in cropAndResize: boxes must be have size ["+p+",4] but had shape "+i.shape+"."})),ae(1===u.rank&&u.shape[0]===p,(function(){return"Error in cropAndResize: boxInd must be have size ["+p+"] but had shape "+i.shape+"."})),ae(2===n.length,(function(){return"Error in cropAndResize: cropSize must be of length 2, but got length "+n.length+"."})),ae(n[0]>=1&&n[1]>=1,(function(){return"cropSize must be atleast [1,1], but was "+n})),ae("bilinear"===a||"nearest"===a,(function(){return"method must be bilinear or nearest, but was "+a}));var l={image:o,boxes:i,boxInd:u},c={method:a,extrapolationValue:s,cropSize:n};return Zt.runKernel("CropAndResize",l,c)}});var Eo=rr({flipLeftRight_:function(e){var t=$t(e,"image","flipLeftRight","float32");ae(4===t.rank,(function(){return"Error in flipLeftRight: image must be rank 4,but got rank "+t.rank+"."}));var r={image:t};return Zt.runKernel("FlipLeftRight",r,{})}});var Io=rr({grayscaleToRGB_:function(e){var t=$t(e,"image","grayscaleToRGB"),r=t.rank-1,n=t.shape[r];ae(t.rank>=2,(function(){return"Error in grayscaleToRGB: images must be at least rank 2, but got rank "+t.rank+"."})),ae(1===n,(function(){return"Error in grayscaleToRGB: last dimension of a grayscale image should be size 1, but got size "+n+"."}));var a=new Array(t.rank);return a.fill(1,0,r),a[r]=3,qn(t,a)}});var Oo=rr({rotateWithOffset_:function(e,t,r,n){void 0===r&&(r=0),void 0===n&&(n=.5);var a=$t(e,"image","rotateWithOffset","float32");ae(4===a.rank,(function(){return"Error in rotateWithOffset: image must be rank 4,but got rank "+a.rank+"."}));var s={image:a},o={radians:t,fillValue:r,center:n};return Zt.runKernel("RotateWithOffset",s,o)}});function Do(e,t,r,n,a,s){null==n&&(n=.5),null==a&&(a=Number.NEGATIVE_INFINITY),null==s&&(s=0);var o=e.shape[0];return r=Math.min(r,o),ae(0<=n&&n<=1,(function(){return"iouThreshold must be in [0, 1], but was '"+n+"'"})),ae(2===e.rank,(function(){return"boxes must be a 2D tensor, but was of rank '"+e.rank+"'"})),ae(4===e.shape[1],(function(){return"boxes must have 4 columns, but 2nd dimension was "+e.shape[1]})),ae(1===t.rank,(function(){return"scores must be a 1D tensor"})),ae(t.shape[0]===o,(function(){return"scores has incompatible shape with boxes. Expected "+o+", but was "+t.shape[0]})),ae(0<=s&&s<=1,(function(){return"softNmsSigma must be in [0, 1], but was '"+s+"'"})),{maxOutputSize:r,iouThreshold:n,scoreThreshold:a,softNmsSigma:s}}var Ao=rr({nonMaxSuppression_:function(e,t,r,n,a){void 0===n&&(n=.5),void 0===a&&(a=Number.NEGATIVE_INFINITY);var s=$t(e,"boxes","nonMaxSuppression","float32"),o=$t(t,"scores","nonMaxSuppression","float32"),i=Do(s,o,r,n,a),u={maxOutputSize:r=i.maxOutputSize,iouThreshold:n=i.iouThreshold,scoreThreshold:a=i.scoreThreshold};return Zt.runKernel("NonMaxSuppressionV3",{boxes:s,scores:o},u)}});function Mo(e,t,r){var n=function(e,t,r){return function(e,t,r){var n=0,a=e.length,s=0,o=!1;for(;n<a;){var i=r(t,e[s=n+(a-n>>>1)]);i>0?n=s+1:(a=s,o=!i)}return o?n:-n-1}(e,t,r||Co)}(e,t,r),a=n<0?-(n+1):n;e.splice(a,0,t)}function Co(e,t){return e>t?1:e<t?-1:0}function Fo(e,t,r,n,a,s,o,i,u){void 0===o&&(o=!1),void 0===i&&(i=!1),void 0===u&&(u=!1);for(var p=[],l=0;l<t.length;l++)t[l]>a&&p.push({score:t[l],boxIndex:l,suppressBeginIndex:0});p.sort(zo);for(var d=s>0?-.5/s:0,h=[],f=[];h.length<r&&p.length>0;){var m=p.pop(),y=m.score,g=m.boxIndex,v=m.suppressBeginIndex;if(y<a)break;for(var b=!1,x=h.length-1;x>=v;--x){var N=Vo(e,g,h[x]);if(N>=n){b=!0;break}if(m.score=m.score*Ro(n,d,N),m.score<=a)break}m.suppressBeginIndex=h.length,b||(m.score===y?(h.push(g),f.push(m.score)):m.score>a&&Mo(p,m,zo))}var w=h.length,k=r-w;i&&k>0&&(h.push.apply(h,c(new Array(k).fill(0))),f.push.apply(f,c(new Array(k).fill(0))));var T={selectedIndices:h};return o&&(T.selectedScores=f),u&&(T.validOutputs=w),T}function Vo(e,t,r){var n=e.subarray(4*t,4*t+4),a=e.subarray(4*r,4*r+4),s=Math.min(n[0],n[2]),o=Math.min(n[1],n[3]),i=Math.max(n[0],n[2]),u=Math.max(n[1],n[3]),p=Math.min(a[0],a[2]),l=Math.min(a[1],a[3]),c=Math.max(a[0],a[2]),d=Math.max(a[1],a[3]),h=(i-s)*(u-o),f=(c-p)*(d-l);if(h<=0||f<=0)return 0;var m=Math.max(s,p),y=Math.max(o,l),g=Math.min(i,c),v=Math.min(u,d),b=Math.max(g-m,0)*Math.max(v-y,0);return b/(h+f-b)}function Ro(e,t,r){var n=Math.exp(t*r*r);return r<=e?n:0}function zo(e,t){return e.score-t.score||e.score===t.score&&t.boxIndex-e.boxIndex}var Lo=function(e,t,r,n,a){return void 0===n&&(n=.5),void 0===a&&(a=Number.NEGATIVE_INFINITY),i(this,void 0,void 0,(function(){var s,o,i,p,l,c,d;return u(this,(function(u){switch(u.label){case 0:return s=$t(e,"boxes","nonMaxSuppressionAsync"),o=$t(t,"scores","nonMaxSuppressionAsync"),i=Do(s,o,r,n,a),r=i.maxOutputSize,n=i.iouThreshold,a=i.scoreThreshold,[4,Promise.all([s.data(),o.data()])];case 1:return p=u.sent(),l=p[0],c=p[1],d=function(e,t,r,n,a){return Fo(e,t,r,n,a,0)}(l,c,r,n,a).selectedIndices,s!==e&&s.dispose(),o!==t&&o.dispose(),[2,Hs(d,"int32")]}}))}))};var Po=rr({nonMaxSuppressionWithScore_:function(e,t,r,n,a,s){void 0===n&&(n=.5),void 0===a&&(a=Number.NEGATIVE_INFINITY),void 0===s&&(s=0);var o=$t(e,"boxes","nonMaxSuppression"),i=$t(t,"scores","nonMaxSuppression"),u=Do(o,i,r,n,a,s),p={boxes:o,scores:i},l={maxOutputSize:r=u.maxOutputSize,iouThreshold:n=u.iouThreshold,scoreThreshold:a=u.scoreThreshold,softNmsSigma:s=u.softNmsSigma},c=Zt.runKernel("NonMaxSuppressionV5",p,l);return{selectedIndices:c[0],selectedScores:c[1]}}});var Bo=function(e,t,r,n,a,s){return void 0===n&&(n=.5),void 0===a&&(a=Number.NEGATIVE_INFINITY),void 0===s&&(s=0),i(this,void 0,void 0,(function(){var o,i,p,l,c,d,h,f,m;return u(this,(function(u){switch(u.label){case 0:return o=$t(e,"boxes","nonMaxSuppressionAsync"),i=$t(t,"scores","nonMaxSuppressionAsync"),p=Do(o,i,r,n,a,s),r=p.maxOutputSize,n=p.iouThreshold,a=p.scoreThreshold,s=p.softNmsSigma,[4,Promise.all([o.data(),i.data()])];case 1:return l=u.sent(),c=l[0],d=l[1],h=function(e,t,r,n,a,s){return Fo(e,t,r,n,a,s,!0)}(c,d,r,n,a,s),f=h.selectedIndices,m=h.selectedScores,o!==e&&o.dispose(),i!==t&&i.dispose(),[2,{selectedIndices:Hs(f,"int32"),selectedScores:Hs(m)}]}}))}))};var Ko=rr({nonMaxSuppressionPadded_:function(e,t,r,n,a,s){void 0===n&&(n=.5),void 0===a&&(a=Number.NEGATIVE_INFINITY),void 0===s&&(s=!1);var o=$t(e,"boxes","nonMaxSuppression"),i=$t(t,"scores","nonMaxSuppression"),u=Do(o,i,r,n,a,null),p={boxes:o,scores:i},l={maxOutputSize:u.maxOutputSize,iouThreshold:u.iouThreshold,scoreThreshold:u.scoreThreshold,padToMaxOutputSize:s},c=Zt.runKernel("NonMaxSuppressionV4",p,l);return{selectedIndices:c[0],validOutputs:c[1]}}});var qo=function(e,t,r,n,a,s){return void 0===n&&(n=.5),void 0===a&&(a=Number.NEGATIVE_INFINITY),void 0===s&&(s=!1),i(this,void 0,void 0,(function(){var o,i,p,c,d,h,f,m,y,g,v,b;return u(this,(function(u){switch(u.label){case 0:return o=$t(e,"boxes","nonMaxSuppressionAsync"),i=$t(t,"scores","nonMaxSuppressionAsync"),p=Do(o,i,r,n,a,null),c=p.maxOutputSize,d=p.iouThreshold,h=p.scoreThreshold,[4,Promise.all([o.data(),i.data()])];case 1:return f=l.apply(void 0,[u.sent(),2]),m=f[0],y=f[1],g=function(e,t,r,n,a,s){return Fo(e,t,r,n,a,0,!1,s,!0)}(m,y,c,d,h,s),v=g.selectedIndices,b=g.validOutputs,o!==e&&o.dispose(),i!==t&&i.dispose(),[2,{selectedIndices:Hs(v,"int32"),validOutputs:Mn(b,"int32")}]}}))}))};var jo=rr({resizeBilinear_:function(e,t,r,n){void 0===r&&(r=!1),void 0===n&&(n=!1);var a=$t(e,"images","resizeBilinear");ae(3===a.rank||4===a.rank,(function(){return"Error in resizeBilinear: x must be rank 3 or 4, but got rank "+a.rank+"."})),ae(2===t.length,(function(){return"Error in resizeBilinear: new shape must 2D, but got shape "+t+"."})),ae(!1===n||!1===r,(function(){return"Error in resizeBilinear: If halfPixelCenters is true, alignCorners must be false."}));var s=a,o=!1;3===a.rank&&(o=!0,s=_r(a,[1,a.shape[0],a.shape[1],a.shape[2]])),l(t,0);var i={images:s},u={alignCorners:r,halfPixelCenters:n,size:t},p=Zt.runKernel("ResizeBilinear",i,u);return o?_r(p,[p.shape[1],p.shape[2],p.shape[3]]):p}});var Uo=rr({resizeNearestNeighbor_:function(e,t,r,n){void 0===r&&(r=!1),void 0===n&&(n=!1);var a=$t(e,"images","resizeNearestNeighbor");ae(3===a.rank||4===a.rank,(function(){return"Error in resizeNearestNeighbor: x must be rank 3 or 4, but got rank "+a.rank+"."})),ae(2===t.length,(function(){return"Error in resizeNearestNeighbor: new shape must 2D, but got shape "+t+"."})),ae("float32"===a.dtype||"int32"===a.dtype,(function(){return"`images` must have `int32` or `float32` as dtype"})),ae(!1===n||!1===r,(function(){return"Error in resizeNearestNeighbor: If halfPixelCenters is true, alignCorners must be false."}));var s=a,o=!1;3===a.rank&&(o=!0,s=_r(a,[1,a.shape[0],a.shape[1],a.shape[2]])),l(t,0);var i={images:s},u={alignCorners:r,halfPixelCenters:n,size:t},p=Zt.runKernel("ResizeNearestNeighbor",i,u);return o?_r(p,[p.shape[1],p.shape[2],p.shape[3]]):p}});var Wo=rr({threshold_:function(e,t,r,n){var a;void 0===t&&(t="binary"),void 0===r&&(r=!1),void 0===n&&(n=.5);var s,o,i,u,p=$t(e,"image","threshold"),c=p.shape[0]*p.shape[1],d=Ar(Hs([n]),255);if(ae(3===p.rank,(function(){return"Error in threshold: image must be rank 3,but got rank "+p.rank+"."})),ae(3===p.shape[2]||1===p.shape[2],(function(){return"Error in threshold: image color channel must be equal to 3 or 1but got "+p.shape[2]+"."})),ae("int32"===p.dtype||"float32"===p.dtype,(function(){return"Error in dtype: image dtype must be int32 or float32,but got dtype "+p.dtype+"."})),ae("otsu"===t||"binary"===t,(function(){return"Method must be binary or otsu, but was "+t})),3===p.shape[2]){s=(a=l(zs(p,[1,1,1],-1),3))[0],o=a[1],i=a[2];var h=Ar(s,.2989),f=Ar(o,.587),m=Ar(i,.114);u=or(or(h,f),m)}else u=e;"otsu"===t&&(d=function(e,t){for(var r,n,a,s,o,i,u=Hs([-1]),p=Hs([0]),l=Hs([0]),c=0;c<e.size-1;c++){r=Cr(e,0,c+1),n=Cr(e,c+1),o=gn(Vn(r),t),i=gn(Vn(n),t);var d=Vn(Ar(r,cs(0,r.size)));a=gn(d,Vn(r));var h=Hr(n.shape,r.size),f=or(cs(0,n.size),h),m=Ar(n,f);s=gn(Vn(m),Vn(n));var y=ua(a,s),g=ua(a,s),v=Ar(o,i);l=Ar(Ar(v,y),g);var b=Hn(l,p);p=xn(b,l,p),u=xn(b,Hs([c]),u)}return u}(Kr(gr(Ns(u),"int32"),Ws([]),256),c));var y=r?ea(u,d):Hn(u,d);return gr(Ar(y,255),"int32")}});var Ho=rr({transform_:function(e,t,r,n,a,s){void 0===r&&(r="nearest"),void 0===n&&(n="constant"),void 0===a&&(a=0);var o=$t(e,"image","transform","float32"),i=$t(t,"transforms","transform","float32");ae(4===o.rank,(function(){return"Error in transform: image must be rank 4,but got rank "+o.rank+"."})),ae(2===i.rank&&(i.shape[0]===o.shape[0]||1===i.shape[0])&&8===i.shape[1],(function(){return"Error in transform: Input transform should be batch x 8 or 1 x 8"})),ae(null==s||2===s.length,(function(){return"Error in transform: outputShape must be [height, width] or null, but got "+s+"."}));var u={image:o,transforms:i},p={interpolation:r,fillMode:n,fillValue:a,outputShape:s};return Zt.runKernel("Transform",u,p)}});var Go=rr({bandPart_:function(e,t,r){ae(t%1==0,(function(){return"bandPart(): numLower must be an integer, got "+t+"."})),ae(r%1==0,(function(){return"bandPart(): numUpper must be an integer, got "+r+"."}));var n=$t(e,"a","bandPart");ae(n.rank>=2,(function(){return"bandPart(): Rank must be at least 2, got "+n.rank+"."}));var a=n.shape,s=l(n.shape.slice(-2),2),o=s[0],i=s[1];if(!(t<=o))throw new Error("bandPart(): numLower ("+t+") must not be greater than the number of rows ("+o+").");if(!(r<=i))throw new Error("bandPart(): numUpper ("+r+") must not be greater than the number of columns ("+i+").");t<0&&(t=o),r<0&&(r=i);var u=_r(cs(0,o,1,"int32"),[-1,1]),p=cs(0,i,1,"int32"),c=ua(u,p),d=ca(ea(c,Mn(+t,"int32")),Gn(c,Mn(-r,"int32"))),h=wa([o,i],n.dtype);return _r(Ks(Js(_r(n,[-1,o,i])).map((function(e){return xn(d,e,h)}))),a)}});var Zo=rr({gramSchmidt_:function(e){var t;if(Array.isArray(e)){t=!1,ae(null!=e&&e.length>0,(function(){return"Gram-Schmidt process: input must not be null, undefined, or empty"}));for(var r=e[0].shape[0],n=function(t){ae(e[t].shape[0]===r,(function(){return"Gram-Schmidt: Non-unique lengths found in the input vectors: ("+e[t].shape[0]+" vs. "+r+")"}))},a=1;a<e.length;++a)n(a)}else t=!0,e=zs(e,e.shape[0],0).map((function(e){return Bs(e,[0])}));ae(e.length<=e[0].shape[0],(function(){return"Gram-Schmidt: Number of vectors ("+e.length+") exceeds number of dimensions ("+e[0].shape[0]+")."}));var s=[],o=e,i=function(e){s.push(Zt.tidy((function(){var t=o[e];if(e>0)for(var r=0;r<e;++r){var n=Ar(Vn(Ar(s[r],t)),s[r]);t=ua(t,n)}return gn(t,zn(t,"euclidean"))})))};for(a=0;a<e.length;++a)i(a);return t?Ks(s,0):s}});function Qo(e,t){return void 0===t&&(t=!1),Zt.tidy((function(){ae(2===e.shape.length,(function(){return"qr2d() requires a 2D Tensor, but got a "+e.shape.length+"D Tensor."}));for(var r=e.shape[0],n=e.shape[1],a=jn(r),s=Ir(e),o=Gs([[1]],[1,1]),i=Ir(o),u=r>=n?n:r,p=function(e){var t,u=s,p=i,c=a;t=l(Zt.tidy((function(){var t=Cr(s,[e,e],[r-e,1]),u=zn(t),p=Cr(s,[e,e],[1,1]),l=xn(Hn(p,0),Gs([[-1]]),Gs([[1]])),c=ua(p,Ar(l,u)),d=gn(t,c);i=1===d.shape[0]?Ir(o):Or([o,Cr(d,[1,0],[d.shape[0]-1,d.shape[1]])],0);var h=sa(gn(Dr(l,c),u)),f=Cr(s,[e,0],[r-e,n]),m=Ar(h,i),y=to(i);if(0===e)s=ua(f,Dr(m,Dr(y,f)));else{var g=ua(f,Dr(m,Dr(y,f)));s=Or([Cr(s,[0,0],[e,n]),g],0)}var v=to(m),b=Cr(a,[0,e],[r,a.shape[1]-e]);if(0===e)a=ua(b,Dr(Dr(b,i),v));else{var x=ua(b,Dr(Dr(b,i),v));a=Or([Cr(a,[0,0],[r,e]),x],1)}return[i,s,a]})),3),i=t[0],s=t[1],a=t[2],jt([u,p,c]).forEach((function(e){return e.dispose()}))},c=0;c<u;++c)p(c);return!t&&r>n&&(a=Cr(a,[0,0],[r,n]),s=Cr(s,[0,0],[n,n])),[a,s]}))}var Yo,Xo=rr({qr_:function(e,t){if(void 0===t&&(t=!1),ae(e.rank>=2,(function(){return"qr() requires input tensor to have a rank >= 2, but got rank "+e.rank})),2===e.rank)return Qo(e,t);var r=e.shape.slice(0,e.shape.length-2).reduce((function(e,t){return e*t})),n=Js(_r(e,[r,e.shape[e.shape.length-2],e.shape[e.shape.length-1]]),0),a=[],s=[];return n.forEach((function(e){var r=l(Qo(e,t),2),n=r[0],o=r[1];a.push(n),s.push(o)})),[_r(Ks(a,0),e.shape),_r(Ks(s,0),e.shape)]}});!function(e){e[e.NONE=0]="NONE",e[e.MEAN=1]="MEAN",e[e.SUM=2]="SUM",e[e.SUM_BY_NONZERO_WEIGHTS=3]="SUM_BY_NONZERO_WEIGHTS"}(Yo||(Yo={}));var Jo=rr({computeWeightedLoss_:function(e,t,r){void 0===r&&(r=Yo.SUM_BY_NONZERO_WEIGHTS);var n=$t(e,"losses","computeWeightedLoss"),a=null;null!=t&&(a=$t(t,"weights","computeWeightedLoss"));var s=null==a?n:Ar(n,a);if(r===Yo.NONE)return s;if(r===Yo.SUM)return Vn(s);if(r===Yo.MEAN){if(null==a)return Na(s);var o=n.size/a.size,i=gn(Vn(s),Vn(a));return o>1?gn(i,Mn(o)):i}if(r===Yo.SUM_BY_NONZERO_WEIGHTS){if(null==a)return gn(Vn(s),Mn(n.size));var u=Ar(a,ka(n.shape)),p=gr(Vn(Da(u,Mn(0))),"float32");return gn(Vn(s),p)}throw Error("Unknown reduction: "+r)}});var $o=rr({absoluteDifference_:function(e,t,r,n){void 0===n&&(n=Yo.SUM_BY_NONZERO_WEIGHTS);var a=$t(e,"labels","absoluteDifference"),s=$t(t,"predictions","absoluteDifference"),o=null;null!=r&&(o=$t(r,"weights","absoluteDifference")),se(a.shape,s.shape,"Error in absoluteDifference: ");var i=nr(ua(a,s));return Jo(i,o,n)}});var ei=rr({cosineDistance_:function(e,t,r,n,a){void 0===a&&(a=Yo.SUM_BY_NONZERO_WEIGHTS);var s=$t(e,"labels","cosineDistance"),o=$t(t,"predictions","cosineDistance"),i=null;null!=n&&(i=$t(n,"weights","cosineDistance")),se(s.shape,o.shape,"Error in cosineDistance: ");var u=Mn(1),p=ua(u,Vn(Ar(s,o),r,!0));return Jo(p,i,a)}});var ti=rr({hingeLoss_:function(e,t,r,n){void 0===n&&(n=Yo.SUM_BY_NONZERO_WEIGHTS);var a=$t(e,"labels","hingeLoss"),s=$t(t,"predictions","hingeLoss"),o=null;null!=r&&(o=$t(r,"weights","hingeLoss")),se(a.shape,s.shape,"Error in hingeLoss: ");var i=Mn(1);a=ua(Ar(Mn(2),a),i);var u=fs(ua(i,Ar(a,s)));return Jo(u,o,n)}});var ri=rr({huberLoss_:function(e,t,r,n,a){void 0===n&&(n=1),void 0===a&&(a=Yo.SUM_BY_NONZERO_WEIGHTS);var s=$t(e,"labels","huberLoss"),o=$t(t,"predictions","huberLoss"),i=null;null!=r&&(i=$t(r,"weights","huberLoss")),se(s.shape,o.shape,"Error in huberLoss: ");var u=Mn(n),p=nr(ua(o,s)),l=Ta(p,u),c=ua(p,l),d=or(Ar(Mn(.5),Fn(l)),Ar(u,c));return Jo(d,i,a)}});var ni=rr({logLoss_:function(e,t,r,n,a){void 0===n&&(n=1e-7),void 0===a&&(a=Yo.SUM_BY_NONZERO_WEIGHTS);var s=$t(e,"labels","logLoss"),o=$t(t,"predictions","logLoss"),i=null;null!=r&&(i=$t(r,"weights","logLoss")),se(s.shape,o.shape,"Error in logLoss: ");var u=Mn(1),p=Mn(n),l=sa(Ar(s,ra(or(o,p)))),c=Ar(ua(u,s),ra(or(ua(u,o),p))),d=ua(l,c);return Jo(d,i,a)}});var ai=rr({meanSquaredError_:function(e,t,r,n){void 0===n&&(n=Yo.SUM_BY_NONZERO_WEIGHTS);var a=$t(e,"labels","meanSquaredError"),s=$t(t,"predictions","meanSquaredError"),o=null;null!=r&&(o=$t(r,"weights","meanSquaredError")),se(a.shape,s.shape,"Error in meanSquaredError: ");var i=Ps(a,s);return Jo(i,o,n)}});var si=rr({sigmoidCrossEntropy_:function(e,t,r,n,a){void 0===n&&(n=0),void 0===a&&(a=Yo.SUM_BY_NONZERO_WEIGHTS);var s=$t(e,"multiClassLabels","sigmoidCrossEntropy"),o=$t(t,"logits","sigmoidCrossEntropy"),i=null;if(null!=r&&(i=$t(r,"weights","sigmoidCrossEntropy")),se(s.shape,o.shape,"Error in sigmoidCrossEntropy: "),n>0){var u=Mn(n),p=Mn(1),l=Mn(.5);s=or(Ar(s,ua(p,u)),Ar(l,u))}var c=function(e,t){var r=$t(e,"labels","sigmoidCrossEntropyWithLogits"),n=$t(t,"logits","sigmoidCrossEntropyWithLogits");se(r.shape,n.shape,"Error in sigmoidCrossEntropyWithLogits: ");var a=fs(n),s=Ar(n,r),o=na(Pn(sa(nr(n))));return or(ua(a,s),o)}(s,o);return Jo(c,i,a)}});var oi=rr({softmaxCrossEntropy_:function(e,t,r,n,a){void 0===n&&(n=0),void 0===a&&(a=Yo.SUM_BY_NONZERO_WEIGHTS);var s=$t(e,"onehotLabels","softmaxCrossEntropy"),o=$t(t,"logits","softmaxCrossEntropy"),i=null;if(null!=r&&(i=$t(r,"weights","softmaxCrossEntropy")),se(s.shape,o.shape,"Error in softmaxCrossEntropy: "),n>0){var u=Mn(n),p=Mn(1),c=Mn(s.shape[1]);s=or(Ar(s,ua(p,u)),gn(u,c))}var d=function(e,t,r){if(void 0===r&&(r=-1),-1===r&&(r=t.rank-1),r!==t.rank-1)throw Error("Softmax cross entropy along a non-last dimension is not yet supported. Labels / logits was rank "+t.rank+" and dim was "+r);var n=aa((function(e,t,n){var a=la(t,[r],!0),s=ua(gr(t,"float32"),a);n([e,s]);var o=sa(Ar(s,e));return{value:Vn(o,[r]),gradFunc:function(e,t){var n=l(t,2),a=n[0],s=n[1],o=En(e.shape,[r]);return[Ar(_r(e,o),ua(gr(a,"float32"),Pn(s))),Ar(_r(e,o),ua(Pn(s),gr(a,"float32")))]}}}));return n(e,t)}(s,o);return Jo(d,i,a)}});var ii=rr({sparseFillEmptyRows_:function(e,t,r,n){var a=$t(e,"indices","sparseFillEmptyRows","int32"),s=$t(t,"values","sparseFillEmptyRows"),o=$t(r,"denseShape","sparseFillEmptyRows","int32"),i=$t(n,"defaultValue","sparseFillEmptyRows",s.dtype);if(2!==a.rank)throw new Error("Indices should be Tensor2D but received shape\n        "+a.shape);if(1!==s.rank)throw new Error("Values should be Tensor1D but received shape "+s.shape);if(1!==o.rank)throw new Error("Dense shape should be Tensor1D but received shape "+o.shape);if(0!==i.rank)throw new Error("Default value should be a scalar but received shape "+i.shape);var u={indices:a,values:s,denseShape:o,defaultValue:i},p=Zt.runKernel("SparseFillEmptyRows",u);return{outputIndices:p[0],outputValues:p[1],emptyRowIndicator:p[2],reverseIndexMap:p[3]}}});var ui=rr({sparseReshape_:function(e,t,r){var n=$t(e,"inputIndices","sparseReshape","int32"),a=$t(t,"inputShape","sparseReshape","int32"),s=$t(r,"newShape","sparseReshape","int32");if(2!==n.rank)throw new Error("Input indices should be Tensor2D but received shape\n        "+n.shape);if(1!==a.rank)throw new Error("Input shape should be Tensor1D but received shape "+a.shape);if(1!==s.rank)throw new Error("New shape should be Tensor1D but received shape "+s.shape);var o={inputIndices:n,inputShape:a,newShape:s},i=Zt.runKernel("SparseReshape",o);return{outputIndices:i[0],outputShape:i[1]}}});var pi=rr({sparseSegmentMean_:function(e,t,r){var n=$t(e,"data","sparseSegmentMean"),a=$t(t,"indices","sparseSegmentMean","int32"),s=$t(r,"segmentIds","sparseSegmentMean","int32");if(n.rank<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==a.rank)throw new Error("Indices should be Tensor1D but received shape\n          "+a.shape);if(1!==s.rank)throw new Error("Segment ids should be Tensor1D but received shape\n          "+s.shape);var o={data:n,indices:a,segmentIds:s};return Zt.runKernel("SparseSegmentMean",o)}});var li=rr({sparseSegmentSum_:function(e,t,r){var n=$t(e,"data","sparseSegmentSum"),a=$t(t,"indices","sparseSegmentSum","int32"),s=$t(r,"segmentIds","sparseSegmentSum","int32");if(n.rank<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==a.rank)throw new Error("Indices should be Tensor1D but received shape\n         "+a.shape);if(1!==s.rank)throw new Error("Segment ids should be Tensor1D but received shape\n         "+s.shape);var o={data:n,indices:a,segmentIds:s};return Zt.runKernel("SparseSegmentSum",o)}});var ci=rr({stringNGrams_:function(e,t,r,n,a,s,o,i){var u=$t(e,"data","stringNGrams","string");if("string"!==u.dtype)throw new Error("Data must be of datatype string");if(1!==u.shape.length)throw new Error("Data must be a vector, saw: "+u.shape);var p=$t(t,"dataSplits","stringNGrams");if("int32"!==p.dtype)throw new Error("Data splits must be of datatype int32");var l={separator:r,nGramWidths:n,leftPad:a,rightPad:s,padWidth:o,preserveShortSequences:i},c={data:u,dataSplits:p},d=Zt.runKernel("StringNGrams",c,l);return{nGrams:d[0],nGramsSplits:d[1]}}});var di=rr({stringSplit_:function(e,t,r){void 0===r&&(r=!0);var n=$t(e,"input","stringSplit","string"),a=$t(t,"delimiter","stringSplit","string");if(1!==n.rank)throw new Error("Input should be Tensor1D but received shape "+n.shape);if(0!==a.rank)throw new Error("Delimiter should be a scalar but received shape "+a.shape);var s={skipEmpty:r},o={input:n,delimiter:a},i=Zt.runKernel("StringSplit",o,s);return{indices:i[0],values:i[1],shape:i[2]}}});var hi=rr({stringToHashBucketFast_:function(e,t){var r=$t(e,"input","stringToHashBucketFast","string"),n={numBuckets:t};if(t<=0)throw new Error("Number of buckets must be at least 1");var a={input:r};return Zt.runKernel("StringToHashBucketFast",a,n)}}),fi={__proto__:null,abs:nr,acos:ar,acosh:sr,add:or,addN:ir,all:ur,any:pr,argMax:lr,argMin:cr,asin:dr,asinh:hr,atan:fr,atan2:mr,atanh:yr,avgPool:Sr,avgPool3d:Er,basicLSTMCell:Vr,batchToSpaceND:Rr,batchNorm:zr,batchNorm2d:Lr,batchNorm3d:Pr,batchNorm4d:Br,bincount:Kr,broadcastArgs:qr,broadcastTo:jr,buffer:Ur,cast:gr,ceil:Wr,clipByValue:Gr,clone:Ir,complex:Zr,concat:Or,concat1d:Qr,concat2d:Yr,concat3d:Xr,concat4d:Jr,conv1d:en,conv2d:$r,conv2dTranspose:rn,conv3d:nn,conv3dTranspose:sn,cos:on,cosh:un,cumprod:pn,cumsum:ln,denseBincount:cn,depthToSpace:dn,depthwiseConv2d:hn,diag:fn,dilation2d:mn,div:gn,divNoNan:wn,dot:kn,einsum:Tn,elu:_n,equal:bn,erf:Sn,euclideanNorm:Ln,exp:Pn,expandDims:Bn,expm1:Kn,eye:jn,fill:Hr,floor:Un,floorDiv:yn,gather:Wn,greater:Hn,greaterEqual:Gn,imag:Zn,isFinite:Qn,isInf:Yn,isNaN:Xn,leakyRelu:Jn,less:$n,lessEqual:ea,linspace:function(e,t,r){if(r<=0)throw new Error("The number of values should be positive.");var n={start:e,stop:t,num:r};return Zt.runKernel("LinSpace",{},n)},localResponseNormalization:ta,log:ra,log1p:na,logSigmoid:ia,logSoftmax:pa,logSumExp:la,logicalAnd:ca,logicalNot:da,logicalOr:ha,logicalXor:fa,lowerBound:function(e,t){return ya(e,t,"left")},matMul:Dr,max:In,maxPool:ga,maxPool3d:va,maxPoolWithArgmax:ba,maximum:xa,mean:Na,meshgrid:function(e,t,r){var n=(void 0===r?{}:r).indexing,a=void 0===n?"xy":n;if("xy"!==a&&"ij"!==a)throw new TypeError(a+" is not a valid third argument to meshgrid");if(void 0===e)return[];var s=$t(e,"x","meshgrid",e instanceof Ft?e.dtype:"float32");if(void 0===t)return[s];var o=$t(t,"y","meshgrid",t instanceof Ft?t.dtype:"float32"),i=ue(s.shape),u=ue(o.shape);return"xy"===a?(s=_r(s,[1,-1]),o=_r(o,[-1,1]),[Dr(ka([u,1],s.dtype),s),Dr(o,ka([1,i],o.dtype))]):(s=_r(s,[-1,1]),o=_r(o,[1,-1]),[Dr(s,ka([1,u],s.dtype)),Dr(ka([i,1],o.dtype),o)])},min:On,minimum:Ta,mirrorPad:_a,mod:Sa,moments:Ea,mul:Ar,multiRNNCell:Ia,multinomial:Oa,neg:sa,notEqual:Da,oneHot:Aa,ones:ka,onesLike:Ma,outerProduct:Ca,pad:Fa,pad1d:Va,pad2d:Ra,pad3d:za,pad4d:La,pool:Ba,pow:Dn,prelu:Ka,print:function(e,t){void 0===t&&(t=!1),console.log(e.toString(t))},prod:qa,raggedGather:ja,raggedTensorToTensor:Ua,rand:Wa,randomGamma:is,randomNormal:us,randomStandardNormal:ps,randomUniform:ls,range:cs,real:ds,reciprocal:hs,relu:fs,relu6:ms,reshape:_r,reverse:ys,reverse1d:gs,reverse2d:vs,reverse3d:bs,reverse4d:xs,round:Ns,rsqrt:ws,scalar:Mn,selu:ks,separableConv2d:Ts,setdiff1dAsync:_s,sigmoid:Mr,sign:Ss,sin:Es,sinh:Is,slice:Cr,slice1d:Os,slice2d:Ds,slice3d:As,slice4d:Ms,softmax:Cs,softplus:oa,spaceToBatchND:Pa,fft:Fs,ifft:Vs,irfft:Rs,rfft:Ls,split:zs,sqrt:Cn,square:Fn,squaredDifference:Ps,squeeze:Bs,stack:Ks,step:qs,stridedSlice:js,sub:ua,sum:Vn,tan:Us,tanh:Fr,tensor:Ws,tensor1d:Hs,tensor2d:Gs,tensor3d:function(e,t,r){if(oe(e),null!=t&&3!==t.length)throw new Error("tensor3d() requires shape to have three numbers");var n=Yt(e,r);if(3!==n.length&&1!==n.length)throw new Error("tensor3d() requires values to be number[][][] or flat/TypedArray");if(1===n.length&&null==t)throw new Error("tensor3d() requires shape to be provided when `values` are a flat array");return An(e,t,n,r)},tensor4d:function(e,t,r){if(oe(e),null!=t&&4!==t.length)throw new Error("tensor4d() requires shape to have four numbers");var n=Yt(e,r);if(4!==n.length&&1!==n.length)throw new Error("tensor4d() requires values to be number[][][][] or flat/TypedArray");if(1===n.length&&null==t)throw new Error("tensor4d() requires shape to be provided when `values` are a flat array");return An(e,t,n,r)},tensor5d:function(e,t,r){if(oe(e),null!=t&&5!==t.length)throw new Error("tensor5d() requires shape to have five numbers");var n=Yt(e,r);if(5!==n.length&&1!==n.length)throw new Error("tensor5d() requires values to be number[][][][][] or flat/TypedArray");if(1===n.length&&null==t)throw new Error("tensor5d() requires shape to be provided when `values` are a flat array");return An(e,t,n,r)},tensor6d:function(e,t,r){if(oe(e),null!=t&&6!==t.length)throw new Error("tensor6d() requires shape to have six numbers");var n=Yt(e,r);if(6!==n.length&&1!==n.length)throw new Error("tensor6d() requires values to be number[][][][][][] or flat/TypedArray");if(1===n.length&&null==t)throw new Error("tensor6d() requires shape to be provided when `values` are a flat array");return An(e,t=t||n,n,r)},tile:qn,topk:Zs,truncatedNormal:Qs,unique:Ys,unsortedSegmentSum:Xs,unstack:Js,upperBound:function(e,t){return ya(e,t,"right")},variable:function(e,t,r,n){return void 0===t&&(t=!0),Zt.makeVariable(e,t,r,n)},where:xn,whereAsync:$s,zeros:wa,zerosLike:Nn,op:rr,OP_SCOPE_SUFFIX:tr,booleanMaskAsync:eo,transpose:to,norm:zn,movingAverage:ro,scatterND:ao,searchSorted:ya,sparseToDense:so,gatherND:oo,dropout:io,enclosingPowerOfTwo:uo,cosineWindow:po,inTopKAsync:lo,image:{flipLeftRight:Eo,grayscaleToRGB:Io,resizeNearestNeighbor:Uo,resizeBilinear:jo,rotateWithOffset:Oo,cropAndResize:So,nonMaxSuppression:Ao,nonMaxSuppressionAsync:Lo,nonMaxSuppressionWithScore:Po,nonMaxSuppressionWithScoreAsync:Bo,nonMaxSuppressionPadded:Ko,nonMaxSuppressionPaddedAsync:qo,threshold:Wo,transform:Ho},linalg:{bandPart:Go,gramSchmidt:Zo,qr:Xo},losses:{absoluteDifference:$o,computeWeightedLoss:Jo,cosineDistance:ei,hingeLoss:ti,huberLoss:ri,logLoss:ni,meanSquaredError:ai,sigmoidCrossEntropy:si,softmaxCrossEntropy:oi},spectral:{fft:Fs,ifft:Vs,rfft:Ls,irfft:Rs},fused:No,signal:{hammingWindow:wo,hannWindow:ko,frame:To,stft:_o},sparse:{sparseFillEmptyRows:ii,sparseReshape:ui,sparseSegmentMean:pi,sparseSegmentSum:li},string:{stringNGrams:ci,stringSplit:di,stringToHashBucketFast:hi}};function mi(e,r,n){if(void 0===n&&(n=""),"number"!=typeof e&&"number"!=typeof r){t.util.assert(e.length===r.length,(function(){return n+" Shapes "+e+" and "+r+" must match"}));for(var a=0;a<e.length;a++){var s=e[a],o=r[a];t.util.assert(s<0||o<0||s===o,(function(){return n+" Shapes "+e+" and "+r+" must match"}))}}}function yi(e){return"number"!=typeof e&&!e.some((function(e){return e<0}))}function gi(e,t,r){var n=vi(e,r),a=!yi(n);if(a&&0===t.length)throw new Error("Tried to calculate elements of an empty list with non-fully-defined elementShape: "+n);if(a&&t.forEach((function(e){n=vi(e.shape,n)})),!yi(n))throw new Error("Non-fully-defined elementShape: "+n);return n}function vi(e,t){if("number"==typeof e)return t;if("number"==typeof t)return e;if(e.length!==t.length)throw new Error("Incompatible ranks during merge: "+e+" vs. "+t);for(var r=[],n=0;n<e.length;++n){var a=e[n],s=t[n];if(a>=0&&s>=0&&a!==s)throw new Error("Incompatible shape during merge: "+e+" vs. "+t);r[n]=a>=0?a:s}return r}var bi=function(){function e(e,r,n,a,s,o,i){this.name=e,this.dtype=r,this.maxSize=n,this.elementShape=a,this.identicalElementShapes=s,this.dynamicSize=o,this.clearAfterRead=i,this.tensors=[],this.closed_=!1,this.idTensor=t.scalar(0),t.keep(this.idTensor)}return Object.defineProperty(e.prototype,"id",{get:function(){return this.idTensor.id},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"closed",{get:function(){return this.closed_},enumerable:!0,configurable:!0}),e.prototype.clearAndClose=function(e){this.tensors.forEach((function(t){null!=e&&e.has(t.tensor.id)||t.tensor.dispose()})),this.tensors=[],this.closed_=!0,this.idTensor.dispose()},e.prototype.size=function(){return this.tensors.length},e.prototype.read=function(e){if(this.closed_)throw new Error("TensorArray "+this.name+" has already been closed.");if(e<0||e>=this.size())throw new Error("Tried to read from index "+e+", but array size is: "+this.size());var t=this.tensors[e];if(t.cleared)throw new Error("TensorArray "+this.name+": Could not read index "+e+" twice because it was cleared after a previous read (perhaps try setting clear_after_read = false?).");return this.clearAfterRead&&(t.cleared=!0),t.read=!0,t.tensor},e.prototype.readMany=function(e){var t=this;return e.map((function(e){return t.read(e)}))},e.prototype.write=function(e,r){if(this.closed_)throw new Error("TensorArray "+this.name+" has already been closed.");if(e<0||!this.dynamicSize&&e>=this.maxSize)throw new Error("Tried to write to index "+e+", but array is not resizeable and size is: "+this.maxSize);var n=this.tensors[e]||{};if(r.dtype!==this.dtype)throw new Error("TensorArray "+this.name+": Could not write to TensorArray index "+e+",\n          because the value dtype is "+r.dtype+", but TensorArray dtype is "+this.dtype+".");if(0!==this.size()||null!=this.elementShape&&0!==this.elementShape.length||(this.elementShape=r.shape),mi(this.elementShape,r.shape,"TensorArray "+this.name+": Could not write to TensorArray index "+e+"."),n.read)throw new Error("TensorArray "+this.name+": Could not write to TensorArray index "+e+", because it has already been read.");if(n.written)throw new Error("TensorArray "+this.name+": Could not write to TensorArray index "+e+", because it has already been written.");n.tensor=r,t.keep(r),n.written=!0,this.tensors[e]=n},e.prototype.writeMany=function(e,t){var r=this;if(e.length!==t.length)throw new Error("TensorArray "+this.name+": could not write multiple tensors,because the index size: "+e.length+" is not the same as tensors size: "+t.length+".");e.forEach((function(e,n){return r.write(e,t[n])}))},e.prototype.gather=function(e,r){if(r&&r!==this.dtype)throw new Error("TensorArray dtype is "+this.dtype+" but gather requested dtype "+r);if(e)e=e.slice(0,this.size());else{e=[];for(var n=0;n<this.size();n++)e.push(n)}if(0===e.length)return t.tensor([],[0].concat(this.elementShape));var a=this.readMany(e);return mi(this.elementShape,a[0].shape,"TensorArray shape mismatch: "),t.stack(a,0)},e.prototype.concat=function(e){if(e&&e!==this.dtype)throw new Error("TensorArray dtype is "+this.dtype+" but concat requested dtype "+e);if(0===this.size())return t.tensor([],[0].concat(this.elementShape));for(var r=[],n=0;n<this.size();n++)r.push(n);var a=this.readMany(r);return mi(this.elementShape,a[0].shape,"TensorArray shape mismatch: tensor array shape ("+this.elementShape+") vs first tensor shape ("+a[0].shape+")"),t.concat(a,0)},e.prototype.scatter=function(e,r){if(r.dtype!==this.dtype)throw new Error("TensorArray dtype is "+this.dtype+" but tensor has dtype "+r.dtype);if(e.length!==r.shape[0])throw new Error("Expected len(indices) == tensor.shape[0], but saw: "+e.length+" vs. "+r.shape[0]);var n=Math.max.apply(Math,c(e));if(!this.dynamicSize&&n>=this.maxSize)throw new Error("Max index must be < array size ("+n+"  vs. "+this.maxSize+")");this.writeMany(e,t.unstack(r,0))},e.prototype.split=function(e,r){var n=this;if(r.dtype!==this.dtype)throw new Error("TensorArray dtype is "+this.dtype+" but tensor has dtype "+r.dtype);var a=0,s=e.map((function(e){return a+=e}));if(a!==r.shape[0])throw new Error("Expected sum of lengths to be equal to\n          tensor.shape[0], but sum of lengths is\n        "+a+", and tensor's shape is: "+r.shape);if(!this.dynamicSize&&e.length!==this.maxSize)throw new Error("TensorArray's size is not equal to the size of lengths ("+this.maxSize+" vs. "+e.length+"), and the TensorArray is not marked as dynamically resizeable");var o=0===a?0:r.size/a,i=[];t.tidy((function(){r=t.reshape(r,[1,a,o]);for(var u=0;u<e.length;++u){var p=[0,0===u?0:s[u-1],0],l=[1,e[u],o];i[u]=t.reshape(t.slice(r,p,l),n.elementShape)}return i}));for(var u=[],p=0;p<e.length;p++)u[p]=p;this.writeMany(u,i)},e}(),xi=function(){function e(e,r,n,a){void 0===a&&(a=-1),this.tensors=e,this.elementShape=r,this.elementDtype=n,null!=e&&e.forEach((function(e){if(n!==e.dtype)throw new Error("Invalid data types; op elements "+n+", but list elements "+e.dtype);mi(r,e.shape,"TensorList shape mismatch: "),t.keep(e)})),this.idTensor=t.scalar(0),this.maxNumElements=a,t.keep(this.idTensor)}return Object.defineProperty(e.prototype,"id",{get:function(){return this.idTensor.id},enumerable:!0,configurable:!0}),e.prototype.copy=function(){return new e(c(this.tensors),this.elementShape,this.elementDtype)},e.prototype.clearAndClose=function(e){this.tensors.forEach((function(t){null!=e&&e.has(t.id)||t.dispose()})),this.tensors.length=0,this.idTensor.dispose()},e.prototype.size=function(){return this.tensors.length},e.prototype.stack=function(e,r,n){var a=this;if(void 0===n&&(n=-1),r!==this.elementDtype)throw new Error("Invalid data types; op elements "+r+", but list elements "+this.elementDtype);if(-1!==n&&this.tensors.length!==n)throw new Error("Operation expected a list with "+n+" elements but got a list with "+this.tensors.length+" elements.");mi(e,this.elementShape,"TensorList shape mismatch: ");var s=gi(this.elementShape,this.tensors,e);return t.tidy((function(){var e=a.tensors.map((function(e){return t.reshape(e,s)}));return t.stack(e,0)}))},e.prototype.popBack=function(e,r){if(r!==this.elementDtype)throw new Error("Invalid data types; op elements "+r+", but list elements "+this.elementDtype);if(0===this.size())throw new Error("Trying to pop from an empty list.");var n=gi(this.elementShape,this.tensors,e),a=this.tensors.pop();return a.kept=!1,mi(a.shape,e,"TensorList shape mismatch: "),t.reshape(a,n)},e.prototype.pushBack=function(e){if(e.dtype!==this.elementDtype)throw new Error("Invalid data types; op elements "+e.dtype+", but list elements "+this.elementDtype);if(mi(e.shape,this.elementShape,"TensorList shape mismatch: "),this.maxNumElements===this.size())throw new Error("Trying to push element into a full list.");t.keep(e),this.tensors.push(e)},e.prototype.resize=function(t){if(t<0)throw new Error("TensorListResize expects size to be non-negative. Got: "+t);if(-1!==this.maxNumElements&&t>this.maxNumElements)throw new Error("TensorListResize input size "+t+" is greater maxNumElement "+this.maxNumElements+".");var r=new e([],this.elementShape,this.elementDtype,this.maxNumElements);r.tensors.length=t;for(var n=0;n<Math.min(this.tensors.length,t);++n)r.tensors[n]=this.tensors[n];return r},e.prototype.getItem=function(e,r,n){if(n!==this.elementDtype)throw new Error("Invalid data types; op elements "+n+", but list elements "+this.elementDtype);if(e<0||e>this.tensors.length)throw new Error("Trying to access element "+e+" in a list with "+this.tensors.length+" elements.");if(null==this.tensors[e])throw new Error("element at index "+e+" is null.");mi(this.tensors[e].shape,r,"TensorList shape mismatch: ");var a=gi(this.elementShape,this.tensors,r);return t.reshape(this.tensors[e],a)},e.prototype.setItem=function(e,r){if(r.dtype!==this.elementDtype)throw new Error("Invalid data types; op elements "+r.dtype+", but list elements "+this.elementDtype);if(e<0||-1!==this.maxNumElements&&e>=this.maxNumElements)throw new Error("Trying to set element "+e+" in a list with max "+this.maxNumElements+" elements.");mi(this.elementShape,r.shape,"TensorList shape mismatch: "),t.keep(r),null!=this.tensors[e]&&(this.tensors[e].kept=!1),this.tensors[e]=r},e.prototype.gather=function(e,r,n){var a=this;if(r!==this.elementDtype)throw new Error("Invalid data types; op elements "+r+", but list elements "+this.elementDtype);mi(this.elementShape,n,"TensorList shape mismatch: "),e=e.slice(0,this.size());var s=gi(this.elementShape,this.tensors,n);return 0===e.length?t.tensor([],[0].concat(s)):t.tidy((function(){var r=e.map((function(e){return t.reshape(a.tensors[e],s)}));return t.stack(r,0)}))},e.prototype.concat=function(e,r){var n=this;if(e&&e!==this.elementDtype)throw new Error("TensorList dtype is "+this.elementDtype+" but concat requested dtype "+e);mi(this.elementShape,r,"TensorList shape mismatch: ");var a=gi(this.elementShape,this.tensors,r);return 0===this.size()?t.tensor([],[0].concat(a)):t.tidy((function(){var e=n.tensors.map((function(e){return t.reshape(e,a)}));return t.concat(e,0)}))},e}();var Ni=function(e,r,n){return i(undefined,void 0,void 0,(function(){var a,s,o,i,p,l,d,h,y,g,v,b,N,w,k,T,_,S,E,I,O,D,A,M,C,F,V,R,z,L,P,B,K,q,j,U,W,H,G,Z,Q,Y,X,J,$,ee,te,re,ne,ae,se,oe,ie,ue,pe,le,ce;return u(this,(function(de){switch(de.label){case 0:switch(e.op){case"If":case"StatelessIf":return[3,1];case"While":case"StatelessWhile":return[3,3];case"LoopCond":return[3,9];case"Switch":return[3,10];case"Merge":return[3,12];case"Enter":return[3,13];case"Exit":return[3,14];case"NextIteration":return[3,15];case"TensorArrayV3":return[3,16];case"TensorArrayWriteV3":return[3,17];case"TensorArrayReadV3":return[3,18];case"TensorArrayGatherV3":return[3,19];case"TensorArrayScatterV3":return[3,20];case"TensorArrayConcatV3":return[3,21];case"TensorArraySplitV3":return[3,22];case"TensorArraySizeV3":return[3,23];case"TensorArrayCloseV3":return[3,24];case"TensorListSetItem":return[3,25];case"TensorListGetItem":return[3,26];case"TensorListScatterV2":case"TensorListScatter":return[3,27];case"TensorListReserve":case"EmptyTensorList":return[3,28];case"TensorListGather":return[3,29];case"TensorListStack":return[3,30];case"TensorListFromTensor":return[3,31];case"TensorListConcat":case"TensorListConcatV2":return[3,32];case"TensorListPushBack":return[3,33];case"TensorListPopBack":return[3,34];case"TensorListSplit":return[3,35];case"TensorListLength":return[3,36];case"TensorListResize":return[3,37]}return[3,38];case 1:return a=f("thenBranch",e,r,n),s=f("elseBranch",e,r,n),o=f("cond",e,r,n),l=f("args",e,r,n),[4,o.data()];case 2:return(y=de.sent())[0]?[2,n.functionMap[a].executeFunctionAsync(l,n.tensorArrayMap,n.tensorListMap)]:[2,n.functionMap[s].executeFunctionAsync(l,n.tensorArrayMap,n.tensorListMap)];case 3:return i=f("body",e,r,n),p=f("cond",e,r,n),l=f("args",e,r,n),[4,n.functionMap[p].executeFunctionAsync(l,n.tensorArrayMap,n.tensorListMap)];case 4:return d=de.sent(),h=l.map((function(e){return e.id})),[4,d[0].data()];case 5:y=de.sent(),d.forEach((function(e){e.kept||-1!==h.indexOf(e.id)||e.dispose()})),g=l,v=function(){var e,t,r;return u(this,(function(a){switch(a.label){case 0:return e=g,[4,n.functionMap[i].executeFunctionAsync(g,n.tensorArrayMap,n.tensorListMap)];case 1:return g=a.sent(),t=g.map((function(e){return e.id})),e.forEach((function(e){e.kept||-1!==h.indexOf(e.id)||-1!==t.indexOf(e.id)||e.dispose()})),[4,n.functionMap[p].executeFunctionAsync(g,n.tensorArrayMap,n.tensorListMap)];case 2:return[4,(r=a.sent())[0].data()];case 3:return y=a.sent(),r.forEach((function(e){e.kept||-1!==h.indexOf(e.id)||-1!==t.indexOf(e.id)||e.dispose()})),[2]}}))},de.label=6;case 6:return y[0]?[5,v()]:[3,8];case 7:return de.sent(),[3,6];case 8:return[2,g];case 9:return[2,[x(b=f("pred",e,r,n))]];case 10:return b=f("pred",e,r,n),(k=f("data",e,r,n)).kept||(k=x(k)),[4,b.data()];case 11:return[2,de.sent()[0]?[void 0,k]:[k,void 0]];case 12:return N=e.inputNames.find((function(e){return void 0!==m(e,r,n)})),N?[2,[x(k=m(N,r,n))]]:[2,void 0];case 13:return w=f("frameName",e,r,n),k=f("tensor",e,r,n),n.enterFrame(w),[2,[x(k)]];case 14:return k=f("tensor",e,r,n),n.exitFrame(),[2,[x(k)]];case 15:return k=f("tensor",e,r,n),n.nextIteration(),[2,[x(k)]];case 16:return pe=f("size",e,r,n),T=f("dtype",e,r,n),se=f("elementShape",e,r,n),_=f("dynamicSize",e,r,n),S=f("clearAfterRead",e,r,n),E=f("identicalElementShapes",e,r,n),I=f("name",e,r,n),O=new bi(I,T,pe,se,E,_,S),n.addTensorArray(O),[2,[O.idTensor,t.scalar(1)]];case 17:return D=f("tensorArrayId",e,r,n),j=f("index",e,r,n),re=f("tensor",e,r,n),(A=n.getTensorArray(D.id)).write(j,re),[2,[A.idTensor]];case 18:return M=f("tensorArrayId",e,r,n),U=f("index",e,r,n),[2,[n.getTensorArray(M.id).read(U)]];case 19:return Q=f("tensorArrayId",e,r,n),Y=f("indices",e,r,n),C=f("dtype",e,r,n),[2,[n.getTensorArray(Q.id).gather(Y,C)]];case 20:return F=f("tensorArrayId",e,r,n),W=f("indices",e,r,n),H=f("tensor",e,r,n),(V=n.getTensorArray(F.id)).scatter(W,H),[2,[V.idTensor]];case 21:return ee=f("tensorArrayId",e,r,n),R=n.getTensorArray(ee.id),te=f("dtype",e,r,n),[2,[R.concat(te)]];case 22:return z=f("tensorArrayId",e,r,n),ae=f("tensor",e,r,n),oe=f("lengths",e,r,n),(L=n.getTensorArray(z.id)).split(oe,ae),[2,[L.idTensor]];case 23:return P=f("tensorArrayId",e,r,n),B=n.getTensorArray(P.id),[2,[t.scalar(B.size(),"int32")]];case 24:return K=f("tensorArrayId",e,r,n),(q=n.getTensorArray(K.id)).clearAndClose(),[2,[q.idTensor]];case 25:return ue=f("tensorListId",e,r,n),j=f("index",e,r,n),re=f("tensor",e,r,n),(ie=n.getTensorList(ue.id)).setItem(j,re),[2,[ie.idTensor]];case 26:return ue=f("tensorListId",e,r,n),U=f("index",e,r,n),se=f("elementShape",e,r,n),ne=f("elementDType",e,r,n),[2,[(ie=n.getTensorList(ue.id)).getItem(U,se,ne)]];case 27:return W=f("indices",e,r,n),H=f("tensor",e,r,n),se=f("elementShape",e,r,n),X=f("numElements",e,r,n),ie=function(e,r,n,a){if(r.length!==e.shape[0])throw new Error("Expected len(indices) == tensor.shape[0], but saw: "+r.length+" vs. "+e.shape[0]);var s=Math.max.apply(Math,c(r));if(null!=a&&-1!==a&&s>=a)throw new Error("Max index must be < array size ("+s+"  vs. "+a+")");var o=new xi([],n,e.dtype,a),i=t.unstack(e,0);return r.forEach((function(e,t){o.setItem(e,i[t])})),o}(H,W,se,X),n.addTensorList(ie),[2,[ie.idTensor]];case 28:return se=f("elementShape",e,r,n),$=f("elementDType",e,r,n),G=void 0,G="TensorListReserve"===e.op?"numElements":"maxNumElements",X=f(G,e,r,n),Z="TensorListReserve"===e.op?-1:X,ie=function(e,t,r,n){return new xi([],e,t,n)}(se,$,0,Z),n.addTensorList(ie),[2,[ie.idTensor]];case 29:return Q=f("tensorListId",e,r,n),Y=f("indices",e,r,n),se=f("elementShape",e,r,n),$=f("elementDType",e,r,n),[2,[(ie=n.getTensorList(Q.id)).gather(Y,$,se)]];case 30:return ue=f("tensorListId",e,r,n),se=f("elementShape",e,r,n),$=f("elementDType",e,r,n),X=f("numElements",e,r,n),[2,[(ie=n.getTensorList(ue.id)).stack(se,$,X)]];case 31:return J=f("tensor",e,r,n),se=f("elementShape",e,r,n),$=f("elementDType",e,r,n),ie=function(e,r,n){var a=e.dtype;if(e.shape.length<1)throw new Error("Tensor must be at least a vector, but saw shape: "+e.shape);if(e.dtype!==n)throw new Error("Invalid data types; op elements "+e.dtype+", but list elements "+n);mi(e.shape.slice(1),r,"TensorList shape mismatch: ");var s=t.unstack(e);return new xi(s,r,a)}(J,se,$),n.addTensorList(ie),[2,[ie.idTensor]];case 32:return ee=f("tensorListId",e,r,n),ie=n.getTensorList(ee.id),te=f("dtype",e,r,n),se=f("elementShape",e,r,n),[2,[ie.concat(te,se)]];case 33:return ue=f("tensorListId",e,r,n),re=f("tensor",e,r,n),(ie=n.getTensorList(ue.id)).pushBack(re),[2,[ie.idTensor]];case 34:return ue=f("tensorListId",e,r,n),se=f("elementShape",e,r,n),ne=f("elementDType",e,r,n),[2,[(ie=n.getTensorList(ue.id)).popBack(se,ne)]];case 35:return ae=f("tensor",e,r,n),se=f("elementShape",e,r,n),oe=f("lengths",e,r,n),ie=function(e,r,n){var a=0,s=r.map((function(e){return a+=e}));if(a!==e.shape[0])throw new Error("Expected sum of lengths to be equal to\n          tensor.shape[0], but sum of lengths is\n        "+a+", and tensor's shape is: "+e.shape);for(var o=vi(e.shape.slice(1),n),i=0===a?0:e.size/a,u=t.tidy((function(){var n=[];e=t.reshape(e,[1,a,i]);for(var u=0;u<r.length;++u){var p=[0,0===u?0:s[u-1],0],l=[1,r[u],i];n[u]=t.reshape(t.slice(e,p,l),o)}return e.dispose(),n})),p=new xi([],n,e.dtype,r.length),l=0;l<u.length;l++)p.setItem(l,u[l]);return p}(ae,oe,se),n.addTensorList(ie),[2,[ie.idTensor]];case 36:return ue=f("tensorListId",e,r,n),ie=n.getTensorList(ue.id),[2,[t.scalar(ie.size(),"int32")]];case 37:return ue=f("tensorListId",e,r,n),pe=f("size",e,r,n),le=n.getTensorList(ue.id),ce=le.resize(pe),n.addTensorList(ce),[2,[ce.idTensor]];case 38:throw TypeError("Node type "+e.op+" is not implemented")}}))}))};function wi(e,t,r){var n=l(f("fusedOps",e,t,r),2),a=n[0],s=n[1],o="biasadd"===a,i=!o,u="prelu"===s,p="fusedbatchnorm"===a,c=f("numArgs",e,t,r);if(o){if(u&&2!==c)throw new Error("FusedConv2d and DepthwiseConv2d with BiasAdd and Prelu must have two extra arguments: bias and alpha.");if(!u&&o&&1!==c)throw new Error("FusedConv2d and DepthwiseConv2d with BiasAdd must have one extra argument: bias.")}if(p)throw new Error("FusedConv2d and DepthwiseConv2d with FusedBatchNorm is not supported");var d=f("strides",e,t,r),h=b(e,t,r),m=f("dataFormat",e,t,r).toUpperCase(),y=f("dilations",e,t,r),g=l(f("args",e,t,r),2),v=g[0],x=g[1];return i&&(x=v,v=void 0),{stride:d,pad:h,dataFormat:m,dilations:y,biasArg:v,preluArg:x,activationFunc:s,leakyreluAlpha:f("leakyreluAlpha",e,t,r)}}function ki(e,t,r){return{boxes:f("boxes",e,t,r),scores:f("scores",e,t,r),maxOutputSize:f("maxOutputSize",e,t,r),iouThreshold:f("iouThreshold",e,t,r),scoreThreshold:f("scoreThreshold",e,t,r),softNmsSigma:f("softNmsSigma",e,t,r)}}var Ti=function(){function e(e,r){this.keyDType=e,this.valueDType=r,this.handle=t.scalar(0),this.tensorMap=new Map,t.keep(this.handle)}return Object.defineProperty(e.prototype,"id",{get:function(){return this.handle.id},enumerable:!0,configurable:!0}),e.prototype.clearAndClose=function(){this.tensorMap.forEach((function(e){return e.dispose()})),this.tensorMap.clear(),this.handle.dispose()},e.prototype.size=function(){return this.tensorMap.size},e.prototype.tensorSize=function(){return Mn(this.size(),"int32")},e.prototype.import=function(e,r){return i(this,void 0,void 0,(function(){var n,a=this;return u(this,(function(s){switch(s.label){case 0:return this.checkKeyAndValueTensor(e,r),[4,e.data()];case 1:return n=s.sent(),this.tensorMap.forEach((function(e){return e.dispose()})),this.tensorMap.clear(),[2,t.tidy((function(){var e=t.unstack(r),s=n.length,o=e.length;t.util.assert(s===o,(function(){return"The number of elements doesn't match, keys has "+s+" elements, the values has "+o+" elements."}));for(var i=0;i<s;i++){var u=n[i],p=e[i];t.keep(p),a.tensorMap.set(u,p)}return a.handle}))]}}))}))},e.prototype.find=function(e,r){return i(this,void 0,void 0,(function(){var n,a=this;return u(this,(function(s){switch(s.label){case 0:return this.checkKeyAndValueTensor(e,r),[4,e.data()];case 1:return n=s.sent(),[2,t.tidy((function(){for(var e=[],s=0;s<n.length;s++){var o=n[s],i=a.findWithDefault(o,r);e.push(i)}return t.stack(e)}))]}}))}))},e.prototype.findWithDefault=function(e,t){var r=this.tensorMap.get(e);return null!=r?r:t},e.prototype.checkKeyAndValueTensor=function(e,t){if(e.dtype!==this.keyDType)throw new Error("Expect key dtype "+this.keyDType+", but got "+e.dtype);if(t.dtype!==this.valueDType)throw new Error("Expect value dtype "+this.valueDType+", but got "+t.dtype)},e}();function _i(e,r,a,s,o){void 0===o&&(o=n.tidy);var p=function(e,r,n){switch(e.category){case"arithmetic":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"BiasAdd":case"AddV2":case"Add":return[n.add(f("a",e,t,r),f("b",e,t,r))];case"AddN":return[n.addN(f("tensors",e,t,r))];case"FloorMod":case"Mod":return[n.mod(f("a",e,t,r),f("b",e,t,r))];case"Mul":return[n.mul(f("a",e,t,r),f("b",e,t,r))];case"RealDiv":case"Div":return[n.div(f("a",e,t,r),f("b",e,t,r))];case"DivNoNan":return[n.divNoNan(f("a",e,t,r),f("b",e,t,r))];case"FloorDiv":return[n.floorDiv(f("a",e,t,r),f("b",e,t,r))];case"Sub":return[n.sub(f("a",e,t,r),f("b",e,t,r))];case"Minimum":return[n.minimum(f("a",e,t,r),f("b",e,t,r))];case"Maximum":return[n.maximum(f("a",e,t,r),f("b",e,t,r))];case"Pow":return[n.pow(f("a",e,t,r),f("b",e,t,r))];case"SquaredDifference":return[n.squaredDifference(f("a",e,t,r),f("b",e,t,r))];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"basic_math":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"Abs":case"ComplexAbs":return[n.abs(f("x",e,t,r))];case"Acos":return[n.acos(f("x",e,t,r))];case"Acosh":return[n.acosh(f("x",e,t,r))];case"Asin":return[n.asin(f("x",e,t,r))];case"Asinh":return[n.asinh(f("x",e,t,r))];case"Atan":return[n.atan(f("x",e,t,r))];case"Atan2":return[n.atan2(f("x",e,t,r),f("y",e,t,r))];case"Atanh":return[n.atanh(f("x",e,t,r))];case"Ceil":return[n.ceil(f("x",e,t,r))];case"Complex":return[n.complex(f("real",e,t,r),f("imag",e,t,r))];case"Cos":return[n.cos(f("x",e,t,r))];case"Cosh":return[n.cosh(f("x",e,t,r))];case"Elu":return[n.elu(f("x",e,t,r))];case"Erf":return[n.erf(f("x",e,t,r))];case"Exp":return[n.exp(f("x",e,t,r))];case"Expm1":return[n.expm1(f("x",e,t,r))];case"Floor":return[n.floor(f("x",e,t,r))];case"Log":return[n.log(f("x",e,t,r))];case"Log1p":return[n.log1p(f("x",e,t,r))];case"Imag":return[n.imag(f("x",e,t,r))];case"Neg":return[n.neg(f("x",e,t,r))];case"Reciprocal":return[n.reciprocal(f("x",e,t,r))];case"Real":return[n.real(f("x",e,t,r))];case"Relu":return[n.relu(f("x",e,t,r))];case"Round":return[n.round(f("x",e,t,r))];case"Selu":return[n.selu(f("x",e,t,r))];case"Sigmoid":return[n.sigmoid(f("x",e,t,r))];case"Sin":return[n.sin(f("x",e,t,r))];case"Sign":return[n.sign(f("x",e,t,r))];case"Sinh":return[n.sinh(f("x",e,t,r))];case"Softplus":return[n.softplus(f("x",e,t,r))];case"Sqrt":return[n.sqrt(f("x",e,t,r))];case"Square":return[n.square(f("x",e,t,r))];case"Tanh":return[n.tanh(f("x",e,t,r))];case"Tan":return[n.tan(f("x",e,t,r))];case"ClipByValue":return[n.clipByValue(f("x",e,t,r),f("clipValueMin",e,t,r),f("clipValueMax",e,t,r))];case"Relu6":return[n.relu6(f("x",e,t,r))];case"Rsqrt":return[n.rsqrt(m(e.inputNames[0],t,r))];case"Prod":return[n.prod(f("x",e,t,r),f("axes",e,t,r))];case"LeakyRelu":return[n.leakyRelu(f("x",e,t,r),f("alpha",e,t,r))];case"Prelu":return[n.prelu(f("x",e,t,r),f("alpha",e,t,r))];case"IsNan":return[n.isNaN(m(e.inputNames[0],t,r))];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"control":return Ni(e,r,n);case"convolution":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"Conv1D":var a=f("stride",e,t,r),s=f("pad",e,t,r),o=f("dataFormat",e,t,r).toUpperCase(),i=f("dilation",e,t,r);return[n.conv1d(f("x",e,t,r),f("filter",e,t,r),a,s,o,i)];case"Conv2D":a=f("strides",e,t,r),s=b(e,t,r),o=f("dataFormat",e,t,r).toUpperCase();var u=f("dilations",e,t,r);return[n.conv2d(f("x",e,t,r),f("filter",e,t,r),[a[1],a[2]],s,o,[u[1],u[2]])];case"_FusedConv2D":var p=wi(e,t,r),l=(a=p.stride,s=p.pad,o=p.dataFormat,u=p.dilations,p.biasArg),c=p.preluArg,d=p.activationFunc,h=p.leakyreluAlpha;return[n.fused.conv2d({x:f("x",e,t,r),filter:f("filter",e,t,r),strides:[a[1],a[2]],pad:s,dataFormat:o,dilations:[u[1],u[2]],bias:l,activation:d,preluActivationWeights:c,leakyreluAlpha:h})];case"FusedDepthwiseConv2dNative":var m=wi(e,t,r);return a=m.stride,s=m.pad,o=m.dataFormat,u=m.dilations,l=m.biasArg,c=m.preluArg,d=m.activationFunc,h=m.leakyreluAlpha,[n.fused.depthwiseConv2d({x:f("x",e,t,r),filter:f("filter",e,t,r),strides:[a[1],a[2]],pad:s,dataFormat:o,dilations:[u[1],u[2]],bias:l,activation:d,preluActivationWeights:c,leakyreluAlpha:h})];case"Conv2DBackpropInput":case"Conv2dTranspose":var y=f("outputShape",e,t,r);return a=f("strides",e,t,r),s=b(e,t,r),[n.conv2dTranspose(f("x",e,t,r),f("filter",e,t,r),y,[a[1],a[2]],s)];case"DepthwiseConv2dNative":case"DepthwiseConv2d":return a=f("strides",e,t,r),s=b(e,t,r),u=f("dilations",e,t,r),o=f("dataFormat",e,t,r).toUpperCase(),[n.depthwiseConv2d(f("input",e,t,r),f("filter",e,t,r),[a[1],a[2]],s,o,[u[1],u[2]])];case"Conv3D":return a=f("strides",e,t,r),s=f("pad",e,t,r),o=f("dataFormat",e,t,r).toUpperCase(),u=f("dilations",e,t,r),[n.conv3d(f("x",e,t,r),f("filter",e,t,r),[a[1],a[2],a[3]],s,o,[u[1],u[2],u[3]])];case"AvgPool":a=f("strides",e,t,r),s=f("pad",e,t,r);var g=f("kernelSize",e,t,r);return[n.avgPool(f("x",e,t,r),[g[1],g[2]],[a[1],a[2]],s)];case"MaxPool":return a=f("strides",e,t,r),s=f("pad",e,t,r),g=f("kernelSize",e,t,r),[n.maxPool(f("x",e,t,r),[g[1],g[2]],[a[1],a[2]],s)];case"MaxPoolWithArgmax":a=f("strides",e,t,r),s=f("pad",e,t,r),g=f("kernelSize",e,t,r);var v=f("includeBatchInIndex",e,t,r),x=n.maxPoolWithArgmax(f("x",e,t,r),[g[1],g[2]],[a[1],a[2]],s,v);return[x.result,x.indexes];case"AvgPool3D":return a=f("strides",e,t,r),s=f("pad",e,t,r),g=f("kernelSize",e,t,r),[n.avgPool3d(f("x",e,t,r),[g[1],g[2],g[3]],[a[1],a[2],a[3]],s)];case"MaxPool3D":return a=f("strides",e,t,r),s=f("pad",e,t,r),g=f("kernelSize",e,t,r),[n.maxPool3d(f("x",e,t,r),[g[1],g[2],g[3]],[a[1],a[2],a[3]],s)];case"Dilation2D":var N=f("strides",e,t,r),w=(s=f("pad",e,t,r),u=f("dilations",e,t,r),N[1]),k=N[2],T=u[1],_=u[2];return[n.dilation2d(f("x",e,t,r),f("filter",e,t,r),[w,k],s,[T,_],"NHWC")];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"creation":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"Fill":var a=f("shape",e,t,r),s=f("dtype",e,t,r),o=f("value",e,t,r);return[n.fill(a,o,s)];case"LinSpace":var i=f("start",e,t,r),u=f("stop",e,t,r),p=f("num",e,t,r);return[n.linspace(i,u,p)];case"Multinomial":var l=f("logits",e,t,r),c=f("numSamples",e,t,r),d=f("seed",e,t,r);return[n.multinomial(l,c,d)];case"OneHot":var h=f("indices",e,t,r),m=f("depth",e,t,r),y=f("onValue",e,t,r),g=f("offValue",e,t,r);return s=f("dtype",e,t,r),[n.oneHot(h,m,y,g,s)];case"Ones":return[n.ones(f("shape",e,t,r),f("dtype",e,t,r))];case"OnesLike":return[n.onesLike(f("x",e,t,r))];case"RandomStandardNormal":return[n.randomStandardNormal(f("shape",e,t,r),f("dtype",e,t,r),f("seed",e,t,r))];case"RandomUniform":return[n.randomUniform(f("shape",e,t,r),f("minval",e,t,r),f("maxval",e,t,r),f("dtype",e,t,r))];case"Range":i=f("start",e,t,r),u=f("stop",e,t,r);var v=f("step",e,t,r);return[n.range(i,u,v,f("dtype",e,t,r))];case"TruncatedNormal":a=f("shape",e,t,r);var b=f("mean",e,t,r),x=f("stdDev",e,t,r);return d=f("seed",e,t,r),[n.truncatedNormal(a,b,x,f("dtype",e,t,r),d)];case"Zeros":return[n.zeros(f("shape",e,t,r),f("dtype",e,t,r))];case"ZerosLike":return[n.zerosLike(f("x",e,t,r))];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"dynamic":return function(e,t,r,n,a){return void 0===a&&(a=fi),i(void 0,void 0,void 0,(function(){var n,s,o,i,p,l,c,d,h,m,y,g;return u(this,(function(u){switch(u.label){case 0:switch(e.op){case"NonMaxSuppressionV5":return[3,1];case"NonMaxSuppressionV4":return[3,3];case"NonMaxSuppressionV3":case"NonMaxSuppressionV2":return[3,5];case"Where":return[3,7];case"ListDiff":return[3,9]}return[3,10];case 1:return n=ki(e,t,r),l=n.boxes,c=n.scores,d=n.maxOutputSize,h=n.iouThreshold,m=n.scoreThreshold,s=n.softNmsSigma,[4,a.image.nonMaxSuppressionWithScoreAsync(l,c,d,h,m,s)];case 2:return[2,[(g=u.sent()).selectedIndices,g.selectedScores]];case 3:return o=ki(e,t,r),l=o.boxes,c=o.scores,d=o.maxOutputSize,h=o.iouThreshold,m=o.scoreThreshold,i=f("padToMaxOutputSize",e,t,r),[4,a.image.nonMaxSuppressionPaddedAsync(l,c,d,h,m,i)];case 4:return[2,[(g=u.sent()).selectedIndices,g.validOutputs]];case 5:return p=ki(e,t,r),l=p.boxes,c=p.scores,d=p.maxOutputSize,h=p.iouThreshold,m=p.scoreThreshold,[4,a.image.nonMaxSuppressionAsync(l,c,d,h,m)];case 6:return[2,[u.sent()]];case 7:return y=a.cast(f("condition",e,t,r),"bool"),[4,a.whereAsync(y)];case 8:return g=[u.sent()],y.dispose(),[2,g];case 9:return[2,a.setdiff1dAsync(f("x",e,t,r),f("y",e,t,r))];case 10:throw TypeError("Node type "+e.op+" is not implemented")}}))}))}(e,r,n);case"evaluation":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"LowerBound":var a=f("sortedSequence",e,t,r),s=f("values",e,t,r);return[n.lowerBound(a,s)];case"TopKV2":var o=f("x",e,t,r),i=f("k",e,t,r),u=f("sorted",e,t,r);return[(p=n.topk(o,i,u)).values,p.indices];case"UpperBound":return a=f("sortedSequence",e,t,r),s=f("values",e,t,r),[n.upperBound(a,s)];case"Unique":return o=f("x",e,t,r),[(p=n.unique(o)).values,p.indices];case"UniqueV2":o=f("x",e,t,r);var p,l=f("axis",e,t,r);return[(p=n.unique(o,l)).values,p.indices];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"image":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"ResizeBilinear":var a=f("images",e,t,r),s=f("size",e,t,r),o=f("alignCorners",e,t,r),i=f("halfPixelCenters",e,t,r);return[n.image.resizeBilinear(a,[s[0],s[1]],o,i)];case"ResizeNearestNeighbor":return a=f("images",e,t,r),s=f("size",e,t,r),o=f("alignCorners",e,t,r),i=f("halfPixelCenters",e,t,r),[n.image.resizeNearestNeighbor(a,[s[0],s[1]],o,i)];case"CropAndResize":var u=f("image",e,t,r),p=f("boxes",e,t,r),l=f("boxInd",e,t,r),c=f("cropSize",e,t,r),d=f("method",e,t,r),h=f("extrapolationValue",e,t,r);return[n.image.cropAndResize(u,p,l,c,d,h)];case"ImageProjectiveTransformV3":a=f("images",e,t,r);var m=f("transforms",e,t,r),y=f("outputShape",e,t,r),g=f("fillValue",e,t,r),v=f("interpolation",e,t,r),b=f("fillMode",e,t,r);return[n.image.transform(a,m,v.toLowerCase(),b.toLowerCase(),g,y)];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"graph":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"Const":return t[e.name];case"PlaceholderWithDefault":var a=f("default",e,t,r);return[m(e.name,t,r)||a];case"Placeholder":return[m(e.name,t,r)];case"Identity":case"StopGradient":case"FakeQuantWithMinMaxVars":case"Snapshot":return[x(f("x",e,t,r))];case"IdentityN":return f("x",e,t,r).map((function(e){return x(e)}));case"Shape":return[n.tensor1d(f("x",e,t,r).shape,"int32")];case"ShapeN":return f("x",e,t,r).map((function(e){return n.tensor1d(e.shape)}));case"Size":return[n.scalar(f("x",e,t,r).size,"int32")];case"Rank":return[n.scalar(f("x",e,t,r).rank,"int32")];case"NoOp":return[n.scalar(1)];case"Print":var s=f("x",e,t,r),o=f("data",e,t,r),i=f("message",e,t,r),u=f("summarize",e,t,r);console.warn("The graph has a tf.print() operation,usually used for debugging, which slows down performance."),console.log(i);for(var p=0;p<o.length;p++)console.log(Array.prototype.slice.call(o[p].dataSync()).slice(0,u));return[s];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"logical":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"Equal":return[n.equal(f("a",e,t,r),f("b",e,t,r))];case"NotEqual":return[n.notEqual(f("a",e,t,r),f("b",e,t,r))];case"Greater":return[n.greater(f("a",e,t,r),f("b",e,t,r))];case"GreaterEqual":return[n.greaterEqual(f("a",e,t,r),f("b",e,t,r))];case"Less":return[n.less(f("a",e,t,r),f("b",e,t,r))];case"LessEqual":return[n.lessEqual(f("a",e,t,r),f("b",e,t,r))];case"LogicalAnd":return[n.logicalAnd(f("a",e,t,r),f("b",e,t,r))];case"LogicalNot":return[n.logicalNot(f("a",e,t,r))];case"LogicalOr":return[n.logicalOr(f("a",e,t,r),f("b",e,t,r))];case"Select":case"SelectV2":return[n.where(f("condition",e,t,r),f("a",e,t,r),f("b",e,t,r))];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"matrices":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"BatchMatMul":case"BatchMatMulV2":case"MatMul":return[n.matMul(f("a",e,t,r),f("b",e,t,r),f("transposeA",e,t,r),f("transposeB",e,t,r))];case"Einsum":return[n.einsum.apply(n,c([f("equation",e,t,r)],f("tensors",e,t,r)))];case"Transpose":return[n.transpose(f("x",e,t,r),f("perm",e,t,r))];case"_FusedMatMul":var a=l(f("fusedOps",e,t,r),2),s=a[0],o=a[1],i="biasadd"===s,u="prelu"===o,p=f("numArgs",e,t,r),d=f("leakyreluAlpha",e,t,r);if(i){if(u&&2!==p)throw new Error("Fused MatMul with BiasAdd and Prelu must have two extra arguments: bias and alpha.");if(!u&&1!==p)throw new Error("Fused MatMul with BiasAdd must have one extra argument: bias.")}var h=l(f("args",e,t,r),2),m=h[0],y=h[1];return[n.fused.matMul({a:f("a",e,t,r),b:f("b",e,t,r),transposeA:f("transposeA",e,t,r),transposeB:f("transposeB",e,t,r),bias:m,activation:o,preluActivationWeights:y,leakyreluAlpha:d})];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"normalization":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"EuclideanNorm":return[n.euclideanNorm(f("x",e,t,r),f("axis",e,t,r),f("keepDims",e,t,r))];case"FusedBatchNorm":case"FusedBatchNormV2":case"FusedBatchNormV3":return[n.batchNorm(f("x",e,t,r),f("mean",e,t,r),f("variance",e,t,r),f("offset",e,t,r),f("scale",e,t,r),f("epsilon",e,t,r))];case"LRN":return[n.localResponseNormalization(f("x",e,t,r),f("radius",e,t,r),f("bias",e,t,r),f("alpha",e,t,r),f("beta",e,t,r))];case"Softmax":return[n.softmax(f("x",e,t,r))];case"LogSoftmax":return[n.logSoftmax(f("x",e,t,r))];case"SparseToDense":return[n.sparseToDense(f("sparseIndices",e,t,r),f("outputShape",e,t,r),f("sparseValues",e,t,r),f("defaultValue",e,t,r))];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"reduction":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"Max":var a=f("axis",e,t,r),s=f("keepDims",e,t,r);return[n.max(f("x",e,t,r),a,s)];case"Mean":return a=f("axis",e,t,r),s=f("keepDims",e,t,r),[n.mean(f("x",e,t,r),a,s)];case"Min":return a=f("axis",e,t,r),s=f("keepDims",e,t,r),[n.min(f("x",e,t,r),a,s)];case"Sum":return a=f("axis",e,t,r),s=f("keepDims",e,t,r),[n.sum(f("x",e,t,r),a,s)];case"All":return a=f("axis",e,t,r),s=f("keepDims",e,t,r),[n.all(f("x",e,t,r),a,s)];case"Any":return a=f("axis",e,t,r),s=f("keepDims",e,t,r),[n.any(f("x",e,t,r),a,s)];case"ArgMax":return a=f("axis",e,t,r),[n.argMax(f("x",e,t,r),a)];case"ArgMin":return a=f("axis",e,t,r),[n.argMin(f("x",e,t,r),a)];case"Prod":return a=f("axis",e,t,r),s=f("keepDims",e,t,r),[n.prod(f("x",e,t,r),a,s)];case"Cumprod":a=f("axis",e,t,r);var o=f("exclusive",e,t,r),i=f("reverse",e,t,r);return[n.cumprod(f("x",e,t,r),a,o,i)];case"Cumsum":return a=f("axis",e,t,r),o=f("exclusive",e,t,r),i=f("reverse",e,t,r),[n.cumsum(f("x",e,t,r),a,o,i)];case"Bincount":var u=f("x",e,t,r),p=f("weights",e,t,r),l=f("size",e,t,r);return[n.bincount(u,p,l)];case"DenseBincount":var c=f("x",e,t,r),d=f("weights",e,t,r),h=f("size",e,t,r),m=f("binaryOutput",e,t,r);return[n.denseBincount(c,d,h,m)];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"slice_join":return o((function(){return function(e,r,n,a){switch(void 0===a&&(a=fi),e.op){case"ConcatV2":case"Concat":var s=f("n",e,r,n),o=f("axis",e,r,n),i=f("tensors",e,r,n);return i=i.slice(0,s),[a.concat(i,o)];case"Gather":var u=f("x",e,r,n),p=f("indices",e,r,n);return[a.gather(u,a.cast(p,"int32"),0)];case"GatherV2":o=f("axis",e,r,n);var l=f("batchDims",e,r,n);return u=f("x",e,r,n),p=f("indices",e,r,n),[a.gather(u,a.cast(p,"int32"),o,l)];case"Reverse":for(var c=f("dims",e,r,n),d=(o=[],0);d<c.length;d++)c[d]&&o.push(d);return u=f("x",e,r,n),[a.reverse(u,o)];case"ReverseV2":return o=f("axis",e,r,n),u=f("x",e,r,n),[a.reverse(u,o)];case"Slice":var h=f("begin",e,r,n),m=f("size",e,r,n);return[a.slice(f("x",e,r,n),h,m)];case"StridedSlice":h=f("begin",e,r,n);var y=f("end",e,r,n),g=f("strides",e,r,n),v=f("beginMask",e,r,n),b=f("endMask",e,r,n),x=f("ellipsisMask",e,r,n),N=f("newAxisMask",e,r,n),w=f("shrinkAxisMask",e,r,n),k=f("x",e,r,n);return[a.stridedSlice(k,h,y,g,v,b,x,N,w)];case"Pack":return t.tidy((function(){var s=f("axis",e,r,n),o=f("tensors",e,r,n),i=o[0].shape,u=a.squeeze(o[0]).shape,p=o.map((function(e){var r=t.util.arraysEqual(e.shape,i);if(!r&&!t.util.arraysEqual(a.squeeze(e).shape,u))throw new Error("the input tensors shape does not match");return r?e:a.reshape(e,i)}));return[a.stack(p,s)]}));case"Unpack":return o=f("axis",e,r,n),k=f("tensor",e,r,n),a.unstack(k,o);case"Tile":var T=f("reps",e,r,n);return[a.tile(f("x",e,r,n),T)];case"Split":case"SplitV":o=f("axis",e,r,n);var _=f("numOrSizeSplits",e,r,n);return k=f("x",e,r,n),a.split(k,_,o);case"ScatterNd":p=f("indices",e,r,n);var S=f("values",e,r,n),E=f("shape",e,r,n);return[a.scatterND(p,S,E)];case"GatherNd":var I=f("x",e,r,n);return p=f("indices",e,r,n),[a.gatherND(I,p)];case"SparseToDense":p=f("sparseIndices",e,r,n),E=f("outputShape",e,r,n);var O=f("sparseValues",e,r,n),D=f("defaultValue",e,r,n);return[a.sparseToDense(p,O,E,O.dtype===D.dtype?D:a.cast(D,O.dtype))];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"sparse":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"SparseFillEmptyRows":var a=n.sparse.sparseFillEmptyRows(f("indices",e,t,r),f("values",e,t,r),f("denseShape",e,t,r),f("defaultValue",e,t,r));return[a.outputIndices,a.outputValues,a.emptyRowIndicator,a.reverseIndexMap];case"SparseReshape":var s=n.sparse.sparseReshape(f("inputIndices",e,t,r),f("inputShape",e,t,r),f("newShape",e,t,r));return[s.outputIndices,s.outputShape];case"SparseSegmentMean":return[n.sparse.sparseSegmentMean(f("data",e,t,r),f("indices",e,t,r),f("segmentIds",e,t,r))];case"SparseSegmentSum":return[n.sparse.sparseSegmentSum(f("data",e,t,r),f("indices",e,t,r),f("segmentIds",e,t,r))];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"spectral":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"FFT":return[n.fft(f("x",e,t,r))];case"IFFT":return[n.ifft(f("x",e,t,r))];case"RFFT":return[n.rfft(f("x",e,t,r))];case"IRFFT":return[n.irfft(f("x",e,t,r))];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"string":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"StringNGrams":var a=n.string.stringNGrams(f("data",e,t,r),f("dataSplits",e,t,r),f("separator",e,t,r),f("nGramWidths",e,t,r),f("leftPad",e,t,r),f("rightPad",e,t,r),f("padWidth",e,t,r),f("preserveShortSequences",e,t,r));return[a.nGrams,a.nGramsSplits];case"StringSplit":var s=n.string.stringSplit(f("input",e,t,r),f("delimiter",e,t,r),f("skipEmpty",e,t,r));return[s.indices,s.values,s.shape];case"StringToHashBucketFast":return[n.string.stringToHashBucketFast(f("input",e,t,r),f("numBuckets",e,t,r))];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"transformation":return o((function(){return function(e,t,r,n){switch(void 0===n&&(n=fi),e.op){case"Cast":return[n.cast(f("x",e,t,r),f("dtype",e,t,r))];case"ExpandDims":var a=f("axis",e,t,r);return[n.expandDims(f("x",e,t,r),a)];case"Squeeze":return a=f("axis",e,t,r),[n.squeeze(f("x",e,t,r),a)];case"Reshape":return[n.reshape(f("x",e,t,r),f("shape",e,t,r))];case"MirrorPad":return[n.mirrorPad(f("x",e,t,r),f("padding",e,t,r),f("mode",e,t,r))];case"PadV2":case"Pad":return[n.pad(f("x",e,t,r),f("padding",e,t,r),f("constantValue",e,t,r))];case"SpaceToBatchND":var s=f("blockShape",e,t,r),o=f("paddings",e,t,r);return[n.spaceToBatchND(f("x",e,t,r),s,o)];case"BatchToSpaceND":s=f("blockShape",e,t,r);var i=f("crops",e,t,r);return[n.batchToSpaceND(f("x",e,t,r),s,i)];case"DepthToSpace":var u=f("blockSize",e,t,r),p=f("dataFormat",e,t,r).toUpperCase();return[n.depthToSpace(f("x",e,t,r),u,p)];case"BroadcastTo":return[n.broadcastTo(f("x",e,t,r),f("shape",e,t,r))];case"BroadcastArgs":return[n.broadcastArgs(f("s0",e,t,r),f("s1",e,t,r))];default:throw TypeError("Node type "+e.op+" is not implemented")}}(e,r,n)}));case"hash_table":return function(e,t,r,n){return i(void 0,void 0,void 0,(function(){var a,s,o,i,p,l,c;return u(this,(function(u){switch(u.label){case 0:switch(e.op){case"HashTable":case"HashTableV2":return[3,1];case"LookupTableImport":case"LookupTableImportV2":return[3,2];case"LookupTableFind":case"LookupTableFindV2":return[3,4];case"LookupTableSize":case"LookupTableSizeV2":return[3,6]}return[3,7];case 1:return a=f("keyDType",e,t,r),s=f("valueDType",e,t,r),c=new Ti(a,s),n.addHashTable(e.name,c),[2,[c.handle]];case 2:return l=f("tableHandle",e,t,r,n),i=f("keys",e,t,r),o=f("values",e,t,r),[4,(c=n.getHashTableById(l.id)).import(i,o)];case 3:case 5:return[2,[u.sent()]];case 4:return l=f("tableHandle",e,t,r,n),i=f("keys",e,t,r),p=f("defaultValue",e,t,r),[4,(c=n.getHashTableById(l.id)).find(i,p)];case 6:return l=f("tableHandle",e,t,r,n),[2,[(c=n.getHashTableById(l.id)).tensorSize()]];case 7:throw TypeError("Node type "+e.op+" is not implemented")}}))}))}(e,r,n,s);case"custom":var a=h(e.op);if(a&&a.customExecutor)return a.customExecutor(new te(e,r,n));throw TypeError("Custom op "+e.op+" is not registered.");default:throw TypeError("Unknown op '"+e.op+"'. File an issue at https://github.com/tensorflow/tfjs/issues so we can add it, or register a custom execution with tf.registerOp()")}}(e,r,a);return n.util.isPromise(p)?p.then((function(e){return[].concat(e)})):[].concat(p)}var Si=function(){function e(e,t,r,n){void 0===e&&(e={}),void 0===t&&(t={}),void 0===r&&(r={}),void 0===n&&(n={}),this.weightMap=e,this.tensorArrayMap=t,this.tensorListMap=r,this.functionMap=n,this.rootContext={id:0,frameName:"",iterationId:0},this.contexts=[this.rootContext],this.lastId=0,this.generateCurrentContextIds()}return e.prototype.newFrame=function(e,t){return{id:e,frameName:t,iterationId:0}},Object.defineProperty(e.prototype,"currentContext",{get:function(){return this.contexts},set:function(e){this.contexts!==e&&(this.contexts=e,this.generateCurrentContextIds())},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"currentContextId",{get:function(){return this._currentContextIds[0]},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"currentContextIds",{get:function(){return this._currentContextIds},enumerable:!0,configurable:!0}),e.prototype.generateCurrentContextIds=function(){for(var e=[],t=0;t<this.contexts.length-1;t++){var r=this.contexts.slice(0,this.contexts.length-t);e.push(this.contextIdforContexts(r))}e.push(""),this._currentContextIds=e},e.prototype.contextIdforContexts=function(e){return e?e.map((function(e){return 0===e.id&&0===e.iterationId?"":e.frameName+"-"+e.iterationId})).join("/"):""},e.prototype.enterFrame=function(e){this.contexts&&(this.lastId++,this.contexts=this.contexts.slice(),this.contexts.push(this.newFrame(this.lastId,e)),this._currentContextIds.unshift(this.contextIdforContexts(this.contexts)))},e.prototype.exitFrame=function(){if(!(this.contexts&&this.contexts.length>1))throw new Error("Cannot exit frame, the context is empty");this.contexts=this.contexts.slice(),this.contexts.splice(-1),this.currentContextIds.shift()},e.prototype.nextIteration=function(){if(!(this.contexts&&this.contexts.length>0))throw new Error("Cannot increase frame iteration, the context is empty");this.contexts=this.contexts.slice(),this.lastId++;var e=Object.assign({},this.contexts[this.contexts.length-1]);e.iterationId+=1,e.id=this.lastId,this.contexts.splice(-1,1,e),this._currentContextIds.splice(0,1,this.contextIdforContexts(this.contexts))},e.prototype.getWeight=function(e){return this.weightMap[e]},e.prototype.addTensorArray=function(e){this.tensorArrayMap[e.id]=e},e.prototype.getTensorArray=function(e){return this.tensorArrayMap[e]},e.prototype.addTensorList=function(e){this.tensorListMap[e.id]=e},e.prototype.getTensorList=function(e){return this.tensorListMap[e]},e.prototype.dispose=function(e){for(var t in this.tensorArrayMap)this.tensorArrayMap[t].clearAndClose(e);for(var t in this.tensorListMap)this.tensorListMap[t].clearAndClose(e)},e}();function Ei(e,t,r,n){var a=new Set,s=[],o=null,i=null,u=new Set,p=Object.keys(e).map((function(e){return v(e)[0]})),l=[];null!=n&&(l=n.map((function(e){return v(e.name)[0]})));for(var d=c(t);d.length>0;){var h=d.pop();(Ai(h)||Mi(h)||Ci(h))&&null==o&&(i=(o=h).children.map((function(e){return e.name})).filter((function(e){return a.has(e)}))),a.add(h.name),null==r[h.name]&&(-1===p.indexOf(h.name)&&-1===l.indexOf(h.name)&&(0!==h.inputs.length?h.inputs.forEach((function(e){u.has(e.name)||(u.add(e.name),d.push(e))})):s.push(h.name)))}return{inputs:e,outputs:t,usedNodes:a,missingInputs:s,dynamicNode:o,syncInputs:i}}var Ii=["Switch","Merge","Enter","Exit","NextIteration","StatelessIf","StatelessWhile","if","While"],Oi=["NonMaxSuppressionV2","NonMaxSuppressionV3","NonMaxSuppressionV5","Where"],Di=["HashTable","HashTableV2","LookupTableImport","LookupTableImportV2","LookupTableFind","LookupTableFindV2","LookupTableSize","LookupTableSizeV2"];function Ai(e){return Ii.indexOf(e.op)>=0}function Mi(e){return Oi.indexOf(e.op)>=0}function Ci(e){return Di.indexOf(e.op)>=0}var Fi=function(){function e(t,r){var n=this;this.graph=t,this.parent=r,this.compiledMap=new Map,this._weightMap={},this.SEPERATOR=",",this._functions={},this._functionExecutorMap={},this.intermediateTensors={},this.keepTensorForDebug=!1,this._outputs=t.outputs,this._inputs=t.inputs,this._initNodes=t.initNodes,this._signature=t.signature,this._functions=t.functions,null!=t.functions&&Object.keys(t.functions).forEach((function(r){n._functionExecutorMap[r]=new e(t.functions[r],n)}))}return Object.defineProperty(e.prototype,"weightIds",{get:function(){return this.parent?this.parent.weightIds:this._weightIds},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"functionExecutorMap",{get:function(){return this.parent?this.parent.functionExecutorMap:this._functionExecutorMap},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"weightMap",{get:function(){return this.parent?this.parent.weightMap:this._weightMap},set:function(e){var t=Object.keys(e).map((function(t){return e[t].map((function(e){return e.id}))}));this._weightIds=[].concat.apply([],c(t)),this._weightMap=e},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"resourceManager",{set:function(e){this._resourceManager=e},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"inputs",{get:function(){return this._inputs.map((function(e){return{name:e.name,shape:e.attrParams.shape?e.attrParams.shape.value:void 0,dtype:e.attrParams.dtype?e.attrParams.dtype.value:void 0}}))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"outputs",{get:function(){return this._outputs.map((function(e){return{name:e.name,shape:e.attrParams.shape?e.attrParams.shape.value:void 0,dtype:e.attrParams.dtype?e.attrParams.dtype.value:void 0}}))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"inputNodes",{get:function(){return this._inputs.map((function(e){return e.signatureKey||e.name}))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"outputNodes",{get:function(){return this._outputs.map((function(e){var t=e.signatureKey||e.name;return e.defaultOutput?t+":"+e.defaultOutput:t}))},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"functions",{get:function(){var e=this;return Object.keys(this._functions).reduce((function(t,r){return t[r]=e._functions[r].signature,t}),{})},enumerable:!0,configurable:!0}),e.prototype.getCompilationKey=function(e,t){var r=e.map((function(e){return e.name})).sort(),n=t.map((function(e){return e.name})).sort();return r.join(this.SEPERATOR)+"--"+n.join(this.SEPERATOR)},e.prototype.compile=function(e,t){var r=Ei(e,t,this.weightMap,this._initNodes),n=r.missingInputs,a=r.dynamicNode,s=r.syncInputs;if(null!=a)throw new Error("This execution contains the node '"+a.name+"', which has the dynamic op '"+a.op+"'. Please use model.executeAsync() instead. Alternatively, to avoid the dynamic ops, specify the inputs ["+s+"]");if(n.length>0){var o=t.map((function(e){return e.name})),i=Object.keys(e);throw new Error("Cannot compute the outputs ["+o+"] from the provided inputs ["+i+"]. Missing the following inputs: ["+n+"]")}return function(e,t,r){var n=r.usedNodes,a=r.inputs,s=[],o=Object.keys(a).map((function(e){return v(e)[0]})).map((function(t){return e.nodes[t]})),i=e.initNodes;o.forEach((function(e){n.has(e.name)&&s.push(e)})),e.weights.forEach((function(e){n.has(e.name)&&s.push(e)})),null!=i&&i.forEach((function(e){n.has(e.name)&&s.push(e)}));for(var u=new Set,p=[];s.length>0;){var l=s.pop();u.add(l.name),t[l.name]||p.push(l),l.children.forEach((function(e){!u.has(e.name)&&n.has(e.name)&&e.inputs.every((function(e){return u.has(e.name)}))&&s.push(e)}))}return p}(this.graph,this.weightMap,r)},e.prototype.execute=function(e,r){var n=this;e=this.mapInputs(e);var a=Object.keys(e).sort();this.checkInputs(e),this.checkInputShapeAndType(e),r=this.mapOutputs(r),this.checkOutputs(r);var s=a.map((function(e){return n.graph.nodes[v(e)[0]]})),o=r.map((function(e){return v(e)[0]})),i=o.map((function(e){return n.graph.nodes[e]}));this.resetIntermediateTensors(),0===i.length&&(i=this._outputs);var u=this.getCompilationKey(s,i),p=this.compiledMap.get(u);null==p&&(p=this.compile(e,i),this.compiledMap.set(u,p));var c={},d={};return t.tidy((function(){var a=new Si(n.weightMap,c,d,n.functionExecutorMap),s=Object.assign({},n.weightMap);Object.keys(e).forEach((function(t){var r=l(v(t),2),n=r[0],a=[];a[r[1]]=e[t],s[n]=a}));for(var i=n.getFrozenTensorIds(s),u={},h=0;h<p.length;h++){var f=p[h];if(!s[f.name]){var y=_i(f,s,a,n._resourceManager);if(t.util.isPromise(y))throw new Error("The execution of the op '"+f.op+"' returned a promise. Please use model.executeAsync() instead.");s[f.name]=y,n.checkTensorForDisposal(f.name,f,s,a,i,o,u)}}return null==n.parent&&a.dispose(i),r.map((function(e){return m(e,s,a)}))}))},e.prototype.getFrozenTensorIds=function(e){var t=[].concat.apply([],Object.keys(e).map((function(t){return e[t]})).map((function(e){return e.map((function(e){return e.id}))})));return new Set(t)},e.prototype.checkTensorForDisposal=function(e,t,r,n,a,s,o){var i=this;"control"!==t.category&&-1===s.indexOf(e)&&(r[e].forEach((function(e){null!=e&&(o[e.id]=(o[e.id]||0)+t.children.length)})),t.inputs.forEach((function(e){if("control"!==e.category){var s=function(e,t,r){return t[g(e,r.currentContextId)]}(e.name,r,n);null!=s&&s.forEach((function(e){if(e&&!e.kept&&!a.has(e.id)){var r=o[e.id];if(1===r){if(i.keepTensorForDebug){var s=l(y(t.name,n),2),u=s[0],p=s[1];i.intermediateTensors[u]||(i.intermediateTensors[u]=[]),i.intermediateTensors[u][p]=e}else e.dispose();delete o[e.id]}else null!=r&&o[e.id]--}}))}})))},e.prototype.executeAsync=function(e,t){return i(this,void 0,void 0,(function(){return u(this,(function(r){return[2,this._executeAsync(e,t)]}))}))},e.prototype.disposeIntermediateTensors=function(){var e=this;this.intermediateTensors&&(Object.keys(this.intermediateTensors).forEach((function(t){return e.intermediateTensors[t].forEach((function(e){return e.dispose()}))})),this.disposeTensorsMap())},e.prototype.disposeTensorsMap=function(){var e=this;this.tensorsMap&&Object.keys(this.tensorsMap).forEach((function(t){e.tensorsMap[t].forEach((function(t){!t||t.kept||t.isDisposed||e.keepIds.has(t.id)||t.dispose()}))}))},e.prototype.getIntermediateTensors=function(){return this.tensorsMap},e.prototype.resetIntermediateTensors=function(){for(var e in this.intermediateTensors)this.intermediateTensors[e].forEach((function(e){return e.dispose()})),delete this.intermediateTensors[e]},e.prototype._executeAsync=function(e,r,n,a,s){return void 0===n&&(n=!1),void 0===a&&(a={}),void 0===s&&(s={}),i(this,void 0,void 0,(function(){var o,i,p,l,d,h=this;return u(this,(function(u){switch(u.label){case 0:n||(e=this.mapInputs(e),this.checkInputs(e),this.checkInputShapeAndType(e),r=this.mapOutputs(r),this.checkOutputs(r));try{this.keepTensorForDebug=t.env().getBool("KEEP_INTERMEDIATE_TENSORS")}catch(e){console.warn(e.message)}return this.resetIntermediateTensors(),o=new Si(this.weightMap,a,s,this.functionExecutorMap),i=this,[4,this.executeWithControlFlow(e,o,r,n)];case 1:return i.tensorsMap=u.sent(),p=r.map((function(e){return m(e,h.tensorsMap,o)})),l=p.map((function(e){return e.id})),d=Object.keys(e).map((function(t){return e[t].id})),this.keepIds=new Set(c(l,d,this.weightIds)),this.keepTensorForDebug||this.disposeTensorsMap(),null==this.parent&&o.dispose(this.keepIds),[2,p]}}))}))},e.prototype.executeFunctionAsync=function(e,t,r){return i(this,void 0,void 0,(function(){var n,a=this;return u(this,(function(s){return n=e.reduce((function(e,t,r){return e[a.inputs[r].name]=t,e}),{}),[2,this._executeAsync(n,this.outputNodes,!0,t,r)]}))}))},e.prototype.executeWithControlFlow=function(e,t,r,n){return i(this,void 0,void 0,(function(){var a,s,o,i,p,d,h,f,y,g,b,x,N,w,k,T,_,S=this;return u(this,(function(u){switch(u.label){case 0:a=Object.keys(e),s=a.map((function(e){return S.graph.nodes[v(e)[0]]})),o=r.map((function(e){return v(e)[0]})),0===(i=o.map((function(e){return S.graph.nodes[e]}))).length&&(i=this._outputs),p=Ei(e,i,this.weightMap,this._initNodes),d=p.usedNodes,h=p.missingInputs,f=p.dynamicNode,y=p.syncInputs,g=c(s,this.graph.weights,this._initNodes||[]).map((function(e){return{node:e,contexts:t.currentContext}})),b=Object.assign({},this.weightMap),Object.keys(e).forEach((function(t){var r=l(v(t),2),n=r[0],a=[];a[r[1]]=e[t],b[n]=a})),x={},N=this.getFrozenTensorIds(b),w={},u.label=1;case 1:return g.length>0?(k=this.processStack(s,g,t,b,w,N,o,x,d),[4,Promise.all(k)]):[3,3];case 2:return u.sent(),[3,1];case 3:if(null!=f||n||console.warn("This model execution did not contain any nodes with control flow or dynamic output shapes. You can use model.execute() instead."),(T=i.filter((function(e){return!Ai(e)&&!m(e.name,b,t)})).map((function(e){return e.name}))).length>0)throw _="",null!=f&&(_="Alternatively, to avoid the dynamic ops, use model.execute() and specify the inputs ["+y+"]"),new Error("Cannot compute the outputs ["+T+"] from the provided inputs ["+a+"]. Consider providing the following inputs: ["+h+"]. "+_);return[2,b]}}))}))},e.prototype.processStack=function(e,r,n,a,s,o,i,u,p){for(var c=this,d=[],h=function(){var e,h,g=r.pop();n.currentContext=g.contexts;var v="";if("Enter"===g.node.op&&f("isConstant",g.node,a,n)&&(e=l(y(g.node.name,n),1),v=e[0]),null==a[g.node.name]){var b=_i(g.node,a,n,m._resourceManager);v||(h=l(y(g.node.name,n),1),v=h[0]);var x=n.currentContext;t.util.isPromise(b)?d.push(b.then((function(e){return a[v]=e,n.currentContext=x,c.checkTensorForDisposal(v,g.node,a,n,o,i,u),c.processChildNodes(g.node,r,n,a,s,p),e}))):(a[v]=b,m.checkTensorForDisposal(v,g.node,a,n,o,i,u),m.processChildNodes(g.node,r,n,a,s,p))}else m.processChildNodes(g.node,r,n,a,s,p)},m=this;r.length>0;)h();return d},e.prototype.processChildNodes=function(e,t,r,n,a,s){e.children.forEach((function(e){var o=l(y(e.name,r),1)[0];!a[o]&&s.has(e.name)&&("Merge"===e.op?e.inputNames.some((function(e){return!!m(e,n,r)}))&&(a[o]=!0,t.push({contexts:r.currentContext,node:e})):e.inputNames.every((function(e){return!!m(e,n,r)}))&&(a[o]=!0,t.push({contexts:r.currentContext,node:e})))}))},e.prototype.dispose=function(){var e=this;Object.keys(this.weightMap).forEach((function(t){return e.weightMap[t].forEach((function(e){return e.dispose()}))}))},e.prototype.checkInputShapeAndType=function(e){var r=this;Object.keys(e).forEach((function(n){var a=e[n],s=l(v(n),1)[0],o=r.graph.nodes[s];if(o.attrParams.shape&&o.attrParams.shape.value){var i=o.attrParams.shape.value,u=i.length===a.shape.length&&a.shape.every((function(e,t){return-1===i[t]||i[t]===e}));t.util.assert(u,(function(){return"The shape of dict['"+o.name+"'] provided in model.execute(dict) must be ["+i+"], but was ["+a.shape+"]"}))}o.attrParams.dtype&&o.attrParams.dtype.value&&t.util.assert(a.dtype===o.attrParams.dtype.value,(function(){return"The dtype of dict['"+o.name+"'] provided in model.execute(dict) must be "+o.attrParams.dtype.value+", but was "+a.dtype}))}))},e.prototype.mapInputs=function(e){var t={};for(var r in e){if(null!=this._signature&&null!=this._signature.inputs&&null!=this._signature.inputs[r])t[this._signature.inputs[r].name]=e[r];else t[r]=e[r]}return t},e.prototype.checkInputs=function(e){var t=this,r=Object.keys(e).filter((function(e){var r=l(v(e),1)[0];return null==t.graph.nodes[r]}));if(r.length>0)throw new Error("The dict provided in model.execute(dict) has keys: ["+r+"] that are not part of graph")},e.prototype.mapOutputs=function(e){var t=this;return e.map((function(e){return null!=t._signature&&null!=t._signature.outputs&&null!=t._signature.outputs[e]?t._signature.outputs[e].name:e}),{})},e.prototype.checkOutputs=function(e){var t=this;e.forEach((function(e){var r=l(v(e),1)[0];if(!t.graph.nodes[r])throw new Error("The output '"+e+"' is not found in the graph")}))},e}(),Vi=function(){function e(e,t){void 0===e&&(e={}),void 0===t&&(t={}),this.hashTableNameToHandle=e,this.hashTableMap=t}return e.prototype.addHashTable=function(e,t){this.hashTableNameToHandle[e]=t.handle,this.hashTableMap[t.id]=t},e.prototype.getHashTableHandleByName=function(e){return this.hashTableNameToHandle[e]},e.prototype.getHashTableById=function(e){return this.hashTableMap[e]},e.prototype.dispose=function(){for(var e in this.hashTableMap)this.hashTableMap[e].clearAndClose(),delete this.hashTableMap[e];for(var t in this.hashTableNameToHandle)this.hashTableNameToHandle[t].dispose(),delete this.hashTableNameToHandle[t]},e}(),Ri="?tfjs-format=file",zi="model.json",Li=function(){function e(e,r,n){void 0===r&&(r={}),void 0===n&&(n=t.io),this.modelUrl=e,this.loadOptions=r,this.version="n/a",this.io=n,null==r&&(this.loadOptions={}),this.resourceManager=new Vi}return Object.defineProperty(e.prototype,"modelVersion",{get:function(){return this.version},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"inputNodes",{get:function(){return this.executor.inputNodes},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"outputNodes",{get:function(){return this.executor.outputNodes},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"inputs",{get:function(){return this.executor.inputs},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"outputs",{get:function(){return this.executor.outputs},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"weights",{get:function(){return this.executor.weightMap},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"metadata",{get:function(){return this.artifacts.userDefinedMetadata},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"modelSignature",{get:function(){return this.signature},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"modelStructuredOutputKeys",{get:function(){return this.structuredOutputKeys},enumerable:!0,configurable:!0}),e.prototype.findIOHandler=function(){var e=this.modelUrl;if(null!=e.load)this.handler=e;else if(null!=this.loadOptions.requestInit)this.handler=this.io.browserHTTPRequest(e,this.loadOptions);else{var t=this.io.getLoadHandlers(e,this.loadOptions);if(0===t.length)t.push(this.io.browserHTTPRequest(e,this.loadOptions));else if(t.length>1)throw new Error("Found more than one ("+t.length+") load handlers for URL '"+[e]+"'");this.handler=t[0]}},e.prototype.load=function(){var e=this;if(this.findIOHandler(),null==this.handler.load)throw new Error("Cannot proceed with model loading because the IOHandler provided does not have the `load` method implemented.");var r=this.handler.load();return t.util.isPromise(r)?r.then((function(t){return e.loadSync(t)})):this.loadSync(r)},e.prototype.loadSync=function(e){this.artifacts=e;var t=this.artifacts.modelTopology,r=this.artifacts.signature;if(null!=this.artifacts.userDefinedMetadata){var n=this.artifacts.userDefinedMetadata;null!=n.signature&&(r=n.signature),null!=n.structuredOutputKeys&&(this.structuredOutputKeys=n.structuredOutputKeys)}this.signature=r,this.version=t.versions.producer+"."+t.versions.minConsumer;var a=this.io.decodeWeights(this.artifacts.weightData,this.artifacts.weightSpecs);if(this.executor=new Fi(B.Instance.transformGraph(t,this.signature)),this.executor.weightMap=this.convertTensorMapToTensorsMap(a),this.executor.resourceManager=this.resourceManager,null!=e.modelInitializer&&null!=e.modelInitializer.node){var s=B.Instance.transformGraph(e.modelInitializer);this.initializer=new Fi(s),this.initializer.weightMap=this.executor.weightMap,this.initializer.resourceManager=this.resourceManager,this.initializer.executeAsync({},[])}return!0},e.prototype.save=function(e,t){return i(this,void 0,void 0,(function(){var t;return u(this,(function(r){if("string"==typeof e){if(0===(t=this.io.getSaveHandlers(e)).length)throw new Error("Cannot find any save handlers for URL '"+e+"'");if(t.length>1)throw new Error("Found more than one ("+t.length+") save handlers for URL '"+e+"'");e=t[0]}if(null==e.save)throw new Error("GraphModel.save() cannot proceed because the IOHandler provided does not have the `save` attribute defined.");return[2,e.save(this.artifacts)]}))}))},e.prototype.predict=function(e,r){var n=this,a=this.execute(e,this.outputNodes);if(this.structuredOutputKeys){var s=a instanceof t.Tensor?[a]:a,o={};return s.forEach((function(e,t){return o[n.structuredOutputKeys[t]]=e})),o}return a},e.prototype.normalizeInputs=function(e){if(!(e instanceof t.Tensor||Array.isArray(e)))return e;if((e=Array.isArray(e)?e:[e]).length!==this.inputNodes.length)throw new Error("Input tensor count mismatch,the graph model has "+this.inputNodes.length+" placeholders, while there are "+e.length+" input tensors.");return this.inputNodes.reduce((function(t,r,n){return t[r]=e[n],t}),{})},e.prototype.normalizeOutputs=function(e){return e=e||this.outputNodes,Array.isArray(e)?e:[e]},e.prototype.execute=function(e,t){e=this.normalizeInputs(e),t=this.normalizeOutputs(t);var r=this.executor.execute(e,t);return r.length>1?r:r[0]},e.prototype.executeAsync=function(e,t){return i(this,void 0,void 0,(function(){var r;return u(this,(function(n){switch(n.label){case 0:return e=this.normalizeInputs(e),t=this.normalizeOutputs(t),[4,this.executor.executeAsync(e,t)];case 1:return[2,(r=n.sent()).length>1?r:r[0]]}}))}))},e.prototype.getIntermediateTensors=function(){return this.executor.getIntermediateTensors()},e.prototype.disposeIntermediateTensors=function(){this.executor.disposeIntermediateTensors()},e.prototype.convertTensorMapToTensorsMap=function(e){return Object.keys(e).reduce((function(t,r){return t[r]=[e[r]],t}),{})},e.prototype.dispose=function(){this.executor.dispose(),this.initializer&&this.initializer.dispose(),this.resourceManager.dispose()},e}();e.GraphModel=Li,e.deregisterOp=function(e){delete d[e]},e.loadGraphModel=function(e,r,n){return void 0===r&&(r={}),void 0===n&&(n=t.io),i(this,void 0,void 0,(function(){var t;return u(this,(function(a){switch(a.label){case 0:if(null==e)throw new Error("modelUrl in loadGraphModel() cannot be null. Please provide a url or an IOHandler that loads the model");return null==r&&(r={}),r.fromTFHub&&"string"==typeof e&&(e=function(e){e.endsWith("/")||(e+="/");return""+e+zi+Ri}(e)),[4,(t=new Li(e,r,n)).load()];case 1:return a.sent(),[2,t]}}))}))},e.loadGraphModelSync=function(e){if(null==e)throw new Error("modelUrl in loadGraphModelSync() cannot be null. Please provide model artifacts or an IOHandler that loads the model");var r;if(e instanceof Array){var n=l(e,2),a=n[0],s=n[1];if(!a)throw new Error("modelJSON must be the first element of the array");if(!(s&&s instanceof ArrayBuffer))throw new Error("An ArrayBuffer of weights must be the second element of the array");if(!("modelTopology"in a))throw new Error("Model JSON is missing 'modelTopology'");if(!("weightsManifest"in a))throw new Error("Model JSON is missing 'weightsManifest'");var o=t.io.getWeightSpecs(a.weightsManifest),i=t.io.getModelArtifactsForJSONSync(a,o,s);r=t.io.fromMemorySync(i)}else if("load"in e)r=e;else{if(!("modelTopology"in e&&"weightSpecs"in e&&"weightData"in e))throw new Error("Unknown model format");r=t.io.fromMemorySync(e)}var u=new Li(r);return u.load(),u},e.registerOp=function(e,t){var r={tfOpName:e,category:"custom",inputs:[],attrs:[],customExecutor:t};d[e]=r},e.version_converter="3.21.0",Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=tf-converter.min.js.map

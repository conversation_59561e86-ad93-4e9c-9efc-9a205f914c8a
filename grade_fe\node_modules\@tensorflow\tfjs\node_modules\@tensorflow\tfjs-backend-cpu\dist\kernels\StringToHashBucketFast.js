/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { StringToHashBucketFast } from '@tensorflow/tfjs-core';
import { stringToHashBucketFastImpl } from './StringToHashBucketFast_impl';
export function stringToHashBucketFast(args) {
    const { inputs, backend, attrs } = args;
    const { numBuckets } = attrs;
    const { input } = inputs;
    if (input.dtype !== 'string') {
        throw new Error('Input must be of datatype string');
    }
    if (numBuckets <= 0) {
        throw new Error(`Number of buckets must be at least 1`);
    }
    const $input = backend.data.get(input.dataId).values;
    const output = stringToHashBucketFastImpl($input, numBuckets);
    return backend.makeTensorInfo(input.shape, 'int32', output);
}
export const stringToHashBucketFastConfig = {
    kernelName: StringToHashBucketFast,
    backendName: 'cpu',
    kernelFunc: stringToHashBucketFast,
};
//# sourceMappingURL=data:application/json;base64,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
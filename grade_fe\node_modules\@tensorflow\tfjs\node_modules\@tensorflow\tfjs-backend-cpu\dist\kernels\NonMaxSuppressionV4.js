/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { kernel_impls, NonMaxSuppressionV4 } from '@tensorflow/tfjs-core';
const nonMaxSuppressionV4Impl = kernel_impls.nonMaxSuppressionV4Impl;
import { assertNotComplex } from '../cpu_util';
export function nonMaxSuppressionV4(args) {
    const { inputs, backend, attrs } = args;
    const { boxes, scores } = inputs;
    const { maxOutputSize, iouThreshold, scoreThreshold, padToMaxOutputSize } = attrs;
    assertNotComplex(boxes, 'NonMaxSuppressionPadded');
    const boxesVals = backend.data.get(boxes.dataId).values;
    const scoresVals = backend.data.get(scores.dataId).values;
    const { selectedIndices, validOutputs } = nonMaxSuppressionV4Impl(boxesVals, scoresVals, maxOutputSize, iouThreshold, scoreThreshold, padToMaxOutputSize);
    return [
        backend.makeTensorInfo([selectedIndices.length], 'int32', new Int32Array(selectedIndices)),
        backend.makeTensorInfo([], 'int32', new Int32Array([validOutputs]))
    ];
}
export const nonMaxSuppressionV4Config = {
    kernelName: NonMaxSuppressionV4,
    backendName: 'cpu',
    kernelFunc: nonMaxSuppressionV4
};
//# sourceMappingURL=data:application/json;base64,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
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, Conv2DBackpropFilter, TensorBuffer } from '@tensorflow/tfjs-core';
import { assertNotComplex } from '../cpu_util';
export function conv2DBackpropFilter(args) {
    const { inputs, backend, attrs } = args;
    const { x, dy } = inputs;
    const { strides, pad, dataFormat, dimRoundingMode, filterShape } = attrs;
    assertNotComplex([x, dy], 'conv2dBackpropFilter');
    const $dataFormat = backend_util.convertConv2DDataFormat(dataFormat);
    const convInfo = backend_util.computeConv2DInfo(x.shape, filterShape, strides, 1 /* dilations */, pad, dimRoundingMode, false /* depthwise */, $dataFormat);
    const { strideHeight, strideWidth, filterHeight, filterWidth } = convInfo;
    const isChannelsLast = convInfo.dataFormat === 'channelsLast';
    const dW = new TensorBuffer(convInfo.filterShape, 'float32');
    const leftPad = convInfo.padInfo.left;
    const topPad = convInfo.padInfo.top;
    const xVals = backend.data.get(x.dataId).values;
    const dyVals = backend.data.get(dy.dataId).values;
    const xBuf = new TensorBuffer(x.shape, x.dtype, xVals);
    const dyBuf = new TensorBuffer(dy.shape, dy.dtype, dyVals);
    for (let wR = 0; wR < filterHeight; ++wR) {
        const yRMin = Math.max(0, Math.ceil((topPad - wR) / strideHeight));
        const yRMax = Math.min(convInfo.outHeight, (convInfo.inHeight + topPad - wR) / strideHeight);
        for (let wC = 0; wC < filterWidth; ++wC) {
            const yCMin = Math.max(0, Math.ceil((leftPad - wC) / strideWidth));
            const yCMax = Math.min(convInfo.outWidth, (convInfo.inWidth + leftPad - wC) / strideWidth);
            for (let d1 = 0; d1 < convInfo.inChannels; ++d1) {
                for (let d2 = 0; d2 < convInfo.outChannels; ++d2) {
                    let dotProd = 0;
                    for (let b = 0; b < convInfo.batchSize; ++b) {
                        for (let yR = yRMin; yR < yRMax; ++yR) {
                            const xR = wR + yR * strideHeight - topPad;
                            for (let yC = yCMin; yC < yCMax; ++yC) {
                                const xC = wC + yC * strideWidth - leftPad;
                                if (isChannelsLast) {
                                    dotProd += xBuf.get(b, xR, xC, d1) *
                                        dyBuf.get(b, yR, yC, d2);
                                }
                                else {
                                    dotProd += xBuf.get(b, d1, xR, xC) *
                                        dyBuf.get(b, d2, yR, yC);
                                }
                            }
                        }
                    }
                    dW.set(dotProd, wR, wC, d1, d2);
                }
            }
        }
    }
    return backend.makeTensorInfo(dW.shape, dW.dtype, dW.values);
}
export const conv2DBackpropFilterConfig = {
    kernelName: Conv2DBackpropFilter,
    backendName: 'cpu',
    kernelFunc: conv2DBackpropFilter
};
//# sourceMappingURL=data:application/json;base64,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
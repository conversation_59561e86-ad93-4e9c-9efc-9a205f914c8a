/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
// Shared functionality among backends.
export { simpleAbsImpl } from './kernels/Abs';
export { addImpl } from './kernels/Add';
export { bincountImpl, bincountReduceImpl } from './kernels/Bincount_impl';
export { castImpl } from './kernels/Cast';
export { ceilImpl } from './kernels/Ceil';
export { concatImpl } from './kernels/Concat_impl';
export { equalImpl } from './kernels/Equal';
export { expImpl } from './kernels/Exp';
export { expm1Impl } from './kernels/Expm1';
export { floorImpl } from './kernels/Floor';
export { gatherNdImpl } from './kernels/GatherNd_Impl';
export { gatherV2Impl } from './kernels/GatherV2_impl';
export { greaterImpl } from './kernels/Greater';
export { greaterEqualImpl } from './kernels/GreaterEqual';
export { lessImpl } from './kernels/Less';
export { lessEqualImpl } from './kernels/LessEqual';
export { linSpaceImpl } from './kernels/LinSpace_impl';
export { logImpl } from './kernels/Log';
export { maxImpl } from './kernels/Max_impl';
export { maximumImpl } from './kernels/Maximum';
export { minimumImpl } from './kernels/Minimum';
export { multiplyImpl } from './kernels/Multiply';
export { negImpl } from './kernels/Neg';
export { notEqualImpl } from './kernels/NotEqual';
export { prodImpl } from './kernels/Prod';
export { raggedGatherImpl } from './kernels/RaggedGather_impl';
export { raggedTensorToTensorImpl } from './kernels/RaggedTensorToTensor_impl';
export { rangeImpl } from './kernels/Range_impl';
export { rsqrtImpl } from './kernels/Rsqrt';
export { scatterImpl } from './kernels/Scatter_impl';
export { sigmoidImpl } from './kernels/Sigmoid';
export { sliceImpl } from './kernels/Slice';
export { sparseFillEmptyRowsImpl } from './kernels/SparseFillEmptyRows_impl';
export { sparseReshapeImpl } from './kernels/SparseReshape_impl';
export { sparseSegmentReductionImpl } from './kernels/SparseSegmentReduction_impl';
export { sqrtImpl } from './kernels/Sqrt';
export { squaredDifferenceImpl } from './kernels/SquaredDifference';
export { stridedSliceImpl } from './kernels/StridedSlice_impl';
export { stringNGramsImpl } from './kernels/StringNGrams_impl';
export { stringSplitImpl } from './kernels/StringSplit_impl';
export { stringToHashBucketFastImpl } from './kernels/StringToHashBucketFast_impl';
export { subImpl } from './kernels/Sub';
export { tileImpl } from './kernels/Tile_impl';
export { topKImpl } from './kernels/TopK_impl';
export { transposeImpl } from './kernels/Transpose_impl';
export { uniqueImpl } from './kernels/Unique_impl';
//# sourceMappingURL=data:application/json;base64,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
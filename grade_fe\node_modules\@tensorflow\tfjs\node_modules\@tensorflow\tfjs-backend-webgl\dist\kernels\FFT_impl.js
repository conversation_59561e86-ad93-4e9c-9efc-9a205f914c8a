/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { util } from '@tensorflow/tfjs-core';
import { FFTProgram } from '../fft_gpu';
import { complex } from './Complex';
import { reshape } from './Reshape';
export function fftImpl(x, inverse, backend) {
    const xData = backend.texData.get(x.dataId);
    const inputSize = util.sizeFromShape(x.shape);
    // Collapse all outer dimensions to a single batch dimension.
    const innerDimensionSize = x.shape[x.shape.length - 1];
    const batch = inputSize / innerDimensionSize;
    const input2D = reshape({ inputs: { x }, backend, attrs: { shape: [batch, innerDimensionSize] } });
    const xShape = input2D.shape;
    const realProgram = new FFTProgram('real', xShape, inverse);
    const imagProgram = new FFTProgram('imag', xShape, inverse);
    const inputs = [
        {
            dataId: xData.complexTensorInfos.real.dataId,
            dtype: xData.complexTensorInfos.real.dtype,
            shape: xShape
        },
        {
            dataId: xData.complexTensorInfos.imag.dataId,
            dtype: xData.complexTensorInfos.imag.dtype,
            shape: xShape
        }
    ];
    const realPart = backend.runWebGLProgram(realProgram, inputs, 'float32');
    const imagPart = backend.runWebGLProgram(imagProgram, inputs, 'float32');
    const complexOutput = complex({ inputs: { real: realPart, imag: imagPart }, backend });
    backend.disposeIntermediateTensorInfo(realPart);
    backend.disposeIntermediateTensorInfo(imagPart);
    const complexOutputReshaped = reshape({ inputs: { x: complexOutput }, backend, attrs: { shape: x.shape } });
    backend.disposeIntermediateTensorInfo(input2D);
    backend.disposeIntermediateTensorInfo(complexOutput);
    return complexOutputReshaped;
}
//# sourceMappingURL=data:application/json;base64,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
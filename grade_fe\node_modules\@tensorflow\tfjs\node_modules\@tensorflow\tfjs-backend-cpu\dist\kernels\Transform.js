/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { Transform, util } from '@tensorflow/tfjs-core';
export function transform(args) {
    const { inputs, attrs, backend } = args;
    const { image, transforms } = inputs;
    const { interpolation, fillMode, fillValue, outputShape } = attrs;
    const [batch, imageHeight, imageWidth, numChannels] = image.shape;
    const [outHeight, outWidth] = outputShape != null ? outputShape : [imageHeight, imageWidth];
    const outShape = [batch, outHeight, outWidth, numChannels];
    const inStrides = util.computeStrides(image.shape);
    const batchInStride = inStrides[0];
    const rowInStride = inStrides[1];
    const colInStride = inStrides[2];
    const outStrides = util.computeStrides(outShape);
    const batchOutStride = outStrides[0];
    const rowOutStride = outStrides[1];
    const colOutStride = outStrides[2];
    const outVals = util.getTypedArrayFromDType(image.dtype, util.sizeFromShape(outShape));
    outVals.fill(fillValue);
    const imageVals = backend.data.get(image.dataId).values;
    const transformVals = backend.data.get(transforms.dataId).values;
    // Ref TF implementation:
    // https://github.com/tensorflow/tensorflow/blob/master/tensorflow/core/kernels/image/image_ops.h
    for (let b = 0; b < batch; ++b) {
        const transform = transforms.shape[0] === 1 ?
            transformVals :
            transformVals.subarray(b * 8, b * 8 + 8);
        for (let outY = 0; outY < outHeight; ++outY) {
            for (let outX = 0; outX < outWidth; ++outX) {
                for (let channel = 0; channel < numChannels; ++channel) {
                    let val;
                    const projection = transform[6] * outX + transform[7] * outY + 1;
                    if (projection === 0) {
                        // Return the fill value for infinite coordinates,
                        // which are outside the input image
                        continue;
                    }
                    const inX = (transform[0] * outX + transform[1] * outY + transform[2]) /
                        projection;
                    const inY = (transform[3] * outX + transform[4] * outY + transform[5]) /
                        projection;
                    const x = mapCoord(inX, imageWidth, fillMode);
                    const y = mapCoord(inY, imageHeight, fillMode);
                    switch (interpolation) {
                        case 'nearest':
                            val = nearestInterpolation(imageVals, imageHeight, imageWidth, batchInStride, rowInStride, colInStride, b, y, x, channel, fillValue);
                            break;
                        case 'bilinear':
                            val = bilinearInterpolation(imageVals, imageHeight, imageWidth, batchInStride, rowInStride, colInStride, b, y, x, channel, fillValue);
                            break;
                        default:
                            throw new Error(`Error in Transform: Expect 'nearest' or ` +
                                `'bilinear', but got ${interpolation}`);
                    }
                    const ind = b * batchOutStride + outY * rowOutStride +
                        outX * colOutStride + channel;
                    outVals[ind] = val;
                }
            }
        }
        return backend.makeTensorInfo(outShape, image.dtype, outVals);
    }
    const dataId = backend.write(outVals, outShape, image.dtype);
    return { dataId, shape: image.shape, dtype: image.dtype };
}
export const transformConfig = {
    kernelName: Transform,
    backendName: 'cpu',
    kernelFunc: transform
};
function mapCoord(outCoord, len, mode) {
    switch (mode) {
        case 'reflect':
            return mapCoordReflect(outCoord, len);
        case 'wrap':
            return mapCoordWrap(outCoord, len);
        case 'nearest':
            return mapCoordNearest(outCoord, len);
        case 'constant':
        default:
            return mapCoordConstant(outCoord, len);
    }
}
function mapCoordReflect(outCoord, len) {
    // Reflect [abcd] to [dcba|abcd|dcba].
    let inCoord = outCoord;
    if (inCoord < 0) {
        if (len <= 1) {
            inCoord = 0;
        }
        else {
            const sz2 = 2 * len;
            if (inCoord < sz2) {
                inCoord = sz2 * Math.trunc(-inCoord / sz2) + inCoord;
            }
            inCoord = inCoord < -len ? inCoord + sz2 : -inCoord - 1;
        }
    }
    else if (inCoord > len - 1) {
        if (len <= 1) {
            inCoord = 0;
        }
        else {
            const sz2 = 2 * len;
            inCoord -= sz2 * Math.trunc(inCoord / sz2);
            if (inCoord >= len) {
                inCoord = sz2 - inCoord - 1;
            }
        }
    }
    // clamp is necessary because when outCoord = 3.5 and len = 4,
    // inCoord = 3.5 and will be rounded to 4 in nearest interpolation.
    return util.clamp(0, inCoord, len - 1);
}
function mapCoordWrap(outCoord, len) {
    // Wrap [abcd] to [abcd|abcd|abcd].
    let inCoord = outCoord;
    if (inCoord < 0) {
        if (len <= 1) {
            inCoord = 0;
        }
        else {
            const sz = len - 1;
            inCoord += len * (Math.trunc(-inCoord / sz) + 1);
        }
    }
    else if (inCoord > len - 1) {
        if (len <= 1) {
            inCoord = 0;
        }
        else {
            const sz = len - 1;
            inCoord -= len * Math.trunc(inCoord / sz);
        }
    }
    // clamp is necessary because when outCoord = -0.5 and len = 4,
    // inCoord = 3.5 and will be rounded to 4 in nearest interpolation.
    return util.clamp(0, inCoord, len - 1);
}
function mapCoordConstant(outCoord, len) {
    return outCoord;
}
function mapCoordNearest(outCoord, len) {
    return util.clamp(0, outCoord, len - 1);
}
function readWithFillValue(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, y, x, channel, fillValue) {
    const ind = batch * batchStride + y * rowStride + x * colStride + channel;
    if (0 <= y && y < imageHeight && 0 <= x && x < imageWidth) {
        return imageVals[ind];
    }
    else {
        return fillValue;
    }
}
function nearestInterpolation(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, y, x, channel, fillValue) {
    const $y = Math.round(y);
    const $x = Math.round(x);
    return readWithFillValue(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, $y, $x, channel, fillValue);
}
function bilinearInterpolation(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, y, x, channel, fillValue) {
    const yFloor = Math.floor(y);
    const xFloor = Math.floor(x);
    const yCeil = yFloor + 1;
    const xCeil = xFloor + 1;
    // f(x, yFloor) = (xCeil - x) / (xCeil - xFloor) * f(xFloor, yFloor)
    //               + (x - xFloor) / (xCeil - xFloor) * f(xCeil, yFloor)
    const valueYFloor = (xCeil - x) *
        readWithFillValue(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, yFloor, xFloor, channel, fillValue) +
        (x - xFloor) *
            readWithFillValue(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, yFloor, xCeil, channel, fillValue);
    // f(x, yCeil) = (xCeil - x) / (xCeil - xFloor) * f(xFloor, yCeil)
    //             + (x - xFloor) / (xCeil - xFloor) * f(xCeil, yCeil)
    const valueYCeil = (xCeil - x) *
        readWithFillValue(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, yCeil, xFloor, channel, fillValue) +
        (x - xFloor) *
            readWithFillValue(imageVals, imageHeight, imageWidth, batchStride, rowStride, colStride, batch, yCeil, xCeil, channel, fillValue);
    // f(x, y) = (yCeil - y) / (yCeil - yFloor) * f(x, yFloor)
    //         + (y - yFloor) / (yCeil - yFloor) * f(x, yCeil)
    return (yCeil - y) * valueYFloor + (y - yFloor) * valueYCeil;
}
//# sourceMappingURL=data:application/json;base64,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
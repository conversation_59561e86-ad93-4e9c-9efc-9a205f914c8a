/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, Dilation2D, util } from '@tensorflow/tfjs-core';
export const dilation2DConfig = {
    kernelName: Dilation2D,
    backendName: 'cpu',
    kernelFunc: ({ inputs, backend, attrs }) => {
        const { x, filter } = inputs;
        const { strides, pad, dilations } = attrs;
        const cpuBackend = backend;
        const xVals = cpuBackend.data.get(x.dataId).values;
        const xRank = x.shape.length;
        const filterVals = cpuBackend.data.get(filter.dataId).values;
        const filterRank = filter.shape.length;
        const { batchSize, inHeight, inWidth, inChannels, outHeight, outWidth, padInfo, strideHeight, strideWidth, filterHeight, filterWidth, dilationHeight, dilationWidth, outShape } = backend_util.computeDilation2DInfo(x.shape, filter.shape, strides, pad, 'NHWC' /* dataFormat */, dilations);
        const outSize = util.sizeFromShape(outShape);
        const outRank = outShape.length;
        const outputVals = util.getArrayFromDType(x.dtype, outSize);
        // Upsampling the input by fill in `dilation size - 1` values between each
        // input value.
        // This implementation follows the TF c++ implementation:
        // https://github.com/tensorflow/tensorflow/blob/d9a3a849edc198e90172bc58eb293de457f9d986/tensorflow/core/kernels/dilation_ops.cc
        for (let b = 0; b < batchSize; ++b) {
            for (let hOut = 0; hOut < outHeight; ++hOut) {
                const hBeg = hOut * strideHeight - padInfo.top;
                for (let wOut = 0; wOut < outWidth; ++wOut) {
                    const wBeg = wOut * strideWidth - padInfo.left;
                    for (let d = 0; d < inChannels; ++d) {
                        let curVal = Number.MIN_SAFE_INTEGER;
                        for (let h = 0; h < filterHeight; ++h) {
                            const hIn = hBeg + h * dilationHeight;
                            if (hIn >= 0 && hIn < inHeight) {
                                for (let w = 0; w < filterWidth; ++w) {
                                    const wIn = wBeg + w * dilationWidth;
                                    if (wIn >= 0 && wIn < inWidth) {
                                        const xIndex = util.locToIndex([b, hIn, wIn, d], xRank, util.computeStrides(x.shape));
                                        const filterIndex = util.locToIndex([h, w, d], filterRank, util.computeStrides(filter.shape));
                                        const val = xVals[xIndex] + filterVals[filterIndex];
                                        if (val > curVal) {
                                            curVal = val;
                                        }
                                    }
                                }
                            }
                        }
                        const outputIndex = util.locToIndex([b, hOut, wOut, d], outRank, util.computeStrides(outShape));
                        outputVals[outputIndex] = curVal;
                    }
                }
            }
        }
        const dataId = cpuBackend.write(util.toTypedArray(outputVals, x.dtype), outShape, x.dtype);
        return { dataId, shape: outShape, dtype: x.dtype };
    }
};
//# sourceMappingURL=data:application/json;base64,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
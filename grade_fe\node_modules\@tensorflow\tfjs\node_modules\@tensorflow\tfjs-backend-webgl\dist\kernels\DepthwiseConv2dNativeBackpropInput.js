/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, DepthwiseConv2dNativeBackpropInput } from '@tensorflow/tfjs-core';
import { DepthwiseConv2DDerInputProgram } from '../conv_backprop_gpu_depthwise';
export function depthwiseConv2dNativeBackpropInput(args) {
    const { inputs, backend, attrs } = args;
    const { dy, filter } = inputs;
    const { strides, dilations, pad, dimRoundingMode, inputShape } = attrs;
    const convInfo = backend_util.computeConv2DInfo(inputShape, filter.shape, strides, dilations, pad, dimRoundingMode, true /* depthwise */);
    const program = new DepthwiseConv2DDerInputProgram(convInfo);
    return backend.runWebGLProgram(program, [dy, filter], 'float32');
}
export const depthwiseConv2dNativeBackpropInputConfig = {
    kernelName: DepthwiseConv2dNativeBackpropInput,
    backendName: 'webgl',
    kernelFunc: depthwiseConv2dNativeBackpropInput
};
//# sourceMappingURL=data:application/json;base64,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
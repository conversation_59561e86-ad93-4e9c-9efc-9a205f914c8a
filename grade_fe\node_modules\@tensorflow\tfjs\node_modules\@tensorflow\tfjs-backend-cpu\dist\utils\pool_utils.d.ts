/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-backend-cpu/dist/utils/pool_utils" />
import { backend_util, DataType, Rank, TensorBuffer, TypedArray } from '@tensorflow/tfjs-core';
export declare function pool(xValues: TypedArray, xShape: number[], dtype: DataType, strides: number[], convInfo: backend_util.Conv2DInfo, poolType: 'max' | 'avg'): TensorBuffer<Rank, DataType>;
export declare function maxPoolPositions(xValues: TypedArray, xShape: number[], dtype: DataType, convInfo: backend_util.Conv2DInfo, flattenPositions?: boolean, includeBatchInIndex?: boolean): TensorBuffer<Rank, 'int32'>;
export declare function pool3d(xValues: TypedArray, xShape: number[], dtype: DataType, strides: number[], convInfo: backend_util.Conv3DInfo, poolType: 'max' | 'avg'): TensorBuffer<Rank, DataType>;
export declare function maxPool3dPositions(xBuf: TensorBuffer<Rank, DataType>, convInfo: backend_util.Conv3DInfo): TensorBuffer<Rank, DataType>;

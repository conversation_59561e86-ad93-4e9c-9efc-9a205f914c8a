/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { DepthToSpace, util } from '@tensorflow/tfjs-core';
export function depthToSpace(args) {
    const { inputs, backend, attrs } = args;
    const { x } = inputs;
    const { blockSize, dataFormat } = attrs;
    util.assert(dataFormat === 'NHWC', () => `Only NHWC dataFormat supported on CPU for depthToSpace. Got ${dataFormat}`);
    const batchSize = x.shape[0];
    const inputHeight = x.shape[1];
    const inputWidth = x.shape[2];
    const inputDepth = x.shape[3];
    const outputHeight = inputHeight * blockSize;
    const outputWidth = inputWidth * blockSize;
    const outputDepth = inputDepth / (blockSize * blockSize);
    const xValues = backend.data.get(x.dataId).values;
    const result = new Float32Array(batchSize * outputHeight * outputWidth * outputDepth);
    let outputIdx = 0;
    for (let b = 0; b < batchSize; ++b) {
        for (let h = 0; h < outputHeight; ++h) {
            const inH = Math.floor(h / blockSize);
            const offsetH = (h % blockSize);
            for (let w = 0; w < outputWidth; ++w) {
                const inW = Math.floor(w / blockSize);
                const offsetW = (w % blockSize);
                const offsetD = (offsetH * blockSize + offsetW) * outputDepth;
                for (let d = 0; d < outputDepth; ++d) {
                    const inD = d + offsetD;
                    const inputIdx = inD + inputDepth * (inW + inputWidth * (inH + inputHeight * b));
                    result[outputIdx++] = xValues[inputIdx];
                }
            }
        }
    }
    return backend.makeTensorInfo([batchSize, outputHeight, outputWidth, outputDepth], x.dtype, result);
}
export const depthToSpaceConfig = {
    kernelName: DepthToSpace,
    backendName: 'cpu',
    kernelFunc: depthToSpace
};
//# sourceMappingURL=data:application/json;base64,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
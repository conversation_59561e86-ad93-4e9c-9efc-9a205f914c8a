/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, GatherNd, util } from '@tensorflow/tfjs-core';
import { GatherNDProgram } from '../gather_nd_gpu';
import { gatherNdImplCPU } from '../kernel_utils/shared';
import { reshape } from './Reshape';
export function gatherNd(args) {
    const { inputs, backend } = args;
    const { params, indices } = inputs;
    const indicesShape = indices.shape;
    const sliceRank = indicesShape[indicesShape.length - 1];
    const paramsSize = util.sizeFromShape(params.shape);
    const [resultShape, numSlices, sliceSize, strides] = backend_util.prepareAndValidate(params, indices);
    const flattenIndices = reshape({ inputs: { x: indices }, backend, attrs: { shape: [numSlices, sliceRank] } });
    const flattenX = reshape({
        inputs: { x: params },
        backend,
        attrs: { shape: [(util.sizeFromShape(params.shape) / sliceSize), sliceSize] }
    });
    if (backend.shouldExecuteOnCPU([params, indices]) ||
        params.dtype === 'string') {
        const indicesData = backend.readSync(indices.dataId);
        const paramsBuf = backend.bufferSync(params);
        const outValue = gatherNdImplCPU(indicesData, paramsBuf, params.dtype, numSlices, sliceRank, sliceSize, strides, params.shape, paramsSize);
        return backend.makeTensorInfo(resultShape, params.dtype, outValue.values);
    }
    const program = new GatherNDProgram(sliceRank, strides, [numSlices, sliceSize], params.shape);
    const res = backend.runWebGLProgram(program, [flattenX, flattenIndices], flattenX.dtype);
    const reshaped = reshape({ inputs: { x: res }, backend, attrs: { shape: resultShape } });
    backend.disposeIntermediateTensorInfo(flattenIndices);
    backend.disposeIntermediateTensorInfo(flattenX);
    backend.disposeIntermediateTensorInfo(res);
    return reshaped;
}
export const gatherNdConfig = {
    kernelName: GatherNd,
    backendName: 'webgl',
    kernelFunc: gatherNd
};
//# sourceMappingURL=data:application/json;base64,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
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { env, LeakyRelu, util } from '@tensorflow/tfjs-core';
import { BinaryOpProgram } from '../binaryop_gpu';
import { BinaryOpPackedProgram } from '../binaryop_packed_gpu';
export const LEAKYRELU = `return (a < 0.) ? b * a : a;`;
export const LEAKYRELU_PACKED = `
  vec4 aLessThanZero = vec4(lessThan(a, vec4(0.)));
  return (aLessThanZero * (b * a)) + ((vec4(1.0) - aLessThanZero) * a);
`;
export function leakyRelu(args) {
    const { inputs, backend, attrs } = args;
    const { x } = inputs;
    const { alpha } = attrs;
    const $alpha = backend.makeTensorInfo([], 'float32', util.createScalarValue(alpha, 'float32'));
    const program = env().getBool('WEBGL_PACK_BINARY_OPERATIONS') ?
        new BinaryOpPackedProgram(LEAKYRELU_PACKED, x.shape, $alpha.shape) :
        new BinaryOpProgram(LEAKYRELU, x.shape, $alpha.shape);
    const result = backend.runWebGLProgram(program, [x, $alpha], 'float32');
    backend.disposeIntermediateTensorInfo($alpha);
    return result;
}
export const leakyReluConfig = {
    kernelName: LeakyRelu,
    backendName: 'webgl',
    kernelFunc: leakyRelu
};
//# sourceMappingURL=data:application/json;base64,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
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, env, Multiply } from '@tensorflow/tfjs-core';
import * as binaryop_complex_gpu from '../binaryop_complex_gpu';
import { BinaryOpComplexProgram } from '../binaryop_complex_gpu';
import { BinaryOpProgram } from '../binaryop_gpu';
import { BinaryOpPackedProgram } from '../binaryop_packed_gpu';
import { multiplyImplCPU as cpuMultiply } from '../kernel_utils/shared';
import { complex } from './Complex';
const MUL = 'return a * b;';
export function multiply(args) {
    const { inputs, backend } = args;
    const { a, b } = inputs;
    const dtype = backend_util.upcastType(a.dtype, b.dtype);
    if (a.dtype === 'complex64') {
        const aData = backend.texData.get(a.dataId);
        const bData = backend.texData.get(b.dataId);
        const realProgram = new BinaryOpComplexProgram(binaryop_complex_gpu.COMPLEX_MULTIPLY.REAL, a.shape, b.shape);
        const imagProgram = new BinaryOpComplexProgram(binaryop_complex_gpu.COMPLEX_MULTIPLY.IMAG, a.shape, b.shape);
        const inputs = [
            {
                dataId: aData.complexTensorInfos.real.dataId,
                dtype: aData.complexTensorInfos.real.dtype,
                shape: a.shape
            },
            {
                dataId: aData.complexTensorInfos.imag.dataId,
                dtype: aData.complexTensorInfos.imag.dtype,
                shape: a.shape
            },
            {
                dataId: bData.complexTensorInfos.real.dataId,
                dtype: bData.complexTensorInfos.real.dtype,
                shape: b.shape
            },
            {
                dataId: bData.complexTensorInfos.imag.dataId,
                dtype: bData.complexTensorInfos.imag.dtype,
                shape: b.shape
            }
        ];
        const realPart = backend.runWebGLProgram(realProgram, inputs, 'float32');
        const imagPart = backend.runWebGLProgram(imagProgram, inputs, 'float32');
        const complexOutput = complex({ inputs: { real: realPart, imag: imagPart }, backend });
        backend.disposeIntermediateTensorInfo(realPart);
        backend.disposeIntermediateTensorInfo(imagPart);
        // TODO(annxingyuan): CPU forwarding for complex inputs.
        return complexOutput;
    }
    if (backend.shouldExecuteOnCPU([a, b])) {
        const aData = backend.texData.get(a.dataId);
        const bData = backend.texData.get(b.dataId);
        const [outValues, outShape] = cpuMultiply(a.shape, b.shape, aData.values, bData.values, dtype);
        const out = backend.makeTensorInfo(outShape, dtype);
        const outData = backend.texData.get(out.dataId);
        outData.values = outValues;
        return out;
    }
    let program;
    if (env().getBool('WEBGL_PACK_BINARY_OPERATIONS')) {
        program = new BinaryOpPackedProgram(MUL, a.shape, b.shape);
    }
    else {
        program = new BinaryOpProgram(MUL, a.shape, b.shape);
    }
    return backend.runWebGLProgram(program, [a, b], dtype);
}
export const multiplyConfig = {
    kernelName: Multiply,
    backendName: 'webgl',
    kernelFunc: multiply
};
//# sourceMappingURL=data:application/json;base64,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
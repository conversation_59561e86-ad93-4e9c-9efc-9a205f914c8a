/**
 * @license
 * Copyright 2022 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { RaggedTensorToTensor } from '@tensorflow/tfjs-core';
import { raggedTensorToTensorImpl } from './RaggedTensorToTensor_impl';
export function raggedTensorToTensor(args) {
    const { inputs, backend, attrs } = args;
    const { shape, values, defaultValue, rowPartitionTensors } = inputs;
    const { rowPartitionTypes } = attrs;
    const $shape = backend.data.get(shape.dataId).values;
    const $values = backend.data.get(values.dataId).values;
    const $defaultValue = backend.data.get(defaultValue.dataId).values;
    const $rowPartitionValues = rowPartitionTensors.map(t => backend.data.get(t.dataId).values);
    const rowPartitionValuesShapes = rowPartitionTensors.map(t => t.shape);
    const [outputShape, output] = raggedTensorToTensorImpl($shape, shape.shape, $values, values.shape, values.dtype, $defaultValue, defaultValue.shape, $rowPartitionValues, rowPartitionValuesShapes, rowPartitionTypes);
    return backend.makeTensorInfo(outputShape, values.dtype, output);
}
export const raggedTensorToTensorConfig = {
    kernelName: RaggedTensorToTensor,
    backendName: 'cpu',
    kernelFunc: raggedTensorToTensor,
};
//# sourceMappingURL=data:application/json;base64,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
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { DenseBincount } from '@tensorflow/tfjs-core';
import { bincountImpl, bincountReduceImpl } from './Bincount_impl';
export function denseBincount(args) {
    const { inputs, backend, attrs } = args;
    const { x, weights } = inputs;
    const { size, binaryOutput } = attrs;
    if (x.shape.length === 1) {
        const xVals = backend.data.get(x.dataId).values;
        const weightsVals = backend.data.get(weights.dataId).values;
        const outVals = bincountImpl(xVals, weightsVals, weights.dtype, weights.shape, size);
        return backend.makeTensorInfo([size], weights.dtype, outVals);
    }
    else if (x.shape.length === 2) {
        const xBuf = backend.bufferSync(x);
        const weightsBuf = backend.bufferSync(weights);
        const outBuf = bincountReduceImpl(xBuf, weightsBuf, size, binaryOutput);
        return backend.makeTensorInfo(outBuf.shape, weights.dtype, outBuf.values);
    }
    throw new Error(`Error in denseBincount: input must be at most rank 2, but got rank` +
        `${x.shape.length}.`);
}
export const denseBincountConfig = {
    kernelName: DenseBincount,
    backendName: 'cpu',
    kernelFunc: denseBincount
};
//# sourceMappingURL=data:application/json;base64,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
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, util } from '@tensorflow/tfjs-core';
export function concatImpl(inputs, outShape, dtype, simplyConcat) {
    const outVals = util.getArrayFromDType(dtype, util.sizeFromShape(outShape));
    if (simplyConcat && dtype !== 'string') {
        // Use built-in TypedArray.set() method for speed.
        let offset = 0;
        inputs.forEach(input => {
            const size = util.sizeFromShape(input.shape);
            outVals.set(input.vals, offset);
            offset += size;
        });
    }
    else {
        let colOffset = 0;
        inputs.forEach(input => {
            const decodedData = dtype === 'string' ?
                backend_util.fromUint8ToStringArray(input.vals) :
                input.vals;
            let tIdx = 0;
            for (let row = 0; row < input.shape[0]; ++row) {
                const resIdx = row * outShape[1] + colOffset;
                for (let col = 0; col < input.shape[1]; ++col) {
                    outVals[resIdx + col] = decodedData[tIdx++];
                }
            }
            colOffset += input.shape[1];
        });
    }
    return outVals;
}
//# sourceMappingURL=data:application/json;base64,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
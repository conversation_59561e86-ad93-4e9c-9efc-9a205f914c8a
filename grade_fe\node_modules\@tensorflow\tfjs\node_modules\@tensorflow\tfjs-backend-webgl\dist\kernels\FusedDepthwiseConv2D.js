/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, env, FusedDepthwiseConv2D, util } from '@tensorflow/tfjs-core';
import { DepthwiseConv2DProgram } from '../conv_gpu_depthwise';
import { DepthwiseConvPacked2DProgram } from '../conv_packed_gpu_depthwise';
import { mapActivationToShaderProgram } from '../kernel_utils/kernel_funcs_utils';
export function fusedDepthwiseConv2D(args) {
    const { inputs, backend, attrs } = args;
    const { x, filter, bias, preluActivationWeights } = inputs;
    const { strides, pad, dilations, dimRoundingMode, activation, leakyreluAlpha } = attrs;
    const intermediates = [];
    let $dilations = dilations;
    if ($dilations == null) {
        $dilations = [1, 1];
    }
    util.assert(backend_util.eitherStridesOrDilationsAreOne(strides, $dilations), () => 'Error in depthwiseConv2d: Either strides or dilations must be ' +
        `1. Got strides ${strides} and dilations '${$dilations}'`);
    const convInfo = backend_util.computeConv2DInfo(x.shape, filter.shape, strides, $dilations, pad, dimRoundingMode, true /* depthwise */);
    const shouldPackDepthwiseConv = env().getBool('WEBGL_PACK_DEPTHWISECONV') &&
        convInfo.strideWidth <= 2 &&
        convInfo.outChannels / convInfo.inChannels === 1;
    const fusedActivation = activation ?
        mapActivationToShaderProgram(activation, shouldPackDepthwiseConv) :
        null;
    const programInputs = [x, filter];
    const hasBias = bias != null;
    const hasPreluActivationWeights = preluActivationWeights != null;
    const hasLeakyreluAlpha = activation === 'leakyrelu';
    if (hasBias) {
        programInputs.push(bias);
    }
    if (hasPreluActivationWeights) {
        programInputs.push(preluActivationWeights);
    }
    if (hasLeakyreluAlpha) {
        const $leakyreluAlpha = backend.makeTensorInfo([], 'float32', util.createScalarValue(leakyreluAlpha, 'float32'));
        programInputs.push($leakyreluAlpha);
        intermediates.push($leakyreluAlpha);
    }
    let program;
    if (shouldPackDepthwiseConv) {
        program = new DepthwiseConvPacked2DProgram(convInfo, hasBias, fusedActivation, hasPreluActivationWeights, hasLeakyreluAlpha);
    }
    else {
        program = new DepthwiseConv2DProgram(convInfo, hasBias, fusedActivation, hasPreluActivationWeights, hasLeakyreluAlpha);
    }
    const customValues = [
        [convInfo.padInfo.top, convInfo.padInfo.left],
        [convInfo.strideHeight, convInfo.strideWidth],
        [convInfo.dilationHeight, convInfo.dilationWidth],
        [convInfo.inHeight, convInfo.inWidth]
    ];
    const result = backend.runWebGLProgram(program, programInputs, 'float32', customValues);
    intermediates.forEach(t => backend.disposeIntermediateTensorInfo(t));
    return result;
}
export const fusedDepthwiseConv2DConfig = {
    kernelName: FusedDepthwiseConv2D,
    backendName: 'webgl',
    kernelFunc: fusedDepthwiseConv2D,
};
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiRnVzZWREZXB0aHdpc2VDb252MkQuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi90ZmpzLWJhY2tlbmQtd2ViZ2wvc3JjL2tlcm5lbHMvRnVzZWREZXB0aHdpc2VDb252MkQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7Ozs7Ozs7Ozs7OztHQWVHO0FBRUgsT0FBTyxFQUFDLFlBQVksRUFBRSxHQUFHLEVBQUUsb0JBQW9CLEVBQStGLElBQUksRUFBQyxNQUFNLHVCQUF1QixDQUFDO0FBR2pMLE9BQU8sRUFBQyxzQkFBc0IsRUFBQyxNQUFNLHVCQUF1QixDQUFDO0FBQzdELE9BQU8sRUFBQyw0QkFBNEIsRUFBQyxNQUFNLDhCQUE4QixDQUFDO0FBQzFFLE9BQU8sRUFBQyw0QkFBNEIsRUFBQyxNQUFNLG9DQUFvQyxDQUFDO0FBRWhGLE1BQU0sVUFBVSxvQkFBb0IsQ0FBQyxJQUlwQztJQUNDLE1BQU0sRUFBQyxNQUFNLEVBQUUsT0FBTyxFQUFFLEtBQUssRUFBQyxHQUFHLElBQUksQ0FBQztJQUN0QyxNQUFNLEVBQUMsQ0FBQyxFQUFFLE1BQU0sRUFBRSxJQUFJLEVBQUUsc0JBQXNCLEVBQUMsR0FBRyxNQUFNLENBQUM7SUFDekQsTUFBTSxFQUFDLE9BQU8sRUFBRSxHQUFHLEVBQUUsU0FBUyxFQUFFLGVBQWUsRUFBRSxVQUFVLEVBQUUsY0FBYyxFQUFDLEdBQ3hFLEtBQUssQ0FBQztJQUVWLE1BQU0sYUFBYSxHQUFpQixFQUFFLENBQUM7SUFFdkMsSUFBSSxVQUFVLEdBQUcsU0FBUyxDQUFDO0lBQzNCLElBQUksVUFBVSxJQUFJLElBQUksRUFBRTtRQUN0QixVQUFVLEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7S0FDckI7SUFFRCxJQUFJLENBQUMsTUFBTSxDQUNQLFlBQVksQ0FBQyw4QkFBOEIsQ0FBQyxPQUFPLEVBQUUsVUFBVSxDQUFDLEVBQ2hFLEdBQUcsRUFBRSxDQUFDLGdFQUFnRTtRQUNsRSxrQkFBa0IsT0FBTyxtQkFBbUIsVUFBVSxHQUFHLENBQUMsQ0FBQztJQUVuRSxNQUFNLFFBQVEsR0FBRyxZQUFZLENBQUMsaUJBQWlCLENBQzNDLENBQUMsQ0FBQyxLQUF5QyxFQUMzQyxNQUFNLENBQUMsS0FBeUMsRUFBRSxPQUFPLEVBQUUsVUFBVSxFQUNyRSxHQUFHLEVBQUUsZUFBZSxFQUFFLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztJQUVoRCxNQUFNLHVCQUF1QixHQUFHLEdBQUcsRUFBRSxDQUFDLE9BQU8sQ0FBQywwQkFBMEIsQ0FBQztRQUNyRSxRQUFRLENBQUMsV0FBVyxJQUFJLENBQUM7UUFDekIsUUFBUSxDQUFDLFdBQVcsR0FBRyxRQUFRLENBQUMsVUFBVSxLQUFLLENBQUMsQ0FBQztJQUNyRCxNQUFNLGVBQWUsR0FBRyxVQUFVLENBQUMsQ0FBQztRQUNoQyw0QkFBNEIsQ0FBQyxVQUFVLEVBQUUsdUJBQXVCLENBQUMsQ0FBQyxDQUFDO1FBQ25FLElBQUksQ0FBQztJQUNULE1BQU0sYUFBYSxHQUFpQixDQUFDLENBQUMsRUFBRSxNQUFNLENBQUMsQ0FBQztJQUVoRCxNQUFNLE9BQU8sR0FBRyxJQUFJLElBQUksSUFBSSxDQUFDO0lBQzdCLE1BQU0seUJBQXlCLEdBQUcsc0JBQXNCLElBQUksSUFBSSxDQUFDO0lBQ2pFLE1BQU0saUJBQWlCLEdBQUcsVUFBVSxLQUFLLFdBQVcsQ0FBQztJQUVyRCxJQUFJLE9BQU8sRUFBRTtRQUNYLGFBQWEsQ0FBQyxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7S0FDMUI7SUFDRCxJQUFJLHlCQUF5QixFQUFFO1FBQzdCLGFBQWEsQ0FBQyxJQUFJLENBQUMsc0JBQXNCLENBQUMsQ0FBQztLQUM1QztJQUNELElBQUksaUJBQWlCLEVBQUU7UUFDckIsTUFBTSxlQUFlLEdBQUcsT0FBTyxDQUFDLGNBQWMsQ0FDMUMsRUFBRSxFQUFFLFNBQVMsRUFDYixJQUFJLENBQUMsaUJBQWlCLENBQUMsY0FBaUMsRUFBRSxTQUFTLENBQUMsQ0FBQyxDQUFDO1FBQzFFLGFBQWEsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQUM7UUFDcEMsYUFBYSxDQUFDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FBQztLQUNyQztJQUVELElBQUksT0FBNEQsQ0FBQztJQUNqRSxJQUFJLHVCQUF1QixFQUFFO1FBQzNCLE9BQU8sR0FBRyxJQUFJLDRCQUE0QixDQUN0QyxRQUFRLEVBQUUsT0FBTyxFQUFFLGVBQWUsRUFBRSx5QkFBeUIsRUFDN0QsaUJBQWlCLENBQUMsQ0FBQztLQUN4QjtTQUFNO1FBQ0wsT0FBTyxHQUFHLElBQUksc0JBQXNCLENBQ2hDLFFBQVEsRUFBRSxPQUFPLEVBQUUsZUFBZSxFQUFFLHlCQUF5QixFQUM3RCxpQkFBaUIsQ0FBQyxDQUFDO0tBQ3hCO0lBQ0QsTUFBTSxZQUFZLEdBQUc7UUFDbkIsQ0FBQyxRQUFRLENBQUMsT0FBTyxDQUFDLEdBQUcsRUFBRSxRQUFRLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQztRQUM3QyxDQUFDLFFBQVEsQ0FBQyxZQUFZLEVBQUUsUUFBUSxDQUFDLFdBQVcsQ0FBQztRQUM3QyxDQUFDLFFBQVEsQ0FBQyxjQUFjLEVBQUUsUUFBUSxDQUFDLGFBQWEsQ0FBQztRQUNqRCxDQUFDLFFBQVEsQ0FBQyxRQUFRLEVBQUUsUUFBUSxDQUFDLE9BQU8sQ0FBQztLQUN0QyxDQUFDO0lBQ0YsTUFBTSxNQUFNLEdBQ1IsT0FBTyxDQUFDLGVBQWUsQ0FBQyxPQUFPLEVBQUUsYUFBYSxFQUFFLFNBQVMsRUFBRSxZQUFZLENBQUMsQ0FBQztJQUU3RSxhQUFhLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsT0FBTyxDQUFDLDZCQUE2QixDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFFckUsT0FBTyxNQUFNLENBQUM7QUFDaEIsQ0FBQztBQUVELE1BQU0sQ0FBQyxNQUFNLDBCQUEwQixHQUFpQjtJQUN0RCxVQUFVLEVBQUUsb0JBQW9CO0lBQ2hDLFdBQVcsRUFBRSxPQUFPO0lBQ3BCLFVBQVUsRUFBRSxvQkFBd0M7Q0FDckQsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCAyMDIwIEdvb2dsZSBMTEMuIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gKi9cblxuaW1wb3J0IHtiYWNrZW5kX3V0aWwsIGVudiwgRnVzZWREZXB0aHdpc2VDb252MkQsIEZ1c2VkRGVwdGh3aXNlQ29udjJEQXR0cnMsIEZ1c2VkRGVwdGh3aXNlQ29udjJESW5wdXRzLCBLZXJuZWxDb25maWcsIEtlcm5lbEZ1bmMsIFRlbnNvckluZm8sIHV0aWx9IGZyb20gJ0B0ZW5zb3JmbG93L3RmanMtY29yZSc7XG5cbmltcG9ydCB7TWF0aEJhY2tlbmRXZWJHTH0gZnJvbSAnLi4vYmFja2VuZF93ZWJnbCc7XG5pbXBvcnQge0RlcHRod2lzZUNvbnYyRFByb2dyYW19IGZyb20gJy4uL2NvbnZfZ3B1X2RlcHRod2lzZSc7XG5pbXBvcnQge0RlcHRod2lzZUNvbnZQYWNrZWQyRFByb2dyYW19IGZyb20gJy4uL2NvbnZfcGFja2VkX2dwdV9kZXB0aHdpc2UnO1xuaW1wb3J0IHttYXBBY3RpdmF0aW9uVG9TaGFkZXJQcm9ncmFtfSBmcm9tICcuLi9rZXJuZWxfdXRpbHMva2VybmVsX2Z1bmNzX3V0aWxzJztcblxuZXhwb3J0IGZ1bmN0aW9uIGZ1c2VkRGVwdGh3aXNlQ29udjJEKGFyZ3M6IHtcbiAgaW5wdXRzOiBGdXNlZERlcHRod2lzZUNvbnYyRElucHV0cyxcbiAgYXR0cnM6IEZ1c2VkRGVwdGh3aXNlQ29udjJEQXR0cnMsXG4gIGJhY2tlbmQ6IE1hdGhCYWNrZW5kV2ViR0xcbn0pIHtcbiAgY29uc3Qge2lucHV0cywgYmFja2VuZCwgYXR0cnN9ID0gYXJncztcbiAgY29uc3Qge3gsIGZpbHRlciwgYmlhcywgcHJlbHVBY3RpdmF0aW9uV2VpZ2h0c30gPSBpbnB1dHM7XG4gIGNvbnN0IHtzdHJpZGVzLCBwYWQsIGRpbGF0aW9ucywgZGltUm91bmRpbmdNb2RlLCBhY3RpdmF0aW9uLCBsZWFreXJlbHVBbHBoYX0gPVxuICAgICAgYXR0cnM7XG5cbiAgY29uc3QgaW50ZXJtZWRpYXRlczogVGVuc29ySW5mb1tdID0gW107XG5cbiAgbGV0ICRkaWxhdGlvbnMgPSBkaWxhdGlvbnM7XG4gIGlmICgkZGlsYXRpb25zID09IG51bGwpIHtcbiAgICAkZGlsYXRpb25zID0gWzEsIDFdO1xuICB9XG5cbiAgdXRpbC5hc3NlcnQoXG4gICAgICBiYWNrZW5kX3V0aWwuZWl0aGVyU3RyaWRlc09yRGlsYXRpb25zQXJlT25lKHN0cmlkZXMsICRkaWxhdGlvbnMpLFxuICAgICAgKCkgPT4gJ0Vycm9yIGluIGRlcHRod2lzZUNvbnYyZDogRWl0aGVyIHN0cmlkZXMgb3IgZGlsYXRpb25zIG11c3QgYmUgJyArXG4gICAgICAgICAgYDEuIEdvdCBzdHJpZGVzICR7c3RyaWRlc30gYW5kIGRpbGF0aW9ucyAnJHskZGlsYXRpb25zfSdgKTtcblxuICBjb25zdCBjb252SW5mbyA9IGJhY2tlbmRfdXRpbC5jb21wdXRlQ29udjJESW5mbyhcbiAgICAgIHguc2hhcGUgYXMgW251bWJlciwgbnVtYmVyLCBudW1iZXIsIG51bWJlcl0sXG4gICAgICBmaWx0ZXIuc2hhcGUgYXMgW251bWJlciwgbnVtYmVyLCBudW1iZXIsIG51bWJlcl0sIHN0cmlkZXMsICRkaWxhdGlvbnMsXG4gICAgICBwYWQsIGRpbVJvdW5kaW5nTW9kZSwgdHJ1ZSAvKiBkZXB0aHdpc2UgKi8pO1xuXG4gIGNvbnN0IHNob3VsZFBhY2tEZXB0aHdpc2VDb252ID0gZW52KCkuZ2V0Qm9vbCgnV0VCR0xfUEFDS19ERVBUSFdJU0VDT05WJykgJiZcbiAgICAgIGNvbnZJbmZvLnN0cmlkZVdpZHRoIDw9IDIgJiZcbiAgICAgIGNvbnZJbmZvLm91dENoYW5uZWxzIC8gY29udkluZm8uaW5DaGFubmVscyA9PT0gMTtcbiAgY29uc3QgZnVzZWRBY3RpdmF0aW9uID0gYWN0aXZhdGlvbiA/XG4gICAgICBtYXBBY3RpdmF0aW9uVG9TaGFkZXJQcm9ncmFtKGFjdGl2YXRpb24sIHNob3VsZFBhY2tEZXB0aHdpc2VDb252KSA6XG4gICAgICBudWxsO1xuICBjb25zdCBwcm9ncmFtSW5wdXRzOiBUZW5zb3JJbmZvW10gPSBbeCwgZmlsdGVyXTtcblxuICBjb25zdCBoYXNCaWFzID0gYmlhcyAhPSBudWxsO1xuICBjb25zdCBoYXNQcmVsdUFjdGl2YXRpb25XZWlnaHRzID0gcHJlbHVBY3RpdmF0aW9uV2VpZ2h0cyAhPSBudWxsO1xuICBjb25zdCBoYXNMZWFreXJlbHVBbHBoYSA9IGFjdGl2YXRpb24gPT09ICdsZWFreXJlbHUnO1xuXG4gIGlmIChoYXNCaWFzKSB7XG4gICAgcHJvZ3JhbUlucHV0cy5wdXNoKGJpYXMpO1xuICB9XG4gIGlmIChoYXNQcmVsdUFjdGl2YXRpb25XZWlnaHRzKSB7XG4gICAgcHJvZ3JhbUlucHV0cy5wdXNoKHByZWx1QWN0aXZhdGlvbldlaWdodHMpO1xuICB9XG4gIGlmIChoYXNMZWFreXJlbHVBbHBoYSkge1xuICAgIGNvbnN0ICRsZWFreXJlbHVBbHBoYSA9IGJhY2tlbmQubWFrZVRlbnNvckluZm8oXG4gICAgICAgIFtdLCAnZmxvYXQzMicsXG4gICAgICAgIHV0aWwuY3JlYXRlU2NhbGFyVmFsdWUobGVha3lyZWx1QWxwaGEgYXMge30gYXMgJ2Zsb2F0MzInLCAnZmxvYXQzMicpKTtcbiAgICBwcm9ncmFtSW5wdXRzLnB1c2goJGxlYWt5cmVsdUFscGhhKTtcbiAgICBpbnRlcm1lZGlhdGVzLnB1c2goJGxlYWt5cmVsdUFscGhhKTtcbiAgfVxuXG4gIGxldCBwcm9ncmFtOiBEZXB0aHdpc2VDb252MkRQcm9ncmFtfERlcHRod2lzZUNvbnZQYWNrZWQyRFByb2dyYW07XG4gIGlmIChzaG91bGRQYWNrRGVwdGh3aXNlQ29udikge1xuICAgIHByb2dyYW0gPSBuZXcgRGVwdGh3aXNlQ29udlBhY2tlZDJEUHJvZ3JhbShcbiAgICAgICAgY29udkluZm8sIGhhc0JpYXMsIGZ1c2VkQWN0aXZhdGlvbiwgaGFzUHJlbHVBY3RpdmF0aW9uV2VpZ2h0cyxcbiAgICAgICAgaGFzTGVha3lyZWx1QWxwaGEpO1xuICB9IGVsc2Uge1xuICAgIHByb2dyYW0gPSBuZXcgRGVwdGh3aXNlQ29udjJEUHJvZ3JhbShcbiAgICAgICAgY29udkluZm8sIGhhc0JpYXMsIGZ1c2VkQWN0aXZhdGlvbiwgaGFzUHJlbHVBY3RpdmF0aW9uV2VpZ2h0cyxcbiAgICAgICAgaGFzTGVha3lyZWx1QWxwaGEpO1xuICB9XG4gIGNvbnN0IGN1c3RvbVZhbHVlcyA9IFtcbiAgICBbY29udkluZm8ucGFkSW5mby50b3AsIGNvbnZJbmZvLnBhZEluZm8ubGVmdF0sXG4gICAgW2NvbnZJbmZvLnN0cmlkZUhlaWdodCwgY29udkluZm8uc3RyaWRlV2lkdGhdLFxuICAgIFtjb252SW5mby5kaWxhdGlvbkhlaWdodCwgY29udkluZm8uZGlsYXRpb25XaWR0aF0sXG4gICAgW2NvbnZJbmZvLmluSGVpZ2h0LCBjb252SW5mby5pbldpZHRoXVxuICBdO1xuICBjb25zdCByZXN1bHQgPVxuICAgICAgYmFja2VuZC5ydW5XZWJHTFByb2dyYW0ocHJvZ3JhbSwgcHJvZ3JhbUlucHV0cywgJ2Zsb2F0MzInLCBjdXN0b21WYWx1ZXMpO1xuXG4gIGludGVybWVkaWF0ZXMuZm9yRWFjaCh0ID0+IGJhY2tlbmQuZGlzcG9zZUludGVybWVkaWF0ZVRlbnNvckluZm8odCkpO1xuXG4gIHJldHVybiByZXN1bHQ7XG59XG5cbmV4cG9ydCBjb25zdCBmdXNlZERlcHRod2lzZUNvbnYyRENvbmZpZzogS2VybmVsQ29uZmlnID0ge1xuICBrZXJuZWxOYW1lOiBGdXNlZERlcHRod2lzZUNvbnYyRCxcbiAgYmFja2VuZE5hbWU6ICd3ZWJnbCcsXG4gIGtlcm5lbEZ1bmM6IGZ1c2VkRGVwdGh3aXNlQ29udjJEIGFzIHt9IGFzIEtlcm5lbEZ1bmMsXG59O1xuIl19
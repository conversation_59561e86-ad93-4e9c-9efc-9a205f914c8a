/**
 * @license
 * Copyright 2022 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { RaggedGather } from '@tensorflow/tfjs-core';
import { raggedGatherImpl } from './RaggedGather_impl';
export function raggedGather(args) {
    const { inputs, backend, attrs } = args;
    const { paramsNestedSplits, paramsDenseValues, indices } = inputs;
    const { outputRaggedRank } = attrs;
    const $paramsNestedSplits = paramsNestedSplits.map(t => backend.data.get(t.dataId).values);
    const $paramsNestedSplitsShapes = paramsNestedSplits.map(t => t.shape);
    const $paramsDenseValues = backend.data.get(paramsDenseValues.dataId).values;
    const $indices = backend.data.get(indices.dataId).values;
    const [outputNestedSplits, outputDenseValues, outputDenseValuesShape] = raggedGatherImpl($paramsNestedSplits, $paramsNestedSplitsShapes, $paramsDenseValues, paramsDenseValues.shape, paramsDenseValues.dtype, $indices, indices.shape, outputRaggedRank);
    const outputNestedSplitsTensors = outputNestedSplits.map((splits) => backend.makeTensorInfo([splits.length], 'int32', splits));
    const outputDenseValuesTensor = backend.makeTensorInfo(outputDenseValuesShape, paramsDenseValues.dtype, outputDenseValues);
    return outputNestedSplitsTensors.concat([outputDenseValuesTensor]);
}
export const raggedGatherConfig = {
    kernelName: RaggedGather,
    backendName: 'cpu',
    kernelFunc: raggedGather,
};
//# sourceMappingURL=data:application/json;base64,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
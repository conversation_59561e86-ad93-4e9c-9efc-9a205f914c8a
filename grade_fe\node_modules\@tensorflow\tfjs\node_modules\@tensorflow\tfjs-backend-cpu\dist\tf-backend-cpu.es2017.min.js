/**
 * @license
 * Copyright 2022 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@tensorflow/tfjs-core"),require("seedrandom")):"function"==typeof define&&define.amd?define(["exports","@tensorflow/tfjs-core","seedrandom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).tf=e.tf||{},e.tf,e.seedrandom)}(this,(function(e,t,n){"use strict";function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var a=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,a.get?a:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,t}var s=a(n);function r(e,n){Array.isArray(e)||(e=[e]),e.forEach((e=>{null!=e&&t.util.assert("complex64"!==e.dtype,(()=>`${n} does not support complex64 tensors in the CPU backend.`))}))}const o=t.kernel_impls.whereImpl;class i extends t.KernelBackend{constructor(){super(),this.blockSize=48,this.firstUse=!0,this.data=new t.DataStorage(this,t.engine())}nextDataId(){return i.nextDataId++}write(e,n,a){this.firstUse&&(this.firstUse=!1,t.env().get("IS_NODE")&&t.backend_util.warn("\n============================\nHi, looks like you are running TensorFlow.js in Node.js. To speed things up dramatically, install our node backend, visit https://github.com/tensorflow/tfjs-node for more details. \n============================"));const s={id:this.nextDataId()};return this.data.set(s,{values:e,dtype:a,refCount:1}),s}makeTensorInfo(e,n,a){let s;if("string"===n&&null!=a&&a.length>0&&t.util.isString(a[0])){const r=a.map((e=>t.util.encodeString(e)));s=this.write(r,e,n)}else s=this.write(a,e,n);return{dataId:s,shape:e,dtype:n}}refCount(e){if(this.data.has(e)){return this.data.get(e).refCount}return 0}incRef(e){this.data.get(e).refCount++}decRef(e){if(this.data.has(e)){this.data.get(e).refCount--}}move(e,t,n,a,s){this.data.set(e,{values:t,dtype:a,refCount:s})}numDataIds(){return this.data.numDataIds()}async read(e){return this.readSync(e)}readSync(e){const{dtype:n,complexTensorInfos:a}=this.data.get(e);if("complex64"===n){const e=this.readSync(a.real.dataId),n=this.readSync(a.imag.dataId);return t.backend_util.mergeRealAndImagArrays(e,n)}return this.data.get(e).values}bufferSync(e){const n=this.readSync(e.dataId);if("string"===e.dtype)try{const a=n.map((e=>t.util.decodeString(e)));return t.buffer(e.shape,e.dtype,a)}catch(e){throw new Error("Failed to decode encoded string bytes into utf-8")}return t.buffer(e.shape,e.dtype,n)}makeOutput(e,n,a){return t.engine().makeTensorFromTensorInfo(this.makeTensorInfo(n,a,e),this)}disposeData(e,t=!1){if(this.data.has(e)){if(this.data.get(e).refCount--,!t&&this.data.get(e).refCount>0)return!1;const{complexTensorInfos:n}=this.data.get(e);null!=n&&(this.disposeData(n.real.dataId,!0),this.disposeData(n.imag.dataId,!0)),this.data.delete(e)}return!0}disposeIntermediateTensorInfo(e){this.disposeData(e.dataId)}async time(e){const n=t.util.now();e();return{kernelMs:t.util.now()-n}}memory(){return{unreliable:!0,reasons:["The reported memory is an upper bound. Due to automatic garbage collection, the true allocated memory may be less."]}}where(e){r([e],"where");const t=this.readSync(e.dataId);return o(e.shape,t)}dispose(){}floatPrecision(){return 32}epsilon(){return super.epsilon()}}function l(e){const t=new Float32Array(e.length);for(let n=0;n<e.length;++n)t[n]=Math.abs(e[n]);return t}i.nextDataId=0;const u={kernelName:t.Abs,backendName:"cpu",kernelFunc:e=>{const{x:n}=e.inputs,a=e.backend;r(n,"abs");let s=new Float32Array(t.util.sizeFromShape(n.shape));return s=l(a.data.get(n.dataId).values),a.makeOutput(s,n.shape,n.dtype)}};function d(e){return(n,a,s,r,o)=>{const i=t.backend_util.assertAndGetBroadcastShape(n,a),l=i.length,u=t.util.computeStrides(i),d=t.util.sizeFromShape(i),c=t.util.getTypedArrayFromDType(o,d),p=n.length,h=a.length,f=t.util.computeStrides(n),m=t.util.computeStrides(a),k=t.backend_util.getBroadcastDims(n,i),g=t.backend_util.getBroadcastDims(a,i);if(k.length+g.length===0)for(let t=0;t<c.length;++t)c[t]=e(s[t%s.length],r[t%r.length]);else for(let n=0;n<c.length;++n){const a=t.util.indexToLoc(n,l,u),o=a.slice(-p);k.forEach((e=>o[e]=0));const i=t.util.locToIndex(o,p,f),d=a.slice(-h);g.forEach((e=>d[e]=0));const I=t.util.locToIndex(d,h,m);c[n]=e(s[i],r[I])}return[c,i]}}function c(e){const{inputs:t,backend:n}=e,{real:a,imag:s}=t,r=n.data.get(a.dataId).values,o=n.data.get(s.dataId).values,i=n.makeTensorInfo(a.shape,"complex64");return n.data.get(i.dataId).complexTensorInfos={real:n.makeTensorInfo(a.shape,"float32",r),imag:n.makeTensorInfo(s.shape,"float32",o)},i}const p={kernelName:t.Complex,backendName:"cpu",kernelFunc:c};function h(e,n,a="float32"){if("complex64"===a){return c({inputs:{real:h(e,n,"float32"),imag:h(e,n,"float32")},backend:e})}const s=t.util.makeZerosTypedArray(t.util.sizeFromShape(n),a);return e.makeTensorInfo(n,a,s)}function f(e){const{inputs:t,backend:n}=e,{x:a}=t;return n.incRef(a.dataId),{dataId:a.dataId,shape:a.shape,dtype:a.dtype}}const m={kernelName:t.Identity,backendName:"cpu",kernelFunc:f};function k(e){const{inputs:t,backend:n}=e,{input:a}=t,s=n.data.get(a.dataId).complexTensorInfos.real,r=n.data.get(s.dataId).values;return n.makeTensorInfo(s.shape,s.dtype,r)}const g={kernelName:t.Real,backendName:"cpu",kernelFunc:k};function I(e,n,a,s){if("int32"===s){return[n,"int32",Int32Array.from(e)]}if("bool"===s){const s=t.util.toTypedArray([0],a),[r,o]=d(((e,t)=>e!==t?1:0))(n,[],e,s,"bool");return[o,"bool",r]}throw new Error(`Error in Cast: failed to cast ${a} to ${s}`)}function b(e){const{inputs:n,backend:a,attrs:s}=e,{x:r}=n,{dtype:o}=s;if("complex64"===o){if("complex64"===r.dtype)return f({inputs:{x:r},backend:a});const e=h(a,r.shape,r.dtype),t=b({inputs:{x:r},backend:a,attrs:{dtype:"float32"}}),n=c({inputs:{real:t,imag:e},backend:a});return a.disposeIntermediateTensorInfo(e),a.disposeIntermediateTensorInfo(t),n}if("complex64"===r.dtype){const e=k({inputs:{input:r},backend:a}),t=b({inputs:{x:e},backend:a,attrs:{dtype:o}});return a.disposeIntermediateTensorInfo(e),t}if(!t.util.hasEncodingLoss(r.dtype,o)){const e=f({inputs:{x:r},backend:a});return{dataId:e.dataId,shape:e.shape,dtype:o}}const i=a.data.get(r.dataId).values,[l,u,d]=I(i,r.shape,r.dtype,o);return a.makeTensorInfo(l,u,d)}const y={kernelName:t.Cast,backendName:"cpu",kernelFunc:b};function S(e,n,a,s){return null==a?({inputs:a,backend:o})=>{const{a:i,b:l}=a,u=o;r([i,l],e);const d=u.data.get(i.dataId).values,c=u.data.get(l.dataId).values,p="string"===i.dtype?t.backend_util.fromUint8ToStringArray(d):d,h="string"===i.dtype?t.backend_util.fromUint8ToStringArray(c):c,f=s||i.dtype,[m,k]=n(i.shape,l.shape,p,h,f);return u.makeTensorInfo(k,f,m)}:({inputs:e,backend:t})=>{const{a:r,b:o}=e,i=t;if("complex64"===r.dtype||"complex64"===o.dtype){const e=b({inputs:{x:r},backend:i,attrs:{dtype:"complex64"}}),t=i.data.get(e.dataId),n=t.complexTensorInfos.real,s=t.complexTensorInfos.imag,l=i.data.get(n.dataId).values,u=i.data.get(s.dataId).values,d=b({inputs:{x:o},backend:i,attrs:{dtype:"complex64"}}),p=i.data.get(d.dataId),h=p.complexTensorInfos.real,f=p.complexTensorInfos.imag,m=i.data.get(h.dataId).values,k=i.data.get(f.dataId).values,[g,I,y]=a(r.shape,o.shape,l,u,m,k),S=i.makeTensorInfo(y,"float32",g),T=i.makeTensorInfo(y,"float32",I),N=c({inputs:{real:S,imag:T},backend:i});return i.disposeIntermediateTensorInfo(e),i.disposeIntermediateTensorInfo(d),i.disposeIntermediateTensorInfo(S),i.disposeIntermediateTensorInfo(T),N}{const e=i.data.get(r.dataId).values,t=i.data.get(o.dataId).values,a=s||r.dtype,[l,u]=n(r.shape,o.shape,e,t,a);return i.makeTensorInfo(u,a,l)}}}function T(e){return(n,a,s,r,o,i)=>{const l=t.backend_util.assertAndGetBroadcastShape(n,a),u=t.util.sizeFromShape(l),d=l.length,c=t.util.computeStrides(l),p=t.util.getTypedArrayFromDType("float32",u),h=t.util.getTypedArrayFromDType("float32",u),f=t.backend_util.getBroadcastDims(n,l),m=t.backend_util.getBroadcastDims(a,l),k=t.backend_util.mergeRealAndImagArrays(s,r),g=t.backend_util.mergeRealAndImagArrays(o,i),I=n.length,b=t.util.computeStrides(n),y=a.length,S=t.util.computeStrides(a);if(f.length+m.length===0)for(let t=0;t<p.length;t++){const n=t%k.length,a=t%g.length,s=e(k[2*n],k[2*n+1],g[2*a],g[2*a+1]);p[t]=s.real,h[t]=s.imag}else for(let n=0;n<p.length;n++){const a=t.util.indexToLoc(n,d,c),s=a.slice(-I);f.forEach((e=>s[e]=0));const r=t.util.locToIndex(s,I,b),o=a.slice(-y);m.forEach((e=>o[e]=0));const i=t.util.locToIndex(o,y,S),l=e(k[2*r],k[2*r+1],g[2*i],g[2*i+1]);p[n]=l.real,h[n]=l.imag}return[p,h,l]}}const N=d(((e,t)=>e+t)),x=T(((e,t,n,a)=>({real:e+n,imag:t+a}))),v=S(t.Add,N,x),F={kernelName:t.Add,backendName:"cpu",kernelFunc:v};function w(e,n,a,s,r){const o=t.util.sizeFromShape(s),i=t.util.makeZerosTypedArray(r,a);for(let t=0;t<e.length;t++){const a=e[t];if(a<0)throw new Error("Input x must be non-negative!");a>=r||(i[a]+=o>0?n[t]:1)}return i}function M(e,n,a,s=!1){const r=e.shape[0],o=e.shape[1],i=t.buffer([r,a],n.dtype);for(let t=0;t<r;t++)for(let r=0;r<o;r++){const o=e.get(t,r);if(o<0)throw new Error("Input x must be non-negative!");o>=a||(s?i.set(1,t,o):n.size>0?i.set(i.get(t,o)+n.get(t,r),t,o):i.set(i.get(t,o)+1,t,o))}return i}function A(e){return(n,a,s)=>{const r=t.util.getTypedArrayFromDType(a,n.length);for(let t=0;t<n.length;++t)r[t]=e(n[t],s);return r}}function D(e,n,a){return({inputs:s,attrs:o,backend:i})=>{const{x:l}=s;if(r(l,e),"string"===l.dtype||"string"===a)throw new Error("unaryKernelFunc does not support string input/output");const u=i,d=u.data.get(l.dataId).values,c=t.util.sizeFromShape(l.shape),p=a||l.dtype,h=t.util.getArrayFromDType(p,c);for(let e=0;e<c;++e)h[e]=n(d[e],o);return u.makeTensorInfo(l.shape,p,h)}}function _(e,t,n){return({inputs:a,attrs:s,backend:o})=>{const{x:i}=a;if(r(i,e),"string"===i.dtype||"string"===n)throw new Error("unaryKernelFunc does not support string input/output");const l=o,u=l.data.get(i.dataId).values,d=n||i.dtype,c=t(u,d,s);return l.makeTensorInfo(i.shape,d,c)}}const E=A((e=>Math.ceil(e))),z=_(t.Ceil,E),R={kernelName:t.Ceil,backendName:"cpu",kernelFunc:z};function W(e,n,a,s){const r=t.util.getArrayFromDType(a,t.util.sizeFromShape(n));if(s&&"string"!==a){let n=0;e.forEach((e=>{const a=t.util.sizeFromShape(e.shape);r.set(e.vals,n),n+=a}))}else{let s=0;e.forEach((e=>{const o="string"===a?t.backend_util.fromUint8ToStringArray(e.vals):e.vals;let i=0;for(let t=0;t<e.shape[0];++t){const a=t*n[1]+s;for(let t=0;t<e.shape[1];++t)r[a+t]=o[i++]}s+=e.shape[1]}))}return r}const P=d(((e,t)=>e===t?1:0)),C=S(t.Equal,P,null,"bool"),H={kernelName:t.Equal,backendName:"cpu",kernelFunc:C},O=A((e=>Math.exp(e))),V=_(t.Exp,O,"float32"),B={kernelName:t.Exp,backendName:"cpu",kernelFunc:V},$=A((e=>Math.expm1(e))),L=_(t.Expm1,$),G={kernelName:t.Expm1,backendName:"cpu",kernelFunc:L},q=A((e=>Math.floor(e))),U=_(t.Floor,q),Z={kernelName:t.Floor,backendName:"cpu",kernelFunc:U};function j(e,n,a,s,r,o,i,l,u){const d=t.buffer([s,o],a);for(let t=0;t<s;t++){const a=[];let s=0;for(let n=0;n<r;n++){const o=e[t*r+n];s+=o*i[n],a.push(o)}if(s<0||s>=u/o)throw new Error(`Invalid indices: ${a} does not index into ${l}`);for(let e=0;e<o;e++)d.values[t*o+e]=n.get(...n.indexToLoc(s*o+e))}return d}function K(e,n,a){const s=t.buffer(a,e.dtype);for(let t=0;t<s.size;++t){const a=s.indexToLoc(t).slice(),r=a[0],o=a[2],i=n.locToIndex([r,o]);a[2]=n.values[i];const l=e.locToIndex(a);0<=l&&l<e.values.length&&(s.values[t]=e.values[l])}return s}const Y=d(((e,t)=>e>t?1:0)),J=S(t.Greater,Y,null,"bool"),Q={kernelName:t.Greater,backendName:"cpu",kernelFunc:J},X=d(((e,t)=>e>=t?1:0)),ee=S(t.GreaterEqual,X,null,"bool"),te={kernelName:t.GreaterEqual,backendName:"cpu",kernelFunc:ee},ne=d(((e,t)=>e<t?1:0)),ae=S(t.Less,ne,null,"bool"),se={kernelName:t.Less,backendName:"cpu",kernelFunc:ae},re=d(((e,t)=>e<=t?1:0)),oe=S(t.LessEqual,re,null,"bool"),ie={kernelName:t.LessEqual,backendName:"cpu",kernelFunc:oe};function le(e,n,a){const s=(n-e)/(a-1),r=t.util.makeZerosTypedArray(a,"float32");r[0]=e;for(let e=1;e<r.length;e++)r[e]=r[e-1]+s;return r}const ue=A((e=>Math.log(e))),de=_(t.Log,ue),ce={kernelName:t.Log,backendName:"cpu",kernelFunc:de};function pe(e,n,a,s){const r=t.util.getTypedArrayFromDType(s,t.util.sizeFromShape(a));for(let t=0;t<r.length;++t){const a=t*n;let s=e[a];for(let t=0;t<n;++t){const n=e[a+t];(Number.isNaN(n)||n>s)&&(s=n)}r[t]=s}return r}const he=d(((e,t)=>Math.max(e,t))),fe=S(t.Maximum,he),me={kernelName:t.Maximum,backendName:"cpu",kernelFunc:fe},ke=d(((e,t)=>Math.min(e,t))),ge=S(t.Minimum,ke),Ie={kernelName:t.Minimum,backendName:"cpu",kernelFunc:ge},be=d(((e,t)=>e*t)),ye=T(((e,t,n,a)=>({real:e*n-t*a,imag:e*a+t*n}))),Se=S(t.Multiply,be,ye),Te={kernelName:t.Multiply,backendName:"cpu",kernelFunc:Se};function Ne(e,n,a){const s=t.util.createScalarValue(-1,a);return be([],n,s,e,a)}const xe={kernelName:t.Neg,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{x:a}=t;r(a,"neg");const s=n.data.get(a.dataId).values,[o,i]=Ne(s,a.shape,a.dtype);return n.makeTensorInfo(i,a.dtype,o)}},ve=d(((e,t)=>e!==t?1:0)),Fe=S(t.NotEqual,ve,null,"bool"),we={kernelName:t.NotEqual,backendName:"cpu",kernelFunc:Fe};function Me(e,n,a,s,r){const o=n.length,i=t.util.sizeFromShape(n),l=t.util.computeStrides(n),u=t.util.computeStrides(r),d=t.util.getTypedArrayFromDType(a,t.util.sizeFromShape(r));for(let n=0;n<i;++n){const a=t.util.indexToLoc(n,o,l),r=new Array(a.length);for(let e=0;e<r.length;e++)r[e]=a[s[e]];d[t.util.locToIndex(r,o,u)]=e[n]}return d}function Ae(e){const{inputs:t,attrs:n,backend:a}=e,{x:s}=t,{perm:o}=n;r(s,"transpose");const i=s.shape.length,l=new Array(i);for(let e=0;e<l.length;e++)l[e]=s.shape[o[e]];const u=Me(a.data.get(s.dataId).values,s.shape,s.dtype,o,l);return{dataId:a.write(u,l,s.dtype),shape:l,dtype:s.dtype}}const De={kernelName:t.Transpose,backendName:"cpu",kernelFunc:Ae};function _e(e,n,a,s){const[r,o]=t.backend_util.computeOutAndReduceShapes(e,s),i=t.upcastType(n,"int32"),l=t.util.makeZerosTypedArray(t.util.sizeFromShape(r),i),u=t.util.sizeFromShape(o);for(let e=0;e<l.length;++e){const t=e*u;let n=1;for(let e=0;e<u;++e)n*=a[t+e];l[e]=n}return{outVals:l,outShape:r,outDtype:i}}const Ee={kernelName:t.Prod,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{axis:i,keepDims:l}=s;r(o,"prod");const u=o.shape.length,d=t.util.parseAxisParam(i,o.shape),c=t.backend_util.getAxesPermutation(d,u);let p=d,h=o;const f=[];null!=c&&(h=Ae({inputs:{x:o},backend:a,attrs:{perm:c}}),f.push(h),p=t.backend_util.getInnerMostAxes(p.length,u));const m=a.data.get(h.dataId).values,{outVals:k,outShape:g,outDtype:I}=_e(h.shape,h.dtype,m,p);let b=g;return l&&(b=t.backend_util.expandShapeToKeepDim(g,d)),f.forEach((e=>a.disposeIntermediateTensorInfo(e))),a.makeTensorInfo(b,I,k)}};function ze(e,t,n,a){const s=[];let r=0;const o=t.length-1+n.length,i=new Array(o).fill(null).map((()=>[0]));!function(e,t){for(let n=0;n<e.length;++n){const a=e[n],s=n===e.length-1?t:e[n+1].length;if(0===a.length)throw new Error("Ragged splits may not be empty");if(a[0]<0)throw new Error("Ragged splits must be non-negative");if(a[a.length-1]>s)throw new Error("Ragged splits must not point past values");for(let e=1;e<a.length;++e)if(a[e-1]>a[e])throw new Error("Ragged splits must be sorted in ascending order")}}(n,a);let l=1;for(let e=0;e<t.length-1;++e){l*=t[e];const n=t[e+1];for(let t=1;t<l+1;++t)i[e].push(t*n)}for(let a=0;a<e.length;++a){let o=e[a],l=e[a]+1;for(let e=0;e<n.length;++e){const a=n[e],s=e+t.length-1;if(s>=0){const e=i[s],t=e[e.length-1]-a[o];for(let e=o;e<l;++e)i[s].push(a[e+1]+t)}o=a[o],l=a[l]}l!==o&&(s.push([o,l]),r+=l-o)}return{outSplits:i,valueSlices:s,numValues:r}}function Re(e,t){const n=e.slice(0,t);for(;n.length<t;)n.push(1);for(let a=t;a<e.length;a++)n[t-1]*=e[a];return n}function We(e,n,a,s,r){const o=n.slice();o[0]=r;const i=t.util.getArrayFromDType(a,t.util.sizeFromShape(o)),l=e.length;return function(e,t,n,a,s,r){const o=Re(t,2)[1],i=Re(r,2)[1];let l=0;for(const t of n)for(let n=t[0];n<t[1];++n){for(let t=0;t<a;++t)s[l*i+t]=e[n*o+t];++l}}(e,n,s,0===l?0:l/n[0],i,o),[i,o]}function Pe(e,n,a,s,r,o,i,l){if(0===e.length)throw new Error("paramsNestedSplits must be non empty");if(0===n[0].length)throw new Error("Split tensors must not be scalars");if(function(e,n,a){e.forEach(((e,s)=>{if(e<0||e>=a){const r=t.util.indexToLoc(s,n.length,t.util.computeStrides(n)).join(",");throw new Error(`indices[${r}] = ${e} is not in [0, ${a})`)}}))}(o,i,n[0][0]-1),0===s.length)throw new Error("params.rank must be nonzero");const u=s[0],{outSplits:d,valueSlices:c,numValues:p}=ze(o,i,e,u),h=function(e){const n=[];for(let a=0;a<e.length;++a){const s=e[a].length,r=t.util.getArrayFromDType("int32",s);n.push(r),e[a].forEach(((e,t)=>r[t]=e))}return n}(d),f=We(a,s,r,c,p);return[h,f[0],f[1]]}var Ce=t.backend_util.RowPartitionType;class He{constructor(e,n,a,s,r,o,i,l,u,d){this.shape=e,this.shapeShape=n,this.values=a,this.valuesShape=s,this.valuesDType=r,this.defaultValue=o,this.defaultValueShape=i,this.rowPartitionValues=l,this.rowPartitionValuesShapes=u,this.rowPartitionTypes=t.backend_util.getRowPartitionTypesHelper(d),this.raggedRank=t.backend_util.getRaggedRank(this.rowPartitionTypes)}getRowPartitionTypeByDimension(e){return this.rowPartitionTypes[0]===Ce.FIRST_DIM_SIZE?this.rowPartitionTypes[e+1]:this.rowPartitionTypes[e]}getRowPartitionTensor(e){return this.rowPartitionTypes[0]===Ce.FIRST_DIM_SIZE?this.rowPartitionValues[e+1]:this.rowPartitionValues[e]}getMaxWidth(e){const t=this.getRowPartitionTensor(e-1);switch(this.getRowPartitionTypeByDimension(e-1)){case Ce.VALUE_ROWIDS:return He.getMaxWidthValueRowID(t);case Ce.ROW_SPLITS:return He.getMaxWidthRowSplit(t);default:throw new Error(`Cannot handle partition type ${Ce[this.getRowPartitionTypeByDimension(e-1)]}`)}}static getMaxWidthRowSplit(e){const t=e.length;if(0===t||1===t)return 0;let n=0;for(let a=0;a<t-1;++a){const t=e[a+1]-e[a];t>n&&(n=t)}return n}static getMaxWidthValueRowID(e){const t=e.length;if(0===t)return 0;let n=0,a=e[0],s=0;for(let r=1;r<t;++r){const t=e[r];t!==a&&(a=t,s=Math.max(r-n,s),n=r)}return Math.max(t-n,s)}tensorShapeFromTensor(e,t,n=!0){if(0===t.length){if(-1===e[0])return[];throw new Error("The only valid scalar shape tensor is the fully unknown shape specified as -1.")}return Ve(e,n)}calculateOutputSize(e){const n=this.valuesShape,a=this.defaultValueShape;t.backend_util.validateDefaultValueShape(a,n);const s=this.tensorShapeFromTensor(this.shape,this.shapeShape),r=t.backend_util.combineRaggedTensorToTensorShapes(this.raggedRank,s,n);r[0]<0&&(r[0]=e);for(let e=1;e<=this.raggedRank;++e)r[e]<0&&(r[e]=this.getMaxWidth(e));return r}calculateFirstParentOutputIndex(e,n,a){const s=Math.min(e,a),r=[];let o=0;for(let e=0;e<s;++e,o+=n)r.push(o);for(let t=s;t<e;++t)r.push(-1);return t.util.assert(r.length===e,(()=>"Final length of result must be equal to firstDimension.")),r}calculateOutputIndexRowSplit(e,t,n,a){const s=e.length,r=[];for(let o=0;o<s-1;++o){const s=e[o+1]-e[o];let i=Math.min(a,s),l=t[o];-1===l&&(i=0);for(let e=0;e<i;++e)r.push(l),l+=n;for(let e=0;e<s-i;++e)r.push(-1)}if(s>0&&r.length!==e[s-1])throw new Error("Invalid row split size.");return r}calculateOutputIndexValueRowID(e,t,n,a){const s=e.length,r=[];if(0===s)return[];let o=0,i=e[0];if(i>=t.length)throw new Error(`Got currentValueRowId=${i}, which is not less than ${t.length}`);let l=t[i];r.push(l);for(let u=1;u<s;++u){const s=e[u];if(s===i)l>=0&&(++o,o<a?l+=n:l=-1);else{if(o=0,i=s,s>=t.length)throw new Error(`Got nextValueRowId=${s} which is not less than ${t.length}`);l=t[s]}r.push(l)}if(r.length!==e.length)throw new Error("Invalid row ids.");return r}calculateOutputIndex(e,t,n,a){const s=this.getRowPartitionTensor(e),r=this.getRowPartitionTypeByDimension(e);switch(r){case Ce.VALUE_ROWIDS:return this.calculateOutputIndexValueRowID(s,t,n,a);case Ce.ROW_SPLITS:if(s.length-1>t.length)throw new Error(`Row partition size is greater than output size: ${s.length-1} > ${t.length}`);return this.calculateOutputIndexRowSplit(s,t,n,a);default:throw new Error(`Unsupported partition type: ${Ce[r]}`)}}getFirstDimensionSize(){const e=this.rowPartitionValues[0];if(0===this.rowPartitionTypes.length)throw new Error("No row_partition_types given.");const t=this.rowPartitionTypes[0];switch(t){case Ce.FIRST_DIM_SIZE:return e[0];case Ce.VALUE_ROWIDS:throw new Error("Cannot handle VALUE_ROWIDS in first dimension.");case Ce.ROW_SPLITS:return this.rowPartitionValuesShapes[0][0]-1;default:throw new Error(`Cannot handle type ${Ce[t]}`)}}compute(){if(this.rowPartitionValues[0].length<=0)throw new Error("Invalid first partition input. Tensor requires at least one element.");const e=this.getFirstDimensionSize(),n=this.calculateOutputSize(e),a=new Array(this.raggedRank+1);a[a.length-1]=1;for(let e=a.length-2;e>=0;--e)a[e]=a[e+1]*n[e+1];const s=Ve(n,!1),r=t.util.getArrayFromDType(this.valuesDType,t.util.sizeFromShape(s));if(a[0]*n[0]>0){let t=this.calculateFirstParentOutputIndex(e,a[0],n[0]);for(let e=1;e<=this.raggedRank;++e){t=this.calculateOutputIndex(e-1,t,a[e],n[e])}this.setOutput(this.raggedRank,t,r,s)}return[s,r]}setOutput(e,n,a,s){if(0===a.length)return;const r=this.values,o=a;let i=s.slice();i=i.slice(e+1);const l=t.util.sizeFromShape(i),u=n.length;let d=this.defaultValue;if(d.length!==l&&1!==d.length){const e=this.defaultValueShape;t.tidy((()=>{const n=t.reshape(d,e),a=t.broadcastTo(n,i);d=a.dataSync()}))}let c=0,p=0,h=0;for(let e=0;e<=u;++e){let t=e<u?n[e]:-1;if(t!==h){if(p<h){const e=r.subarray(c*l);Oe(o.subarray(p*l),e,(h-p)*l)}if(e>=u){const e=a.length;t=Math.floor(e/l)}if(t>h)if(1===this.defaultValue.length)o.subarray(h*l,t*l).fill(this.defaultValue[0]),h=t;else for(;t>h;){Oe(o.slice(h*l),d,l),++h}t<0?(c=e+1,p=h):(c=e,p=h,h=p+1)}else++h}}}function Oe(e,t,n){for(let a=0;a<n;a++)e[a]=t[a]}function Ve(e,t){const n=[];for(let a of e){if(a<0){if(!t)throw new Error(`Dimension ${a} must be >= 0`);if(a<-1)throw new Error(`Dimension ${a} must be >= -1`);a=-1}n.push(a)}return n}function Be(e,t,n,a,s,r,o,i,l,u){return new He(e,t,n,a,s,r,o,i,l,u).compute()}function $e(e,n,a,s){if(e===n||e<n&&a<0||n<e&&a>1)return t.util.makeZerosTypedArray(0,s);const r=Math.abs(Math.ceil((n-e)/a)),o=t.util.makeZerosTypedArray(r,s);n<e&&1===a&&(a=-1),o[0]=e;for(let e=1;e<o.length;e++)o[e]=o[e-1]+a;return o}const Le=A((e=>1/Math.sqrt(e))),Ge=_(t.Rsqrt,Le),qe={kernelName:t.Rsqrt,backendName:"cpu",kernelFunc:Ge};function Ue(e,n,a,s,r,o,i,l,u,d){const c=[s/r,r],p=e.values,h=n.values;if(0===s)return t.buffer(a,n.dtype);const f=t.buffer(c,n.dtype);"string"==typeof u||"number"==typeof u?f.values.fill(u):"boolean"==typeof u&&f.values.fill(+u);for(let e=0;e<o;e++){const t=[];let o=0;for(let n=0;n<i;n++){const a=p[e*i+n];t.push(a),o+=a*l[n]}if(o<0||o>=s/r)throw new Error(`Invalid indices: ${t} does not index into ${a}`);for(let t=0;t<r;t++)d?f.values[o*r+t]+=h[e*r+t]:f.values[o*r+t]=0===n.rank?h[0]:h[e*r+t]}return f}const Ze=A((e=>1/(1+Math.exp(-e)))),je=D(t.Sigmoid,(e=>1/(1+Math.exp(-e)))),Ke={kernelName:t.Sigmoid,backendName:"cpu",kernelFunc:je};function Ye(e,n,a,s,r){const o=t.slice_util.isSliceContinous(s,n,a),i=t.util.sizeFromShape(a),l=t.util.computeStrides(s);if(o){const a=t.slice_util.computeFlatOffset(n,l);return"string"===r?e.slice(a,a+i):e.subarray(a,a+i)}const u="string"===r?t.backend_util.fromUint8ToStringArray(e):e,d=t.buffer(s,r,u),c=t.buffer(a,r);for(let e=0;e<c.size;++e){const t=c.indexToLoc(e),a=t.map(((e,t)=>e+n[t]));c.set(d.get(...a),...t)}return"string"===r?t.backend_util.fromStringArrayToUint8(c.values):c.values}function Je(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{begin:i,size:l}=s;r(o,"slice");const[u,d]=t.slice_util.parseSliceParams(o,i,l);t.slice_util.assertParamsValid(o,u,d);const c=Ye(a.data.get(o.dataId).values,u,d,o.shape,o.dtype);return a.makeTensorInfo(d,o.dtype,c)}const Qe={kernelName:t.Slice,backendName:"cpu",kernelFunc:Je};function Xe(e,n,a,s,r,o,i){const l=n[0],u=o[0],d=new Array(u),c=new Array(l),p=n[1];if(0===u){if(0!==l)throw new Error(t.backend_util.getSparseFillEmptyRowsIndicesDenseShapeMismatch(l));return[t.util.getArrayFromDType(a,0),[0,p],t.util.getArrayFromDType(r,0),d,c]}let h=!0,f=0;const m=new Array(u).fill(0);for(let n=0;n<l;++n){const a=e[n*p];if(a<0)throw new Error(t.backend_util.getSparseFillEmptyRowsNegativeIndexErrorMessage(n,a));if(a>=u)throw new Error(t.backend_util.getSparseFillEmptyRowsOutOfRangeIndexErrorMessage(n,a,u));++m[a],h=h&&a>=f,f=a}let k=!0;for(let e=0;e<u;++e){const t=0===m[e];d[e]=t,k=k&&!t,m[e]=Math.max(m[e],1),e>0&&(m[e]+=m[e-1])}if(k&&h){const t=e,n=s;for(let e=0;e<l;++e)c[e]=e;return[t,[l,p],n,d,c]}{const n=m[u-1],o=t.util.getArrayFromDType(a,n*p),h=t.util.getArrayFromDType(r,n),f=new Array(u).fill(0);for(let t=0;t<l;++t){const n=e[t*p],a=f[n],r=(0===n?0:m[n-1])+a;f[n]++;for(let n=0;n<p;++n)o[r*p+n]=e[t*p+n];h[r]=s[t],c[t]=r}for(let e=0;e<u;++e){if(0===f[e]){const t=0===e?0:m[e-1];o[t*p+0]=e;for(let e=1;e<p;++e)o[t*p+e]=0;h[t]=i}}return[o,[n,p],h,d,c]}}function et(e,n,a,s,r){const o=t.util.sizeFromShape(s),i=n[0],l=r.length,u=[];let d=1,c=-1;for(let e=0;e<l;++e){const n=r[e];if(-1===n){if(-1!==c)throw new Error(t.backend_util.getSparseReshapeMultipleNegativeOneOutputDimErrorMessage(c,e));c=e,u.push(1)}else{if(n<0)throw new Error(t.backend_util.getSparseReshapeNegativeOutputDimErrorMessage(e,n));d*=n,u.push(n)}}if(-1!==c){if(d<=0)throw new Error(t.backend_util.getSparseReshapeEmptyTensorZeroOutputDimErrorMessage());const e=Math.trunc(o/d);if(d*e!==o)throw new Error(t.backend_util.getSparseReshapeInputOutputMultipleErrorMessage(s,u));u[c]=e}if(t.util.sizeFromShape(u)!==o)throw new Error(t.backend_util.getSparseReshapeInputOutputMismatchErrorMessage(s,u));const p=s.length,h=[];if(p>0){h[p-1]=1;for(let e=p-2;e>=0;--e)h[e]=h[e+1]*s[e+1]}const f=[];if(l>0){f[l-1]=1;for(let e=l-2;e>=0;--e)f[e]=f[e+1]*u[e+1]}const m=t.util.getArrayFromDType(a,i*l);for(let t=0;t<i;++t){let n=0;for(let a=0;a<p;++a)n+=e[t*p+a]*h[a];for(let e=0;e<l;++e)m[t*l+e]=Math.trunc(n/f[e]),n%=f[e]}return[m,[i,l],u]}function tt(e,n,a,s,r,o=!1,i=0){const l=s.length,u=[n[0],e.length/n[0]],d=u[1],c=l>0?r[l-1]+1:0;if(c<0)throw new Error(t.backend_util.getSparseSegmentReductionNegativeSegmentIdsErrorMessage());const p=n.slice();p[0]=c;const h=p.reduce(((e,t)=>e*t),1),f=t.util.getArrayFromDType(a,h);if(0===l)return c>0&&f.fill(i),[f,p];if(c<=0)throw new Error(t.backend_util.getSparseSegmentReductionNegativeSegmentIdsErrorMessage());let m=0,k=1,g=0,I=r[m];for(;;){let n=0;if(k<l){if(n=r[k],I===n){++k;continue}if(I>=n)throw new Error(t.backend_util.getSparseSegmentReductionNonIncreasingSegmentIdsErrorMessage())}if(I<0||I>=c)throw new Error(t.backend_util.getSparseSegmentReductionSegmentIdOutOfRangeErrorMessage(I,c));I>g&&f.fill(i,g*d,I*d);for(let n=m;n<k;++n){const a=s[n];if(a<0||a>=u[0])throw new Error(t.backend_util.getSparseSegmentReductionIndicesOutOfRangeErrorMessage(n,s[n],u[0]));for(let t=0;t<d;t++)f[I*d+t]+=e[a*d+t]}if(o)for(let e=0;e<d;e++)f[I*d+e]/=k-m;if(m=k,++k,g=I+1,I=n,k>l)break}return g<c&&f.fill(i,g*d,c*d),[f,p]}const nt=A((e=>Math.sqrt(e))),at=D(t.Sqrt,(e=>Math.sqrt(e))),st={kernelName:t.Sqrt,backendName:"cpu",kernelFunc:at},rt=d(((e,t)=>{const n=e-t;return n*n})),ot=S(t.SquaredDifference,rt),it={kernelName:t.SquaredDifference,backendName:"cpu",kernelFunc:ot};function lt(e,n,a,s){const r=t.buffer(e,n.dtype);for(let e=0;e<r.size;e++){const t=r.indexToLoc(e),o=new Array(t.length);for(let e=0;e<o.length;e++)o[e]=t[e]*a[e]+s[e];r.set(n.get(...o),...t)}return r}class ut{constructor(e,n,a,s,r,o){this.separator=t.util.encodeString(e),this.nGramWidths=n,this.leftPad=t.util.encodeString(a),this.rightPad=t.util.encodeString(s),this.padWidth=r,this.preserveShort=o}getPadWidth(e){return Math.min(this.padWidth<0?e-1:this.padWidth,e-1)}getNumNGrams(e,t){const n=this.getPadWidth(t);return Math.max(0,e+2*n-t+1)}createNGrams(e,t,n,a,s,r){for(let o=0;o<s;++o){const i=this.getPadWidth(r),l=Math.max(0,i-o),u=Math.max(0,i-(s-(o+1))),d=r-(l+u),c=t+(l>0?0:o-i);let p=0;p+=l*this.leftPad.length;for(let t=0;t<d;++t)p+=e[c+t].length;p+=u*this.rightPad.length;p+=(l+u+d-1)*this.separator.length,n[a+o]=new Uint8Array(p);const h=n[a+o];let f=0;const m=e=>e.forEach((e=>h[f++]=e));for(let e=0;e<l;++e)m(this.leftPad),m(this.separator);for(let t=0;t<d-1;++t)m(e[c+t]),m(this.separator);if(d>0){m(e[c+d-1]);for(let e=0;e<u;++e)m(this.separator),m(this.rightPad)}else{for(let e=0;e<u-1;++e)m(this.rightPad),m(this.separator);m(this.rightPad)}}}compute(e,n){const a=e.length,s=n.length;if(s>0){let e=n[0];if(0!==e)throw new Error(`First split value must be 0, got ${e}`);for(let t=1;t<s;++t){let s=n[t]>=e;if(s=s&&n[t]<=a,!s)throw new Error(`Invalid split value ${n[t]}, must be in [${e}, ${a}]`);e=n[t]}if(e!==a)throw new Error(`Last split value must be data size. Expected ${a}, got ${e}`)}const r=s-1,o=t.util.getArrayFromDType("int32",s);if(0===a||0===s){const e=new Array(a);for(let e=0;e<=r;++e)o[e]=0;return[e,o]}o[0]=0;for(let e=1;e<=r;++e){const t=n[e]-n[e-1];let a=0;this.nGramWidths.forEach((e=>{a+=this.getNumNGrams(t,e)})),this.preserveShort&&t>0&&0===a&&(a=1),o[e]=o[e-1]+a}const i=new Array(o[r]);for(let t=0;t<r;++t){const a=n[t];let s=o[t];if(this.nGramWidths.forEach((r=>{const o=n[t+1]-n[t],l=this.getNumNGrams(o,r);this.createNGrams(e,a,i,s,l,r),s+=l})),this.preserveShort&&s===o[t]){const r=n[t+1]-n[t];if(0===r)continue;const o=r+2*this.padWidth,l=1;this.createNGrams(e,a,i,s,l,o)}}return[i,o]}}function dt(e,t,n,a,s,r,o,i){return new ut(n,a,s,r,o,i).compute(e,t)}function ct(e,t,n,a){if(!e.length)return;if(0===t.length){for(let t=0;t<e.length;++t)a.push(e.subarray(t,t+1));return}if(1===t.length){const s=t[0];let r=e.indexOf(s);for(;-1!==r;){const t=e.subarray(0,r);n&&0===t.length||a.push(t),r=(e=e.subarray(r+1)).indexOf(s)}return void(n&&0===e.length||a.push(e))}let s=0;for(let r=0;r<e.length+1;r++)if(r===e.length||-1!==t.indexOf(e[r])){const t=e.subarray(s,r);n&&0===t.length||a.push(t),s=r+1}}function pt(e,n,a){const s=e.length,r=[];let o=0,i=0;const l=new Array(s);for(let t=0;t<s;++t){const s=r.length;ct(e[t],n,a,r);const u=r.length-s;l[t]=u,o+=u,i=Math.max(i,u)}const u=t.util.getArrayFromDType("int32",2*o),d=new Array(o),c=[s,i];let p=0;for(let e=0;e<s;++e)for(let t=0;t<l[e];++t)u[2*p]=e,u[2*p+1]=t,d[p]=r[p],++p;return[u,d,c]}function ht(e,n){const a=t.util.getArrayFromDType("int32",e.length);for(let s=0;s<e.length;++s)a[s]=t.util.fingerPrint64(e[s]).modulo(n).getLowBitsUnsigned();return a}const ft=d(((e,t)=>e-t)),mt=T(((e,t,n,a)=>({real:e-n,imag:t-a}))),kt=S(t.Sub,ft,mt),gt={kernelName:t.Sub,backendName:"cpu",kernelFunc:kt};function It(e,n){const a=new Array(e.rank);for(let t=0;t<a.length;t++)a[t]=e.shape[t]*n[t];const s=t.buffer(a,e.dtype);for(let t=0;t<s.values.length;++t){const n=s.indexToLoc(t),a=new Array(e.rank);for(let t=0;t<a.length;t++)a[t]=n[t]%e.shape[t];const r=e.locToIndex(a);s.values[t]=e.values[r]}return s}const bt=(e,t)=>{const n=t.value-e.value;return 0===n?e.index-t.index:n};function yt(e,n,a=0,s=e.length-1){for(;s>a;){if(s-a>600){const t=s-a+1,r=n-a+1,o=Math.log(t),i=.5*Math.exp(2*o/3),l=.5*Math.sqrt(o*i*(t-i)/t)*Math.sign(r-t/2);yt(e,n,Math.max(a,Math.floor(n-r*i/t+l)),Math.min(s,Math.floor(n+(t-r)*i/t+l)))}const r=e[n];let o=a,i=s;for(t.util.swap(e,a,n),bt(e[s],r)>0&&t.util.swap(e,a,s);o<i;){for(t.util.swap(e,o,i),o++,i--;bt(e[o],r)<0;)o+=1;for(;bt(e[i],r)>0;)i-=1}0===bt(e[a],r)?t.util.swap(e,a,i):(i+=1,t.util.swap(e,i,s)),i<=n&&(a=i+1),n<=i&&(s=i-1)}}function St(e,n,a,s,r){const o=n[n.length-1],[i,l]=[e.length/o,o],u=t.util.getTypedArrayFromDType(a,i*s),d=t.util.getTypedArrayFromDType("int32",i*s);for(let t=0;t<i;t++){const n=t*l,a=e.subarray(n,n+l);let o=new Array(a.length);a.forEach(((e,t)=>o[t]={value:e,index:t})),s<o.length&&(yt(o,s),o=o.slice(0,s)),r&&o.sort(bt);const i=t*s,c=u.subarray(i,i+s),p=d.subarray(i,i+s);for(let e=0;e<s;e++)c[e]=o[e].value,p[e]=o[e].index}const c=n.slice();return c[c.length-1]=s,[t.buffer(c,a,u),t.buffer(c,"int32",d)]}function Tt(e,n,a,s){const r=t.util.parseAxisParam(n,a)[0],o=[1,a[0],1];for(let e=0;e<r;e++)o[0]*=a[e];o[1]=a[r];for(let e=r+1;e<a.length;e++)o[2]*=a[e];const i={},l=new Int32Array(a[r]),u=new t.TensorBuffer(o,s,e),d=[],c=1===o[0]&&1===o[2];for(let t=0;t<a[r];t++){let n;if(c)n=e[t].toString();else{const e=[];for(let n=0;n<o[0];n++)for(let a=0;a<o[2];a++)e.push(u.get(n,t,a));n=e.join(",")}if(void 0!==i[n])l[t]=i[n];else{const e=Object.keys(i).length;i[n]=e,l[t]=e,d.push(t)}}const p=o.slice();p[1]=Object.keys(i).length;const h=new t.TensorBuffer(p,s);d.forEach(((e,t)=>{for(let n=0;n<o[0];n++)for(let a=0;a<o[2];a++)h.set(u.get(n,e,a),n,t,a)}));const f=a.slice();return f[r]=p[1],{outputValues:h.values,outputShape:f,indices:l}}var Nt={__proto__:null,simpleAbsImpl:l,addImpl:N,bincountImpl:w,bincountReduceImpl:M,castImpl:I,ceilImpl:E,concatImpl:W,equalImpl:P,expImpl:O,expm1Impl:$,floorImpl:q,gatherNdImpl:j,gatherV2Impl:K,greaterImpl:Y,greaterEqualImpl:X,lessImpl:ne,lessEqualImpl:re,linSpaceImpl:le,logImpl:ue,maxImpl:pe,maximumImpl:he,minimumImpl:ke,multiplyImpl:be,negImpl:Ne,notEqualImpl:ve,prodImpl:_e,raggedGatherImpl:Pe,raggedTensorToTensorImpl:Be,rangeImpl:$e,rsqrtImpl:Le,scatterImpl:Ue,sigmoidImpl:Ze,sliceImpl:Ye,sparseFillEmptyRowsImpl:Xe,sparseReshapeImpl:et,sparseSegmentReductionImpl:tt,sqrtImpl:nt,squaredDifferenceImpl:rt,stridedSliceImpl:lt,stringNGramsImpl:dt,stringSplitImpl:pt,stringToHashBucketFastImpl:ht,subImpl:ft,tileImpl:It,topKImpl:St,transposeImpl:Me,uniqueImpl:Tt};t.registerBackend("cpu",(()=>new i),1);const xt=D(t.Elu,(e=>e>=0?e:Math.exp(e)-1)),vt={kernelName:t.Elu,backendName:"cpu",kernelFunc:xt};function Ft(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{alpha:i}=s;r([o],"leakyRelu");const l=t.util.sizeFromShape(o.shape),u=a.data.get(o.dataId).values,d=t.util.getTypedArrayFromDType("float32",l);for(let e=0;e<u.length;e++)d[e]=u[e]<0?i*u[e]:u[e];return a.makeTensorInfo(o.shape,"float32",d)}const wt={kernelName:t.LeakyRelu,backendName:"cpu",kernelFunc:Ft},Mt=d(((e,t)=>e<0?t*e:e));function At(e){const{inputs:t,backend:n}=e,{x:a,alpha:s}=t;r([a,s],"prelu");const o=n.data.get(a.dataId).values,i=n.data.get(s.dataId).values,[l,u]=Mt(a.shape,s.shape,o,i,"float32");return n.makeTensorInfo(u,"float32",l)}const Dt={kernelName:t.Prelu,backendName:"cpu",kernelFunc:At},_t=D(t.Relu,(e=>Math.max(0,e))),Et={kernelName:t.Relu,backendName:"cpu",kernelFunc:_t},zt=D(t.Relu6,(e=>Math.min(Math.max(0,e),6))),Rt={kernelName:t.Relu6,backendName:"cpu",kernelFunc:zt};function Wt(e,t,n,a,s){if("linear"===n)return f({inputs:{x:t},backend:e});if("relu"===n)return _t({inputs:{x:t},backend:e});if("elu"===n)return xt({inputs:{x:t},backend:e});if("relu6"===n)return zt({inputs:{x:t},backend:e});if("prelu"===n)return At({inputs:{x:t,alpha:a},backend:e});if("leakyrelu"===n)return Ft({inputs:{x:t},backend:e,attrs:{alpha:s}});if("sigmoid"===n)return je({inputs:{x:t},backend:e});throw new Error(`Activation ${n} has not been implemented for the CPU backend.`)}function Pt(e){const{inputs:n,backend:a,attrs:s}=e,{x:r}=n,{shape:o}=s,i=t.util.sizeFromShape(r.shape),l=t.util.inferFromImplicitShape(o,i),u=t.util.sizeFromShape(l);t.util.assert(i===u,(()=>`The new shape (${l}) has ${u} elements and the old shape (${r.shape}) has ${i} elements. The new shape and old shape must have the same number of elements.`)),a.incRef(r.dataId);const d=a.data.get(r.dataId);if(null!=d.complexTensorInfos){const e=d.complexTensorInfos.real,t=d.complexTensorInfos.imag;e.shape=l,t.shape=l}return{dataId:r.dataId,shape:l,dtype:r.dtype}}const Ct={kernelName:t.Reshape,backendName:"cpu",kernelFunc:Pt};function Ht(e){const{inputs:n,backend:a,attrs:s}=e,{a:o,b:i}=n,{transposeA:l,transposeB:u}=s;r([o,i],"matMul");const d=o.shape.length,c=i.shape.length,p=l?o.shape[d-2]:o.shape[d-1],h=u?i.shape[c-1]:i.shape[c-2],f=l?o.shape[d-1]:o.shape[d-2],m=u?i.shape[c-2]:i.shape[c-1],k=o.shape.slice(0,-2),g=i.shape.slice(0,-2),I=t.util.sizeFromShape(k),b=t.util.sizeFromShape(g),y=t.broadcast_util.assertAndGetBroadcastShape(o.shape.slice(0,-2),i.shape.slice(0,-2)).concat([f,m]);t.util.assert(p===h,(()=>`Error in matMul: inner shapes (${p}) and (${h}) of Tensors with shapes ${o.shape} and ${i.shape} and transposeA=${l} and transposeB=${u} must match.`));const S=u?[b,m,h]:[b,h,m],T=Pt({inputs:{x:o},backend:a,attrs:{shape:l?[I,p,f]:[I,f,p]}}),N=Pt({inputs:{x:i},backend:a,attrs:{shape:S}}),x=l?T.shape[1]:T.shape[2],v=l?T.shape[2]:T.shape[1],F=u?N.shape[1]:N.shape[2],w=Math.max(I,b),M=a.data.get(T.dataId).values,A=a.data.get(N.dataId).values,D=t.util.computeStrides(T.shape),_=t.util.computeStrides(N.shape),[E,z,R]=l?[D[0],1,D[1]]:[D[0],D[1],1],[W,P,C]=u?[1,_[1],_[0]]:[_[1],1,_[0]],H=v*F,O=t.buffer([w,v,F],T.dtype),V=O.values,B=a.blockSize;for(let e=0;e<w;e++)for(let t=0;t<v;t+=B)for(let n=0;n<F;n+=B)for(let a=0;a<x;a+=B){const s=Math.min(t+B,v),r=Math.min(n+B,F),o=Math.min(a+B,x);for(let i=t;i<s;i++)for(let t=n;t<r;t++){let n=0;for(let s=a;s<o;s++){const a=Math.min(e,I-1)*E,r=Math.min(e,b-1)*C;n+=M[a+i*z+s*R]*A[s*W+t*P+r]}V[e*H+(i*F+t)]+=n}}return a.disposeIntermediateTensorInfo(T),a.disposeIntermediateTensorInfo(N),a.makeTensorInfo(y,O.dtype,O.values)}const Ot={kernelName:t.BatchMatMul,backendName:"cpu",kernelFunc:Ht};const Vt={kernelName:t._FusedMatMul,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{a:s,b:r,bias:o,preluActivationWeights:i}=t,{transposeA:l,transposeB:u,activation:d,leakyreluAlpha:c}=a;let p,h,f;const m=[];p=Ht({inputs:{a:s,b:r},attrs:{transposeA:l,transposeB:u},backend:n}),o&&(h=v({inputs:{a:p,b:o},backend:n}),m.push(p),p=h),d&&(f=Wt(n,p,d,i,c),m.push(p),p=f);for(const e of m)n.disposeIntermediateTensorInfo(e);return p}},Bt=D(t.Acos,(e=>Math.acos(e))),$t={kernelName:t.Acos,backendName:"cpu",kernelFunc:Bt},Lt=D(t.Acosh,(e=>Math.acosh(e))),Gt={kernelName:t.Acosh,backendName:"cpu",kernelFunc:Lt};const qt={kernelName:t.AddN,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a}=e,s=n;r(n,"addN");const o=s.map((e=>a.data.get(e.dataId).values)),i=t.buffer(s[0].shape,s[0].dtype),l=i.values;for(let e=0;e<s.length;e++){const t=o[e];for(let e=0;e<l.length;e++)l[e]+=t[e]}return a.makeTensorInfo(i.shape,i.dtype,i.values)}};const Ut={kernelName:t.All,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{axis:i,keepDims:l}=s;r(o,"all");const u=t.util.parseAxisParam(i,o.shape);let d=u;const c=t.backend_util.getAxesPermutation(d,o.shape.length);let p=o;null!=c&&(p=Ae({inputs:{x:o},backend:a,attrs:{perm:c}}),d=t.backend_util.getInnerMostAxes(d.length,o.shape.length)),t.backend_util.assertAxesAreInnerMostDims("all",d,p.shape.length);const[h,f]=t.backend_util.computeOutAndReduceShapes(p.shape,d),m=t.util.sizeFromShape(f),k=t.util.makeZerosTypedArray(t.util.sizeFromShape(h),p.dtype),g=a.data.get(p.dataId).values;for(let e=0;e<k.length;++e){const t=e*m;let n=g[t];for(let e=0;e<m;++e){const a=g[t+e];n=n&&a}k[e]=n}null!=c&&a.disposeIntermediateTensorInfo(p);const I=a.makeTensorInfo(h,p.dtype,k);if(l){const e=Pt({inputs:{x:I},backend:a,attrs:{shape:t.backend_util.expandShapeToKeepDim(h,u)}});return a.disposeIntermediateTensorInfo(I),e}return I}};const Zt={kernelName:t.Any,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{axis:i,keepDims:l}=s;r(o,"any");const u=t.util.parseAxisParam(i,o.shape);let d=u;const c=t.backend_util.getAxesPermutation(d,o.shape.length);let p=o;null!=c&&(p=Ae({inputs:{x:o},backend:a,attrs:{perm:c}}),d=t.backend_util.getInnerMostAxes(d.length,o.shape.length)),t.backend_util.assertAxesAreInnerMostDims("any",d,p.shape.length);const[h,f]=t.backend_util.computeOutAndReduceShapes(p.shape,d),m=t.util.sizeFromShape(f),k=t.util.makeZerosTypedArray(t.util.sizeFromShape(h),p.dtype),g=a.data.get(p.dataId).values;for(let e=0;e<k.length;++e){const t=e*m;let n=g[t];for(let e=0;e<m;++e){const a=g[t+e];n=n||a}k[e]=n}null!=c&&a.disposeIntermediateTensorInfo(p);const I=a.makeTensorInfo(h,p.dtype,k);if(l){const e=Pt({inputs:{x:I},backend:a,attrs:{shape:t.backend_util.expandShapeToKeepDim(h,u)}});return a.disposeIntermediateTensorInfo(I),e}return I}};const jt={kernelName:t.ArgMax,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{axis:i}=s;r(o,"argMax");let l=t.util.parseAxisParam(i,o.shape);const u=t.backend_util.getAxesPermutation(l,o.shape.length);let d=o;const c=[];null!=u&&(d=Ae({inputs:{x:o},backend:a,attrs:{perm:u}}),c.push(d),l=t.backend_util.getInnerMostAxes(l.length,d.shape.length)),l=[l[0]],t.backend_util.assertAxesAreInnerMostDims("argMax",l,d.shape.length);const[p,h]=t.backend_util.computeOutAndReduceShapes(d.shape,l),f=t.util.sizeFromShape(p),m=t.util.makeZerosTypedArray(f,"int32"),k=t.util.sizeFromShape(h),g=a.data.get(d.dataId).values;for(let e=0;e<m.length;++e){const t=e*k;let n=g[t],a=0;for(let e=0;e<k;++e){const s=g[t+e];s>n&&(n=s,a=e)}m[e]=a}return c.forEach((e=>a.disposeIntermediateTensorInfo(e))),a.makeTensorInfo(p,"int32",m)}};const Kt={kernelName:t.ArgMin,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{axis:i}=s;r(o,"argMin");let l=t.util.parseAxisParam(i,o.shape);const u=t.backend_util.getAxesPermutation(l,o.shape.length);let d=o;const c=[];null!=u&&(d=Ae({inputs:{x:o},backend:a,attrs:{perm:u}}),c.push(d),l=t.backend_util.getInnerMostAxes(l.length,d.shape.length)),l=[l[0]],t.backend_util.assertAxesAreInnerMostDims("argMin",l,d.shape.length);const[p,h]=t.backend_util.computeOutAndReduceShapes(d.shape,l),f=t.util.sizeFromShape(p),m=t.util.makeZerosTypedArray(f,"int32"),k=t.util.sizeFromShape(h),g=a.data.get(d.dataId).values;for(let e=0;e<m.length;++e){const t=e*k;let n=g[t],a=0;for(let e=0;e<k;++e){const s=g[t+e];s<n&&(n=s,a=e)}m[e]=a}return c.forEach((e=>a.disposeIntermediateTensorInfo(e))),a.makeTensorInfo(p,"int32",m)}},Yt=D(t.Asin,(e=>Math.asin(e))),Jt={kernelName:t.Asin,backendName:"cpu",kernelFunc:Yt},Qt=D(t.Asinh,(e=>Math.asinh(e))),Xt={kernelName:t.Asinh,backendName:"cpu",kernelFunc:Qt},en=D(t.Atan,(e=>Math.atan(e))),tn={kernelName:t.Atan,backendName:"cpu",kernelFunc:en},nn=d(((e,t)=>Math.atan2(e,t))),an=S(t.Atan2,nn),sn={kernelName:t.Atan2,backendName:"cpu",kernelFunc:an},rn=D(t.Atanh,(e=>Math.atanh(e))),on={kernelName:t.Atanh,backendName:"cpu",kernelFunc:rn};function ln(e,n,a,s,r,o){const i=r.strideHeight,l=r.strideWidth,u=r.dilationHeight,d=r.dilationWidth,c=r.effectiveFilterHeight,p=r.effectiveFilterWidth,h=r.padInfo.top,f=r.padInfo.left,m="max"===o?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,k=t.buffer(r.outShape,a),g=k.values,I=r.outShape[1]*r.outShape[2]*r.outShape[3],b=r.outShape[2]*r.outShape[3],y=r.outShape[3];for(let t=0;t<r.batchSize;++t){const n=t*I,a=t*s[0];for(let t=0;t<r.inChannels;++t)for(let k=0;k<r.outHeight;++k){const I=k*i-h,S=Math.max(0,I),T=Math.min(r.inHeight,c+I),N=n+k*b;for(let n=0;n<r.outWidth;++n){const i=n*l-f,c=Math.max(0,i),h=Math.min(r.inWidth,p+i);let k=m,I=0,b=0;for(let n=S;n<T;n+=u){const r=a+n*s[1];for(let n=c;n<h;n+=d){const a=e[r+n*s[2]+t];"max"===o&&a>k?k=a:"avg"===o&&(I+=a,b++)}if(isNaN(k))break}g[N+n*y+t]="avg"===o?I/b:k}}}return k}function un(e,n,a,s,r=!1,o=!1){const i=t.buffer(s.outShape,"int32"),l=s.strideHeight,u=s.strideWidth,d=s.dilationHeight,c=s.dilationWidth,p=s.effectiveFilterHeight,h=s.effectiveFilterWidth,f=s.padInfo.top,m=s.padInfo.left,k=t.buffer(n,a,e);for(let e=0;e<s.batchSize;++e)for(let t=0;t<s.inChannels;++t)for(let n=0;n<s.outHeight;++n){const a=n*l-f;let g=a;for(;g<0;)g+=d;const I=Math.min(s.inHeight,p+a);for(let l=0;l<s.outWidth;++l){const p=l*u-m;let f=p;for(;f<0;)f+=c;const b=Math.min(s.inWidth,h+p);let y=Number.NEGATIVE_INFINITY,S=-1;for(let n=g;n<I;n+=d){const i=n-a;for(let a=f;a<b;a+=c){const l=a-p,u=k.get(e,n,a,t);u>y&&(y=u,S=r?o?((e*s.inHeight+n)*s.inWidth+a)*s.inChannels+t:(n*s.inWidth+a)*s.inChannels+t:i*h+l)}}i.set(S,e,n,l,t)}}return i}function dn(e,n,a,s,r,o){const i=r.strideDepth,l=r.strideHeight,u=r.strideWidth,d=r.dilationDepth,c=r.dilationHeight,p=r.dilationWidth,h=r.effectiveFilterDepth,f=r.effectiveFilterHeight,m=r.effectiveFilterWidth,k=r.padInfo.front,g=r.padInfo.top,I=r.padInfo.left,b="max"===o?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,y=t.buffer(r.outShape,a),S=y.values,T=r.outShape[1]*r.outShape[2]*r.outShape[3]*r.outShape[4],N=r.outShape[2]*r.outShape[3]*r.outShape[4],x=r.outShape[3]*r.outShape[4],v=r.outShape[4];for(let t=0;t<r.batchSize;++t){const n=t*T,a=t*s[0];for(let t=0;t<r.inChannels;++t)for(let y=0;y<r.outDepth;++y){const T=y*i-k;let F=T;for(;F<0;)F+=d;const w=Math.min(r.inDepth,h+T),M=n+y*N;for(let n=0;n<r.outHeight;++n){const i=n*l-g;let h=i;for(;h<0;)h+=c;const k=Math.min(r.inHeight,f+i),y=M+n*x;for(let n=0;n<r.outWidth;++n){const i=n*u-I;let l=i;for(;l<0;)l+=p;const f=Math.min(r.inWidth,m+i),g=y+n*v;let T=b,N=0,x=0;for(let n=F;n<w;n+=d){const r=a+n*s[1];for(let n=h;n<k;n+=c){const a=r+n*s[2];for(let n=l;n<f;n+=p){const r=e[a+n*s[3]+t];if("max"===o&&r>T?T=r:"avg"===o&&(N+=r,x++),isNaN(T))break}if(isNaN(T))break}if(isNaN(T))break}S[g+t]="avg"===o?N/x:T}}}}return y}const cn={kernelName:t.AvgPool,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n;r(o,"avgPool");const{filterSize:i,strides:l,pad:u,dimRoundingMode:d}=s;t.util.assert(t.backend_util.eitherStridesOrDilationsAreOne(l,1),(()=>`Error in avgPool: Either strides or dilations must be 1. Got strides ${l} and dilations '1'`));const c=t.backend_util.computePool2DInfo(o.shape,i,l,1,u,d);let p;if(1===c.filterWidth&&1===c.filterHeight&&t.util.arraysEqual(c.inShape,c.outShape))p=f({inputs:{x:o},backend:a});else{const e=a.data.get(o.dataId).values,n=t.util.computeStrides(o.shape),s=ln(e,o.shape,o.dtype,n,c,"avg");p=a.makeTensorInfo(c.outShape,o.dtype,s.values)}return p}};const pn={kernelName:t.AvgPool3D,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{filterSize:i,strides:l,pad:u,dimRoundingMode:d,dataFormat:c}=s;r(o,"avgPool3d");const p=t.backend_util.computePool3DInfo(o.shape,i,l,1,u,d,c),h=dn(a.data.get(o.dataId).values,o.shape,o.dtype,t.util.computeStrides(o.shape),p,"avg");return a.makeTensorInfo(h.shape,"float32",h.values)}};const hn={kernelName:t.AvgPool3DGrad,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{dy:o,input:i}=n,{filterSize:l,strides:u,pad:d,dimRoundingMode:c}=s;r([o,i],"avgPool3DGrad");const p=t.backend_util.computePool3DInfo(i.shape,l,u,1,d,c),h=p.strideDepth,f=p.strideHeight,m=p.strideWidth,k=p.filterDepth,g=p.filterHeight,I=p.filterWidth,b=p.dilationDepth,y=p.dilationHeight,S=p.dilationWidth,T=p.effectiveFilterDepth,N=p.effectiveFilterHeight,x=p.effectiveFilterWidth,v=T-1-p.padInfo.front,F=x-1-p.padInfo.left,w=N-1-p.padInfo.top,M=t.buffer(i.shape,"float32"),A=1/(k*g*I),D=a.bufferSync(o);for(let e=0;e<p.batchSize;++e)for(let t=0;t<p.inChannels;++t)for(let n=0;n<p.inDepth;++n)for(let a=0;a<p.inHeight;++a)for(let s=0;s<p.inWidth;++s){const r=n-v,o=a-w,i=s-F;let l=0;for(let n=0;n<T;n+=b){const a=(r+n)/h;if(!(a<0||a>=p.outDepth||Math.floor(a)!==a))for(let n=0;n<N;n+=y){const s=(o+n)/f;if(!(s<0||s>=p.outHeight||Math.floor(s)!==s))for(let n=0;n<x;n+=S){const r=(i+n)/m;if(r<0||r>=p.outWidth||Math.floor(r)!==r)continue;l+=D.get(e,a,s,r,t)}}}M.set(l*A,e,n,a,s,t)}return a.makeTensorInfo(M.shape,M.dtype,M.values)}};const fn={kernelName:t.AvgPoolGrad,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{dy:o,input:i}=n,l=i;r([o,i],"avgPoolGrad");const{filterSize:u,strides:d,pad:c}=s,p=t.backend_util.computePool2DInfo(l.shape,u,d,1,c),h=p.strideHeight,f=p.strideWidth,m=p.filterHeight,k=p.filterWidth,g=p.dilationHeight,I=p.dilationWidth,b=p.effectiveFilterHeight,y=p.effectiveFilterWidth,S=y-1-p.padInfo.left,T=b-1-p.padInfo.top,N=t.buffer(l.shape,"float32"),x=1/(m*k),v=a.data.get(o.dataId).values,F=t.buffer(o.shape,"float32",v);for(let e=0;e<p.batchSize;++e)for(let t=0;t<p.inChannels;++t)for(let n=0;n<p.inHeight;++n)for(let a=0;a<p.inWidth;++a){const s=n-T,r=a-S;let o=0;for(let n=0;n<b;n+=g){const a=(s+n)/h;if(!(a<0||a>=p.outHeight||Math.floor(a)!==a))for(let n=0;n<y;n+=I){const s=(r+n)/f;if(s<0||s>=p.outWidth||Math.floor(s)!==s)continue;o+=F.get(e,a,s,t)}}N.set(o*x,e,n,a,t)}return a.makeTensorInfo(N.shape,N.dtype,N.values)}};const mn={kernelName:t.FusedBatchNorm,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o,scale:i,offset:l,mean:u,variance:d}=n;t.util.assert(u.shape.length===d.shape.length,(()=>"Batch normalization gradient requires mean and variance to have equal ranks.")),t.util.assert(null==l||u.shape.length===l.shape.length,(()=>"Batch normalization gradient requires mean and offset to have equal ranks.")),t.util.assert(null==i||u.shape.length===i.shape.length,(()=>"Batch normalization gradient requires mean and scale to have equal ranks.")),r([o,u,d,i,l],"batchNorm");let{varianceEpsilon:c}=s;null==c&&(c=.001);const p=a.data.get(o.dataId).values,h=a.data.get(u.dataId).values,f=a.data.get(d.dataId).values,m=i?a.data.get(i.dataId).values:new Float32Array([1]),k=l?a.data.get(l.dataId).values:new Float32Array([0]),g=new Float32Array(p.length),I=k.length,b=m.length,y=f.length,S=h.length;let T=0,N=0,x=0,v=0;for(let e=0;e<p.length;++e)g[e]=k[T++]+(p[e]-h[N++])*m[x++]/Math.sqrt(f[v++]+c),T>=I&&(T=0),N>=S&&(N=0),x>=b&&(x=0),v>=y&&(v=0);return a.makeTensorInfo(o.shape,o.dtype,g)}};const kn={kernelName:t.BatchToSpaceND,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{blockShape:i,crops:l}=s;r([o],"batchToSpaceND");const u=i.reduce(((e,t)=>e*t)),d=t.backend_util.getReshaped(o.shape,i,u),c=t.backend_util.getPermuted(d.length,i.length),p=t.backend_util.getReshapedPermuted(o.shape,i,u),h=t.backend_util.getSliceBeginCoords(l,i.length),f=t.backend_util.getSliceSize(p,l,i.length),m=Pt({inputs:{x:o},backend:a,attrs:{shape:d}}),k=Ae({inputs:{x:m},backend:a,attrs:{perm:c}}),g=Pt({inputs:{x:k},backend:a,attrs:{shape:p}}),I=Je({inputs:{x:g},backend:a,attrs:{begin:h,size:f}});return a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(k),a.disposeIntermediateTensorInfo(g),I}};const gn={kernelName:t.Bincount,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,weights:r}=t,{size:o}=a,i=w(n.data.get(s.dataId).values,n.data.get(r.dataId).values,r.dtype,r.shape,o);return n.makeTensorInfo([o],r.dtype,i)}};const In={kernelName:t.BroadcastArgs,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a}=e,{s0:s,s1:r}=n,o=a.data.get(s.dataId).values,i=a.data.get(r.dataId).values,l=t.backend_util.assertAndGetBroadcastShape(Array.from(o),Array.from(i));return a.makeTensorInfo([l.length],"int32",Int32Array.from(l))}},bn=D(t.ClipByValue,((e,t)=>{const n=t;return e>n.clipValueMax?n.clipValueMax:e<n.clipValueMin?n.clipValueMin:e})),yn={kernelName:t.ClipByValue,backendName:"cpu",kernelFunc:bn},Sn={kernelName:t.ComplexAbs,backendName:"cpu",kernelFunc:e=>{const{x:n}=e.inputs,a=e.backend,s=new Float32Array(t.util.sizeFromShape(n.shape)),r=a.data.get(n.dataId),o=r.complexTensorInfos.real,i=r.complexTensorInfos.imag,l=a.data.get(o.dataId).values,u=a.data.get(i.dataId).values;for(let e=0;e<l.length;e++){const t=l[e],n=u[e];s[e]=Math.hypot(t,n)}return a.makeOutput(s,n.shape,"float32")}};function Tn(e){const{inputs:t,backend:n}=e,{input:a}=t,s=n.data.get(a.dataId).complexTensorInfos.imag,r=n.data.get(s.dataId).values;return n.makeTensorInfo(s.shape,s.dtype,r)}const Nn={kernelName:t.Imag,backendName:"cpu",kernelFunc:Tn};function xn(e){const{inputs:n,backend:a,attrs:s}=e,{axis:r}=s,o=t.util.parseAxisParam(r,n[0].shape)[0],i=n.map((e=>e.shape));t.backend_util.assertParamsConsistent(i,o);let l=t.backend_util.computeOutShape(n.map((e=>e.shape)),o);if(0===t.util.sizeFromShape(l))return a.makeTensorInfo(l,n[0].dtype,[]);const u=n.filter((e=>t.util.sizeFromShape(e.shape)>0));if(1===u.length)return f({inputs:{x:u[0]},backend:a});if("complex64"===u[0].dtype){const e=u.map((e=>k({inputs:{input:e},backend:a}))),t=u.map((e=>Tn({inputs:{input:e},backend:a}))),n=xn({inputs:e,backend:a,attrs:{axis:o}}),s=xn({inputs:t,backend:a,attrs:{axis:o}}),r=c({inputs:{real:n,imag:s},backend:a});return e.forEach((e=>a.disposeIntermediateTensorInfo(e))),t.forEach((e=>a.disposeIntermediateTensorInfo(e))),a.disposeIntermediateTensorInfo(n),a.disposeIntermediateTensorInfo(s),r}const d=u.map((e=>{const n=t.util.sizeFromShape(e.shape.slice(o));return Pt({inputs:{x:e},backend:a,attrs:{shape:[-1,n]}})})),p=d.map((e=>({vals:a.data.get(e.dataId).values,shape:e.shape})));l=t.backend_util.computeOutShape(d.map((e=>e.shape)),1);const h=1===d[0].shape[0],m=W(p,l,n[0].dtype,h),g=t.backend_util.computeOutShape(u.map((e=>e.shape)),o),I=a.makeTensorInfo(g,n[0].dtype,m);return d.forEach((e=>a.disposeIntermediateTensorInfo(e))),I}const vn={kernelName:t.Concat,backendName:"cpu",kernelFunc:xn};function Fn(e){const{inputs:n,backend:a,attrs:s}=e,{x:o,filter:i}=n,{strides:l,pad:u,dataFormat:d,dilations:c,dimRoundingMode:p}=s;r([o,i],"conv2d");const h=t.backend_util.convertConv2DDataFormat(d),f=t.backend_util.computeConv2DInfo(o.shape,i.shape,l,c,u,p,!1,h),m=f.filterHeight,k=f.filterWidth,g=f.dilationHeight,I=f.dilationWidth,b=f.padInfo.left,y=f.padInfo.top,S="channelsLast"===f.dataFormat,T=new t.TensorBuffer(f.outShape,o.dtype),N=t.util.computeStrides(o.shape),x=t.util.computeStrides(i.shape),v=N[0],F=S?N[1]:N[2],w=S?N[2]:1,M=S?1:N[1],A=T.strides[0],D=S?T.strides[1]:T.strides[2],_=S?T.strides[2]:1,E=S?1:T.strides[1],z=a.data.get(o.dataId).values,R=a.data.get(i.dataId).values,W=T.values;for(let e=0;e<f.batchSize;++e){const t=e*v,n=e*A;for(let e=0;e<f.outHeight;++e){const a=n+e*D,s=e*f.strideHeight-y;for(let e=0;e<m;++e){const n=s+e*g;if(n<0||n>=f.inHeight)continue;const r=e*x[0],o=t+n*F;for(let e=0;e<f.outWidth;++e){const t=a+e*_,n=e*f.strideWidth-b;for(let e=0;e<k;++e){const a=n+e*I;if(a<0||a>=f.inWidth)continue;const s=o+a*w;let i=r+e*x[1];for(let e=0;e<f.inChannels;++e){const n=z[s+e*M];for(let e=0;e<f.outChannels;++e)W[t+e*E]+=n*R[i+e];i+=f.outChannels}}}}}}return a.makeTensorInfo(T.shape,T.dtype,W)}const wn={kernelName:t.Conv2D,backendName:"cpu",kernelFunc:Fn};const Mn={kernelName:t.Conv2DBackpropFilter,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o,dy:i}=n,{strides:l,pad:u,dataFormat:d,dimRoundingMode:c,filterShape:p}=s;r([o,i],"conv2dBackpropFilter");const h=t.backend_util.convertConv2DDataFormat(d),f=t.backend_util.computeConv2DInfo(o.shape,p,l,1,u,c,!1,h),{strideHeight:m,strideWidth:k,filterHeight:g,filterWidth:I}=f,b="channelsLast"===f.dataFormat,y=new t.TensorBuffer(f.filterShape,"float32"),S=f.padInfo.left,T=f.padInfo.top,N=a.data.get(o.dataId).values,x=a.data.get(i.dataId).values,v=new t.TensorBuffer(o.shape,o.dtype,N),F=new t.TensorBuffer(i.shape,i.dtype,x);for(let e=0;e<g;++e){const t=Math.max(0,Math.ceil((T-e)/m)),n=Math.min(f.outHeight,(f.inHeight+T-e)/m);for(let a=0;a<I;++a){const s=Math.max(0,Math.ceil((S-a)/k)),r=Math.min(f.outWidth,(f.inWidth+S-a)/k);for(let o=0;o<f.inChannels;++o)for(let i=0;i<f.outChannels;++i){let l=0;for(let u=0;u<f.batchSize;++u)for(let d=t;d<n;++d){const t=e+d*m-T;for(let e=s;e<r;++e){const n=a+e*k-S;l+=b?v.get(u,t,n,o)*F.get(u,d,e,i):v.get(u,o,t,n)*F.get(u,i,d,e)}}y.set(l,e,a,o,i)}}}return a.makeTensorInfo(y.shape,y.dtype,y.values)}};const An={kernelName:t.Conv2DBackpropInput,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{dy:o,filter:i}=n,{inputShape:l,strides:u,pad:d,dataFormat:c,dimRoundingMode:p}=s;r([o,i],"conv2dBackpropInput");const h=t.util.computeStrides(i.shape),f=t.util.computeStrides(o.shape);let m=t.backend_util.convertConv2DDataFormat(c);const k=t.backend_util.computeConv2DInfo(l,i.shape,u,1,d,p,!1,m),g=new t.TensorBuffer(k.inShape,"float32"),I=g.values,b=a.data.get(o.dataId).values,y=a.data.get(i.dataId).values,[S,T,N]=h,{batchSize:x,filterHeight:v,filterWidth:F,inChannels:w,inHeight:M,inWidth:A,outChannels:D,outHeight:_,outWidth:E,strideHeight:z,strideWidth:R}=k;m=k.dataFormat;const W=v-1-k.padInfo.top,P=F-1-k.padInfo.left,C="channelsLast"===m,H=g.strides[0],O=C?g.strides[1]:g.strides[2],V=C?g.strides[2]:1,B=C?1:g.strides[1],$=f[0],L=C?f[1]:f[2],G=C?f[2]:1,q=C?1:f[1];for(let e=0;e<x;++e)for(let t=0;t<w;++t)for(let n=0;n<M;++n){const a=n-W,s=Math.max(0,Math.ceil(a/z)),r=Math.min(_,(v+a)/z);for(let o=0;o<A;++o){const i=o-P,l=Math.max(0,Math.ceil(i/R)),u=Math.min(E,(F+i)/R);let d=0;for(let n=s;n<r;++n){const s=n*z-a;for(let a=l;a<u;++a){const r=$*e+L*n+G*a,o=S*(v-1-s)+T*(F-1-(a*R-i))+N*t;for(let e=0;e<D;++e){d+=b[r+q*e]*y[o+e]}}}I[H*e+O*n+V*o+B*t]=d}}return a.makeTensorInfo(g.shape,g.dtype,g.values)}};const Dn={kernelName:t.Conv3D,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o,filter:i}=n,{strides:l,pad:u,dilations:d}=s;r([o,i],"conv3d");const c=t.backend_util.computeConv3DInfo(o.shape,i.shape,l,d,u),{filterDepth:p,filterHeight:h,filterWidth:f,dilationDepth:m,dilationHeight:k,dilationWidth:g,padInfo:I}=c,b=I.front,y=I.left,S=I.top,T=new t.TensorBuffer(c.outShape,o.dtype),N=a.data.get(o.dataId).values,x=a.data.get(i.dataId).values,v=T.values,F=t.util.computeStrides(o.shape),w=t.util.computeStrides(i.shape);for(let e=0;e<c.batchSize;++e){const t=e*F[0],n=e*T.strides[0];for(let e=0;e<c.outDepth;++e){const a=n+e*T.strides[1],s=e*c.strideDepth-b;for(let e=0;e<p;++e){const n=s+e*m;if(n<0||n>=c.inDepth)continue;const r=e*w[0],o=t+n*F[1];for(let e=0;e<c.outHeight;++e){const t=a+e*T.strides[2],n=e*c.strideHeight-S;for(let e=0;e<h;++e){const a=n+e*k;if(a<0||a>=c.inHeight)continue;const s=r+e*w[1],i=o+a*F[2];for(let e=0;e<c.outWidth;++e){const n=t+e*c.outChannels,a=e*c.strideWidth-y;for(let e=0;e<f;++e){const t=a+e*g;if(t<0||t>=c.inWidth)continue;const r=s+e*w[2],o=i+t*c.inChannels;let l=r;for(let e=0;e<c.inChannels;++e){const t=N[o+e];for(let e=0;e<c.outChannels;++e)v[n+e]+=t*x[l+e];l+=c.outChannels}}}}}}}}return a.makeTensorInfo(T.shape,T.dtype,T.values)}};const _n={kernelName:t.Conv3DBackpropFilterV2,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o,dy:i}=n,{strides:l,pad:u,filterShape:d}=s;r([o,i],"conv3dBackpropFilterV2");const c=t.util.computeStrides(o.shape),p=t.util.computeStrides(i.shape),h=t.backend_util.computeConv3DInfo(o.shape,d,l,1,u),f=h.strideDepth,m=h.strideHeight,k=h.strideWidth,g=h.filterDepth,I=h.filterHeight,b=h.filterWidth,y=new t.TensorBuffer(h.filterShape,"float32"),S=y.values,[T,N,x,v]=y.strides,F=a.data.get(i.dataId).values,[w,M,A,D]=p,_=a.data.get(o.dataId).values,[E,z,R,W]=c,P=h.padInfo.front,C=h.padInfo.left,H=h.padInfo.top;for(let e=0;e<g;++e){const t=Math.max(0,Math.ceil((P-e)/f)),n=Math.min(h.outDepth,(h.inDepth+P-e)/f),a=e*T;for(let s=0;s<I;++s){const r=Math.max(0,Math.ceil((H-s)/m)),o=Math.min(h.outHeight,(h.inHeight+H-s)/m),i=s*N+a;for(let a=0;a<b;++a){const l=Math.max(0,Math.ceil((C-a)/k)),u=Math.min(h.outWidth,(h.inWidth+C-a)/k),d=a*x+i;for(let i=0;i<h.inChannels;++i){const c=i*v+d;for(let d=0;d<h.outChannels;++d){let p=0;for(let c=0;c<h.batchSize;++c){const h=c*E,g=c*w;for(let c=t;c<n;++c){const t=(e+c*f-P)*z+h,n=c*M+g;for(let e=r;e<o;++e){const r=(s+e*m-H)*R+t,o=e*A+n;for(let e=l;e<u;++e){const t=e*D+o;p+=_[(a+e*k-C)*W+r+i]*F[t+d]}}}}S[c+d]=p}}}}}return a.makeTensorInfo(y.shape,y.dtype,y.values)}};const En={kernelName:t.Conv3DBackpropInputV2,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{dy:o,filter:i}=n,{pad:l,strides:u,inputShape:d}=s;r([o],"conv3dBackpropInputV2");const c=t.util.computeStrides(o.shape),p=t.util.computeStrides(i.shape),h=t.backend_util.computeConv3DInfo(d,i.shape,u,1,l),f=new t.TensorBuffer(h.inShape,"float32"),m=f.values,[k,g,I,b]=f.strides,y=a.data.get(o.dataId).values,[S,T,N,x]=c,v=a.data.get(i.dataId).values,[F,w,M,A]=p,{batchSize:D,filterDepth:_,filterHeight:E,filterWidth:z,inChannels:R,inDepth:W,inHeight:P,inWidth:C,outChannels:H,outDepth:O,outHeight:V,outWidth:B,strideDepth:$,strideHeight:L,strideWidth:G}=h,q=_-1-h.padInfo.front,U=E-1-h.padInfo.top,Z=z-1-h.padInfo.left;for(let e=0;e<D;++e)for(let t=0;t<R;++t)for(let n=0;n<W;++n){const a=n-q,s=Math.max(0,Math.ceil(a/$)),r=Math.min(O,(_+a)/$);for(let o=0;o<P;++o){const i=o-U,l=Math.max(0,Math.ceil(i/L)),u=Math.min(V,(E+i)/L);for(let d=0;d<C;++d){const c=d-Z,p=Math.max(0,Math.ceil(c/G)),h=Math.min(B,(z+c)/G);let f=0;for(let n=s;n<r;++n){const s=n*$-a;for(let a=l;a<u;++a){const r=a*L-i;for(let o=p;o<h;++o){const i=S*e+T*n+N*a+x*o,l=F*(_-1-s)+w*(E-1-r)+M*(z-1-(o*G-c))+A*t;for(let e=0;e<H;++e){f+=y[i+e]*v[l+e]}}}}m[k*e+g*n+I*o+b*d+t]=f}}}return a.makeTensorInfo(f.shape,f.dtype,f.values)}},zn=D(t.Cos,(e=>Math.cos(e))),Rn={kernelName:t.Cos,backendName:"cpu",kernelFunc:zn},Wn=D(t.Cosh,(e=>Math.cosh(e))),Pn={kernelName:t.Cosh,backendName:"cpu",kernelFunc:Wn};const Cn={kernelName:t.CropAndResize,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{image:r,boxes:o,boxInd:i}=n,{cropSize:l,method:u,extrapolationValue:d}=s,[c,p,h,f]=r.shape,m=o.shape[0],[k,g]=l,I=t.buffer([m,k,g,f],"float32"),b=a.data.get(o.dataId).values,y=a.data.get(i.dataId).values,S=a.data.get(r.dataId).values,T=t.util.computeStrides(r.shape),N=t.util.computeStrides(I.shape);for(let e=0;e<m;e++){const t=4*e,n=b[t],a=b[t+1],s=b[t+2],r=b[t+3],o=y[e];if(o>=c)continue;const i=k>1?(s-n)*(p-1)/(k-1):0,l=g>1?(r-a)*(h-1)/(g-1):0;for(let t=0;t<k;t++){const c=k>1?n*(p-1)+t*i:.5*(n+s)*(p-1);if(c<0||c>p-1)for(let n=0;n<g;n++)for(let a=0;a<f;a++){const s=a+n*N[2]+t*N[1]+e*N[0];I.values[s]=d}else if("bilinear"===u){const n=Math.floor(c),s=Math.ceil(c),i=c-n;for(let u=0;u<g;u++){const c=g>1?a*(h-1)+u*l:.5*(a+r)*(h-1);if(c<0||c>h-1){for(let n=0;n<f;n++){const a=n+u*N[2]+t*N[1]+e*N[0];I.values[a]=d}continue}const p=Math.floor(c),m=Math.ceil(c),k=c-p;for(let a=0;a<f;a++){let r=a+p*T[2]+n*T[1]+o*T[0];const l=S[r];r=a+m*T[2]+n*T[1]+o*T[0];const d=S[r];r=a+p*T[2]+s*T[1]+o*T[0];const c=S[r];r=a+m*T[2]+s*T[1]+o*T[0];const h=l+(d-l)*k,f=c+(S[r]-c)*k;r=a+u*N[2]+t*N[1]+e*N[0],I.values[r]=h+(f-h)*i}}}else for(let n=0;n<g;++n){const s=g>1?a*(h-1)+n*l:.5*(a+r)*(h-1);if(s<0||s>h-1){for(let a=0;a<f;a++){const s=a+n*N[2]+t*N[1]+e*N[0];I.values[s]=d}continue}const i=Math.round(s),u=Math.round(c);for(let a=0;a<f;a++){const s=a+i*T[2]+u*T[1]+o*T[0],r=a+n*N[2]+t*N[1]+e*N[0];I.values[r]=S[s]}}}}return a.makeTensorInfo(I.shape,I.dtype,I.values)}};const Hn={kernelName:t.Cumprod,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{axis:i,exclusive:l,reverse:u}=s;r(o,"cumprod");const d=t.backend_util.getAxesPermutation([i],o.shape.length);let c=o;null!=d&&(c=Ae({inputs:{x:o},backend:a,attrs:{perm:d}}));const p=t.backend_util.getInnerMostAxes(1,o.shape.length)[0];if(p!==c.shape.length-1)throw new Error(`backend.cumprod in CPU expects an inner-most axis=${c.shape.length-1} but got axis=${p}`);const h=t.upcastType(c.dtype,"int32"),f=t.util.makeOnesTypedArray(t.util.sizeFromShape(c.shape),h),m=a.data.get(c.dataId).values,k=c.shape[c.shape.length-1],g=u?(e,t)=>e+k-t-1:(e,t)=>e+t;for(let e=0;e<m.length;e+=k)for(let t=0;t<k;t++){const n=g(e,t);if(0===t)f[n]=l?1:m[n];else{const a=g(e,t-1);f[n]=l?m[a]*f[a]:m[n]*f[a]}}const I=a.makeTensorInfo(c.shape,h,f);if(null!=d){const e=Ae({inputs:{x:I},backend:a,attrs:{perm:t.backend_util.getUndoAxesPermutation(d)}});return a.disposeIntermediateTensorInfo(I),a.disposeIntermediateTensorInfo(c),e}return I}};const On={kernelName:t.Cumsum,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{axis:i,exclusive:l,reverse:u}=s;r(o,"cumsum");const d=t.backend_util.getAxesPermutation([i],o.shape.length);let c=o;null!=d&&(c=Ae({inputs:{x:o},backend:a,attrs:{perm:d}}));const p=t.backend_util.getInnerMostAxes(1,o.shape.length)[0];if(p!==c.shape.length-1)throw new Error(`backend.cumsum in CPU expects an inner-most axis=${c.shape.length-1} but got axis=${p}`);const h=t.upcastType(c.dtype,"int32"),f=t.util.makeZerosTypedArray(t.util.sizeFromShape(c.shape),h),m=a.data.get(c.dataId).values,k=c.shape[c.shape.length-1],g=u?(e,t)=>e+k-t-1:(e,t)=>e+t;for(let e=0;e<m.length;e+=k)for(let t=0;t<k;t++){const n=g(e,t);if(0===t)f[n]=l?0:m[n];else{const a=g(e,t-1);f[n]=l?m[a]+f[a]:m[n]+f[a]}}const I=a.makeTensorInfo(c.shape,h,f);if(null!=d){const e=Ae({inputs:{x:I},backend:a,attrs:{perm:t.backend_util.getUndoAxesPermutation(d)}});return a.disposeIntermediateTensorInfo(I),a.disposeIntermediateTensorInfo(c),e}return I}};const Vn={kernelName:t.DenseBincount,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,weights:r}=t,{size:o,binaryOutput:i}=a;if(1===s.shape.length){const e=w(n.data.get(s.dataId).values,n.data.get(r.dataId).values,r.dtype,r.shape,o);return n.makeTensorInfo([o],r.dtype,e)}if(2===s.shape.length){const e=M(n.bufferSync(s),n.bufferSync(r),o,i);return n.makeTensorInfo(e.shape,r.dtype,e.values)}throw new Error(`Error in denseBincount: input must be at most rank 2, but got rank${s.shape.length}.`)}};const Bn={kernelName:t.DepthToSpace,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:r}=n,{blockSize:o,dataFormat:i}=s;t.util.assert("NHWC"===i,(()=>`Only NHWC dataFormat supported on CPU for depthToSpace. Got ${i}`));const l=r.shape[0],u=r.shape[1],d=r.shape[2],c=r.shape[3],p=u*o,h=d*o,f=c/(o*o),m=a.data.get(r.dataId).values,k=new Float32Array(l*p*h*f);let g=0;for(let e=0;e<l;++e)for(let t=0;t<p;++t){const n=Math.floor(t/o),a=t%o;for(let t=0;t<h;++t){const s=Math.floor(t/o),r=(a*o+t%o)*f;for(let t=0;t<f;++t){const a=t+r+c*(s+d*(n+u*e));k[g++]=m[a]}}}return a.makeTensorInfo([l,p,h,f],r.dtype,k)}};function $n(e){const{inputs:n,backend:a,attrs:s}=e,{x:o,filter:i}=n,{strides:l,pad:u,dilations:d,dimRoundingMode:c}=s;r([o,i],"depthwiseConv2DNative");const p=t.util.computeStrides(o.shape),h=t.util.computeStrides(i.shape);let f=d;null==f&&(f=[1,1]),t.util.assert(t.backend_util.eitherStridesOrDilationsAreOne(l,f),(()=>`Error in depthwiseConv2d: Either strides or dilations must be 1. Got strides ${l} and dilations '${f}'`));const m=t.backend_util.computeConv2DInfo(o.shape,i.shape,l,f,u,c,!0),{filterHeight:k,filterWidth:g,dilationHeight:I,dilationWidth:b,padInfo:y}=m,S=y.left,T=y.top,N=m.outChannels/m.inChannels,x=new t.TensorBuffer(m.outShape,o.dtype),v=a.data.get(o.dataId).values,F=a.data.get(i.dataId).values,w=x.values;for(let e=0;e<m.batchSize;++e){const t=e*p[0],n=e*x.strides[0];for(let e=0;e<m.outHeight;++e){const a=n+e*x.strides[1],s=e*m.strideHeight-T;for(let e=0;e<k;++e){const n=s+e*I;if(n<0||n>=m.inHeight)continue;const r=e*h[0],o=t+n*p[1];for(let e=0;e<m.outWidth;++e){const t=a+e*x.strides[2],n=e*m.strideWidth-S;for(let e=0;e<g;++e){const a=n+e*b;if(a<0||a>=m.inWidth)continue;const s=r+e*h[1],i=o+a*m.inChannels;let l=t,u=s;for(let e=0;e<m.inChannels;++e){const t=v[i+e];for(let e=0;e<N;++e)w[l+e]+=t*F[u+e];l+=N,u+=N}}}}}}return a.makeTensorInfo(x.shape,x.dtype,x.values)}const Ln={kernelName:t.DepthwiseConv2dNative,backendName:"cpu",kernelFunc:$n};const Gn={kernelName:t.DepthwiseConv2dNativeBackpropFilter,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o,dy:i}=n,{strides:l,dilations:u,pad:d,dimRoundingMode:c,filterShape:p}=s;r([o,i],"depthwiseConv2dNativeBackpropFilter");const h=t.backend_util.computeConv2DInfo(o.shape,p,l,u,d,c,!0),{strideHeight:f,strideWidth:m,filterHeight:k,filterWidth:g}=h,I=new t.TensorBuffer(h.filterShape,"float32"),b=h.padInfo.left,y=h.padInfo.top,S=h.outChannels/h.inChannels,T=a.data.get(o.dataId).values,N=new t.TensorBuffer(o.shape,o.dtype,T),x=a.data.get(i.dataId).values,v=new t.TensorBuffer(i.shape,i.dtype,x);for(let e=0;e<k;++e){const t=Math.max(0,Math.ceil((y-e)/f)),n=Math.min(h.outHeight,(h.inHeight+y-e)/f);for(let a=0;a<g;++a){const s=Math.max(0,Math.ceil((b-a)/m)),r=Math.min(h.outWidth,(h.inWidth+b-a)/m);for(let o=0;o<h.outChannels;++o){const i=Math.trunc(o/S),l=o%S;let u=0;for(let l=0;l<h.batchSize;++l)for(let d=t;d<n;++d){const t=e+d*f-y;for(let e=s;e<r;++e){const n=a+e*m-b;u+=N.get(l,t,n,i)*v.get(l,d,e,o)}}I.set(u,e,a,i,l)}}}return a.makeTensorInfo(I.shape,I.dtype,I.values)}};const qn={kernelName:t.DepthwiseConv2dNativeBackpropInput,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{dy:o,filter:i}=n,{strides:l,dilations:u,pad:d,dimRoundingMode:c,inputShape:p}=s;r([o,i],"depthwiseConv2DNativeBackpropInput");const h=t.util.computeStrides(o.shape),f=t.util.computeStrides(i.shape),m=t.backend_util.computeConv2DInfo(p,i.shape,l,u,d,c,!0),k=new t.TensorBuffer(m.inShape,"float32"),g=k.values,[I,b,y]=k.strides,S=a.data.get(o.dataId).values,[T,N,x]=h,v=a.data.get(i.dataId).values,[F,w,M]=f,{batchSize:A,filterHeight:D,filterWidth:_,inChannels:E,inHeight:z,inWidth:R,outChannels:W,outHeight:P,outWidth:C,strideHeight:H,strideWidth:O}=m,V=D-1-m.padInfo.top,B=_-1-m.padInfo.left,$=W/E;for(let e=0;e<A;++e)for(let t=0;t<E;++t)for(let n=0;n<z;++n){const a=n-V,s=Math.max(0,Math.ceil(a/H)),r=Math.min(P,(D+a)/H);for(let o=0;o<R;++o){const i=o-B,l=Math.max(0,Math.ceil(i/O)),u=Math.min(C,(_+i)/O);let d=0;for(let n=s;n<r;++n){const s=n*H-a;for(let a=l;a<u;++a){const r=T*e+N*n+x*a,o=F*(D-1-s)+w*(_-1-(a*O-i))+M*t;for(let e=0;e<$;++e){d+=S[r+(t*$+e)]*v[o+e]}}}g[I*e+b*n+y*o+t]=d}}return a.makeTensorInfo(k.shape,k.dtype,k.values)}};const Un={kernelName:t.Diag,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a}=e,{x:s}=n,r=t.util.sizeFromShape(s.shape),o=a.data.get(s.dataId).values,i=t.buffer([r,r],s.dtype),l=i.values;for(let e=0;e<o.length;e++)l[e*r+e]=o[e];const u=[...s.shape,...s.shape];return a.makeTensorInfo(u,i.dtype,i.values)}},Zn={kernelName:t.Dilation2D,backendName:"cpu",kernelFunc:({inputs:e,backend:n,attrs:a})=>{const{x:s,filter:r}=e,{strides:o,pad:i,dilations:l}=a,u=n,d=u.data.get(s.dataId).values,c=s.shape.length,p=u.data.get(r.dataId).values,h=r.shape.length,{batchSize:f,inHeight:m,inWidth:k,inChannels:g,outHeight:I,outWidth:b,padInfo:y,strideHeight:S,strideWidth:T,filterHeight:N,filterWidth:x,dilationHeight:v,dilationWidth:F,outShape:w}=t.backend_util.computeDilation2DInfo(s.shape,r.shape,o,i,"NHWC",l),M=t.util.sizeFromShape(w),A=w.length,D=t.util.getArrayFromDType(s.dtype,M);for(let e=0;e<f;++e)for(let n=0;n<I;++n){const a=n*S-y.top;for(let o=0;o<b;++o){const i=o*T-y.left;for(let l=0;l<g;++l){let u=Number.MIN_SAFE_INTEGER;for(let n=0;n<N;++n){const o=a+n*v;if(o>=0&&o<m)for(let a=0;a<x;++a){const f=i+a*F;if(f>=0&&f<k){const i=t.util.locToIndex([e,o,f,l],c,t.util.computeStrides(s.shape)),m=t.util.locToIndex([n,a,l],h,t.util.computeStrides(r.shape)),k=d[i]+p[m];k>u&&(u=k)}}}D[t.util.locToIndex([e,n,o,l],A,t.util.computeStrides(w))]=u}}}return{dataId:u.write(t.util.toTypedArray(D,s.dtype),w,s.dtype),shape:w,dtype:s.dtype}}},jn={kernelName:t.Dilation2DBackpropFilter,backendName:"cpu",kernelFunc:({inputs:e,backend:n,attrs:a})=>{const{x:s,filter:r,dy:o}=e,{strides:i,pad:l,dilations:u}=a,d=n,c=t.util.toNestedArray(s.shape,d.data.get(s.dataId).values),p=t.util.toNestedArray(r.shape,d.data.get(r.dataId).values),{batchSize:h,inHeight:f,inWidth:m,inChannels:k,outHeight:g,outWidth:I,padInfo:b,strideHeight:y,strideWidth:S,filterHeight:T,filterWidth:N,dilationHeight:x,dilationWidth:v,outShape:F}=t.backend_util.computeDilation2DInfo(s.shape,r.shape,i,l,"NHWC",u);t.util.assert(o.rank===F.length,(()=>`Error in ${t.Dilation2DBackpropFilter}, dy must have the same rank as output ${F.length}, but got ${o.rank}`));const w=t.util.toNestedArray(F,d.data.get(o.dataId).values),M=t.util.makeZerosNestedTypedArray(r.shape,r.dtype);for(let e=0;e<h;++e)for(let t=0;t<g;++t){const n=t*y-b.top;for(let a=0;a<I;++a){const s=a*S-b.left;for(let r=0;r<k;++r){let o=Number.MIN_SAFE_INTEGER,i=0,l=0;for(let t=0;t<T;++t){const a=n+t*x;if(a>=0&&a<f)for(let n=0;n<N;++n){const u=s+n*v;if(u>=0&&u<m){const s=c[e][a][u][r]+p[t][n][r];s>o&&(o=s,i=t,l=n)}}}M[i][l][r]+=w[e][t][a][r]}}}return{dataId:d.write(t.util.toTypedArray(M,s.dtype),r.shape,r.dtype),shape:r.shape,dtype:r.dtype}}},Kn={kernelName:t.Dilation2DBackpropInput,backendName:"cpu",kernelFunc:({inputs:e,backend:n,attrs:a})=>{const{x:s,filter:r,dy:o}=e,{strides:i,pad:l,dilations:u}=a,d=n,c=t.util.toNestedArray(s.shape,d.data.get(s.dataId).values),p=t.util.toNestedArray(r.shape,d.data.get(r.dataId).values),{batchSize:h,inHeight:f,inWidth:m,inChannels:k,outHeight:g,outWidth:I,padInfo:b,strideHeight:y,strideWidth:S,filterHeight:T,filterWidth:N,dilationHeight:x,dilationWidth:v,outShape:F}=t.backend_util.computeDilation2DInfo(s.shape,r.shape,i,l,"NHWC",u);t.util.assert(o.rank===F.length,(()=>`Error in ${t.Dilation2DBackpropInput}, dy must have the same rank as output ${F.length}, but got ${o.rank}`));const w=t.util.toNestedArray(F,d.data.get(o.dataId).values),M=t.util.makeZerosNestedTypedArray(s.shape,s.dtype);for(let e=0;e<h;++e)for(let t=0;t<g;++t){const n=t*y-b.top;for(let a=0;a<I;++a){const s=a*S-b.left;for(let r=0;r<k;++r){let o=Number.MIN_SAFE_INTEGER,i=n<0?0:n,l=s<0?0:s;for(let t=0;t<T;++t){const a=n+t*x;if(a>=0&&a<f)for(let n=0;n<N;++n){const u=s+n*v;if(u>=0&&u<m){const s=c[e][a][u][r]+p[t][n][r];s>o&&(o=s,i=a,l=u)}}}M[e][i][l][r]+=w[e][t][a][r]}}}return{dataId:d.write(t.util.toTypedArray(M,s.dtype),s.shape,s.dtype),shape:s.shape,dtype:s.dtype}}};function Yn(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{axis:i,keepDims:l}=s;let u;r(o,"sum"),u="bool"===o.dtype?b({inputs:{x:o},backend:a,attrs:{dtype:"int32"}}):f({inputs:{x:o},backend:a});const d=u.shape.length,c=t.util.parseAxisParam(i,u.shape),p=t.backend_util.getAxesPermutation(c,d);let m=c,k=u;null!=p&&(k=Ae({inputs:{x:u},backend:a,attrs:{perm:p}}),m=t.backend_util.getInnerMostAxes(m.length,d)),t.backend_util.assertAxesAreInnerMostDims("sum",m,k.shape.length);const[g,I]=t.backend_util.computeOutAndReduceShapes(k.shape,m);let y=h(a,g,t.backend_util.upcastType(k.dtype,"int32"));const S=t.util.sizeFromShape(I),T=a.data.get(y.dataId).values,N=a.data.get(k.dataId).values;for(let e=0;e<T.length;++e){const t=e*S;let n=0;for(let e=0;e<S;++e)n+=N[t+e];T[e]=n}if(l){const e=y;y=Pt({inputs:{x:y},backend:a,attrs:{shape:t.backend_util.expandShapeToKeepDim(y.shape,c)}}),a.disposeIntermediateTensorInfo(e)}return a.disposeIntermediateTensorInfo(u),null!=p&&a.disposeIntermediateTensorInfo(k),y}const Jn={kernelName:t.Sum,backendName:"cpu",kernelFunc:Yn};const Qn={kernelName:t.Einsum,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{equation:r}=s,o=n,{allDims:i,summedDims:l,idDims:u}=t.backend_util.decodeEinsumEquation(r,o.length);t.backend_util.checkEinsumDimSizes(i.length,u,o);const{path:d,steps:c}=t.backend_util.getEinsumComputePath(l,u),p=c.length;let h=null,f=i.length;const m=[];for(let e=0;e<p;++e){for(const n of c[e]){const{permutationIndices:e,expandDims:s}=t.backend_util.getEinsumPermutation(f,u[n]);let r;t.backend_util.isIdentityPermutation(e)?r=o[n]:(r=Ae({inputs:{x:o[n]},backend:a,attrs:{perm:e}}),m.push(r));const i=r.shape.slice();for(let e=0;e<s.length;++e)i.splice(s[e],0,1);t.util.arraysEqual(r.shape,i)||(r=Pt({inputs:{x:r},backend:a,attrs:{shape:i}}),m.push(r)),null===h?h=r:(h=Se({inputs:{a:r,b:h},backend:a}),m.push(h))}e<p-1&&(d[e]>=0&&(h=Yn({inputs:{x:h},backend:a,attrs:{axis:d[e]-(i.length-f),keepDims:!1}}),m.push(h)),f--)}for(const e of m)e!==h&&a.disposeIntermediateTensorInfo(e);return h}};const Xn={kernelName:t.EluGrad,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a}=e,{dy:s,y:o}=n;r([s,o],"eluGrad");const i=new Float32Array(t.util.sizeFromShape(o.shape)),l=a.data.get(o.dataId).values,u=a.data.get(s.dataId).values;for(let e=0;e<l.length;++e){const t=l[e];i[e]=t>=1?u[e]:u[e]*(t+1)}return a.makeTensorInfo(o.shape,"float32",i)}},ea=t.backend_util.ERF_P,ta=t.backend_util.ERF_A1,na=t.backend_util.ERF_A2,aa=t.backend_util.ERF_A3,sa=t.backend_util.ERF_A4,ra=t.backend_util.ERF_A5,oa=D(t.Erf,(e=>{const t=Math.sign(e),n=Math.abs(e),a=1/(1+ea*n);return t*(1-((((ra*a+sa)*a+aa)*a+na)*a+ta)*a*Math.exp(-n*n))})),ia={kernelName:t.Erf,backendName:"cpu",kernelFunc:oa};function la(e){const{inputs:n,backend:a,attrs:s}=e,{input:r}=n,{dim:o}=s,i=r.shape.length,l=r.shape.slice();let u=o;return o<0&&(t.util.assert(-(i+1)<=o,(()=>`Axis must be in the interval [${-(i+1)}, ${i}]`)),u=i+o+1),l.splice(u,0,1),Pt({inputs:{x:r},backend:a,attrs:{shape:l}})}const ua={kernelName:t.ExpandDims,backendName:"cpu",kernelFunc:la},da=d(((e,t)=>e/t)),ca=S(t.RealDiv,da),pa={kernelName:t.RealDiv,backendName:"cpu",kernelFunc:ca};function ha(e,n,a){const s=e.shape,r=s[0],o=s[1],i=a.data.get(e.dataId),l=i.complexTensorInfos.real,u=i.complexTensorInfos.imag,d=[r,o],p=t.util.sizeFromShape(d),h=t.util.getTypedArrayFromDType("float32",p),f=t.util.getTypedArrayFromDType("float32",p);for(let e=0;e<r;e++){const s=Je({inputs:{x:l},backend:a,attrs:{begin:[e,0],size:[1,o]}}),r=Je({inputs:{x:u},backend:a,attrs:{begin:[e,0],size:[1,o]}}),i=c({inputs:{real:s,imag:r},backend:a}),{real:d,imag:p}=fa(i,n,a),m=t.backend_util.mergeRealAndImagArrays(d,p);for(let n=0;n<o;n++){const a=t.backend_util.getComplexWithIndex(m,n);h[e*o+n]=a.real,f[e*o+n]=a.imag}a.disposeIntermediateTensorInfo(s),a.disposeIntermediateTensorInfo(r),a.disposeIntermediateTensorInfo(i)}const m=a.makeTensorInfo(d,"float32",h),k=a.makeTensorInfo(d,"float32",f),g=c({inputs:{real:m,imag:k},backend:a});return a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(k),g}function fa(e,n,a){const s=t.util.sizeFromShape(e.shape),r=a.data.get(e.dataId),o=a.data.get(r.complexTensorInfos.real.dataId).values,i=a.data.get(r.complexTensorInfos.imag.dataId).values;if(0==((l=s)&l-1)){const r=ma(o,i,s,n,a),l=[e.shape[0],e.shape[1]];if(n){const e=a.makeTensorInfo(l,"float32",r.real),n=a.makeTensorInfo(l,"float32",r.imag),o=a.makeTensorInfo([],"float32",t.util.createScalarValue(s,"float32")),i=f({inputs:{x:o},backend:a}),u=pa.kernelFunc({inputs:{a:e,b:o},backend:a}),d=pa.kernelFunc({inputs:{a:n,b:i},backend:a}),c=a.data.get(u.dataId).values,p=a.data.get(d.dataId).values;return a.disposeIntermediateTensorInfo(e),a.disposeIntermediateTensorInfo(n),a.disposeIntermediateTensorInfo(o),a.disposeIntermediateTensorInfo(i),a.disposeIntermediateTensorInfo(u),a.disposeIntermediateTensorInfo(d),{real:c,imag:p}}return r}{const e=function(e,n,a){const s=new Float32Array(2*n);for(let r=0;r<n;r++){let o=0,i=0;for(let s=0;s<n;s++){const l=t.backend_util.exponent(r*s,n,a),u=t.backend_util.getComplexWithIndex(e,s);o+=u.real*l.real-u.imag*l.imag,i+=u.real*l.imag+u.imag*l.real}a&&(o/=n,i/=n),t.backend_util.assignToTypedArray(s,o,i,r)}return s}(t.backend_util.mergeRealAndImagArrays(o,i),s,n);return t.backend_util.splitRealAndImagArrays(e)}var l}function ma(e,n,a,s,r){if(1===a)return{real:e,imag:n};const o=t.backend_util.mergeRealAndImagArrays(e,n),i=a/2,l=t.backend_util.complexWithEvenIndex(o),u=l.real,d=l.imag,p=[u.length],h=r.makeTensorInfo(p,"float32",u),f=r.makeTensorInfo(p,"float32",d),m=c({inputs:{real:h,imag:f},backend:r}),g=t.backend_util.complexWithOddIndex(o),I=g.real,b=g.imag,y=[I.length],S=r.makeTensorInfo(y,"float32",I),T=r.makeTensorInfo(y,"float32",b),N=c({inputs:{real:S,imag:T},backend:r}),x=ma(u,d,i,s,r),F=x.real,w=x.imag,M=[F.length],A=r.makeTensorInfo(M,"float32",F),D=r.makeTensorInfo(M,"float32",w),_=c({inputs:{real:A,imag:D},backend:r}),E=ma(I,b,i,s,r),z=E.real,R=E.imag,W=[z.length],P=r.makeTensorInfo(W,"float32",z),C=r.makeTensorInfo(W,"float32",R),H=c({inputs:{real:P,imag:C},backend:r}),O=t.backend_util.exponents(a,s),V=[O.real.length],B=r.makeTensorInfo(V,"float32",O.real),$=r.makeTensorInfo(V,"float32",O.imag),L=c({inputs:{real:B,imag:$},backend:r}),G=Se({inputs:{a:L,b:H},backend:r}),q=v({inputs:{a:_,b:G},backend:r}),U=kt({inputs:{a:_,b:G},backend:r}),Z=k({inputs:{input:q},backend:r}),j=k({inputs:{input:U},backend:r}),K=Tn({inputs:{input:q},backend:r}),Y=Tn({inputs:{input:U},backend:r}),J=xn({inputs:[Z,j],backend:r,attrs:{axis:0}}),Q=xn({inputs:[K,Y],backend:r,attrs:{axis:0}}),X=r.data.get(J.dataId).values,ee=r.data.get(Q.dataId).values;return r.disposeIntermediateTensorInfo(h),r.disposeIntermediateTensorInfo(f),r.disposeIntermediateTensorInfo(m),r.disposeIntermediateTensorInfo(S),r.disposeIntermediateTensorInfo(T),r.disposeIntermediateTensorInfo(N),r.disposeIntermediateTensorInfo(A),r.disposeIntermediateTensorInfo(D),r.disposeIntermediateTensorInfo(_),r.disposeIntermediateTensorInfo(P),r.disposeIntermediateTensorInfo(C),r.disposeIntermediateTensorInfo(H),r.disposeIntermediateTensorInfo(B),r.disposeIntermediateTensorInfo($),r.disposeIntermediateTensorInfo(L),r.disposeIntermediateTensorInfo(G),r.disposeIntermediateTensorInfo(q),r.disposeIntermediateTensorInfo(U),r.disposeIntermediateTensorInfo(Z),r.disposeIntermediateTensorInfo(K),r.disposeIntermediateTensorInfo(j),r.disposeIntermediateTensorInfo(Y),r.disposeIntermediateTensorInfo(J),r.disposeIntermediateTensorInfo(Q),{real:X,imag:ee}}const ka={kernelName:t.FFT,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a}=e,{input:s}=n,r=t.util.sizeFromShape(s.shape),o=s.shape[s.shape.length-1],i=Pt({inputs:{x:s},backend:a,attrs:{shape:[r/o,o]}}),l=ha(i,!1,a),u=Pt({inputs:{x:l},backend:a,attrs:{shape:s.shape}});return a.disposeIntermediateTensorInfo(i),a.disposeIntermediateTensorInfo(l),u}};function ga(e){const{backend:n,attrs:a}=e,{shape:s,value:r,dtype:o}=a,i=o||t.util.inferDtype(r),l=t.util.getArrayFromDType(i,t.util.sizeFromShape(s));return function(e,t,n){e.fill(t)}(l,r),n.makeTensorInfo(s,i,l)}const Ia={kernelName:t.Fill,backendName:"cpu",kernelFunc:ga};const ba={kernelName:t.FlipLeftRight,backendName:"cpu",kernelFunc:({inputs:e,attrs:n,backend:a})=>{const{image:s}=e,r=a,o=t.util.getTypedArrayFromDType(s.dtype,t.util.sizeFromShape(s.shape)),[i,l,u,d]=s.shape,c=r.data.get(s.dataId).values;for(let e=0;e<i;e++){const t=e*u*l*d;for(let e=0;e<l;e++){const n=e*(u*d);for(let e=0;e<u;e++){const a=e*d;for(let s=0;s<d;s++){const r=Math.round(u-e-1),i=t+n+a+s;let l=c[i];if(r>=0&&r<u){l=c[t+n+r*d+s]}o[i]=l}}}}return{dataId:r.write(o,s.shape,s.dtype),shape:s.shape,dtype:s.dtype}}},ya=d(((e,t)=>Math.floor(e/t))),Sa=S(t.FloorDiv,ya,null,"int32"),Ta={kernelName:t.FloorDiv,backendName:"cpu",kernelFunc:Sa};const Na={kernelName:t.FusedConv2D,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,filter:r,bias:o,preluActivationWeights:i}=t,{strides:l,pad:u,dataFormat:d,dilations:c,dimRoundingMode:p,activation:h,leakyreluAlpha:f}=a;let m=Fn({inputs:{x:s,filter:r},backend:n,attrs:{strides:l,pad:u,dataFormat:d,dilations:c,dimRoundingMode:p}});if(o){const e=m;if("NCHW"===d&&1===o.shape.length&&1!==o.shape[0]){const e=Pt({inputs:{x:o},backend:n,attrs:{shape:[o.shape[0],1,1]}});m=v({inputs:{a:m,b:e},backend:n}),n.disposeIntermediateTensorInfo(e)}else m=v({inputs:{a:m,b:o},backend:n});n.disposeIntermediateTensorInfo(e)}if(h){const e=m;if("NCHW"===d&&"prelu"===h&&1===i.shape.length&&1!==i.shape[0]){const e=Pt({inputs:{x:i},backend:n,attrs:{shape:[i.shape[0],1,1]}});m=Wt(n,m,h,e,f),n.disposeIntermediateTensorInfo(e)}else m=Wt(n,m,h,i,f);n.disposeIntermediateTensorInfo(e)}return m}};const xa={kernelName:t.FusedDepthwiseConv2D,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,filter:r,bias:o,preluActivationWeights:i}=t,{strides:l,pad:u,dataFormat:d,dilations:c,dimRoundingMode:p,activation:h,leakyreluAlpha:f}=a;let m=$n({inputs:{x:s,filter:r},backend:n,attrs:{strides:l,pad:u,dataFormat:d,dilations:c,dimRoundingMode:p}});if(o){const e=m;m=v({inputs:{a:m,b:o},backend:n}),n.disposeIntermediateTensorInfo(e)}if(h){const e=m;m=Wt(n,m,h,i,f),n.disposeIntermediateTensorInfo(e)}return m}};const va={kernelName:t.GatherNd,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a}=e,{params:s,indices:r}=n,o=t.util.sizeFromShape(s.shape),i=r.shape,l=i[i.length-1],[u,d,c,p]=t.backend_util.prepareAndValidate(s,r);if(0===d)return a.makeTensorInfo(u,s.dtype,[]);const h=j(a.data.get(r.dataId).values,a.bufferSync(s),s.dtype,d,l,c,p,s.shape,o);return a.makeTensorInfo(u,s.dtype,h.values)}};const Fa={kernelName:t.GatherV2,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o,indices:i}=n,{axis:l,batchDims:u}=s;r([o,i],"gatherV2");const d=t.util.parseAxisParam(l,o.shape)[0],c=a.data.get(i.dataId).values,p=o.shape[d];for(let e=0;e<c.length;++e){const n=c[e];t.util.assert(n<=p-1&&n>=0,(()=>`GatherV2: the index value ${n} is not in [0, ${p-1}]`))}let h=u;null==u&&(h=0);const f=t.util.sizeFromShape(i.shape),m=t.backend_util.segment_util.collectGatherOpShapeInfo(o,i,d,h),k=Pt({inputs:{x:o},backend:a,attrs:{shape:[m.batchSize,m.outerSize,m.dimSize,m.sliceSize]}}),g=Pt({inputs:{x:i},backend:a,attrs:{shape:[m.batchSize,f/m.batchSize]}}),I=[m.batchSize,m.outerSize,f/m.batchSize,m.sliceSize],b=a.bufferSync(g),y=K(a.bufferSync(k),b,I);return a.disposeIntermediateTensorInfo(k),a.disposeIntermediateTensorInfo(g),a.makeTensorInfo(m.outputShape,y.dtype,y.values)}};const wa={kernelName:t.IFFT,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a}=e,{input:s}=n,r=t.util.sizeFromShape(s.shape),o=s.shape[s.shape.length-1],i=Pt({inputs:{x:s},backend:a,attrs:{shape:[r/o,o]}}),l=ha(i,!0,a),u=Pt({inputs:{x:l},backend:a,attrs:{shape:s.shape}});return a.disposeIntermediateTensorInfo(i),a.disposeIntermediateTensorInfo(l),u}},Ma=D(t.IsFinite,(e=>Number.isFinite(e)?1:0),"bool"),Aa={kernelName:t.IsFinite,backendName:"cpu",kernelFunc:Ma},Da=D(t.IsInf,(e=>Math.abs(e)===1/0?1:0),"bool"),_a={kernelName:t.IsInf,backendName:"cpu",kernelFunc:Da},Ea=D(t.IsNan,(e=>Number.isNaN(e)?1:0),"bool"),za={kernelName:t.IsNan,backendName:"cpu",kernelFunc:Ea};const Ra={kernelName:t.LinSpace,backendName:"cpu",kernelFunc:function(e){const{backend:t,attrs:n}=e,{start:a,stop:s,num:r}=n,o=le(a,s,r);return t.makeTensorInfo([o.length],"float32",o)}},Wa=D(t.Log1p,(e=>Math.log1p(e))),Pa={kernelName:t.Log1p,backendName:"cpu",kernelFunc:Wa},Ca=d(((e,t)=>e&&t)),Ha=S(t.LogicalAnd,Ca,null,"bool"),Oa={kernelName:t.LogicalAnd,backendName:"cpu",kernelFunc:Ha},Va=D(t.LogicalNot,(e=>e?0:1),"bool"),Ba={kernelName:t.LogicalNot,backendName:"cpu",kernelFunc:Va},$a=d(((e,t)=>e||t)),La=S(t.LogicalOr,$a,null,"bool"),Ga={kernelName:t.LogicalOr,backendName:"cpu",kernelFunc:La};const qa={kernelName:t.LRN,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{depthRadius:i,bias:l,alpha:u,beta:d}=s;r(o,"LRN");const c=o.shape[3],p=c-1,h=a.data.get(o.dataId).values,f=t.util.sizeFromShape(o.shape),m=new Float32Array(f);function k(e){const t=e%c;let n=e-t+Math.max(0,t-i);const a=e-t+Math.min(t+i,p);let s=0;for(;n<=a;n++){const e=h[n];s+=e*e}return s}for(let e=0;e<f;e++){const t=k(e),n=h[e]*Math.pow(l+u*t,-d);m[e]=n}return a.makeTensorInfo(o.shape,o.dtype,m)}};const Ua={kernelName:t.LRNGrad,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o,y:i,dy:l}=n,{depthRadius:u,bias:d,alpha:c,beta:p}=s;r(l,"LRNGrad");const h=t.util.sizeFromShape(l.shape),f=l.shape[3],m=a.data.get(l.dataId).values,k=a.data.get(o.dataId).values,g=a.data.get(i.dataId).values,I=new Float32Array(h),b=h;for(let e=0;e<b;e++){const t=e%f,n=e-t+Math.max(0,t-u),a=e-t+Math.min(f,t+u+1);let s=0;for(let e=n;e<a;e++)s+=Math.pow(k[e],2);s=c*s+d;for(let t=n;t<a;t++){let n=-2*c*p*k[t]*g[e]/s;e===t&&(n+=Math.pow(s,-p)),n*=m[e],I[t]+=n}}return a.makeTensorInfo(l.shape,o.dtype,I)}};function Za(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{reductionIndices:i,keepDims:l}=s,u=a;let d=o.shape;const c=d.length,p=t.util.parseAxisParam(i,d);let h=p;const f=t.backend_util.getAxesPermutation(h,c);let m=u.data.get(o.dataId).values;if(null!=f){const e=new Array(c);for(let t=0;t<e.length;t++)e[t]=d[f[t]];m=Me(m,d,o.dtype,f,e),h=t.backend_util.getInnerMostAxes(h.length,c),d=e}r(o,"max"),t.backend_util.assertAxesAreInnerMostDims("max",h,c);const[k,g]=t.backend_util.computeOutAndReduceShapes(d,h),I=pe(m,t.util.sizeFromShape(g),k,o.dtype),b=u.write(I,k,o.dtype);let y=k;if(l){y=t.backend_util.expandShapeToKeepDim(k,p)}return{dataId:b,shape:y,dtype:o.dtype}}const ja={kernelName:t.Max,backendName:"cpu",kernelFunc:Za};const Ka={kernelName:t.MaxPool,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n;r(o,"maxPool");const{filterSize:i,strides:l,pad:u,dimRoundingMode:d}=s;t.util.assert(t.backend_util.eitherStridesOrDilationsAreOne(l,1),(()=>`Error in maxPool: Either strides or dilations must be 1. Got strides ${l} and dilations '1'`));const c=t.backend_util.computePool2DInfo(o.shape,i,l,1,u,d);let p;if(1===c.filterWidth&&1===c.filterHeight&&t.util.arraysEqual(c.inShape,c.outShape))p=f({inputs:{x:o},backend:a});else{const e=a.data.get(o.dataId).values,n=t.util.computeStrides(o.shape),s=ln(e,o.shape,o.dtype,n,c,"max");p=a.makeTensorInfo(c.outShape,o.dtype,s.values)}return p}};const Ya={kernelName:t.MaxPool3D,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{filterSize:i,strides:l,pad:u,dimRoundingMode:d,dataFormat:c}=s;r(o,"maxPool3d");const p=t.backend_util.computePool3DInfo(o.shape,i,l,1,u,d,c),h=dn(a.data.get(o.dataId).values,o.shape,o.dtype,t.util.computeStrides(o.shape),p,"max");return a.makeTensorInfo(h.shape,"float32",h.values)}};const Ja={kernelName:t.MaxPool3DGrad,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{dy:o,input:i}=n,{filterSize:l,strides:u,pad:d,dimRoundingMode:c}=s;r([o,i],"maxPool3DGrad");const p=t.backend_util.computePool3DInfo(i.shape,l,u,1,d,c),h=function(e,n){const a=t.buffer(n.outShape,"int32"),s=n.strideDepth,r=n.strideHeight,o=n.strideWidth,i=n.dilationDepth,l=n.dilationHeight,u=n.dilationWidth,d=n.effectiveFilterDepth,c=n.effectiveFilterHeight,p=n.effectiveFilterWidth,h=n.padInfo.front,f=n.padInfo.top,m=n.padInfo.left;for(let t=0;t<n.batchSize;++t)for(let k=0;k<n.inChannels;++k)for(let g=0;g<n.outDepth;++g){const I=g*s-h;let b=I;for(;b<0;)b+=i;const y=Math.min(n.inDepth,d+I);for(let s=0;s<n.outHeight;++s){const d=s*r-f;let h=d;for(;h<0;)h+=l;const S=Math.min(n.inHeight,c+d);for(let r=0;r<n.outWidth;++r){const f=r*o-m;let T=f;for(;T<0;)T+=u;const N=Math.min(n.inWidth,p+f);let x=Number.NEGATIVE_INFINITY,v=-1;for(let n=b;n<y;n+=i){const a=n-I;for(let s=h;s<S;s+=l){const r=s-d;for(let o=T;o<N;o+=u){const i=o-f,l=e.get(t,n,s,o,k);l>=x&&(x=l,v=a*c*p+r*c+i)}}}a.set(v,t,g,s,r,k)}}}return a}(a.bufferSync(i),p),f=p.strideDepth,m=p.strideHeight,k=p.strideWidth,g=p.dilationDepth,I=p.dilationHeight,b=p.dilationWidth,y=p.effectiveFilterDepth,S=p.effectiveFilterHeight,T=p.effectiveFilterWidth,N=y-1-p.padInfo.front,x=T-1-p.padInfo.left,v=S-1-p.padInfo.top,F=t.buffer(i.shape,"float32"),w=a.bufferSync(o);for(let e=0;e<p.batchSize;++e)for(let t=0;t<p.inChannels;++t)for(let n=0;n<p.inDepth;++n)for(let a=0;a<p.inHeight;++a)for(let s=0;s<p.inWidth;++s){const r=n-N,o=a-v,i=s-x;let l=0;for(let n=0;n<y;n+=g){const a=(r+n)/f;if(!(a<0||a>=p.outDepth||Math.floor(a)!==a))for(let s=0;s<S;s+=I){const r=(o+s)/m;if(!(r<0||r>=p.outHeight||Math.floor(r)!==r))for(let o=0;o<T;o+=b){const u=(i+o)/k;if(u<0||u>=p.outWidth||Math.floor(u)!==u)continue;const d=y*S*T-1-h.get(e,a,r,u,t)===n*S*T+s*T+o?1:0;if(0===d)continue;l+=w.get(e,a,r,u,t)*d}}}F.set(l,e,n,a,s,t)}return a.makeTensorInfo(F.shape,F.dtype,F.values)}};const Qa={kernelName:t.MaxPoolGrad,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{dy:o,input:i,output:l}=n,u=i;r([i,l],"maxPoolGrad");const{filterSize:d,strides:c,pad:p,dimRoundingMode:h}=s,f=t.backend_util.computePool2DInfo(u.shape,d,c,1,p,h),m=a.data.get(u.dataId).values,k=t.buffer(f.outShape,u.dtype,un(m,u.shape,u.dtype,f).values),g=f.strideHeight,I=f.strideWidth,b=f.dilationHeight,y=f.dilationWidth,S=f.effectiveFilterHeight,T=f.effectiveFilterWidth,N=T-1-f.padInfo.left,x=S-1-f.padInfo.top,v=t.buffer(u.shape,"float32"),F=a.data.get(o.dataId).values,w=t.buffer(o.shape,"float32",F);for(let e=0;e<f.batchSize;++e)for(let t=0;t<f.inChannels;++t)for(let n=0;n<f.inHeight;++n)for(let a=0;a<f.inWidth;++a){const s=n-x,r=a-N;let o=0;for(let n=0;n<S;n+=b){const a=(s+n)/g;if(!(a<0||a>=f.outHeight||Math.floor(a)!==a))for(let s=0;s<T;s+=y){const i=(r+s)/I;if(i<0||i>=f.outWidth||Math.floor(i)!==i)continue;const l=S*T-1-k.get(e,a,i,t)===n*T+s?1:0;if(0===l)continue;o+=w.get(e,a,i,t)*l}}v.set(o,e,n,a,t)}return a.makeTensorInfo(v.shape,v.dtype,v.values)}};const Xa={kernelName:t.MaxPoolWithArgmax,backendName:"cpu",kernelFunc:({inputs:e,attrs:n,backend:a})=>{const{x:s}=e,{filterSize:o,strides:i,pad:l,includeBatchInIndex:u}=n,d=a;r(s,"MaxPoolWithArgmax");const c=d.data.get(s.dataId).values,p=t.backend_util.computePool2DInfo(s.shape,o,i,[1,1],l),[h,f]=function(e,n,a,s,r){const o=ln(e,0,a,t.util.computeStrides(n),r,"max"),i=un(e,n,a,r,!0,s);return[o.values,i.values]}(c,s.shape,s.dtype,u,p),m=d.write(h,p.outShape,s.dtype),k=d.write(f,p.outShape,s.dtype);return[{dataId:m,shape:p.outShape,dtype:s.dtype},{dataId:k,shape:p.outShape,dtype:"int32"}]}};const es={kernelName:t.Mean,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:r}=n,{axis:o,keepDims:i}=s,l=t.util.parseAxisParam(o,r.shape),u=t.backend_util.computeOutAndReduceShapes(r.shape,l)[1],d=t.util.sizeFromShape(u),c=[],p=a.makeTensorInfo([],"float32",new Float32Array([d]));c.push(p);const h=b({inputs:{x:r},backend:a,attrs:{dtype:"float32"}});c.push(h);const f=ca({inputs:{a:h,b:p},backend:a});c.push(f);const m=Yn({inputs:{x:f},backend:a,attrs:{axis:o,keepDims:i}});return c.forEach((e=>a.disposeIntermediateTensorInfo(e))),m}};const ts={kernelName:t.Min,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{axis:i,keepDims:l}=s;r(o,"min");const u=t.util.parseAxisParam(i,o.shape);let d=u;const c=t.backend_util.getAxesPermutation(d,o.shape.length);let p=o;null!=c&&(p=Ae({inputs:{x:o},backend:a,attrs:{perm:c}}),d=t.backend_util.getInnerMostAxes(d.length,o.shape.length)),t.backend_util.assertAxesAreInnerMostDims("min",d,p.shape.length);const[h,f]=t.backend_util.computeOutAndReduceShapes(p.shape,d),m=t.util.sizeFromShape(f),k=t.util.makeZerosTypedArray(t.util.sizeFromShape(h),p.dtype),g=a.data.get(p.dataId).values;for(let e=0;e<k.length;++e){const t=e*m;let n=g[t];for(let e=0;e<m;++e){const a=g[t+e];(Number.isNaN(a)||a<n)&&(n=a)}k[e]=n}null!=c&&a.disposeIntermediateTensorInfo(p);const I=a.makeTensorInfo(h,p.dtype,k);if(l){const e=Pt({inputs:{x:I},backend:a,attrs:{shape:t.backend_util.expandShapeToKeepDim(h,u)}});return a.disposeIntermediateTensorInfo(I),e}return I}};const ns={kernelName:t.MirrorPad,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{paddings:i,mode:l}=s;r(o,"mirrorPad");const u=i.map(((e,t)=>e[0]+o.shape[t]+e[1])),d=i.map((e=>e[0])),c=i.map(((e,t)=>e[0]+o.shape[t])),p="reflect"===l?0:1,h=a.data.get(o.dataId).values,f=o.shape.length,m=t.util.computeStrides(o.shape),k=t.util.sizeFromShape(u),g=u.length,I=t.util.computeStrides(u),b=t.util.getTypedArrayFromDType(o.dtype,k);for(let e=0;e<k;e++){let n=t.util.indexToLoc(e,g,I);for(let e=0;e<g;e++)n[e]<d[e]?n[e]=2*d[e]-n[e]-p:n[e]>=c[e]&&(n[e]=2*(c[e]-1)-n[e]+p);n=n.map(((e,t)=>e-d[t]));const a=t.util.locToIndex(n,f,m);b[e]=h[a]}return{dataId:a.write(b,u,o.dtype),shape:u,dtype:o.dtype}}},as=d(((e,t)=>{const n=e%t;return e<0&&t<0||e>=0&&t>=0?n:(n+t)%t})),ss=S(t.Mod,as),rs={kernelName:t.Mod,backendName:"cpu",kernelFunc:ss};function os(e){const{inputs:n,backend:a,attrs:s}=e,{logits:r}=n,{dim:o}=s,i=r.shape.length;let l=o;if(-1===l&&(l=i-1),l!==i-1)throw Error(`Softmax along a non-last dimension is not yet supported. Logits was rank ${i} and dim was ${l}`);const u=t.util.parseAxisParam([l],r.shape),d=Za({inputs:{x:r},backend:a,attrs:{reductionIndices:u,keepDims:!1}}),c=t.backend_util.expandShapeToKeepDim(d.shape,u),p=Pt({inputs:{x:d},backend:a,attrs:{shape:c}}),h=kt({inputs:{a:r,b:p},backend:a}),f=V({inputs:{x:h},backend:a}),m=Yn({inputs:{x:f},backend:a,attrs:{axis:u,keepDims:!1}}),k=Pt({inputs:{x:m},backend:a,attrs:{shape:c}}),g=ca({inputs:{a:f,b:k},backend:a});return a.disposeIntermediateTensorInfo(d),a.disposeIntermediateTensorInfo(p),a.disposeIntermediateTensorInfo(h),a.disposeIntermediateTensorInfo(f),a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(k),g}const is={kernelName:t.Softmax,backendName:"cpu",kernelFunc:os};const ls={kernelName:t.Multinomial,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:o}=e,{logits:i}=n,{numSamples:l,seed:u,normalized:d}=o;r(i,"multinomial");const c=d?i:os({inputs:{logits:i},backend:a,attrs:{dim:-1}}),p=c.shape[0],h=c.shape[1],f=a.data.get(c.dataId).values,m=[p,l],k=t.util.makeZerosTypedArray(t.util.sizeFromShape(m),"int32");for(let e=0;e<p;++e){const t=e*h,n=new Float32Array(h-1);n[0]=f[t];for(let e=1;e<n.length;++e)n[e]=n[e-1]+f[t+e];const a=s.alea(u.toString()),r=e*l;for(let e=0;e<l;++e){const t=a();k[r+e]=n.length;for(let a=0;a<n.length;a++)if(t<n[a]){k[r+e]=a;break}}}return d||a.disposeIntermediateTensorInfo(c),a.makeTensorInfo(m,"int32",k)}},us=t.kernel_impls.nonMaxSuppressionV3Impl;const ds={kernelName:t.NonMaxSuppressionV3,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{boxes:s,scores:o}=t,{maxOutputSize:i,iouThreshold:l,scoreThreshold:u}=a;r(s,"NonMaxSuppression");const d=n.data.get(s.dataId).values,c=n.data.get(o.dataId).values,{selectedIndices:p}=us(d,c,i,l,u);return n.makeTensorInfo([p.length],"int32",new Int32Array(p))}},cs=t.kernel_impls.nonMaxSuppressionV4Impl;const ps={kernelName:t.NonMaxSuppressionV4,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{boxes:s,scores:o}=t,{maxOutputSize:i,iouThreshold:l,scoreThreshold:u,padToMaxOutputSize:d}=a;r(s,"NonMaxSuppressionPadded");const c=n.data.get(s.dataId).values,p=n.data.get(o.dataId).values,{selectedIndices:h,validOutputs:f}=cs(c,p,i,l,u,d);return[n.makeTensorInfo([h.length],"int32",new Int32Array(h)),n.makeTensorInfo([],"int32",new Int32Array([f]))]}},hs=t.kernel_impls.nonMaxSuppressionV5Impl;const fs={kernelName:t.NonMaxSuppressionV5,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{boxes:s,scores:o}=t,{maxOutputSize:i,iouThreshold:l,scoreThreshold:u,softNmsSigma:d}=a;r(s,"NonMaxSuppressionWithScore");const c=n.data.get(s.dataId).values,p=n.data.get(o.dataId).values,h=i,f=l,m=u,k=d,{selectedIndices:g,selectedScores:I}=hs(c,p,h,f,m,k);return[n.makeTensorInfo([g.length],"int32",new Int32Array(g)),n.makeTensorInfo([I.length],"float32",new Float32Array(I))]}};const ms={kernelName:t.OneHot,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{indices:o}=n,{dtype:i,depth:l,onValue:u,offValue:d}=s;r(o,"oneHot");const c=t.util.sizeFromShape(o.shape),p=new Float32Array(c*l);p.fill(d);const h=a.data.get(o.dataId).values;for(let e=0;e<c;++e)h[e]>=0&&h[e]<l&&(p[e*l+h[e]]=u);return a.makeTensorInfo([...o.shape,l],i,p)}};function ks(e){const{inputs:t,backend:n}=e,{x:a}=t;if("string"===a.dtype)throw new Error("zerosLike is not supported for string tensors");if("complex64"===a.dtype){const e=k({inputs:{input:a},backend:n}),t=ks({inputs:{x:e},backend:n}),s=Tn({inputs:{input:a},backend:n}),r=ks({inputs:{x:s},backend:n}),o=c({inputs:{real:t,imag:r},backend:n});return n.disposeIntermediateTensorInfo(e),n.disposeIntermediateTensorInfo(t),n.disposeIntermediateTensorInfo(s),n.disposeIntermediateTensorInfo(r),o}return ga({backend:n,attrs:{shape:a.shape,value:0,dtype:a.dtype}})}const gs={kernelName:t.ZerosLike,backendName:"cpu",kernelFunc:ks};const Is={kernelName:t.OnesLike,backendName:"cpu",kernelFunc:function e(t){const{inputs:n,backend:a}=t,{x:s}=n;if("string"===s.dtype)throw new Error("onesLike is not supported for string tensors");if("complex64"===s.dtype){const t=k({inputs:{input:s},backend:a}),n=e({inputs:{x:t},backend:a}),r=Tn({inputs:{input:s},backend:a}),o=ks({inputs:{x:r},backend:a}),i=c({inputs:{real:n,imag:o},backend:a});return a.disposeIntermediateTensorInfo(t),a.disposeIntermediateTensorInfo(n),a.disposeIntermediateTensorInfo(r),a.disposeIntermediateTensorInfo(o),i}return ga({backend:a,attrs:{shape:s.shape,value:1,dtype:s.dtype}})}};function bs(e){const{inputs:n,backend:a,attrs:s}=e,{axis:r}=s;if(1===n.length)return la({inputs:{input:n[0]},backend:a,attrs:{dim:r}});const o=n[0].shape,i=n[0].dtype;n.forEach((e=>{t.util.assertShapesMatch(o,e.shape,"All tensors passed to stack must have matching shapes"),t.util.assert(i===e.dtype,(()=>"All tensors passed to stack must have matching dtypes"))}));const l=[],u=xn({inputs:n.map((e=>{const t=la({inputs:{input:e},backend:a,attrs:{dim:r}});return l.push(t),t})),backend:a,attrs:{axis:r}});return l.forEach((e=>a.disposeIntermediateTensorInfo(e))),u}const ys={kernelName:t.Pack,backendName:"cpu",kernelFunc:bs};const Ss={kernelName:t.PadV2,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{paddings:i,constantValue:l}=s;r(o,"pad");const u=i.map(((e,t)=>e[0]+o.shape[t]+e[1])),d=i.map((e=>e[0])),c=a.data.get(o.dataId).values,p=t.util.sizeFromShape(o.shape),h=o.shape.length,f=t.util.computeStrides(o.shape),m=t.util.sizeFromShape(u),k=u.length,g=t.util.computeStrides(u),I=t.util.getTypedArrayFromDType(o.dtype,m);0!==l&&I.fill(l);for(let e=0;e<p;e++){const n=t.util.indexToLoc(e,h,f).map(((e,t)=>e+d[t]));I[t.util.locToIndex(n,k,g)]=c[e]}return{dataId:a.write(I,u,o.dtype),shape:u,dtype:o.dtype}}},Ts=d(((e,t)=>Math.pow(e,t))),Ns=S(t.Pow,Ts),xs={kernelName:t.Pow,backendName:"cpu",kernelFunc:Ns};const vs={kernelName:t.RaggedGather,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{paramsNestedSplits:s,paramsDenseValues:r,indices:o}=t,i=s.map((e=>n.data.get(e.dataId).values)),l=s.map((e=>e.shape)),u=n.data.get(r.dataId).values,d=n.data.get(o.dataId).values,[c,p,h]=Pe(i,l,u,r.shape,r.dtype,d,o.shape),f=c.map((e=>n.makeTensorInfo([e.length],"int32",e))),m=n.makeTensorInfo(h,r.dtype,p);return f.concat([m])}};const Fs={kernelName:t.RaggedTensorToTensor,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{shape:s,values:r,defaultValue:o,rowPartitionTensors:i}=t,{rowPartitionTypes:l}=a,u=n.data.get(s.dataId).values,d=n.data.get(r.dataId).values,c=n.data.get(o.dataId).values,p=i.map((e=>n.data.get(e.dataId).values)),h=i.map((e=>e.shape)),[f,m]=Be(u,s.shape,d,r.shape,r.dtype,c,o.shape,p,h,l);return n.makeTensorInfo(f,r.dtype,m)}};const ws={kernelName:t.Range,backendName:"cpu",kernelFunc:function(e){const{backend:t,attrs:n}=e,{start:a,stop:s,dtype:r,step:o}=n,i=$e(a,s,o,r);return t.makeTensorInfo([i.length],r,i)}},Ms=D(t.Reciprocal,(e=>1/e)),As={kernelName:t.Reciprocal,backendName:"cpu",kernelFunc:Ms};const Ds={kernelName:t.ResizeBilinear,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{images:o}=n,{alignCorners:i,halfPixelCenters:l,size:u}=s;r(o,"resizeBilinear");const d=t.util.computeStrides(o.shape),[c,p]=u,[h,f,m,k]=o.shape,g=a.data.get(o.dataId).values,I=new Float32Array(t.util.sizeFromShape([h,c,p,k])),b=[i&&c>1?f-1:f,i&&p>1?m-1:m],y=[i&&c>1?c-1:c,i&&p>1?p-1:p];let S=0;const T=b[0]/y[0],N=b[1]/y[1];for(let e=0;e<h;e++)for(let t=0;t<c;t++){let n;n=l?T*(t+.5)-.5:T*t;const a=Math.max(0,Math.floor(n)),s=n-a,r=Math.min(f-1,Math.ceil(n)),o=e*d[0]+a*d[1],i=e*d[0]+r*d[1];for(let e=0;e<p;e++){let t;t=l?N*(e+.5)-.5:N*e;const n=Math.max(0,Math.floor(t)),a=t-n,r=Math.min(m-1,Math.ceil(t)),u=o+n*d[2],c=i+n*d[2],p=o+r*d[2],h=i+r*d[2];for(let e=0;e<k;e++){const t=g[u+e],n=g[c+e],r=t+(g[p+e]-t)*a,o=r+(n+(g[h+e]-n)*a-r)*s;I[S++]=o}}}return a.makeTensorInfo([h,c,p,k],"float32",I)}};const _s={kernelName:t.ResizeBilinearGrad,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{images:o,dy:i}=n,{alignCorners:l}=s;r([i,o],"resizeBilinearGrad");const u=t.util.computeStrides(o.shape),[d,c,p,h]=o.shape,[,f,m]=i.shape,k=new Float32Array(d*c*p*h),g=[l&&f>1?c-1:c,l&&m>1?p-1:p],I=[l&&f>1?f-1:f,l&&m>1?m-1:m],b=g[0]/I[0],y=g[1]/I[1],S=a.data.get(i.dataId).values;let T=0;for(let e=0;e<d;e++){const t=e*u[0];for(let e=0;e<f;e++){const n=e*b,a=Math.floor(n),s=Math.min(Math.ceil(n),c-1),r=t+a*u[1],o=t+s*u[1],i=n-a,l=1-i;for(let e=0;e<m;e++){const t=e*y,n=Math.floor(t),a=Math.min(Math.ceil(t),p-1),s=t-n,d=1-s,c=r+n*u[2],f=r+a*u[2],m=o+n*u[2],g=o+a*u[2],I=l*d,b=l*s,N=i*d,x=i*s;for(let e=0;e<h;e++){const t=S[T++];k[c+e]+=t*I,k[f+e]+=t*b,k[m+e]+=t*N,k[g+e]+=t*x}}}}return a.makeTensorInfo([d,p,c,h],"float32",k)}};const Es={kernelName:t.ResizeNearestNeighbor,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{images:o}=n,{alignCorners:i,halfPixelCenters:l,size:u}=s;r(o,"resizeNearestNeighbor");const d=t.util.computeStrides(o.shape),[c,p]=u,[h,f,m,k]=o.shape,g=a.data.get(o.dataId).values,I=new Float32Array(h*c*p*k),b=[i&&c>1?f-1:f,i&&p>1?m-1:m],y=[i&&c>1?c-1:c,i&&p>1?p-1:p],S=b[0]/y[0],T=b[1]/y[1];let N=0;for(let e=0;e<h;e++){const t=e*d[0];for(let e=0;e<c;e++){const n=l?S*(e+.5):S*e;let a=Math.min(f-1,i?Math.round(n):Math.floor(n));l&&(a=Math.max(0,a));const s=t+a*d[1];for(let e=0;e<p;e++){const t=l?T*(e+.5):T*e;let n=Math.min(m-1,i?Math.round(t):Math.floor(t));l&&(n=Math.max(0,n));const a=s+n*d[2];for(let e=0;e<k;e++){const t=g[a+e];I[N++]=t}}}}return a.makeTensorInfo([h,c,p,k],o.dtype,I)}};const zs={kernelName:t.ResizeNearestNeighborGrad,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{images:o,dy:i}=n,{alignCorners:l}=s;r([i,o],"resizeNearestNeighborGrad");const u=t.util.computeStrides(o.shape),d=t.util.computeStrides(i.shape),[c,p,h,f]=o.shape,[,m,k]=i.shape,g=new Float32Array(c*p*h*f),I=a.data.get(i.dataId).values,b=[l&&m>1?p-1:p,l&&k>1?h-1:h],y=[l&&m>1?m-1:m,l&&k>1?k-1:k],S=b[0]/y[0],T=b[1]/y[1],N=1/S,x=1/T,v=2*Math.ceil(N)+2,F=2*Math.ceil(x)+2;for(let e=0;e<c;e++){const t=e*u[0];for(let e=0;e<p;e++){const n=t+e*u[1],a=Math.floor(e*N),s=Math.floor(a-v/2);for(let a=0;a<h;a++){const r=n+a*u[2],o=Math.floor(a*x),i=Math.floor(o-F/2);for(let n=0;n<f;n++){let o=0;for(let r=0;r<v;r++){const u=r+s;if(u<0||u>=m)continue;const c=t+u*d[1],f=u*S;if(e===Math.min(p-1,l?Math.round(f):Math.floor(f)))for(let e=0;e<F;e++){const t=e+i;if(t<0||t>=k)continue;const s=c+t*d[2],r=t*T;a===Math.min(h-1,l?Math.round(r):Math.floor(r))&&(o+=I[s+n])}}g[r+n]=o}}}}return a.makeTensorInfo(o.shape,o.dtype,g)}};const Rs={kernelName:t.Reverse,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{dims:i}=s;r(o,"reverse");const l=o.shape.length,u=t.util.parseAxisParam(i,o.shape);if(0===l)return f({inputs:{x:o},backend:a});const d=new t.TensorBuffer(o.shape,o.dtype),c=a.bufferSync(o);for(let e=0;e<d.size;e++){const t=d.indexToLoc(e),n=t.slice();u.forEach((e=>n[e]=o.shape[e]-1-n[e])),d.set(c.get(...n),...t)}return a.makeTensorInfo(d.shape,d.dtype,d.values)}},Ws={kernelName:t.RotateWithOffset,backendName:"cpu",kernelFunc:({inputs:e,attrs:n,backend:a})=>{const{image:s}=e,{radians:r,fillValue:o,center:i}=n,l=a,u=t.util.getTypedArrayFromDType(s.dtype,t.util.sizeFromShape(s.shape)),[d,c,p,h]=s.shape,[f,m]=t.backend_util.getImageCenter(i,c,p),k=Math.sin(r),g=Math.cos(r),I=l.data.get(s.dataId).values;for(let e=0;e<d;e++){const t=e*p*c*h;for(let e=0;e<c;e++){const n=e*(p*h);for(let a=0;a<p;a++){const s=a*h;for(let r=0;r<h;r++){const i=[d,e,a,r],l=i[2],b=i[1];let y=(l-f)*g-(b-m)*k,S=(l-f)*k+(b-m)*g;y=Math.round(y+f),S=Math.round(S+m);let T=o;if("number"!=typeof o&&(T=3===r?255:o[r]),y>=0&&y<p&&S>=0&&S<c){T=I[t+S*(p*h)+y*h+r]}u[t+n+s+r]=T}}}}return{dataId:l.write(u,s.shape,s.dtype),shape:s.shape,dtype:s.dtype}}},Ps=D(t.Round,(e=>{const t=Math.floor(e);return e-t<.5?Math.floor(e):e-t>.5?Math.ceil(e):t%2==0?t:t+1})),Cs={kernelName:t.Round,backendName:"cpu",kernelFunc:Ps};const Hs={kernelName:t.ScatterNd,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{indices:r,updates:o}=n,{shape:i}=s,{sliceRank:l,numUpdates:u,sliceSize:d,strides:c,outputSize:p}=t.backend_util.calculateShapes(o,r,i),h=Ue(a.bufferSync(r),a.bufferSync(o),i,p,d,u,l,c,0,!0);return a.makeTensorInfo(i,h.dtype,h.values)}};function Os(e,t){let n=0,a=e.length,s=0;for(;n<a;)s=Math.floor((n+a)/2),e[s]<t?n=s+1:a=s;return a}function Vs(e,t){let n=0,a=e.length,s=0;for(;n<a;)s=Math.floor((n+a)/2),e[s]<=t?n=s+1:a=s;return a}const Bs={kernelName:t.SearchSorted,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{sortedSequence:r,values:o}=n,{side:i}=s,l=function(e,n,a,s,r,o){const i=t.util.getArrayFromDType("int32",a*r);for(let t=0;t<a;++t){const a=e.slice(t*s,(t+1)*s),l=t*r;for(let e=0;e<r;++e)i[l+e]="left"===o?Os(a,n[e+l]):Vs(a,n[e+l])}return i}(a.data.get(r.dataId).values,a.data.get(o.dataId).values,r.shape[0],r.shape[1],o.shape[1],i);return a.makeTensorInfo(o.shape,"int32",l)}};const $s={kernelName:t.Select,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a}=e,{condition:s,t:o,e:i}=n;r([s,o,i],"select");const l=s.shape.length,u=a.data.get(s.dataId).values,d=a.data.get(o.dataId).values,c=a.data.get(i.dataId).values,p=t.upcastType(o.dtype,i.dtype),h=t.util.makeZerosTypedArray(t.util.sizeFromShape(o.shape),p);let f=0;const m=0===l||l>1||1===o.shape.length?1:t.util.sizeFromShape(o.shape.slice(1));for(let e=0;e<u.length;e++)for(let t=0;t<m;t++)1===u[e]?h[f++]=d[e]:h[f++]=c[e];return a.makeTensorInfo(o.shape,p,h)}},Ls=t.backend_util.SELU_SCALEALPHA,Gs=t.backend_util.SELU_SCALE,qs=D(t.Selu,(e=>e>=0?Gs*e:Ls*(Math.exp(e)-1))),Us={kernelName:t.Selu,backendName:"cpu",kernelFunc:qs},Zs=D(t.Sign,(e=>e<0?-1:e>0?1:0)),js={kernelName:t.Sign,backendName:"cpu",kernelFunc:Zs},Ks=D(t.Sin,(e=>Math.sin(e))),Ys={kernelName:t.Sin,backendName:"cpu",kernelFunc:Ks},Js=D(t.Sinh,(e=>Math.sinh(e))),Qs={kernelName:t.Sinh,backendName:"cpu",kernelFunc:Js},Xs=Math.log(1.1920928955078125e-7)+2,er=D(t.Softplus,(e=>{const t=e>-Xs,n=e<Xs,a=Math.exp(e);let s;return s=n?a:t?e:Math.log(1+a),s})),tr={kernelName:t.Softplus,backendName:"cpu",kernelFunc:er};const nr={kernelName:t.SpaceToBatchND,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{blockShape:i,paddings:l}=s;r([o],"spaceToBatchND");const u=t.util.sizeFromShape(i),d=[[0,0]];d.push(...l);for(let e=1+i.length;e<o.shape.length;++e)d.push([0,0]);const c=Ss.kernelFunc({inputs:{x:o},backend:a,attrs:{paddings:d,constantValue:0}}),p=t.backend_util.getReshaped(c.shape,i,u,!1),h=t.backend_util.getPermuted(p.length,i.length,!1),f=t.backend_util.getReshapedPermuted(c.shape,i,u,!1),m=Pt({inputs:{x:c},backend:a,attrs:{shape:p}}),k=Ae({inputs:{x:m},backend:a,attrs:{perm:h}}),g=Pt({inputs:{x:k},backend:a,attrs:{shape:f}});return a.disposeIntermediateTensorInfo(c),a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(k),g}};const ar={kernelName:t.SparseFillEmptyRows,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{indices:a,values:s,denseShape:r,defaultValue:o}=t;if(1!==r.shape.length)throw new Error(`Dense shape must be a vector, saw:\n        ${r.shape}`);if(2!==a.shape.length)throw new Error(`Indices must be a matrix, saw:\n        ${a.shape}`);if(1!==s.shape.length)throw new Error(`Values must be a vector, saw:\n        ${s.shape}`);if(0!==o.shape.length)throw new Error(`Default value must be a scalar, saw:\n        ${o.shape}`);const i=n.data.get(a.dataId).values,l=n.data.get(s.dataId).values,u=n.data.get(r.dataId).values,d=n.data.get(o.dataId).values[0],[c,p,h,f,m]=Xe(i,a.shape,a.dtype,l,s.dtype,u,d);return[n.makeTensorInfo(p,a.dtype,c),n.makeTensorInfo([p[0]],s.dtype,h),n.makeTensorInfo([f.length],"bool",new Uint8Array(f.map((e=>Number(e))))),n.makeTensorInfo([m.length],a.dtype,new Int32Array(m))]}};const sr={kernelName:t.SparseReshape,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{inputIndices:a,inputShape:s,newShape:r}=t;if(2!==a.shape.length)throw new Error(`Input indices should be a matrix but received shape\n        ${a.shape}`);if(1!==s.shape.length)throw new Error(`Input shape should be a vector but received shape\n        ${s.shape}`);if(1!==r.shape.length)throw new Error(`Target shape should be a vector but received shape ${r.shape}`);const o=Array.from(n.data.get(s.dataId).values),i=n.data.get(a.dataId).values,l=Array.from(n.data.get(r.dataId).values),[u,d,c]=et(i,a.shape,a.dtype,o,l);return[n.makeTensorInfo(d,a.dtype,u),n.makeTensorInfo([c.length],r.dtype,new Int32Array(c))]}};const rr={kernelName:t.SparseSegmentMean,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{data:a,indices:s,segmentIds:r}=t;if(a.shape.length<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==s.shape.length)throw new Error(`Indices should be a vector but received shape\n          ${s.shape}`);if(1!==r.shape.length)throw new Error(`Segment ids should be a vector but received shape\n          ${r.shape}`);if(s.shape[0]!==r.shape[0])throw new Error("segmentIds and indices should have same size.");const o=n.data.get(a.dataId).values,i=n.data.get(s.dataId).values,l=n.data.get(r.dataId).values,[u,d]=tt(o,a.shape,a.dtype,i,l,!0);return n.makeTensorInfo(d,a.dtype,u)}};const or={kernelName:t.SparseSegmentSum,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{data:a,indices:s,segmentIds:r}=t;if(a.shape.length<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==s.shape.length)throw new Error(`Indices should be a vector but received shape\n         ${s.shape}`);if(1!==r.shape.length)throw new Error(`Segment ids should be a vector but received shape\n         ${r.shape}`);if(s.shape[0]!==r.shape[0])throw new Error("segmentIds and indices should have same size.");const o=n.data.get(a.dataId).values,i=n.data.get(s.dataId).values,l=n.data.get(r.dataId).values,[u,d]=tt(o,a.shape,a.dtype,i,l);return n.makeTensorInfo(d,a.dtype,u)}};const ir={kernelName:t.SparseToDense,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{sparseIndices:r,sparseValues:o,defaultValue:i}=n,{outputShape:l}=s,{sliceRank:u,numUpdates:d,sliceSize:c,strides:p,outputSize:h}=t.backend_util.calculateShapes(o,r,l),f=!1,m=a.bufferSync(r);let k;switch(o.dtype){case"bool":k=Ue(m,a.bufferSync(o),l,h,c,d,u,p,Boolean(a.data.get(i.dataId).values[0]),f);break;case"float32":k=Ue(m,a.bufferSync(o),l,h,c,d,u,p,a.data.get(i.dataId).values[0],f);break;case"int32":k=Ue(m,a.bufferSync(o),l,h,c,d,u,p,a.data.get(i.dataId).values[0],f);break;case"string":k=Ue(m,a.bufferSync(o),l,h,c,d,u,p,t.util.decodeString(a.data.get(i.dataId).values[0]),f);break;default:throw new Error(`Unsupported type ${o.dtype}`)}return a.makeTensorInfo(l,k.dtype,k.values)}};const lr={kernelName:t.SplitV,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:r}=n,{numOrSizeSplits:o,axis:i}=s,l=t.util.parseAxisParam(i,r.shape)[0],u=t.backend_util.prepareSplitSize(r,o,l),d=new Array(r.shape.length).fill(0),c=r.shape.slice();return u.map((e=>{const t=[...c];t[l]=e;const n=Je({inputs:{x:r},backend:a,attrs:{begin:d,size:t}});return d[l]+=e,n}))}},ur={kernelName:t.Square,backendName:"cpu",kernelFunc:({inputs:e,backend:t})=>{const{x:n}=e,a=t;r(n,"square");const s=a.data.get(n.dataId).values,o=new Float32Array(s.length);for(let e=0;e<s.length;++e){const t=s[e];o[e]=t*t}return{dataId:a.write(o,n.shape,n.dtype),shape:n.shape,dtype:n.dtype}}},dr=D(t.Step,((e,t)=>{const n=t;return isNaN(e)?NaN:e>0?1:n.alpha})),cr={kernelName:t.Step,backendName:"cpu",kernelFunc:dr};const pr={kernelName:t.StridedSlice,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o}=n,{begin:i,end:l,strides:u,beginMask:d,endMask:c,ellipsisMask:p,newAxisMask:h,shrinkAxisMask:f}=s;r(o,"stridedSlice");const{finalShapeSparse:m,finalShape:k,isIdentity:g,sliceDim0:I,isSimpleSlice:b,begin:y,end:S,strides:T}=t.slice_util.sliceInfo(o.shape,i,l,u,d,c,p,h,f);let N;if(g)N=Pt({inputs:{x:o},backend:a,attrs:{shape:k}});else if(I||b){t.util.assert(o.shape.length>=1,(()=>`Input must have rank at least 1, got: ${o.shape.length}`));const e=t.slice_util.computeOutShape(y,S,T),n=Je({inputs:{x:o},backend:a,attrs:{begin:y,size:e}});N=Pt({inputs:{x:n},backend:a,attrs:{shape:k}}),a.disposeIntermediateTensorInfo(n)}else{const e=lt(m,a.bufferSync(o),T,y);N=a.makeTensorInfo(k,e.dtype,e.values)}return N}};const hr={kernelName:t.StringNGrams,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{separator:s,nGramWidths:r,leftPad:o,rightPad:i,padWidth:l,preserveShortSequences:u}=a,{data:d,dataSplits:c}=t,p=n.data.get(d.dataId).values,h=n.data.get(c.dataId).values,[f,m]=dt(p,h,s,r,o,i,l,u);return[n.makeTensorInfo([f.length],"string",f),n.makeTensorInfo(c.shape,"int32",m)]}};const fr={kernelName:t.StringSplit,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{skipEmpty:s}=a,{input:r,delimiter:o}=t;if("string"!==r.dtype)throw new Error("Input must be of datatype string");if(1!==r.shape.length)throw new Error(`Input must be a vector, got shape: ${r.shape}`);if(0!==o.shape.length)throw new Error(`Delimiter must be a scalar, got shape: ${o.shape}`);const i=n.data.get(r.dataId).values,l=n.data.get(o.dataId).values[0],[u,d,c]=pt(i,l,s),p=d.length;return[n.makeTensorInfo([p,2],"int32",u),n.makeTensorInfo([p],"string",d),n.makeTensorInfo([2],"int32",new Int32Array(c))]}};const mr={kernelName:t.StringToHashBucketFast,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{numBuckets:s}=a,{input:r}=t;if("string"!==r.dtype)throw new Error("Input must be of datatype string");if(s<=0)throw new Error("Number of buckets must be at least 1");const o=ht(n.data.get(r.dataId).values,s);return n.makeTensorInfo(r.shape,"int32",o)}},kr=D(t.Tan,(e=>Math.tan(e))),gr={kernelName:t.Tan,backendName:"cpu",kernelFunc:kr},Ir=D(t.Tanh,(e=>Math.tanh(e))),br={kernelName:t.Tanh,backendName:"cpu",kernelFunc:Ir};const yr={kernelName:t.Tile,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s}=t,{reps:o}=a;r(s,"tile");const i=It(n.bufferSync(s),o);return n.makeTensorInfo(i.shape,i.dtype,i.values)}};const Sr={kernelName:t.TopK,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s}=t,{k:o,sorted:i}=a;r(s,"topk");const l=n.data.get(s.dataId).values,[u,d]=St(l,s.shape,s.dtype,o,i);return[n.makeTensorInfo(u.shape,u.dtype,u.values),n.makeTensorInfo(d.shape,d.dtype,d.values)]}};const Tr={kernelName:t.Transform,backendName:"cpu",kernelFunc:function(e){const{inputs:n,attrs:a,backend:s}=e,{image:r,transforms:o}=n,{interpolation:i,fillMode:l,fillValue:u,outputShape:d}=a,[c,p,h,f]=r.shape,[m,k]=null!=d?d:[p,h],g=[c,m,k,f],I=t.util.computeStrides(r.shape),b=I[0],y=I[1],S=I[2],T=t.util.computeStrides(g),N=T[0],x=T[1],v=T[2],F=t.util.getTypedArrayFromDType(r.dtype,t.util.sizeFromShape(g));F.fill(u);const w=s.data.get(r.dataId).values,M=s.data.get(o.dataId).values;for(let e=0;e<c;++e){const t=1===o.shape[0]?M:M.subarray(8*e,8*e+8);for(let n=0;n<m;++n)for(let a=0;a<k;++a)for(let s=0;s<f;++s){let r;const o=t[6]*a+t[7]*n+1;if(0===o)continue;const d=(t[0]*a+t[1]*n+t[2])/o,c=(t[3]*a+t[4]*n+t[5])/o,f=Nr(d,h,l),m=Nr(c,p,l);switch(i){case"nearest":r=vr(w,p,h,b,y,S,e,m,f,s,u);break;case"bilinear":r=Fr(w,p,h,b,y,S,e,m,f,s,u);break;default:throw new Error(`Error in Transform: Expect 'nearest' or 'bilinear', but got ${i}`)}F[e*N+n*x+a*v+s]=r}return s.makeTensorInfo(g,r.dtype,F)}return{dataId:s.write(F,g,r.dtype),shape:r.shape,dtype:r.dtype}}};function Nr(e,n,a){switch(a){case"reflect":return function(e,n){let a=e;if(a<0)if(n<=1)a=0;else{const e=2*n;a<e&&(a=e*Math.trunc(-a/e)+a),a=a<-n?a+e:-a-1}else if(a>n-1)if(n<=1)a=0;else{const e=2*n;a-=e*Math.trunc(a/e),a>=n&&(a=e-a-1)}return t.util.clamp(0,a,n-1)}(e,n);case"wrap":return function(e,n){let a=e;if(a<0)if(n<=1)a=0;else{const e=n-1;a+=n*(Math.trunc(-a/e)+1)}else if(a>n-1)if(n<=1)a=0;else{const e=n-1;a-=n*Math.trunc(a/e)}return t.util.clamp(0,a,n-1)}(e,n);case"nearest":return function(e,n){return t.util.clamp(0,e,n-1)}(e,n);default:return function(e,t){return e}(e)}}function xr(e,t,n,a,s,r,o,i,l,u,d){return 0<=i&&i<t&&0<=l&&l<n?e[o*a+i*s+l*r+u]:d}function vr(e,t,n,a,s,r,o,i,l,u,d){return xr(e,t,n,a,s,r,o,Math.round(i),Math.round(l),u,d)}function Fr(e,t,n,a,s,r,o,i,l,u,d){const c=Math.floor(i),p=Math.floor(l),h=c+1,f=p+1;return(h-i)*((f-l)*xr(e,t,n,a,s,r,o,c,p,u,d)+(l-p)*xr(e,t,n,a,s,r,o,c,f,u,d))+(i-c)*((f-l)*xr(e,t,n,a,s,r,o,h,p,u,d)+(l-p)*xr(e,t,n,a,s,r,o,h,f,u,d))}const wr={kernelName:t.Unique,backendName:"cpu",kernelFunc:function(e){const{inputs:t,attrs:n,backend:a}=e,{axis:s}=n,{x:o}=t;r(o,"unique");const i=a.data.get(o.dataId).values,{outputValues:l,outputShape:u,indices:d}=Tt(i,s,o.shape,o.dtype);return[a.makeTensorInfo(u,o.dtype,l),a.makeTensorInfo([d.length],"int32",d)]}};const Mr={kernelName:t.Unpack,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{value:s}=t;let{axis:r}=a;r<0&&(r+=s.shape.length);const o=s.shape.length,i=s.shape[r],l=new Array(o-1);let u=0;for(let e=0;e<o;e++)e!==r&&(l[u++]=s.shape[e]);const d=new Array(o).fill(0),c=s.shape.slice();c[r]=1;const p=new Array(i);for(let e=0;e<p.length;e++){d[r]=e;const t=Je({inputs:{x:s},backend:n,attrs:{begin:d,size:c}});p[e]=Pt({inputs:{x:t},backend:n,attrs:{shape:l}}),n.disposeIntermediateTensorInfo(t)}return p}};const Ar={kernelName:t.UnsortedSegmentSum,backendName:"cpu",kernelFunc:function(e){const{inputs:n,backend:a,attrs:s}=e,{x:o,segmentIds:i}=n,{numSegments:l}=s;r(o,"unsortedSegmentSum");const u=[],d=[],c=o.shape.length-i.shape.length;let p=i;for(let e=0;e<c;++e){const t=la({inputs:{input:p},backend:a,attrs:{dim:e+1}});p=t,d.push(t)}for(let e=0;e<l;++e){const n=t.util.createScalarValue(e,"int32"),s=a.makeTensorInfo([],"int32",n),r=C({inputs:{a:s,b:p},backend:a}),i=b({inputs:{x:r},backend:a,attrs:{dtype:"float32"}}),l=Se({inputs:{a:i,b:o},backend:a}),c=Yn({inputs:{x:l},backend:a,attrs:{axis:0,keepDims:!1}});u.push(c),d.push(s),d.push(r),d.push(i),d.push(l),d.push(c)}const h=bs({inputs:u,backend:a,attrs:{axis:0}});return d.forEach((e=>a.disposeIntermediateTensorInfo(e))),h}},Dr=[Vt,u,$t,Gt,F,qt,Ut,Zt,jt,Kt,Jt,Xt,tn,sn,on,cn,pn,hn,fn,Ot,mn,kn,gn,In,y,R,yn,p,Sn,vn,wn,Mn,An,Dn,_n,En,Rn,Pn,Cn,Hn,On,Vn,Bn,Ln,Gn,qn,Un,Zn,jn,Kn,Qn,vt,Xn,H,ia,B,ua,G,ka,Ia,ba,Z,Ta,Na,xa,va,Fa,Q,te,m,wa,Nn,Aa,_a,za,wt,se,ie,Ra,ce,Pa,Oa,Ba,Ga,qa,Ua,ja,me,Ka,Ya,Ja,Qa,Xa,es,ts,Ie,ns,rs,ls,Te,xe,ds,ps,fs,we,ms,Is,ys,Ss,xs,Dt,Ee,vs,Fs,ws,g,pa,As,Et,Rt,Ct,Ds,_s,Es,zs,Rs,Ws,Cs,qe,Hs,Bs,$s,Us,Ke,js,Ys,Qs,Qe,is,tr,nr,ar,sr,rr,or,ir,lr,st,ur,it,cr,pr,hr,fr,mr,gt,Jn,gr,br,yr,Sr,Tr,De,wr,Mr,Ar,gs];for(const e of Dr)t.registerKernel(e);e.MathBackendCPU=i,e.shared=Nt,e.version_cpu="3.21.0",Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=tf-backend-cpu.es2017.min.js.map

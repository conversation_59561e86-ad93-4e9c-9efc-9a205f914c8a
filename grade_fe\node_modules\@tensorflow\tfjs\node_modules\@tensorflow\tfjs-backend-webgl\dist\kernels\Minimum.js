/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { Minimum } from '@tensorflow/tfjs-core';
import { CHECK_NAN_SNIPPET } from '../binaryop_gpu';
import { CHECK_NAN_SNIPPET_PACKED } from '../binaryop_packed_gpu';
import { binaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';
import { minimumImplCPU } from '../kernel_utils/shared';
const MINIMUM = CHECK_NAN_SNIPPET + `
  return min(a, b);
`;
const MINIMUM_PACKED = `
  vec4 result = vec4(min(a, b));
  bvec4 isNaNA = isnan(a);
  bvec4 isNaNB = isnan(b);
  bvec4 isNaN = bvec4(isNaNA.x || isNaNB.x, isNaNA.y || isNaNB.y, isNaNA.z || isNaNB.z, isNaNA.w || isNaNB.w);
  ` +
    CHECK_NAN_SNIPPET_PACKED + `
  return result;
`;
export const minimum = binaryKernelFunc({
    opSnippet: MINIMUM,
    packedOpSnippet: MINIMUM_PACKED,
    cpuKernelImpl: minimumImplCPU
});
export const minimumConfig = {
    kernelName: Minimum,
    backendName: 'webgl',
    kernelFunc: minimum
};
//# sourceMappingURL=data:application/json;base64,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
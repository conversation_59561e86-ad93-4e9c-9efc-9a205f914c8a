/**
 * @license
 * Copyright 2019 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { kernel_impls, NonMaxSuppressionV5 } from '@tensorflow/tfjs-core';
const nonMaxSuppressionV5Impl = kernel_impls.nonMaxSuppressionV5Impl;
import { assertNotComplex } from '../cpu_util';
export function nonMaxSuppressionV5(args) {
    const { inputs, backend, attrs } = args;
    const { boxes, scores } = inputs;
    const { maxOutputSize, iouThreshold, scoreThreshold, softNmsSigma } = attrs;
    assertNotComplex(boxes, 'NonMaxSuppressionWithScore');
    const boxesVals = backend.data.get(boxes.dataId).values;
    const scoresVals = backend.data.get(scores.dataId).values;
    const maxOutputSizeVal = maxOutputSize;
    const iouThresholdVal = iouThreshold;
    const scoreThresholdVal = scoreThreshold;
    const softNmsSigmaVal = softNmsSigma;
    const { selectedIndices, selectedScores } = nonMaxSuppressionV5Impl(boxesVals, scoresVals, maxOutputSizeVal, iouThresholdVal, scoreThresholdVal, softNmsSigmaVal);
    return [
        backend.makeTensorInfo([selectedIndices.length], 'int32', new Int32Array(selectedIndices)),
        backend.makeTensorInfo([selectedScores.length], 'float32', new Float32Array(selectedScores))
    ];
}
export const nonMaxSuppressionV5Config = {
    kernelName: NonMaxSuppressionV5,
    backendName: 'cpu',
    kernelFunc: nonMaxSuppressionV5
};
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiTm9uTWF4U3VwcHJlc3Npb25WNS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uL3RmanMtYmFja2VuZC1jcHUvc3JjL2tlcm5lbHMvTm9uTWF4U3VwcHJlc3Npb25WNS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7Ozs7Ozs7Ozs7O0dBZUc7QUFFSCxPQUFPLEVBQUMsWUFBWSxFQUE0QixtQkFBbUIsRUFBOEUsTUFBTSx1QkFBdUIsQ0FBQztBQUUvSyxNQUFNLHVCQUF1QixHQUFHLFlBQVksQ0FBQyx1QkFBdUIsQ0FBQztBQUVyRSxPQUFPLEVBQUMsZ0JBQWdCLEVBQUMsTUFBTSxhQUFhLENBQUM7QUFFN0MsTUFBTSxVQUFVLG1CQUFtQixDQUFDLElBSW5DO0lBQ0MsTUFBTSxFQUFDLE1BQU0sRUFBRSxPQUFPLEVBQUUsS0FBSyxFQUFDLEdBQUcsSUFBSSxDQUFDO0lBQ3RDLE1BQU0sRUFBQyxLQUFLLEVBQUUsTUFBTSxFQUFDLEdBQUcsTUFBTSxDQUFDO0lBQy9CLE1BQU0sRUFBQyxhQUFhLEVBQUUsWUFBWSxFQUFFLGNBQWMsRUFBRSxZQUFZLEVBQUMsR0FBRyxLQUFLLENBQUM7SUFFMUUsZ0JBQWdCLENBQUMsS0FBSyxFQUFFLDRCQUE0QixDQUFDLENBQUM7SUFFdEQsTUFBTSxTQUFTLEdBQUcsT0FBTyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLE1BQU0sQ0FBQyxDQUFDLE1BQW9CLENBQUM7SUFDdEUsTUFBTSxVQUFVLEdBQUcsT0FBTyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLE1BQW9CLENBQUM7SUFFeEUsTUFBTSxnQkFBZ0IsR0FBRyxhQUFhLENBQUM7SUFDdkMsTUFBTSxlQUFlLEdBQUcsWUFBWSxDQUFDO0lBQ3JDLE1BQU0saUJBQWlCLEdBQUcsY0FBYyxDQUFDO0lBQ3pDLE1BQU0sZUFBZSxHQUFHLFlBQVksQ0FBQztJQUVyQyxNQUFNLEVBQUMsZUFBZSxFQUFFLGNBQWMsRUFBQyxHQUFHLHVCQUF1QixDQUM3RCxTQUFTLEVBQUUsVUFBVSxFQUFFLGdCQUFnQixFQUFFLGVBQWUsRUFDeEQsaUJBQWlCLEVBQUUsZUFBZSxDQUFDLENBQUM7SUFFeEMsT0FBTztRQUNMLE9BQU8sQ0FBQyxjQUFjLENBQ2xCLENBQUMsZUFBZSxDQUFDLE1BQU0sQ0FBQyxFQUFFLE9BQU8sRUFBRSxJQUFJLFVBQVUsQ0FBQyxlQUFlLENBQUMsQ0FBQztRQUN2RSxPQUFPLENBQUMsY0FBYyxDQUNsQixDQUFDLGNBQWMsQ0FBQyxNQUFNLENBQUMsRUFBRSxTQUFTLEVBQUUsSUFBSSxZQUFZLENBQUMsY0FBYyxDQUFDLENBQUM7S0FDMUUsQ0FBQztBQUNKLENBQUM7QUFFRCxNQUFNLENBQUMsTUFBTSx5QkFBeUIsR0FBaUI7SUFDckQsVUFBVSxFQUFFLG1CQUFtQjtJQUMvQixXQUFXLEVBQUUsS0FBSztJQUNsQixVQUFVLEVBQUUsbUJBQXVDO0NBQ3BELENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgMjAxOSBHb29nbGUgTExDLiBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbiAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAqIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuICogU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKiA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuICovXG5cbmltcG9ydCB7a2VybmVsX2ltcGxzLCBLZXJuZWxDb25maWcsIEtlcm5lbEZ1bmMsIE5vbk1heFN1cHByZXNzaW9uVjUsIE5vbk1heFN1cHByZXNzaW9uVjVBdHRycywgTm9uTWF4U3VwcHJlc3Npb25WNUlucHV0cywgVGVuc29ySW5mbywgVHlwZWRBcnJheX0gZnJvbSAnQHRlbnNvcmZsb3cvdGZqcy1jb3JlJztcblxuY29uc3Qgbm9uTWF4U3VwcHJlc3Npb25WNUltcGwgPSBrZXJuZWxfaW1wbHMubm9uTWF4U3VwcHJlc3Npb25WNUltcGw7XG5pbXBvcnQge01hdGhCYWNrZW5kQ1BVfSBmcm9tICcuLi9iYWNrZW5kX2NwdSc7XG5pbXBvcnQge2Fzc2VydE5vdENvbXBsZXh9IGZyb20gJy4uL2NwdV91dGlsJztcblxuZXhwb3J0IGZ1bmN0aW9uIG5vbk1heFN1cHByZXNzaW9uVjUoYXJnczoge1xuICBpbnB1dHM6IE5vbk1heFN1cHByZXNzaW9uVjVJbnB1dHMsXG4gIGJhY2tlbmQ6IE1hdGhCYWNrZW5kQ1BVLFxuICBhdHRyczogTm9uTWF4U3VwcHJlc3Npb25WNUF0dHJzXG59KTogW1RlbnNvckluZm8sIFRlbnNvckluZm9dIHtcbiAgY29uc3Qge2lucHV0cywgYmFja2VuZCwgYXR0cnN9ID0gYXJncztcbiAgY29uc3Qge2JveGVzLCBzY29yZXN9ID0gaW5wdXRzO1xuICBjb25zdCB7bWF4T3V0cHV0U2l6ZSwgaW91VGhyZXNob2xkLCBzY29yZVRocmVzaG9sZCwgc29mdE5tc1NpZ21hfSA9IGF0dHJzO1xuXG4gIGFzc2VydE5vdENvbXBsZXgoYm94ZXMsICdOb25NYXhTdXBwcmVzc2lvbldpdGhTY29yZScpO1xuXG4gIGNvbnN0IGJveGVzVmFscyA9IGJhY2tlbmQuZGF0YS5nZXQoYm94ZXMuZGF0YUlkKS52YWx1ZXMgYXMgVHlwZWRBcnJheTtcbiAgY29uc3Qgc2NvcmVzVmFscyA9IGJhY2tlbmQuZGF0YS5nZXQoc2NvcmVzLmRhdGFJZCkudmFsdWVzIGFzIFR5cGVkQXJyYXk7XG5cbiAgY29uc3QgbWF4T3V0cHV0U2l6ZVZhbCA9IG1heE91dHB1dFNpemU7XG4gIGNvbnN0IGlvdVRocmVzaG9sZFZhbCA9IGlvdVRocmVzaG9sZDtcbiAgY29uc3Qgc2NvcmVUaHJlc2hvbGRWYWwgPSBzY29yZVRocmVzaG9sZDtcbiAgY29uc3Qgc29mdE5tc1NpZ21hVmFsID0gc29mdE5tc1NpZ21hO1xuXG4gIGNvbnN0IHtzZWxlY3RlZEluZGljZXMsIHNlbGVjdGVkU2NvcmVzfSA9IG5vbk1heFN1cHByZXNzaW9uVjVJbXBsKFxuICAgICAgYm94ZXNWYWxzLCBzY29yZXNWYWxzLCBtYXhPdXRwdXRTaXplVmFsLCBpb3VUaHJlc2hvbGRWYWwsXG4gICAgICBzY29yZVRocmVzaG9sZFZhbCwgc29mdE5tc1NpZ21hVmFsKTtcblxuICByZXR1cm4gW1xuICAgIGJhY2tlbmQubWFrZVRlbnNvckluZm8oXG4gICAgICAgIFtzZWxlY3RlZEluZGljZXMubGVuZ3RoXSwgJ2ludDMyJywgbmV3IEludDMyQXJyYXkoc2VsZWN0ZWRJbmRpY2VzKSksXG4gICAgYmFja2VuZC5tYWtlVGVuc29ySW5mbyhcbiAgICAgICAgW3NlbGVjdGVkU2NvcmVzLmxlbmd0aF0sICdmbG9hdDMyJywgbmV3IEZsb2F0MzJBcnJheShzZWxlY3RlZFNjb3JlcykpXG4gIF07XG59XG5cbmV4cG9ydCBjb25zdCBub25NYXhTdXBwcmVzc2lvblY1Q29uZmlnOiBLZXJuZWxDb25maWcgPSB7XG4gIGtlcm5lbE5hbWU6IE5vbk1heFN1cHByZXNzaW9uVjUsXG4gIGJhY2tlbmROYW1lOiAnY3B1JyxcbiAga2VybmVsRnVuYzogbm9uTWF4U3VwcHJlc3Npb25WNSBhcyB7fSBhcyBLZXJuZWxGdW5jXG59O1xuIl19
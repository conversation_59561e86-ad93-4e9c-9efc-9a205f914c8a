/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the License);
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { BatchMatMul, broadcast_util, buffer, util } from '@tensorflow/tfjs-core';
import { assertNotComplex } from '../cpu_util';
import { reshape } from './Reshape';
export function batchMatMul(args) {
    const { inputs, backend, attrs } = args;
    const { a, b } = inputs;
    const { transposeA, transposeB } = attrs;
    assertNotComplex([a, b], 'matMul');
    const aRank = a.shape.length;
    const bRank = b.shape.length;
    const innerShapeA = transposeA ? a.shape[aRank - 2] : a.shape[aRank - 1];
    const innerShapeB = transposeB ? b.shape[bRank - 1] : b.shape[bRank - 2];
    const outerShapeA = transposeA ? a.shape[aRank - 1] : a.shape[aRank - 2];
    const outerShapeB = transposeB ? b.shape[bRank - 2] : b.shape[bRank - 1];
    const outerDimsA = a.shape.slice(0, -2);
    const outerDimsB = b.shape.slice(0, -2);
    const batchDimA = util.sizeFromShape(outerDimsA);
    const batchDimB = util.sizeFromShape(outerDimsB);
    const outShapeOuterDims = broadcast_util.assertAndGetBroadcastShape(a.shape.slice(0, -2), b.shape.slice(0, -2));
    const outShape = outShapeOuterDims.concat([outerShapeA, outerShapeB]);
    util.assert(innerShapeA === innerShapeB, () => `Error in matMul: inner shapes (${innerShapeA}) and (` +
        `${innerShapeB}) of Tensors with shapes ${a.shape} and ` +
        `${b.shape} and transposeA=${transposeA}` +
        ` and transposeB=${transposeB} must match.`);
    const a3dShape = transposeA ? [batchDimA, innerShapeA, outerShapeA] :
        [batchDimA, outerShapeA, innerShapeA];
    const b3dShape = transposeB ? [batchDimB, outerShapeB, innerShapeB] :
        [batchDimB, innerShapeB, outerShapeB];
    // The rest of the implementation is designed to operate on rank-3 tensors
    const a3d = reshape({ inputs: { x: a }, backend, attrs: { shape: a3dShape } });
    const b3d = reshape({ inputs: { x: b }, backend, attrs: { shape: b3dShape } });
    const sharedDim = transposeA ? a3d.shape[1] : a3d.shape[2];
    const leftDim = transposeA ? a3d.shape[2] : a3d.shape[1];
    const rightDim = transposeB ? b3d.shape[1] : b3d.shape[2];
    const batchDim = Math.max(batchDimA, batchDimB);
    const a3dValues = backend.data.get(a3d.dataId).values;
    const b3dValues = backend.data.get(b3d.dataId).values;
    const a3dStrides = util.computeStrides(a3d.shape);
    const b3dStrides = util.computeStrides(b3d.shape);
    const [aBatch, aOuterStep, aInnerStep] = transposeA ?
        [a3dStrides[0], 1, a3dStrides[1]] :
        [a3dStrides[0], a3dStrides[1], 1];
    const [bInnerStep, bOuterStep, bBatch] = transposeB ?
        [1, b3dStrides[1], b3dStrides[0]] :
        [b3dStrides[1], 1, b3dStrides[0]];
    const size = leftDim * rightDim;
    const result = buffer([batchDim, leftDim, rightDim], a3d.dtype);
    const resVals = result.values;
    const blockSize = backend.blockSize;
    for (let bi = 0; bi < batchDim; bi++) {
        for (let i0 = 0; i0 < leftDim; i0 += blockSize) {
            for (let j0 = 0; j0 < rightDim; j0 += blockSize) {
                for (let k0 = 0; k0 < sharedDim; k0 += blockSize) {
                    // for when blockSize doesn't evenly divide the input
                    const iBlock = Math.min(i0 + blockSize, leftDim);
                    const jBlock = Math.min(j0 + blockSize, rightDim);
                    const kBlock = Math.min(k0 + blockSize, sharedDim);
                    for (let i = i0; i < iBlock; i++) {
                        for (let j = j0; j < jBlock; j++) {
                            let sum = 0.0;
                            for (let k = k0; k < kBlock; k++) {
                                const batchOffsetA = Math.min(bi, batchDimA - 1) * aBatch;
                                const batchOffsetB = Math.min(bi, batchDimB - 1) * bBatch;
                                const aVal = a3dValues[batchOffsetA + i * aOuterStep + k * aInnerStep];
                                const bVal = b3dValues[k * bInnerStep + j * bOuterStep + batchOffsetB];
                                sum += aVal * bVal;
                            }
                            resVals[bi * size + (i * rightDim + j)] += sum;
                        }
                    }
                }
            }
        }
    }
    backend.disposeIntermediateTensorInfo(a3d);
    backend.disposeIntermediateTensorInfo(b3d);
    // set correct shape on output.
    return backend.makeTensorInfo(outShape, result.dtype, result.values);
}
export const batchMatMulConfig = {
    kernelName: BatchMatMul,
    backendName: 'cpu',
    kernelFunc: batchMatMul,
};
//# sourceMappingURL=data:application/json;base64,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
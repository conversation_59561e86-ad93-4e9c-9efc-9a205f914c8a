/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, Conv3DBackpropInputV2, TensorBuffer, util } from '@tensorflow/tfjs-core';
import { assertNotComplex } from '../cpu_util';
export function conv3DBackpropInputV2(args) {
    const { inputs, backend, attrs } = args;
    const { dy, filter } = inputs;
    const { pad, strides, inputShape } = attrs;
    assertNotComplex([dy], 'conv3dBackpropInputV2');
    const dyStrides = util.computeStrides(dy.shape);
    const filterStrides = util.computeStrides(filter.shape);
    const convInfo = backend_util.computeConv3DInfo(inputShape, filter.shape, strides, 1 /* dilations */, pad);
    const dx = new TensorBuffer(convInfo.inShape, 'float32');
    const dxValues = dx.values;
    const [dxS0, dxS1, dxS2, dxS3] = dx.strides;
    const dyValues = backend.data.get(dy.dataId).values;
    const [dyS0, dyS1, dyS2, dyS3] = dyStrides;
    const fltValues = backend.data.get(filter.dataId).values;
    const [fltS0, fltS1, fltS2, fltS3] = filterStrides;
    const { batchSize, filterDepth, filterHeight, filterWidth, inChannels, inDepth, inHeight, inWidth, outChannels, outDepth, outHeight, outWidth, strideDepth, strideHeight, strideWidth } = convInfo;
    const frontPad = filterDepth - 1 - convInfo.padInfo.front;
    const topPad = filterHeight - 1 - convInfo.padInfo.top;
    const leftPad = filterWidth - 1 - convInfo.padInfo.left;
    for (let b = 0; b < batchSize; ++b) {
        for (let d1 = 0; d1 < inChannels; ++d1) {
            // Frames of depth
            for (let xF = 0; xF < inDepth; ++xF) {
                const xFCorner = xF - frontPad;
                const xFMin = Math.max(0, Math.ceil(xFCorner / strideDepth));
                const yFMax = Math.min(outDepth, (filterDepth + xFCorner) / strideDepth);
                // Rows as per standard 2d matrix notation
                for (let xR = 0; xR < inHeight; ++xR) {
                    const xRCorner = xR - topPad;
                    const xRMin = Math.max(0, Math.ceil(xRCorner / strideHeight));
                    const yRMax = Math.min(outHeight, (filterHeight + xRCorner) / strideHeight);
                    // Columns as per standard 2d matrix notation
                    for (let xC = 0; xC < inWidth; ++xC) {
                        const xCCorner = xC - leftPad;
                        const xCMin = Math.max(0, Math.ceil(xCCorner / strideWidth));
                        const yCMax = Math.min(outWidth, (filterWidth + xCCorner) / strideWidth);
                        let dotProd = 0;
                        for (let yF = xFMin; yF < yFMax; ++yF) {
                            const wF = yF * strideDepth - xFCorner;
                            for (let yR = xRMin; yR < yRMax; ++yR) {
                                const wR = yR * strideHeight - xRCorner;
                                for (let yC = xCMin; yC < yCMax; ++yC) {
                                    const wC = yC * strideWidth - xCCorner;
                                    const dyOffset = dyS0 * b + dyS1 * yF + dyS2 * yR + dyS3 * yC;
                                    const fltOffset = fltS0 * (filterDepth - 1 - wF) +
                                        fltS1 * (filterHeight - 1 - wR) +
                                        fltS2 * (filterWidth - 1 - wC) + fltS3 * d1;
                                    for (let d2 = 0; d2 < outChannels; ++d2) {
                                        const pixel = dyValues[dyOffset + d2];
                                        const weight = fltValues[fltOffset + d2];
                                        dotProd += pixel * weight;
                                    }
                                }
                            }
                        }
                        dxValues[dxS0 * b + dxS1 * xF + dxS2 * xR + dxS3 * xC + d1] =
                            dotProd;
                    }
                }
            }
        }
    }
    return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);
}
export const conv3DBackpropInputV2Config = {
    kernelName: Conv3DBackpropInputV2,
    backendName: 'cpu',
    kernelFunc: conv3DBackpropInputV2
};
//# sourceMappingURL=data:application/json;base64,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
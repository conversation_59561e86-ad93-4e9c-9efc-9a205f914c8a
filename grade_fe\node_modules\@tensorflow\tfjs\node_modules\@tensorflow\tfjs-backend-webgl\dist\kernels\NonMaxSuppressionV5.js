/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, kernel_impls, NonMaxSuppressionV5 } from '@tensorflow/tfjs-core';
const nonMaxSuppressionV5Impl = kernel_impls.nonMaxSuppressionV5Impl;
export function nonMaxSuppressionV5(args) {
    backend_util.warn('tf.nonMaxSuppression() in webgl locks the UI thread. ' +
        'Call tf.nonMaxSuppressionAsync() instead');
    const { inputs, backend, attrs } = args;
    const { boxes, scores } = inputs;
    const { maxOutputSize, iouThreshold, scoreThreshold, softNmsSigma } = attrs;
    const boxesVals = backend.readSync(boxes.dataId);
    const scoresVals = backend.readSync(scores.dataId);
    const maxOutputSizeVal = maxOutputSize;
    const iouThresholdVal = iouThreshold;
    const scoreThresholdVal = scoreThreshold;
    const softNmsSigmaVal = softNmsSigma;
    const { selectedIndices, selectedScores } = nonMaxSuppressionV5Impl(boxesVals, scoresVals, maxOutputSizeVal, iouThresholdVal, scoreThresholdVal, softNmsSigmaVal);
    return [
        backend.makeTensorInfo([selectedIndices.length], 'int32', new Int32Array(selectedIndices)),
        backend.makeTensorInfo([selectedScores.length], 'float32', new Float32Array(selectedScores))
    ];
}
export const nonMaxSuppressionV5Config = {
    kernelName: NonMaxSuppressionV5,
    backendName: 'webgl',
    kernelFunc: nonMaxSuppressionV5
};
//# sourceMappingURL=data:application/json;base64,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
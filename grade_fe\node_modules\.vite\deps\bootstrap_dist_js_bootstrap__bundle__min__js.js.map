{"version": 3, "sources": ["../../bootstrap/js/src/dom/data.js", "../../bootstrap/js/src/util/index.js", "../../bootstrap/js/src/dom/event-handler.js", "../../bootstrap/js/src/dom/manipulator.js", "../../bootstrap/js/src/util/config.js", "../../bootstrap/js/src/base-component.js", "../../bootstrap/js/src/dom/selector-engine.js", "../../bootstrap/js/src/util/component-functions.js", "../../bootstrap/js/src/alert.js", "../../bootstrap/js/src/button.js", "../../bootstrap/js/src/util/swipe.js", "../../bootstrap/js/src/carousel.js", "../../bootstrap/js/src/collapse.js", "../../bootstrap/node_modules/@popperjs/core/lib/enums.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../bootstrap/node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/math.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/userAgent.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/within.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../bootstrap/node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/getVariation.js", "../../bootstrap/node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../bootstrap/node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../bootstrap/node_modules/@popperjs/core/lib/modifiers/flip.js", "../../bootstrap/node_modules/@popperjs/core/lib/modifiers/hide.js", "../../bootstrap/node_modules/@popperjs/core/lib/modifiers/offset.js", "../../bootstrap/node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../bootstrap/node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../bootstrap/node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../bootstrap/node_modules/@popperjs/core/lib/createPopper.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/debounce.js", "../../bootstrap/node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../bootstrap/node_modules/@popperjs/core/lib/popper-lite.js", "../../bootstrap/node_modules/@popperjs/core/lib/popper.js", "../../bootstrap/js/src/dropdown.js", "../../bootstrap/js/src/util/backdrop.js", "../../bootstrap/js/src/util/focustrap.js", "../../bootstrap/js/src/util/scrollbar.js", "../../bootstrap/js/src/modal.js", "../../bootstrap/js/src/offcanvas.js", "../../bootstrap/js/src/util/sanitizer.js", "../../bootstrap/js/src/util/template-factory.js", "../../bootstrap/js/src/tooltip.js", "../../bootstrap/js/src/popover.js", "../../bootstrap/js/src/scrollspy.js", "../../bootstrap/js/src/tab.js", "../../bootstrap/js/src/toast.js", "../../bootstrap/js/index.umd.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1_000_000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n/**\n * Properly escape IDs selectors to handle weird IDs\n * @param {string} selector\n * @returns {string}\n */\nconst parseSelector = selector => {\n  if (selector && window.CSS && window.CSS.escape) {\n    // document.querySelector needs escaping to handle IDs (html5+) containing for instance /\n    selector = selector.replace(/#([^\\s\"#']+)/g, (match, id) => `#${CSS.escape(id)}`)\n  }\n\n  return selector\n}\n\n// Shout-out Angus Croll (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`\n  }\n\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false\n  }\n\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0]\n  }\n\n  return typeof object.nodeType !== 'undefined'\n}\n\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object\n  }\n\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(parseSelector(object))\n  }\n\n  return null\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])')\n\n  if (!closedDetails) {\n    return elementIsVisible\n  }\n\n  if (closedDetails !== element) {\n    const summary = element.closest('summary')\n    if (summary && summary.parentNode !== closedDetails) {\n      return false\n    }\n\n    if (summary === null) {\n      return false\n    }\n  }\n\n  return elementIsVisible\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight // eslint-disable-line no-unused-expressions\n}\n\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback()\n        }\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = (possibleCallback, args = [], defaultValue = possibleCallback) => {\n  return typeof possibleCallback === 'function' ? possibleCallback(...args) : defaultValue\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0]\n  }\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition,\n  findShadowRoot,\n  getElement,\n  getjQuery,\n  getNextActiveElement,\n  getTransitionDurationFromElement,\n  getUID,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  onDOMContentLoaded,\n  parseSelector,\n  reflow,\n  triggerTransitionEnd,\n  toType\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index.js'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction makeEventUid(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getElementEvents(element) {\n  const uid = makeEventUid(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    hydrateObj(event, { delegateTarget: element })\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        hydrateObj(event, { delegateTarget: target })\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, callable, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.callable === callable && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const isDelegated = typeof handler === 'string'\n  // TODO: tooltip passes `false` instead of selector, so we need to check\n  const callable = isDelegated ? delegationFunction : (handler || delegationFunction)\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [isDelegated, callable, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  let [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    callable = wrapFunction(callable)\n  }\n\n  const events = getElementEvents(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, callable, isDelegated ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = makeEventUid(callable, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = isDelegated ?\n    bootstrapDelegationHandler(element, handler, callable) :\n    bootstrapHandler(element, callable)\n\n  fn.delegationSelector = isDelegated ? handler : null\n  fn.callable = callable\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, isDelegated)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const [handlerKey, event] of Object.entries(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getElementEvents(element)\n    const storeElementEvent = events[typeEvent] || {}\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof callable !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!Object.keys(storeElementEvent).length) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, callable, isDelegated ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    for (const [keyHandlers, event] of Object.entries(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    const evt = hydrateObj(new Event(event, { bubbles, cancelable: true }), args)\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nfunction hydrateObj(obj, meta = {}) {\n  for (const [key, value] of Object.entries(meta)) {\n    try {\n      obj[key] = value\n    } catch {\n      Object.defineProperty(obj, key, {\n        configurable: true,\n        get() {\n          return value\n        }\n      })\n    }\n  }\n\n  return obj\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  if (value === Number(value).toString()) {\n    return Number(value)\n  }\n\n  if (value === '' || value === 'null') {\n    return null\n  }\n\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  try {\n    return JSON.parse(decodeURIComponent(value))\n  } catch {\n    return value\n  }\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'))\n\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '')\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n      attributes[pureKey] = normalizeData(element.dataset[key])\n    }\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator.js'\nimport { isElement, toType } from './index.js'\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {}\n  }\n\n  static get DefaultType() {\n    return {}\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    return config\n  }\n\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {} // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    }\n  }\n\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const [property, expectedTypes] of Object.entries(configTypes)) {\n      const value = config[property]\n      const valueType = isElement(value) ? 'element' : toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(\n          `${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n        )\n      }\n    }\n  }\n}\n\nexport default Config\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data.js'\nimport EventHandler from './dom/event-handler.js'\nimport Config from './util/config.js'\nimport { executeAfterTransition, getElement } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst VERSION = '5.3.3'\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super()\n\n    element = getElement(element)\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    this._config = this._getConfig(config)\n\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null\n    }\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible, parseSelector } from '../util/index.js'\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || (!hrefAttribute.includes('#') && !hrefAttribute.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`\n    }\n\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null\n  }\n\n  return selector ? selector.split(',').map(sel => parseSelector(sel)).join(',') : null\n}\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  },\n\n  getSelectorFromElement(element) {\n    const selector = getSelector(element)\n\n    if (selector) {\n      return SelectorEngine.findOne(selector) ? selector : null\n    }\n\n    return null\n  },\n\n  getElementFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.findOne(selector) : null\n  },\n\n  getMultipleElementsFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.find(selector) : []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport { isDisabled } from './index.js'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = SelectorEngine.getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport { execute } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  endCallback: null,\n  leftCallback: null,\n  rightCallback: null\n}\n\nconst DefaultType = {\n  endCallback: '(function|null)',\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index.js'\nimport Swipe from './util/swipe.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)', // TODO:v6 remove boolean support\n  keyboard: 'boolean',\n  pause: '(string|boolean)',\n  ride: '(boolean|string)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // TODO: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  getElement,\n  reflow\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  parent: null,\n  toggle: true\n}\n\nconst DefaultType = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = SelectorEngine.getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = SelectorEngine.getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = SelectorEngine.getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  for (const element of SelectorEngine.getMultipleElementsFromSelector(this)) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}", "import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}", "import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref, win) {\n  var x = _ref.x,\n      y = _ref.y;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }, getWindow(popper)) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  execute,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 2],\n  popperConfig: null,\n  reference: 'toggle'\n}\n\nconst DefaultType = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.prev(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // TODO: v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ?\n      this :\n      (SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.next(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode))\n\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport {\n  execute, executeAfterTransition, getElement, reflow\n} from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n}\n\nconst DefaultType = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n}\n\nconst DefaultType = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport { isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport FocusTrap from './util/focustrap.js'\nimport {\n  defineJQueryPlugin, isRTL, isVisible, reflow\n} from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  focus: true,\n  keyboard: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  focus: 'boolean',\n  keyboard: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    EventHandler.off(window, EVENT_KEY)\n    EventHandler.off(this._dialog, EVENT_KEY)\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_MOUSEDOWN_DISMISS, event => {\n      // a bad trick to segregate clicks that may start inside dialog but end outside, and avoid listen to scrollbar clicks\n      EventHandler.one(this._element, EVENT_CLICK_DISMISS, event2 => {\n        if (this._element !== event.target || this._element !== event2.target) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n          return\n        }\n\n        if (this._config.backdrop) {\n          this.hide()\n        }\n      })\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport FocusTrap from './util/focustrap.js'\nimport {\n  defineJQueryPlugin,\n  isDisabled,\n  isVisible\n} from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll || this._config.backdrop) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n// js-docs-start allow-list\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  dd: [],\n  div: [],\n  dl: [],\n  dt: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n// js-docs-end allow-list\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\n/**\n * A pattern that recognizes URLs that are safe wrt. XSS in URL navigation\n * contexts.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/15.2.8/packages/core/src/sanitization/url_sanitizer.ts#L38\n */\n// eslint-disable-next-line unicorn/better-regex\nconst SAFE_URL_PATTERN = /^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer.js'\nimport { execute, getElement, isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n}\n\nconst DefaultContentType = {\n  entry: '(string|element|function|null)',\n  selector: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this])\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport {\n  defineJQueryPlugin, execute, findShadowRoot, getElement, getUID, isRTL, noop\n} from './util/index.js'\nimport { DefaultAllowlist } from './util/sanitizer.js'\nimport TemplateFactory from './util/template-factory.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 6],\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n            '<div class=\"tooltip-arrow\"></div>' +\n            '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  title: '',\n  trigger: 'hover focus'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = null\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n    this._newContent = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n\n    if (!this._config.selector) {\n      this._fixTitle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle() {\n    if (!this._isEnabled) {\n      return\n    }\n\n    this._activeTrigger.click = !this._activeTrigger.click\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'))\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // TODO: v6 remove this or make it optional\n    this._disposePopper()\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    this._popper = this._createPopper(tip)\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (this._isHovered === false) {\n        this._leave()\n      }\n\n      this._isHovered = false\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = null // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        this._disposePopper()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // TODO: remove this check in v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // TODO: v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    this._newContent = content\n    if (this._isShown()) {\n      this._disposePopper()\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title')\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = execute(this._config.placement, [this, tip, this._element])\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this._element])\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context.toggle()\n        })\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.setAttribute('data-bs-original-title', title) // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const [key, value] of Object.entries(this._config)) {\n      if (this.constructor.Default[key] !== value) {\n        config[key] = value\n      }\n    }\n\n    config.selector = false\n    config.trigger = 'manual'\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    if (this.tip) {\n      this.tip.remove()\n      this.tip = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Tooltip from './tooltip.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  content: '',\n  offset: [0, 8],\n  placement: 'right',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n    '<div class=\"popover-arrow\"></div>' +\n    '<h3 class=\"popover-header\"></h3>' +\n    '<div class=\"popover-body\"></div>' +\n    '</div>',\n  trigger: 'click'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin, getElement, isDisabled, isVisible\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null,\n  threshold: [0.1, 0.5, 1]\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element',\n  threshold: 'array'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n    config.rootMargin = config.offset ? `${config.offset}px 0px -30%` : config.rootMargin\n\n    if (typeof config.threshold === 'string') {\n      config.threshold = config.threshold.split(',').map(value => Number.parseFloat(value))\n    }\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height, behavior: 'smooth' })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: this._config.threshold,\n      rootMargin: this._config.rootMargin\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(decodeURI(anchor.hash), this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(decodeURI(anchor.hash), anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport { defineJQueryPlugin, getNextActiveElement, isDisabled } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst HOME_KEY = 'Home'\nconst END_KEY = 'End'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = `:not(${SELECTOR_DROPDOWN_TOGGLE})`\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // TODO: could only be `tab` in v6\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // TODO: should throw exception in v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(SelectorEngine.getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.add(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(SelectorEngine.getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.remove(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY, HOME_KEY, END_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n\n    const children = this._getChildren().filter(element => !isDisabled(element))\n    let nextActiveElement\n\n    if ([HOME_KEY, END_KEY].includes(event.key)) {\n      nextActiveElement = children[event.key === HOME_KEY ? 0 : children.length - 1]\n    } else {\n      const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n      nextActiveElement = getNextActiveElement(children, event.target, isNext, true)\n    }\n\n    if (nextActiveElement) {\n      nextActiveElement.focus({ preventScroll: true })\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = SelectorEngine.getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport { defineJQueryPlugin, reflow } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${D<PERSON>A_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout': {\n        this._hasMouseInteraction = isInteracting\n        break\n      }\n\n      case 'focusin':\n      case 'focusout': {\n        this._hasKeyboardInteraction = isInteracting\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert.js'\nimport Button from './src/button.js'\nimport Carousel from './src/carousel.js'\nimport Collapse from './src/collapse.js'\nimport Dropdown from './src/dropdown.js'\nimport Modal from './src/modal.js'\nimport Offcanvas from './src/offcanvas.js'\nimport Popover from './src/popover.js'\nimport ScrollSpy from './src/scrollspy.js'\nimport Tab from './src/tab.js'\nimport Toast from './src/toast.js'\nimport Tooltip from './src/tooltip.js'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"], "mappings": ";;;;;;;;;;;AAWA,YAAMA,IAAa,oBAAIC,OAEvBC,IAAe,EACbC,IAAIC,IAASC,IAAKC,IAAAA;AACXN,UAAWO,IAAIH,EAAAA,KAClBJ,EAAWG,IAAIC,IAAS,oBAAIH,KAAAA;AAG9B,cAAMO,KAAcR,EAAWS,IAAIL,EAAAA;AAI9BI,QAAAA,GAAYD,IAAIF,EAAAA,KAA6B,MAArBG,GAAYE,OAMzCF,GAAYL,IAAIE,IAAKC,EAAAA,IAJnBK,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKN,GAAYO,KAAAA,CAAAA,EAAQ,CAAA,CAAA,GAAA;MAAA,GAOhIN,KAAGA,CAACL,IAASC,OACPL,EAAWO,IAAIH,EAAAA,KACVJ,EAAWS,IAAIL,EAAAA,EAASK,IAAIJ,EAAAA,KAG9B,MAGTW,OAAOZ,IAASC,IAAAA;AACd,YAAA,CAAKL,EAAWO,IAAIH,EAAAA,EAClB;AAGF,cAAMI,KAAcR,EAAWS,IAAIL,EAAAA;AAEnCI,QAAAA,GAAYS,OAAOZ,EAAAA,GAGM,MAArBG,GAAYE,QACdV,EAAWiB,OAAOb,EAAAA;MAEtB,EAAA,GC5CIc,IAAiB,iBAOjBC,IAAgBC,CAAAA,QAChBA,MAAYC,OAAOC,OAAOD,OAAOC,IAAIC,WAEvCH,KAAWA,GAASI,QAAQ,iBAAiB,CAACC,IAAOC,OAAQ,IAAGJ,IAAIC,OAAOG,EAAAA,CAAAA,EAAAA,IAGtEN,KA+CHO,IAAuBvB,CAAAA,OAAAA;AAC3BA,QAAAA,GAAQwB,cAAc,IAAIC,MAAMX,CAAAA,CAAAA;MAAgB,GAG5CY,IAAYC,CAAAA,OAAAA,EAAAA,CACXA,MAA4B,YAAA,OAAXA,QAAAA,WAIXA,GAAOC,WAChBD,KAASA,GAAO,CAAA,IAAA,WAGJA,GAAOE,WAGjBC,IAAaH,CAAAA,OAEbD,EAAUC,EAAAA,IACLA,GAAOC,SAASD,GAAO,CAAA,IAAKA,KAGf,YAAA,OAAXA,MAAuBA,GAAOI,SAAS,IACzCC,SAASC,cAAclB,EAAcY,EAAAA,CAAAA,IAGvC,MAGHO,IAAYlC,CAAAA,OAAAA;AAChB,YAAA,CAAK0B,EAAU1B,EAAAA,KAAgD,MAApCA,GAAQmC,eAAAA,EAAiBJ,OAClD,QAAA;AAGF,cAAMK,KAAgF,cAA7DC,iBAAiBrC,EAAAA,EAASsC,iBAAiB,YAAA,GAE9DC,KAAgBvC,GAAQwC,QAAQ,qBAAA;AAEtC,YAAA,CAAKD,GACH,QAAOH;AAGT,YAAIG,OAAkBvC,IAAS;AAC7B,gBAAMyC,KAAUzC,GAAQwC,QAAQ,SAAA;AAChC,cAAIC,MAAWA,GAAQC,eAAeH,GACpC,QAAA;AAGF,cAAgB,SAAZE,GACF,QAAA;QAEJ;AAEA,eAAOL;MAAgB,GAGnBO,IAAa3C,CAAAA,OAAAA,CACZA,MAAWA,GAAQ6B,aAAae,KAAKC,gBAAAA,CAAAA,CAItC7C,GAAQ8C,UAAUC,SAAS,UAAA,MAAA,WAIpB/C,GAAQgD,WACVhD,GAAQgD,WAGVhD,GAAQiD,aAAa,UAAA,KAAoD,YAArCjD,GAAQkD,aAAa,UAAA,IAG5DC,IAAiBnD,CAAAA,OAAAA;AACrB,YAAA,CAAKgC,SAASoB,gBAAgBC,aAC5B,QAAO;AAIT,YAAmC,cAAA,OAAxBrD,GAAQsD,aAA4B;AAC7C,gBAAMC,KAAOvD,GAAQsD,YAAAA;AACrB,iBAAOC,cAAgBC,aAAaD,KAAO;QAC7C;AAEA,eAAIvD,cAAmBwD,aACdxD,KAIJA,GAAQ0C,aAINS,EAAenD,GAAQ0C,UAAAA,IAHrB;MAGgC,GAGrCe,IAAOA,MAAAA;MAAAA,GAUPC,IAAS1D,CAAAA,OAAAA;AACbA,QAAAA,GAAQ2D;MAAY,GAGhBC,IAAYA,MACZ3C,OAAO4C,UAAAA,CAAW7B,SAAS8B,KAAKb,aAAa,mBAAA,IACxChC,OAAO4C,SAGT,MAGHE,IAA4B,CAAA,GAmB5BC,IAAQA,MAAuC,UAAjChC,SAASoB,gBAAgBa,KAEvCC,IAAqBC,CAAAA,OAAAA;AAnBAC,YAAAA;AAAAA,QAAAA,KAoBN,MAAA;AACjB,gBAAMC,KAAIT,EAAAA;AAEV,cAAIS,IAAG;AACL,kBAAMC,KAAOH,GAAOI,MACdC,KAAqBH,GAAEI,GAAGH,EAAAA;AAChCD,YAAAA,GAAEI,GAAGH,EAAAA,IAAQH,GAAOO,iBACpBL,GAAEI,GAAGH,EAAAA,EAAMK,cAAcR,IACzBE,GAAEI,GAAGH,EAAAA,EAAMM,aAAa,OACtBP,GAAEI,GAAGH,EAAAA,IAAQE,IACNL,GAAOO;UAElB;QAAA,GA/B0B,cAAxB1C,SAAS6C,cAENd,EAA0BhC,UAC7BC,SAAS8C,iBAAiB,oBAAoB,MAAA;AAC5C,qBAAWV,MAAYL,EACrBK,CAAAA,GAAAA;QACF,CAAA,GAIJL,EAA0BgB,KAAKX,EAAAA,KAE/BA,GAAAA;MAoBA,GAGEY,IAAUA,CAACC,IAAkBC,KAAO,CAAA,GAAIC,KAAeF,OACxB,cAAA,OAArBA,KAAkCA,GAAAA,GAAoBC,EAAAA,IAAQC,IAGxEC,IAAyBA,CAAChB,IAAUiB,IAAmBC,KAAAA,SAAoB;AAC/E,YAAA,CAAKA,GAEH,QAAA,KADAN,EAAQZ,EAAAA;AAIV,cACMmB,MA7LiCvF,CAAAA,OAAAA;AACvC,cAAA,CAAKA,GACH,QAAO;AAIT,cAAA,EAAIwF,oBAAEA,IAAkBC,iBAAEA,GAAAA,IAAoBxE,OAAOoB,iBAAiBrC,EAAAA;AAEtE,gBAAM0F,KAA0BC,OAAOC,WAAWJ,EAAAA,GAC5CK,KAAuBF,OAAOC,WAAWH,EAAAA;AAG/C,iBAAKC,MAA4BG,MAKjCL,KAAqBA,GAAmBM,MAAM,GAAA,EAAK,CAAA,GACnDL,KAAkBA,GAAgBK,MAAM,GAAA,EAAK,CAAA,GAxDf,OA0DtBH,OAAOC,WAAWJ,EAAAA,IAAsBG,OAAOC,WAAWH,EAAAA,MAPzD;QAOoG,GAyKnDJ,EAAAA,IADlC;AAGxB,YAAIU,KAAAA;AAEJ,cAAMC,KAAUA,CAAAA,EAAGC,QAAAA,GAAAA,MAAAA;AACbA,UAAAA,OAAWZ,OAIfU,KAAAA,MACAV,GAAkBa,oBAAoBpF,GAAgBkF,EAAAA,GACtDhB,EAAQZ,EAAAA;QAAS;AAGnBiB,QAAAA,GAAkBP,iBAAiBhE,GAAgBkF,EAAAA,GACnDG,WAAW,MAAA;AACJJ,UAAAA,MACHxE,EAAqB8D,EAAAA;QACvB,GACCE,EAAAA;MAAiB,GAYhBa,IAAuBA,CAACC,IAAMC,IAAeC,IAAeC,OAAAA;AAChE,cAAMC,KAAaJ,GAAKtE;AACxB,YAAI2E,KAAQL,GAAKM,QAAQL,EAAAA;AAIzB,eAAA,OAAII,KAAAA,CACMH,MAAiBC,KAAiBH,GAAKI,KAAa,CAAA,IAAKJ,GAAK,CAAA,KAGxEK,MAASH,KAAgB,IAAA,IAErBC,OACFE,MAASA,KAAQD,MAAcA,KAG1BJ,GAAKO,KAAKC,IAAI,GAAGD,KAAKE,IAAIJ,IAAOD,KAAa,CAAA,CAAA,CAAA;MAAI,GC7QrDM,IAAiB,sBACjBC,IAAiB,QACjBC,IAAgB,UAChBC,IAAgB,CAAA;AACtB,UAAIC,IAAW;AACf,YAAMC,IAAe,EACnBC,YAAY,aACZC,YAAY,WAAA,GAGRC,IAAe,oBAAIC,IAAI,CAC3B,SACA,YACA,WACA,aACA,eACA,cACA,kBACA,aACA,YACA,aACA,eACA,aACA,WACA,YACA,SACA,qBACA,cACA,aACA,YACA,eACA,eACA,eACA,aACA,gBACA,iBACA,gBACA,iBACA,cACA,SACA,QACA,UACA,SACA,UACA,UACA,WACA,YACA,QACA,UACA,gBACA,UACA,QACA,oBACA,oBACA,SACA,SACA,QAAA,CAAA;AAOF,eAASC,EAAazH,IAAS0H,IAAAA;AAC7B,eAAQA,MAAQ,GAAEA,EAAAA,KAAQP,GAAAA,MAAiBnH,GAAQmH,YAAYA;MACjE;AAEA,eAASQ,EAAiB3H,IAAAA;AACxB,cAAM0H,KAAMD,EAAazH,EAAAA;AAKzB,eAHAA,GAAQmH,WAAWO,IACnBR,EAAcQ,EAAAA,IAAOR,EAAcQ,EAAAA,KAAQ,CAAA,GAEpCR,EAAcQ,EAAAA;MACvB;AAoCA,eAASE,EAAYC,IAAQC,IAAUC,KAAqB,MAAA;AAC1D,eAAOC,OAAOC,OAAOJ,EAAAA,EAClBK,KAAKC,CAAAA,OAASA,GAAML,aAAaA,MAAYK,GAAMJ,uBAAuBA,EAAAA;MAC/E;AAEA,eAASK,EAAoBC,IAAmBrC,IAASsC,IAAAA;AACvD,cAAMC,KAAiC,YAAA,OAAZvC,IAErB8B,KAAWS,KAAcD,KAAsBtC,MAAWsC;AAChE,YAAIE,KAAYC,EAAaJ,EAAAA;AAM7B,eAJKd,EAAapH,IAAIqI,EAAAA,MACpBA,KAAYH,KAGP,CAACE,IAAaT,IAAUU,EAAAA;MACjC;AAEA,eAASE,EAAW1I,IAASqI,IAAmBrC,IAASsC,IAAoBK,IAAAA;AAC3E,YAAiC,YAAA,OAAtBN,MAAAA,CAAmCrI,GAC5C;AAGF,YAAA,CAAKuI,IAAaT,IAAUU,EAAAA,IAAaJ,EAAoBC,IAAmBrC,IAASsC,EAAAA;AAIzF,YAAID,MAAqBjB,GAAc;AACrC,gBAAMwB,KAAenE,CAAAA,OACZ,SAAU0D,IAAAA;AACf,gBAAA,CAAKA,GAAMU,iBAAkBV,GAAMU,kBAAkBV,GAAMW,kBAAAA,CAAmBX,GAAMW,eAAe/F,SAASoF,GAAMU,aAAAA,EAChH,QAAOpE,GAAGsE,KAAKC,MAAMb,EAAAA;UAAAA;AAK3BL,UAAAA,KAAWc,GAAad,EAAAA;QAC1B;AAEA,cAAMD,KAASF,EAAiB3H,EAAAA,GAC1BiJ,KAAWpB,GAAOW,EAAAA,MAAeX,GAAOW,EAAAA,IAAa,CAAA,IACrDU,KAAmBtB,EAAYqB,IAAUnB,IAAUS,KAAcvC,KAAU,IAAA;AAEjF,YAAIkD,GAGF,QAAA,MAFAA,GAAiBP,SAASO,GAAiBP,UAAUA;AAKvD,cAAMjB,KAAMD,EAAaK,IAAUO,GAAkBjH,QAAQ2F,GAAgB,EAAA,CAAA,GACvEtC,KAAK8D,KAxEb,yBAAoCvI,IAASgB,IAAUyD,IAAAA;AACrD,iBAAO,SAASuB,GAAQmC,IAAAA;AACtB,kBAAMgB,KAAcnJ,GAAQoJ,iBAAiBpI,EAAAA;AAE7C,qBAAK,EAAIiF,QAAEA,GAAAA,IAAWkC,IAAOlC,MAAUA,OAAW+C,MAAM/C,KAASA,GAAOvD,WACtE,YAAW2G,MAAcF,GACvB,KAAIE,OAAepD,GAUnB,QANAqD,EAAWnB,IAAO,EAAEW,gBAAgB7C,GAAAA,CAAAA,GAEhCD,GAAQ2C,UACVY,EAAaC,IAAIxJ,IAASmI,GAAMsB,MAAMzI,IAAUyD,EAAAA,GAG3CA,GAAGiF,MAAMzD,IAAQ,CAACkC,EAAAA,CAAAA;UAAAA;QAIjC,EAqD+BnI,IAASgG,IAAS8B,EAAAA,IArFjD,yBAA0B9H,IAASyE,IAAAA;AACjC,iBAAO,SAASuB,GAAQmC,IAAAA;AAOtB,mBANAmB,EAAWnB,IAAO,EAAEW,gBAAgB9I,GAAAA,CAAAA,GAEhCgG,GAAQ2C,UACVY,EAAaC,IAAIxJ,IAASmI,GAAMsB,MAAMhF,EAAAA,GAGjCA,GAAGiF,MAAM1J,IAAS,CAACmI,EAAAA,CAAAA;UAAAA;QAE9B,EA4EqBnI,IAAS8H,EAAAA;AAE5BrD,QAAAA,GAAGsD,qBAAqBQ,KAAcvC,KAAU,MAChDvB,GAAGqD,WAAWA,IACdrD,GAAGkE,SAASA,IACZlE,GAAG0C,WAAWO,IACduB,GAASvB,EAAAA,IAAOjD,IAEhBzE,GAAQ8E,iBAAiB0D,IAAW/D,IAAI8D,EAAAA;MAC1C;AAEA,eAASoB,EAAc3J,IAAS6H,IAAQW,IAAWxC,IAAS+B,IAAAA;AAC1D,cAAMtD,KAAKmD,EAAYC,GAAOW,EAAAA,GAAYxC,IAAS+B,EAAAA;AAE9CtD,QAAAA,OAILzE,GAAQkG,oBAAoBsC,IAAW/D,IAAImF,QAAQ7B,EAAAA,CAAAA,GAAAA,OAC5CF,GAAOW,EAAAA,EAAW/D,GAAG0C,QAAAA;MAC9B;AAEA,eAAS0C,EAAyB7J,IAAS6H,IAAQW,IAAWsB,IAAAA;AAC5D,cAAMC,KAAoBlC,GAAOW,EAAAA,KAAc,CAAA;AAE/C,mBAAK,CAAOwB,IAAY7B,EAAAA,KAAUH,OAAOiC,QAAQF,EAAAA,EAC3CC,CAAAA,GAAWE,SAASJ,EAAAA,KACtBH,EAAc3J,IAAS6H,IAAQW,IAAWL,GAAML,UAAUK,GAAMJ,kBAAAA;MAGtE;AAEA,eAASU,EAAaN,IAAAA;AAGpB,eADAA,KAAQA,GAAM/G,QAAQ4F,GAAgB,EAAA,GAC/BI,EAAae,EAAAA,KAAUA;MAChC;AAEA,YAAMoB,IAAe,EACnBY,GAAGnK,IAASmI,IAAOnC,IAASsC,IAAAA;AAC1BI,UAAW1I,IAASmI,IAAOnC,IAASsC,IAAAA,KAAoB;MAAA,GAG1D8B,IAAIpK,IAASmI,IAAOnC,IAASsC,IAAAA;AAC3BI,UAAW1I,IAASmI,IAAOnC,IAASsC,IAAAA,IAAoB;MAAA,GAG1DkB,IAAIxJ,IAASqI,IAAmBrC,IAASsC,IAAAA;AACvC,YAAiC,YAAA,OAAtBD,MAAAA,CAAmCrI,GAC5C;AAGF,cAAA,CAAOuI,IAAaT,IAAUU,EAAAA,IAAaJ,EAAoBC,IAAmBrC,IAASsC,EAAAA,GACrF+B,KAAc7B,OAAcH,IAC5BR,KAASF,EAAiB3H,EAAAA,GAC1B+J,KAAoBlC,GAAOW,EAAAA,KAAc,CAAA,GACzC8B,KAAcjC,GAAkBkC,WAAW,GAAA;AAEjD,YAAA,WAAWzC,IAAX;AAUA,cAAIwC,GACF,YAAWE,MAAgBxC,OAAOrH,KAAKkH,EAAAA,EACrCgC,GAAyB7J,IAAS6H,IAAQ2C,IAAcnC,GAAkBoC,MAAM,CAAA,CAAA;AAIpF,qBAAK,CAAOC,IAAavC,EAAAA,KAAUH,OAAOiC,QAAQF,EAAAA,GAAoB;AACpE,kBAAMC,KAAaU,GAAYtJ,QAAQ6F,GAAe,EAAA;AAEjDoD,YAAAA,MAAAA,CAAehC,GAAkB6B,SAASF,EAAAA,KAC7CL,EAAc3J,IAAS6H,IAAQW,IAAWL,GAAML,UAAUK,GAAMJ,kBAAAA;UAEpE;QAdA,OARA;AAEE,cAAA,CAAKC,OAAOrH,KAAKoJ,EAAAA,EAAmBhI,OAClC;AAGF4H,YAAc3J,IAAS6H,IAAQW,IAAWV,IAAUS,KAAcvC,KAAU,IAAA;QAE9E;MAAA,GAiBF2E,QAAQ3K,IAASmI,IAAOjD,IAAAA;AACtB,YAAqB,YAAA,OAAViD,MAAAA,CAAuBnI,GAChC,QAAO;AAGT,cAAMqE,KAAIT,EAAAA;AAIV,YAAIgH,KAAc,MACdC,KAAAA,MACAC,KAAAA,MACAC,KAAAA;AALgB5C,QAAAA,OADFM,EAAaN,EAAAA,KAQZ9D,OACjBuG,KAAcvG,GAAE5C,MAAM0G,IAAOjD,EAAAA,GAE7Bb,GAAErE,EAAAA,EAAS2K,QAAQC,EAAAA,GACnBC,KAAAA,CAAWD,GAAYI,qBAAAA,GACvBF,KAAAA,CAAkBF,GAAYK,8BAAAA,GAC9BF,KAAmBH,GAAYM,mBAAAA;AAGjC,cAAMC,KAAM7B,EAAW,IAAI7H,MAAM0G,IAAO,EAAE0C,SAAAA,IAASO,YAAAA,KAAY,CAAA,GAASlG,EAAAA;AAcxE,eAZI6F,MACFI,GAAIE,eAAAA,GAGFP,MACF9K,GAAQwB,cAAc2J,EAAAA,GAGpBA,GAAIJ,oBAAoBH,MAC1BA,GAAYS,eAAAA,GAGPF;MACT,EAAA;AAGF,eAAS7B,EAAWgC,IAAKC,KAAO,CAAA,GAAA;AAC9B,mBAAK,CAAOtL,IAAKuL,EAAAA,KAAUxD,OAAOiC,QAAQsB,EAAAA,EACxC,KAAA;AACED,UAAAA,GAAIrL,EAAAA,IAAOuL;QAAAA,SACXC,IAAAA;AACAzD,iBAAO0D,eAAeJ,IAAKrL,IAAK,EAC9B0L,cAAAA,MACAtL,KAAGA,MACMmL,GAAAA,CAAAA;QAGb;AAGF,eAAOF;MACT;ACnTA,eAASM,EAAcJ,IAAAA;AACrB,YAAc,WAAVA,GACF,QAAA;AAGF,YAAc,YAAVA,GACF,QAAA;AAGF,YAAIA,OAAU7F,OAAO6F,EAAAA,EAAOK,SAAAA,EAC1B,QAAOlG,OAAO6F,EAAAA;AAGhB,YAAc,OAAVA,MAA0B,WAAVA,GAClB,QAAO;AAGT,YAAqB,YAAA,OAAVA,GACT,QAAOA;AAGT,YAAA;AACE,iBAAOM,KAAKC,MAAMC,mBAAmBR,EAAAA,CAAAA;QAAAA,SACrCC,IAAAA;AACA,iBAAOD;QACT;MACF;AAEA,eAASS,EAAiBhM,IAAAA;AACxB,eAAOA,GAAImB,QAAQ,UAAU8K,CAAAA,OAAQ,IAAGA,GAAIC,YAAAA,CAAAA,EAAAA;MAC9C;AAEA,YAAMC,IAAc,EAClBC,iBAAiBrM,IAASC,IAAKuL,IAAAA;AAC7BxL,QAAAA,GAAQsM,aAAc,WAAUL,EAAiBhM,EAAAA,CAAAA,IAAQuL,EAAAA;MAAAA,GAG3De,oBAAoBvM,IAASC,IAAAA;AAC3BD,QAAAA,GAAQwM,gBAAiB,WAAUP,EAAiBhM,EAAAA,CAAAA,EAAAA;MAAAA,GAGtDwM,kBAAkBzM,IAAAA;AAChB,YAAA,CAAKA,GACH,QAAO,CAAA;AAGT,cAAM0M,KAAa,CAAA,GACbC,KAAS3E,OAAOrH,KAAKX,GAAQ4M,OAAAA,EAASC,OAAO5M,CAAAA,OAAOA,GAAIsK,WAAW,IAAA,KAAA,CAAUtK,GAAIsK,WAAW,UAAA,CAAA;AAElG,mBAAWtK,MAAO0M,IAAQ;AACxB,cAAIG,KAAU7M,GAAImB,QAAQ,OAAO,EAAA;AACjC0L,UAAAA,KAAUA,GAAQC,OAAO,CAAA,EAAGZ,YAAAA,IAAgBW,GAAQrC,MAAM,GAAGqC,GAAQ/K,MAAAA,GACrE2K,GAAWI,EAAAA,IAAWlB,EAAc5L,GAAQ4M,QAAQ3M,EAAAA,CAAAA;QACtD;AAEA,eAAOyM;MAAAA,GAGTM,kBAAgBA,CAAChN,IAASC,OACjB2L,EAAc5L,GAAQkD,aAAc,WAAU+I,EAAiBhM,EAAAA,CAAAA,EAAAA,CAAAA,EAAAA;MCpD1E,MAAMgN,EAAAA;QAEJ,WAAA,UAAWC;AACT,iBAAO,CAAA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAO,CAAA;QACT;QAEA,WAAA,OAAW5I;AACT,gBAAM,IAAI6I,MAAM,qEAAA;QAClB;QAEAC,WAAWC,IAAAA;AAIT,iBAHAA,KAAStE,KAAKuE,gBAAgBD,EAAAA,GAC9BA,KAAStE,KAAKwE,kBAAkBF,EAAAA,GAChCtE,KAAKyE,iBAAiBH,EAAAA,GACfA;QACT;QAEAE,kBAAkBF,IAAAA;AAChB,iBAAOA;QACT;QAEAC,gBAAgBD,IAAQtN,IAAAA;AACtB,gBAAM0N,KAAahM,EAAU1B,EAAAA,IAAWoM,EAAYY,iBAAiBhN,IAAS,QAAA,IAAY,CAAA;AAE1F,iBAAO,EAAA,GACFgJ,KAAK2E,YAAYT,SAAAA,GACM,YAAA,OAAfQ,KAA0BA,KAAa,CAAA,GAAA,GAC9ChM,EAAU1B,EAAAA,IAAWoM,EAAYK,kBAAkBzM,EAAAA,IAAW,CAAA,GAAA,GAC5C,YAAA,OAAXsN,KAAsBA,KAAS,CAAA,EAAA;QAE9C;QAEAG,iBAAiBH,IAAQM,KAAc5E,KAAK2E,YAAYR,aAAAA;AACtD,qBAAK,CAAOU,IAAUC,EAAAA,KAAkB9F,OAAOiC,QAAQ2D,EAAAA,GAAc;AACnE,kBAAMpC,KAAQ8B,GAAOO,EAAAA,GACfE,KAAYrM,EAAU8J,EAAAA,IAAS,YH1BrC7J,SADSA,KG2B+C6J,MHzBlD,GAAE7J,EAAAA,KAGLqG,OAAOgG,UAAUnC,SAAS9C,KAAKpH,EAAAA,EAAQN,MAAM,aAAA,EAAe,CAAA,EAAG8K,YAAAA;AGwBlE,gBAAA,CAAK,IAAI8B,OAAOH,EAAAA,EAAeI,KAAKH,EAAAA,EAClC,OAAM,IAAII,UACP,GAAEnF,KAAK2E,YAAYpJ,KAAK6J,YAAAA,CAAAA,aAA0BP,EAAAA,oBAA4BE,EAAAA,wBAAiCD,EAAAA,IAAAA;UAGtH;AHlCWnM,cAAAA;QGmCb;MAAA;MCvCF,MAAM0M,UAAsBpB,EAAAA;QAC1BU,YAAY3N,IAASsN,IAAAA;AACnBgB,gBAAAA,IAEAtO,KAAU8B,EAAW9B,EAAAA,OAKrBgJ,KAAKuF,WAAWvO,IAChBgJ,KAAKwF,UAAUxF,KAAKqE,WAAWC,EAAAA,GAE/BxN,EAAKC,IAAIiJ,KAAKuF,UAAUvF,KAAK2E,YAAYc,UAAUzF,IAAAA;QACrD;QAGA0F,UAAAA;AACE5O,YAAKc,OAAOoI,KAAKuF,UAAUvF,KAAK2E,YAAYc,QAAAA,GAC5ClF,EAAaC,IAAIR,KAAKuF,UAAUvF,KAAK2E,YAAYgB,SAAAA;AAEjD,qBAAWC,MAAgB5G,OAAO6G,oBAAoB7F,IAAAA,EACpDA,MAAK4F,EAAAA,IAAgB;QAEzB;QAEAE,eAAe1K,IAAUpE,IAAS+O,KAAAA,MAAa;AAC7C3J,YAAuBhB,IAAUpE,IAAS+O,EAAAA;QAC5C;QAEA1B,WAAWC,IAAAA;AAIT,iBAHAA,KAAStE,KAAKuE,gBAAgBD,IAAQtE,KAAKuF,QAAAA,GAC3CjB,KAAStE,KAAKwE,kBAAkBF,EAAAA,GAChCtE,KAAKyE,iBAAiBH,EAAAA,GACfA;QACT;QAGA,OAAA,YAAmBtN,IAAAA;AACjB,iBAAOF,EAAKO,IAAIyB,EAAW9B,EAAAA,GAAUgJ,KAAKyF,QAAAA;QAC5C;QAEA,OAAA,oBAA2BzO,IAASsN,KAAS,CAAA,GAAA;AAC3C,iBAAOtE,KAAKgG,YAAYhP,EAAAA,KAAY,IAAIgJ,KAAKhJ,IAA2B,YAAA,OAAXsN,KAAsBA,KAAS,IAAA;QAC9F;QAEA,WAAA,UAAW2B;AACT,iBApDY;QAqDd;QAEA,WAAA,WAAWR;AACT,iBAAQ,MAAKzF,KAAKzE,IAAAA;QACpB;QAEA,WAAA,YAAWoK;AACT,iBAAQ,IAAG3F,KAAKyF,QAAAA;QAClB;QAEA,OAAA,UAAiBnK,IAAAA;AACf,iBAAQ,GAAEA,EAAAA,GAAO0E,KAAK2F,SAAAA;QACxB;MAAA;ACxEF,YAAMO,IAAclP,CAAAA,OAAAA;AAClB,YAAIgB,KAAWhB,GAAQkD,aAAa,gBAAA;AAEpC,YAAA,CAAKlC,MAAyB,QAAbA,IAAkB;AACjC,cAAImO,KAAgBnP,GAAQkD,aAAa,MAAA;AAMzC,cAAA,CAAKiM,MAAAA,CAAmBA,GAAcjF,SAAS,GAAA,KAAA,CAASiF,GAAc5E,WAAW,GAAA,EAC/E,QAAO;AAIL4E,UAAAA,GAAcjF,SAAS,GAAA,KAAA,CAASiF,GAAc5E,WAAW,GAAA,MAC3D4E,KAAiB,IAAGA,GAAcrJ,MAAM,GAAA,EAAK,CAAA,CAAA,KAG/C9E,KAAWmO,MAAmC,QAAlBA,KAAwBA,GAAcC,KAAAA,IAAS;QAC7E;AAEA,eAAOpO,KAAWA,GAAS8E,MAAM,GAAA,EAAKuJ,IAAIC,CAAAA,OAAOvO,EAAcuO,EAAAA,CAAAA,EAAMC,KAAK,GAAA,IAAO;MAAI,GAGjFC,IAAiB,EACrBtH,MAAIA,CAAClH,IAAUhB,KAAUgC,SAASoB,oBACzB,CAAA,EAAGqM,OAAAA,GAAUC,QAAQ1B,UAAU5E,iBAAiBL,KAAK/I,IAASgB,EAAAA,CAAAA,GAGvE2O,SAAOA,CAAC3O,IAAUhB,KAAUgC,SAASoB,oBAC5BsM,QAAQ1B,UAAU/L,cAAc8G,KAAK/I,IAASgB,EAAAA,GAGvD4O,UAAQA,CAAC5P,IAASgB,OACT,CAAA,EAAGyO,OAAAA,GAAUzP,GAAQ4P,QAAAA,EAAU/C,OAAOgD,CAAAA,OAASA,GAAMC,QAAQ9O,EAAAA,CAAAA,GAGtE+O,QAAQ/P,IAASgB,IAAAA;AACf,cAAM+O,KAAU,CAAA;AAChB,YAAIC,KAAWhQ,GAAQ0C,WAAWF,QAAQxB,EAAAA;AAE1C,eAAOgP,KACLD,CAAAA,GAAQhL,KAAKiL,EAAAA,GACbA,KAAWA,GAAStN,WAAWF,QAAQxB,EAAAA;AAGzC,eAAO+O;MAAAA,GAGTE,KAAKjQ,IAASgB,IAAAA;AACZ,YAAIkP,KAAWlQ,GAAQmQ;AAEvB,eAAOD,MAAU;AACf,cAAIA,GAASJ,QAAQ9O,EAAAA,EACnB,QAAO,CAACkP,EAAAA;AAGVA,UAAAA,KAAWA,GAASC;QACtB;AAEA,eAAO,CAAA;MAAA,GAGTC,KAAKpQ,IAASgB,IAAAA;AACZ,YAAIoP,KAAOpQ,GAAQqQ;AAEnB,eAAOD,MAAM;AACX,cAAIA,GAAKN,QAAQ9O,EAAAA,EACf,QAAO,CAACoP,EAAAA;AAGVA,UAAAA,KAAOA,GAAKC;QACd;AAEA,eAAO,CAAA;MAAA,GAGTC,kBAAkBtQ,IAAAA;AAChB,cAAMuQ,KAAa,CACjB,KACA,UACA,SACA,YACA,UACA,WACA,cACA,0BAAA,EACAlB,IAAIrO,CAAAA,OAAa,GAAEA,EAAAA,uBAAAA,EAAiCuO,KAAK,GAAA;AAE3D,eAAOvG,KAAKd,KAAKqI,IAAYvQ,EAAAA,EAAS6M,OAAO2D,CAAAA,OAAAA,CAAO7N,EAAW6N,EAAAA,KAAOtO,EAAUsO,EAAAA,CAAAA;MAAAA,GAGlFC,uBAAuBzQ,IAAAA;AACrB,cAAMgB,KAAWkO,EAAYlP,EAAAA;AAE7B,eAAIgB,MACKwO,EAAeG,QAAQ3O,EAAAA,IAAYA,KAGrC;MAAA,GAGT0P,uBAAuB1Q,IAAAA;AACrB,cAAMgB,KAAWkO,EAAYlP,EAAAA;AAE7B,eAAOgB,KAAWwO,EAAeG,QAAQ3O,EAAAA,IAAY;MAAA,GAGvD2P,gCAAgC3Q,IAAAA;AAC9B,cAAMgB,KAAWkO,EAAYlP,EAAAA;AAE7B,eAAOgB,KAAWwO,EAAetH,KAAKlH,EAAAA,IAAY,CAAA;MACpD,EAAA,GC/GI4P,IAAuBA,CAACC,IAAWC,KAAS,WAAA;AAChD,cAAMC,KAAc,gBAAeF,GAAUlC,SAAAA,IACvCrK,KAAOuM,GAAUtM;AAEvBgF,UAAaY,GAAGnI,UAAU+O,IAAa,qBAAoBzM,EAAAA,MAAU,SAAU6D,IAAAA;AAK7E,cAJI,CAAC,KAAK,MAAA,EAAQ+B,SAASlB,KAAKgI,OAAAA,KAC9B7I,GAAMkD,eAAAA,GAGJ1I,EAAWqG,IAAAA,EACb;AAGF,gBAAM/C,KAASuJ,EAAekB,uBAAuB1H,IAAAA,KAASA,KAAKxG,QAAS,IAAG8B,EAAAA,EAAAA;AAC9DuM,UAAAA,GAAUI,oBAAoBhL,EAAAA,EAGtC6K,EAAAA,EAAAA;QACX,CAAA;MAAE,GCXEnC,IAAa,aAEbuC,IAAe,QAAOvC,CAAAA,IACtBwC,IAAgB,SAAQxC,CAAAA;MAQ9B,MAAMyC,UAAc/C,EAAAA;QAElB,WAAA,OAAW9J;AACT,iBAhBS;QAiBX;QAGA8M,QAAAA;AAGE,cAFmB9H,EAAaoB,QAAQ3B,KAAKuF,UAAU2C,CAAAA,EAExCnG,iBACb;AAGF/B,eAAKuF,SAASzL,UAAUlC,OApBJ,MAAA;AAsBpB,gBAAMmO,KAAa/F,KAAKuF,SAASzL,UAAUC,SAvBvB,MAAA;AAwBpBiG,eAAK8F,eAAe,MAAM9F,KAAKsI,gBAAAA,GAAmBtI,KAAKuF,UAAUQ,EAAAA;QACnE;QAGAuC,kBAAAA;AACEtI,eAAKuF,SAAS3N,OAAAA,GACd2I,EAAaoB,QAAQ3B,KAAKuF,UAAU4C,CAAAA,GACpCnI,KAAK0F,QAAAA;QACP;QAGA,OAAA,gBAAuBpB,IAAAA;AACrB,iBAAOtE,KAAKuI,KAAK,WAAA;AACf,kBAAMC,KAAOJ,EAAMH,oBAAoBjI,IAAAA;AAEvC,gBAAsB,YAAA,OAAXsE,IAAX;AAIA,kBAAA,WAAIkE,GAAKlE,EAAAA,KAAyBA,GAAO/C,WAAW,GAAA,KAAmB,kBAAX+C,GAC1D,OAAM,IAAIa,UAAW,oBAAmBb,EAAAA,GAAAA;AAG1CkE,cAAAA,GAAKlE,EAAAA,EAAQtE,IAAAA;YANb;UAOF,CAAA;QACF;MAAA;AAOF4H,QAAqBQ,GAAO,OAAA,GAM5BlN,EAAmBkN,CAAAA;ACrEnB,YAMMK,IAAuB;MAO7B,MAAMC,UAAerD,EAAAA;QAEnB,WAAA,OAAW9J;AACT,iBAhBS;QAiBX;QAGAoN,SAAAA;AAEE3I,eAAKuF,SAASjC,aAAa,gBAAgBtD,KAAKuF,SAASzL,UAAU6O,OAjB7C,QAAA,CAAA;QAkBxB;QAGA,OAAA,gBAAuBrE,IAAAA;AACrB,iBAAOtE,KAAKuI,KAAK,WAAA;AACf,kBAAMC,KAAOE,EAAOT,oBAAoBjI,IAAAA;AAEzB,yBAAXsE,MACFkE,GAAKlE,EAAAA,EAAAA;UAET,CAAA;QACF;MAAA;AAOF/D,QAAaY,GAAGnI,UAlCc,4BAkCkByP,GAAsBtJ,CAAAA,OAAAA;AACpEA,QAAAA,GAAMkD,eAAAA;AAEN,cAAMuG,KAASzJ,GAAMlC,OAAOzD,QAAQiP,CAAAA;AACvBC,UAAOT,oBAAoBW,EAAAA,EAEnCD,OAAAA;MAAQ,CAAA,GAOfzN,EAAmBwN,CAAAA;ACtDnB,YACM/C,IAAY,aACZkD,IAAoB,aAAYlD,CAAAA,IAChCmD,IAAmB,YAAWnD,CAAAA,IAC9BoD,IAAkB,WAAUpD,CAAAA,IAC5BqD,KAAqB,cAAarD,CAAAA,IAClCsD,KAAmB,YAAWtD,CAAAA,IAM9BzB,KAAU,EACdgF,aAAa,MACbC,cAAc,MACdC,eAAe,KAAA,GAGXjF,KAAc,EAClB+E,aAAa,mBACbC,cAAc,mBACdC,eAAe,kBAAA;MAOjB,MAAMC,WAAcpF,EAAAA;QAClBU,YAAY3N,IAASsN,IAAAA;AACnBgB,gBAAAA,GACAtF,KAAKuF,WAAWvO,IAEXA,MAAYqS,GAAMC,YAAAA,MAIvBtJ,KAAKwF,UAAUxF,KAAKqE,WAAWC,EAAAA,GAC/BtE,KAAKuJ,UAAU,GACfvJ,KAAKwJ,wBAAwB5I,QAAQ3I,OAAOwR,YAAAA,GAC5CzJ,KAAK0J,YAAAA;QACP;QAGA,WAAA,UAAWxF;AACT,iBAAOA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAOA;QACT;QAEA,WAAA,OAAW5I;AACT,iBArDS;QAsDX;QAGAmK,UAAAA;AACEnF,YAAaC,IAAIR,KAAKuF,UAAUI,CAAAA;QAClC;QAGAgE,OAAOxK,IAAAA;AACAa,eAAKwJ,wBAMNxJ,KAAK4J,wBAAwBzK,EAAAA,MAC/Ba,KAAKuJ,UAAUpK,GAAM0K,WANrB7J,KAAKuJ,UAAUpK,GAAM2K,QAAQ,CAAA,EAAGD;QAQpC;QAEAE,KAAK5K,IAAAA;AACCa,eAAK4J,wBAAwBzK,EAAAA,MAC/Ba,KAAKuJ,UAAUpK,GAAM0K,UAAU7J,KAAKuJ,UAGtCvJ,KAAKgK,aAAAA,GACLhO,EAAQgE,KAAKwF,QAAQ0D,WAAAA;QACvB;QAEAe,MAAM9K,IAAAA;AACJa,eAAKuJ,UAAUpK,GAAM2K,WAAW3K,GAAM2K,QAAQ/Q,SAAS,IACrD,IACAoG,GAAM2K,QAAQ,CAAA,EAAGD,UAAU7J,KAAKuJ;QACpC;QAEAS,eAAAA;AACE,gBAAME,KAAYtM,KAAKuM,IAAInK,KAAKuJ,OAAAA;AAEhC,cAAIW,MAlFgB,GAmFlB;AAGF,gBAAME,KAAYF,KAAYlK,KAAKuJ;AAEnCvJ,eAAKuJ,UAAU,GAEVa,MAILpO,EAAQoO,KAAY,IAAIpK,KAAKwF,QAAQ4D,gBAAgBpJ,KAAKwF,QAAQ2D,YAAAA;QACpE;QAEAO,cAAAA;AACM1J,eAAKwJ,yBACPjJ,EAAaY,GAAGnB,KAAKuF,UAAUyD,IAAmB7J,CAAAA,OAASa,KAAK2J,OAAOxK,EAAAA,CAAAA,GACvEoB,EAAaY,GAAGnB,KAAKuF,UAAU0D,IAAiB9J,CAAAA,OAASa,KAAK+J,KAAK5K,EAAAA,CAAAA,GAEnEa,KAAKuF,SAASzL,UAAUuQ,IAvGG,eAAA,MAyG3B9J,EAAaY,GAAGnB,KAAKuF,UAAUsD,GAAkB1J,CAAAA,OAASa,KAAK2J,OAAOxK,EAAAA,CAAAA,GACtEoB,EAAaY,GAAGnB,KAAKuF,UAAUuD,GAAiB3J,CAAAA,OAASa,KAAKiK,MAAM9K,EAAAA,CAAAA,GACpEoB,EAAaY,GAAGnB,KAAKuF,UAAUwD,GAAgB5J,CAAAA,OAASa,KAAK+J,KAAK5K,EAAAA,CAAAA;QAEtE;QAEAyK,wBAAwBzK,IAAAA;AACtB,iBAAOa,KAAKwJ,0BAjHS,UAiHiBrK,GAAMmL,eAlHrB,YAkHyDnL,GAAMmL;QACxF;QAGA,OAAA,cAAOhB;AACL,iBAAO,kBAAkBtQ,SAASoB,mBAAmBmQ,UAAUC,iBAAiB;QAClF;MAAA;ACrHF,YAEM7E,KAAa,gBACb8E,KAAe,aAMfC,KAAa,QACbC,KAAa,QACbC,KAAiB,QACjBC,KAAkB,SAElBC,KAAe,QAAOnF,EAAAA,IACtBoF,KAAc,OAAMpF,EAAAA,IACpBqF,KAAiB,UAASrF,EAAAA,IAC1BsF,KAAoB,aAAYtF,EAAAA,IAChCuF,KAAoB,aAAYvF,EAAAA,IAChCwF,KAAoB,YAAWxF,EAAAA,IAC/ByF,KAAuB,OAAMzF,EAAAA,GAAY8E,EAAAA,IACzCY,KAAwB,QAAO1F,EAAAA,GAAY8E,EAAAA,IAE3Ca,KAAsB,YACtBC,KAAoB,UAOpBC,KAAkB,WAClBC,KAAgB,kBAChBC,KAAuBF,KAAkBC,IAMzCE,KAAmB,EACvBC,WAAkBf,IAClBgB,YAAmBjB,GAAAA,GAGf1G,KAAU,EACd4H,UAAU,KACVC,UAAAA,MACAC,OAAO,SACPC,MAAAA,OACAC,OAAAA,MACAC,MAAAA,KAAM,GAGFhI,KAAc,EAClB2H,UAAU,oBACVC,UAAU,WACVC,OAAO,oBACPC,MAAM,oBACNC,OAAO,WACPC,MAAM,UAAA;MAOR,MAAMC,WAAiB/G,EAAAA;QACrBV,YAAY3N,IAASsN,IAAAA;AACnBgB,gBAAMtO,IAASsN,EAAAA,GAEftE,KAAKqM,YAAY,MACjBrM,KAAKsM,iBAAiB,MACtBtM,KAAKuM,aAAAA,OACLvM,KAAKwM,eAAe,MACpBxM,KAAKyM,eAAe,MAEpBzM,KAAK0M,qBAAqBlG,EAAeG,QAzCjB,wBAyC8C3G,KAAKuF,QAAAA,GAC3EvF,KAAK2M,mBAAAA,GAED3M,KAAKwF,QAAQyG,SAASX,MACxBtL,KAAK4M,MAAAA;QAET;QAGA,WAAA,UAAW1I;AACT,iBAAOA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAOA;QACT;QAEA,WAAA,OAAW5I;AACT,iBA9FS;QA+FX;QAGA6L,OAAAA;AACEpH,eAAK6M,OAAOnC,EAAAA;QACd;QAEAoC,kBAAAA;AAAAA,WAIO9T,SAAS+T,UAAU7T,EAAU8G,KAAKuF,QAAAA,KACrCvF,KAAKoH,KAAAA;QAET;QAEAH,OAAAA;AACEjH,eAAK6M,OAAOlC,EAAAA;QACd;QAEAqB,QAAAA;AACMhM,eAAKuM,cACPhU,EAAqByH,KAAKuF,QAAAA,GAG5BvF,KAAKgN,eAAAA;QACP;QAEAJ,QAAAA;AACE5M,eAAKgN,eAAAA,GACLhN,KAAKiN,gBAAAA,GAELjN,KAAKqM,YAAYa,YAAY,MAAMlN,KAAK8M,gBAAAA,GAAmB9M,KAAKwF,QAAQsG,QAAAA;QAC1E;QAEAqB,oBAAAA;AACOnN,eAAKwF,QAAQyG,SAIdjM,KAAKuM,aACPhM,EAAaa,IAAIpB,KAAKuF,UAAUwF,IAAY,MAAM/K,KAAK4M,MAAAA,CAAAA,IAIzD5M,KAAK4M,MAAAA;QACP;QAEAQ,GAAG1P,IAAAA;AACD,gBAAM2P,KAAQrN,KAAKsN,UAAAA;AACnB,cAAI5P,KAAQ2P,GAAMtU,SAAS,KAAK2E,KAAQ,EACtC;AAGF,cAAIsC,KAAKuM,WAEP,QAAA,KADAhM,EAAaa,IAAIpB,KAAKuF,UAAUwF,IAAY,MAAM/K,KAAKoN,GAAG1P,EAAAA,CAAAA;AAI5D,gBAAM6P,KAAcvN,KAAKwN,cAAcxN,KAAKyN,WAAAA,CAAAA;AAC5C,cAAIF,OAAgB7P,GAClB;AAGF,gBAAMgQ,KAAQhQ,KAAQ6P,KAAc7C,KAAaC;AAEjD3K,eAAK6M,OAAOa,IAAOL,GAAM3P,EAAAA,CAAAA;QAC3B;QAEAgI,UAAAA;AACM1F,eAAKyM,gBACPzM,KAAKyM,aAAa/G,QAAAA,GAGpBJ,MAAMI,QAAAA;QACR;QAGAlB,kBAAkBF,IAAAA;AAEhB,iBADAA,GAAOqJ,kBAAkBrJ,GAAOwH,UACzBxH;QACT;QAEAqI,qBAAAA;AACM3M,eAAKwF,QAAQuG,YACfxL,EAAaY,GAAGnB,KAAKuF,UAAUyF,IAAe7L,CAAAA,OAASa,KAAK4N,SAASzO,EAAAA,CAAAA,GAG5C,YAAvBa,KAAKwF,QAAQwG,UACfzL,EAAaY,GAAGnB,KAAKuF,UAAU0F,IAAkB,MAAMjL,KAAKgM,MAAAA,CAAAA,GAC5DzL,EAAaY,GAAGnB,KAAKuF,UAAU2F,IAAkB,MAAMlL,KAAKmN,kBAAAA,CAAAA,IAG1DnN,KAAKwF,QAAQ0G,SAAS7C,GAAMC,YAAAA,KAC9BtJ,KAAK6N,wBAAAA;QAET;QAEAA,0BAAAA;AACE,qBAAWC,MAAOtH,EAAetH,KAhKX,sBAgKmCc,KAAKuF,QAAAA,EAC5DhF,GAAaY,GAAG2M,IAAK3C,IAAkBhM,CAAAA,OAASA,GAAMkD,eAAAA,CAAAA;AAGxD,gBAqBM0L,KAAc,EAClB5E,cAAcA,MAAMnJ,KAAK6M,OAAO7M,KAAKgO,kBAAkBpD,EAAAA,CAAAA,GACvDxB,eAAeA,MAAMpJ,KAAK6M,OAAO7M,KAAKgO,kBAAkBnD,EAAAA,CAAAA,GACxD3B,aAxBkB+E,MAAAA;AACS,wBAAvBjO,KAAKwF,QAAQwG,UAYjBhM,KAAKgM,MAAAA,GACDhM,KAAKwM,gBACP0B,aAAalO,KAAKwM,YAAAA,GAGpBxM,KAAKwM,eAAerP,WAAW,MAAM6C,KAAKmN,kBAAAA,GAjNjB,MAiN+DnN,KAAKwF,QAAQsG,QAAAA;UAAS,EAAA;AAShH9L,eAAKyM,eAAe,IAAIpD,GAAMrJ,KAAKuF,UAAUwI,EAAAA;QAC/C;QAEAH,SAASzO,IAAAA;AACP,cAAI,kBAAkB+F,KAAK/F,GAAMlC,OAAO+K,OAAAA,EACtC;AAGF,gBAAMoC,KAAYuB,GAAiBxM,GAAMlI,GAAAA;AACrCmT,UAAAA,OACFjL,GAAMkD,eAAAA,GACNrC,KAAK6M,OAAO7M,KAAKgO,kBAAkB5D,EAAAA,CAAAA;QAEvC;QAEAoD,cAAcxW,IAAAA;AACZ,iBAAOgJ,KAAKsN,UAAAA,EAAY3P,QAAQ3G,EAAAA;QAClC;QAEAmX,2BAA2BzQ,IAAAA;AACzB,cAAA,CAAKsC,KAAK0M,mBACR;AAGF,gBAAM0B,KAAkB5H,EAAeG,QAAQ6E,IAAiBxL,KAAK0M,kBAAAA;AAErE0B,UAAAA,GAAgBtU,UAAUlC,OAAO2T,EAAAA,GACjC6C,GAAgB5K,gBAAgB,cAAA;AAEhC,gBAAM6K,KAAqB7H,EAAeG,QAAS,sBAAqBjJ,EAAAA,MAAWsC,KAAK0M,kBAAAA;AAEpF2B,UAAAA,OACFA,GAAmBvU,UAAUuQ,IAAIkB,EAAAA,GACjC8C,GAAmB/K,aAAa,gBAAgB,MAAA;QAEpD;QAEA2J,kBAAAA;AACE,gBAAMjW,KAAUgJ,KAAKsM,kBAAkBtM,KAAKyN,WAAAA;AAE5C,cAAA,CAAKzW,GACH;AAGF,gBAAMsX,KAAkB3R,OAAO4R,SAASvX,GAAQkD,aAAa,kBAAA,GAAqB,EAAA;AAElF8F,eAAKwF,QAAQsG,WAAWwC,MAAmBtO,KAAKwF,QAAQmI;QAC1D;QAEAd,OAAOa,IAAO1W,KAAU,MAAA;AACtB,cAAIgJ,KAAKuM,WACP;AAGF,gBAAMjP,KAAgB0C,KAAKyN,WAAAA,GACrBe,KAASd,OAAUhD,IACnB+D,KAAczX,MAAWoG,EAAqB4C,KAAKsN,UAAAA,GAAahQ,IAAekR,IAAQxO,KAAKwF,QAAQ2G,IAAAA;AAE1G,cAAIsC,OAAgBnR,GAClB;AAGF,gBAAMoR,KAAmB1O,KAAKwN,cAAciB,EAAAA,GAEtCE,KAAeC,CAAAA,OACZrO,EAAaoB,QAAQ3B,KAAKuF,UAAUqJ,IAAW,EACpD/O,eAAe4O,IACfrE,WAAWpK,KAAK6O,kBAAkBnB,EAAAA,GAClChW,MAAMsI,KAAKwN,cAAclQ,EAAAA,GACzB8P,IAAIsB,GAAAA,CAAAA;AAMR,cAFmBC,GAAa7D,EAAAA,EAEjB/I,iBACb;AAGF,cAAA,CAAKzE,MAAAA,CAAkBmR,GAGrB;AAGF,gBAAMK,KAAYlO,QAAQZ,KAAKqM,SAAAA;AAC/BrM,eAAKgM,MAAAA,GAELhM,KAAKuM,aAAAA,MAELvM,KAAKmO,2BAA2BO,EAAAA,GAChC1O,KAAKsM,iBAAiBmC;AAEtB,gBAAMM,KAAuBP,KAnSR,wBADF,qBAqSbQ,KAAiBR,KAnSH,uBACA;AAoSpBC,UAAAA,GAAY3U,UAAUuQ,IAAI2E,EAAAA,GAE1BtU,EAAO+T,EAAAA,GAEPnR,GAAcxD,UAAUuQ,IAAI0E,EAAAA,GAC5BN,GAAY3U,UAAUuQ,IAAI0E,EAAAA,GAa1B/O,KAAK8F,eAXoBmJ,MAAAA;AACvBR,YAAAA,GAAY3U,UAAUlC,OAAOmX,IAAsBC,EAAAA,GACnDP,GAAY3U,UAAUuQ,IAAIkB,EAAAA,GAE1BjO,GAAcxD,UAAUlC,OAAO2T,IAAmByD,IAAgBD,EAAAA,GAElE/O,KAAKuM,aAAAA,OAELoC,GAAa5D,EAAAA;UAAW,GAGYzN,IAAe0C,KAAKkP,YAAAA,CAAAA,GAEtDJ,MACF9O,KAAK4M,MAAAA;QAET;QAEAsC,cAAAA;AACE,iBAAOlP,KAAKuF,SAASzL,UAAUC,SAlUV,OAAA;QAmUvB;QAEA0T,aAAAA;AACE,iBAAOjH,EAAeG,QAAQ+E,IAAsB1L,KAAKuF,QAAAA;QAC3D;QAEA+H,YAAAA;AACE,iBAAO9G,EAAetH,KAAKuM,IAAezL,KAAKuF,QAAAA;QACjD;QAEAyH,iBAAAA;AACMhN,eAAKqM,cACP8C,cAAcnP,KAAKqM,SAAAA,GACnBrM,KAAKqM,YAAY;QAErB;QAEA2B,kBAAkB5D,IAAAA;AAChB,iBAAIpP,EAAAA,IACKoP,OAAcQ,KAAiBD,KAAaD,KAG9CN,OAAcQ,KAAiBF,KAAaC;QACrD;QAEAkE,kBAAkBnB,IAAAA;AAChB,iBAAI1S,EAAAA,IACK0S,OAAU/C,KAAaC,KAAiBC,KAG1C6C,OAAU/C,KAAaE,KAAkBD;QAClD;QAGA,OAAA,gBAAuBtG,IAAAA;AACrB,iBAAOtE,KAAKuI,KAAK,WAAA;AACf,kBAAMC,KAAO4D,GAASnE,oBAAoBjI,MAAMsE,EAAAA;AAEhD,gBAAsB,YAAA,OAAXA,IAAAA;AAKX,kBAAsB,YAAA,OAAXA,IAAqB;AAC9B,oBAAA,WAAIkE,GAAKlE,EAAAA,KAAyBA,GAAO/C,WAAW,GAAA,KAAmB,kBAAX+C,GAC1D,OAAM,IAAIa,UAAW,oBAAmBb,EAAAA,GAAAA;AAG1CkE,gBAAAA,GAAKlE,EAAAA,EAAAA;cACP;YAAA,MAVEkE,CAAAA,GAAK4E,GAAG9I,EAAAA;UAWZ,CAAA;QACF;MAAA;AAOF/D,QAAaY,GAAGnI,UAAUqS,IAlXE,uCAkXyC,SAAUlM,IAAAA;AAC7E,cAAMlC,KAASuJ,EAAekB,uBAAuB1H,IAAAA;AAErD,YAAA,CAAK/C,MAAAA,CAAWA,GAAOnD,UAAUC,SAASuR,EAAAA,EACxC;AAGFnM,QAAAA,GAAMkD,eAAAA;AAEN,cAAM+M,KAAWhD,GAASnE,oBAAoBhL,EAAAA,GACxCoS,KAAarP,KAAK9F,aAAa,kBAAA;AAErC,eAAImV,MACFD,GAAShC,GAAGiC,EAAAA,GAAAA,KACZD,GAASjC,kBAAAA,KAIyC,WAAhD/J,EAAYY,iBAAiBhE,MAAM,OAAA,KACrCoP,GAAShI,KAAAA,GAAAA,KACTgI,GAASjC,kBAAAA,MAIXiC,GAASnI,KAAAA,GAAAA,KACTmI,GAASjC,kBAAAA;MACX,CAAA,GAEA5M,EAAaY,GAAGlJ,QAAQmT,IAAqB,MAAA;AAC3C,cAAMkE,KAAY9I,EAAetH,KA9YR,2BAAA;AAgZzB,mBAAWkQ,MAAYE,GACrBlD,IAASnE,oBAAoBmH,EAAAA;MAC/B,CAAA,GAOFlU,EAAmBkR,EAAAA;ACncnB,YAEMzG,KAAa,gBAGb4J,KAAc,OAAM5J,EAAAA,IACpB6J,KAAe,QAAO7J,EAAAA,IACtB8J,KAAc,OAAM9J,EAAAA,IACpB+J,KAAgB,SAAQ/J,EAAAA,IACxB0F,KAAwB,QAAO1F,EAAAA,aAE/BgK,KAAkB,QAClBC,KAAsB,YACtBC,KAAwB,cAExBC,KAA8B,WAAUF,EAAAA,KAAwBA,EAAAA,IAOhEnH,KAAuB,+BAEvBvE,KAAU,EACd6L,QAAQ,MACRpH,QAAAA,KAAQ,GAGJxE,KAAc,EAClB4L,QAAQ,kBACRpH,QAAQ,UAAA;MAOV,MAAMqH,WAAiB3K,EAAAA;QACrBV,YAAY3N,IAASsN,IAAAA;AACnBgB,gBAAMtO,IAASsN,EAAAA,GAEftE,KAAKiQ,mBAAAA,OACLjQ,KAAKkQ,gBAAgB,CAAA;AAErB,gBAAMC,KAAa3J,EAAetH,KAAKuJ,EAAAA;AAEvC,qBAAW2H,MAAQD,IAAY;AAC7B,kBAAMnY,KAAWwO,EAAeiB,uBAAuB2I,EAAAA,GACjDC,KAAgB7J,EAAetH,KAAKlH,EAAAA,EACvC6L,OAAOyM,CAAAA,OAAgBA,OAAiBtQ,KAAKuF,QAAAA;AAE/B,qBAAbvN,MAAqBqY,GAActX,UACrCiH,KAAKkQ,cAAcnU,KAAKqU,EAAAA;UAE5B;AAEApQ,eAAKuQ,oBAAAA,GAEAvQ,KAAKwF,QAAQuK,UAChB/P,KAAKwQ,0BAA0BxQ,KAAKkQ,eAAelQ,KAAKyQ,SAAAA,CAAAA,GAGtDzQ,KAAKwF,QAAQmD,UACf3I,KAAK2I,OAAAA;QAET;QAGA,WAAA,UAAWzE;AACT,iBAAOA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAOA;QACT;QAEA,WAAA,OAAW5I;AACT,iBA9ES;QA+EX;QAGAoN,SAAAA;AACM3I,eAAKyQ,SAAAA,IACPzQ,KAAK0Q,KAAAA,IAEL1Q,KAAK2Q,KAAAA;QAET;QAEAA,OAAAA;AACE,cAAI3Q,KAAKiQ,oBAAoBjQ,KAAKyQ,SAAAA,EAChC;AAGF,cAAIG,KAAiB,CAAA;AASrB,cANI5Q,KAAKwF,QAAQuK,WACfa,KAAiB5Q,KAAK6Q,uBA9EH,sCAAA,EA+EhBhN,OAAO7M,CAAAA,OAAWA,OAAYgJ,KAAKuF,QAAAA,EACnCc,IAAIrP,CAAAA,OAAWgZ,GAAS/H,oBAAoBjR,IAAS,EAAE2R,QAAAA,MAAQ,CAAA,CAAA,IAGhEiI,GAAe7X,UAAU6X,GAAe,CAAA,EAAGX,iBAC7C;AAIF,cADmB1P,EAAaoB,QAAQ3B,KAAKuF,UAAUgK,EAAAA,EACxCxN,iBACb;AAGF,qBAAW+O,MAAkBF,GAC3BE,CAAAA,GAAeJ,KAAAA;AAGjB,gBAAMK,KAAY/Q,KAAKgR,cAAAA;AAEvBhR,eAAKuF,SAASzL,UAAUlC,OAAOgY,EAAAA,GAC/B5P,KAAKuF,SAASzL,UAAUuQ,IAAIwF,EAAAA,GAE5B7P,KAAKuF,SAAS0L,MAAMF,EAAAA,IAAa,GAEjC/Q,KAAKwQ,0BAA0BxQ,KAAKkQ,eAAAA,IAAe,GACnDlQ,KAAKiQ,mBAAAA;AAEL,gBAYMiB,KAAc,SADSH,GAAU,CAAA,EAAG3L,YAAAA,IAAgB2L,GAAUtP,MAAM,CAAA,CAAA;AAG1EzB,eAAK8F,eAdYqL,MAAAA;AACfnR,iBAAKiQ,mBAAAA,OAELjQ,KAAKuF,SAASzL,UAAUlC,OAAOiY,EAAAA,GAC/B7P,KAAKuF,SAASzL,UAAUuQ,IAAIuF,IAAqBD,EAAAA,GAEjD3P,KAAKuF,SAAS0L,MAAMF,EAAAA,IAAa,IAEjCxQ,EAAaoB,QAAQ3B,KAAKuF,UAAUiK,EAAAA;UAAY,GAMpBxP,KAAKuF,UAAAA,IAAU,GAC7CvF,KAAKuF,SAAS0L,MAAMF,EAAAA,IAAc,GAAE/Q,KAAKuF,SAAS2L,EAAAA,CAAAA;QACpD;QAEAR,OAAAA;AACE,cAAI1Q,KAAKiQ,oBAAAA,CAAqBjQ,KAAKyQ,SAAAA,EACjC;AAIF,cADmBlQ,EAAaoB,QAAQ3B,KAAKuF,UAAUkK,EAAAA,EACxC1N,iBACb;AAGF,gBAAMgP,KAAY/Q,KAAKgR,cAAAA;AAEvBhR,eAAKuF,SAAS0L,MAAMF,EAAAA,IAAc,GAAE/Q,KAAKuF,SAAS6L,sBAAAA,EAAwBL,EAAAA,CAAAA,MAE1ErW,EAAOsF,KAAKuF,QAAAA,GAEZvF,KAAKuF,SAASzL,UAAUuQ,IAAIwF,EAAAA,GAC5B7P,KAAKuF,SAASzL,UAAUlC,OAAOgY,IAAqBD,EAAAA;AAEpD,qBAAWhO,MAAW3B,KAAKkQ,eAAe;AACxC,kBAAMlZ,KAAUwP,EAAekB,uBAAuB/F,EAAAA;AAElD3K,YAAAA,MAAAA,CAAYgJ,KAAKyQ,SAASzZ,EAAAA,KAC5BgJ,KAAKwQ,0BAA0B,CAAC7O,EAAAA,GAAAA,KAAU;UAE9C;AAEA3B,eAAKiQ,mBAAAA,MASLjQ,KAAKuF,SAAS0L,MAAMF,EAAAA,IAAa,IAEjC/Q,KAAK8F,eATYqL,MAAAA;AACfnR,iBAAKiQ,mBAAAA,OACLjQ,KAAKuF,SAASzL,UAAUlC,OAAOiY,EAAAA,GAC/B7P,KAAKuF,SAASzL,UAAUuQ,IAAIuF,EAAAA,GAC5BrP,EAAaoB,QAAQ3B,KAAKuF,UAAUmK,EAAAA;UAAa,GAKrB1P,KAAKuF,UAAAA,IAAU;QAC/C;QAEAkL,SAASzZ,KAAUgJ,KAAKuF,UAAAA;AACtB,iBAAOvO,GAAQ8C,UAAUC,SAAS4V,EAAAA;QACpC;QAGAnL,kBAAkBF,IAAAA;AAGhB,iBAFAA,GAAOqE,SAAS/H,QAAQ0D,GAAOqE,MAAAA,GAC/BrE,GAAOyL,SAASjX,EAAWwL,GAAOyL,MAAAA,GAC3BzL;QACT;QAEA0M,gBAAAA;AACE,iBAAOhR,KAAKuF,SAASzL,UAAUC,SAtLL,qBAAA,IAEhB,UACC;QAoLb;QAEAwW,sBAAAA;AACE,cAAA,CAAKvQ,KAAKwF,QAAQuK,OAChB;AAGF,gBAAMnJ,KAAW5G,KAAK6Q,uBAAuBpI,EAAAA;AAE7C,qBAAWzR,MAAW4P,IAAU;AAC9B,kBAAMyK,KAAW7K,EAAekB,uBAAuB1Q,EAAAA;AAEnDqa,YAAAA,MACFrR,KAAKwQ,0BAA0B,CAACxZ,EAAAA,GAAUgJ,KAAKyQ,SAASY,EAAAA,CAAAA;UAE5D;QACF;QAEAR,uBAAuB7Y,IAAAA;AACrB,gBAAM4O,KAAWJ,EAAetH,KAAK4Q,IAA4B9P,KAAKwF,QAAQuK,MAAAA;AAE9E,iBAAOvJ,EAAetH,KAAKlH,IAAUgI,KAAKwF,QAAQuK,MAAAA,EAAQlM,OAAO7M,CAAAA,OAAAA,CAAY4P,GAAS1F,SAASlK,EAAAA,CAAAA;QACjG;QAEAwZ,0BAA0Bc,IAAcC,IAAAA;AACtC,cAAKD,GAAavY,OAIlB,YAAW/B,MAAWsa,GACpBta,CAAAA,GAAQ8C,UAAU6O,OAvNK,aAAA,CAuNyB4I,EAAAA,GAChDva,GAAQsM,aAAa,iBAAiBiO,EAAAA;QAE1C;QAGA,OAAA,gBAAuBjN,IAAAA;AACrB,gBAAMkB,KAAU,CAAA;AAKhB,iBAJsB,YAAA,OAAXlB,MAAuB,YAAYY,KAAKZ,EAAAA,MACjDkB,GAAQmD,SAAAA,QAGH3I,KAAKuI,KAAK,WAAA;AACf,kBAAMC,KAAOwH,GAAS/H,oBAAoBjI,MAAMwF,EAAAA;AAEhD,gBAAsB,YAAA,OAAXlB,IAAqB;AAC9B,kBAAA,WAAWkE,GAAKlE,EAAAA,EACd,OAAM,IAAIa,UAAW,oBAAmBb,EAAAA,GAAAA;AAG1CkE,cAAAA,GAAKlE,EAAAA,EAAAA;YACP;UACF,CAAA;QACF;MAAA;AAOF/D,QAAaY,GAAGnI,UAAUqS,IAAsB5C,IAAsB,SAAUtJ,IAAAA;AAAAA,SAEjD,QAAzBA,GAAMlC,OAAO+K,WAAoB7I,GAAMW,kBAAmD,QAAjCX,GAAMW,eAAekI,YAChF7I,GAAMkD,eAAAA;AAGR,mBAAWrL,MAAWwP,EAAemB,gCAAgC3H,IAAAA,EACnEgQ,IAAS/H,oBAAoBjR,IAAS,EAAE2R,QAAAA,MAAQ,CAAA,EAASA,OAAAA;MAE7D,CAAA,GAMAzN,EAAmB8U,EAAAA;ACtSZ,UAAIwB,KAAM,OACNC,KAAS,UACTC,KAAQ,SACRC,KAAO,QACPC,KAAO,QACPC,KAAiB,CAACL,IAAKC,IAAQC,IAAOC,EAAAA,GACtCG,KAAQ,SACRC,KAAM,OACNC,KAAkB,mBAClBC,KAAW,YACXC,KAAS,UACTC,KAAY,aACZC,KAAmCP,GAAeQ,OAAO,SAAUC,IAAKC,IAAAA;AACjF,eAAOD,GAAI7L,OAAO,CAAC8L,KAAY,MAAMT,IAAOS,KAAY,MAAMR,EAAAA,CAAAA;MAChE,GAAG,CAAA,CAAA,GACQS,KAA0B,CAAA,EAAG/L,OAAOoL,IAAgB,CAACD,EAAAA,CAAAA,EAAOS,OAAO,SAAUC,IAAKC,IAAAA;AAC3F,eAAOD,GAAI7L,OAAO,CAAC8L,IAAWA,KAAY,MAAMT,IAAOS,KAAY,MAAMR,EAAAA,CAAAA;MAC3E,GAAG,CAAA,CAAA,GAEQU,KAAa,cACbC,KAAO,QACPC,KAAY,aAEZC,KAAa,cACbC,KAAO,QACPC,KAAY,aAEZC,KAAc,eACdC,KAAQ,SACRC,KAAa,cACbC,KAAiB,CAACT,IAAYC,IAAMC,IAAWC,IAAYC,IAAMC,IAAWC,IAAaC,IAAOC,EAAAA;AC9B5F,eAASE,GAAYnc,IAAAA;AAClC,eAAOA,MAAWA,GAAQoc,YAAY,IAAIjQ,YAAAA,IAAgB;MAC5D;ACFe,eAASkQ,GAAUC,IAAAA;AAChC,YAAY,QAARA,GACF,QAAOrb;AAGT,YAAwB,sBAApBqb,GAAKzQ,SAAAA,GAAkC;AACzC,cAAI0Q,KAAgBD,GAAKC;AACzB,iBAAOA,MAAgBA,GAAcC,eAAwBvb;QACjE;AAEE,eAAOqb;MACT;ACTA,eAAS5a,GAAU4a,IAAAA;AAEjB,eAAOA,cADUD,GAAUC,EAAAA,EAAM5M,WACI4M,cAAgB5M;MACvD;AAEA,eAAS+M,GAAcH,IAAAA;AAErB,eAAOA,cADUD,GAAUC,EAAAA,EAAMI,eACIJ,cAAgBI;MACvD;AAEA,eAASC,GAAaL,IAAAA;AAEpB,eAA0B,eAAA,OAAf9Y,eAKJ8Y,cADUD,GAAUC,EAAAA,EAAM9Y,cACI8Y,cAAgB9Y;MACvD;ACwDA,YAAAoZ,KAAe,EACbtY,MAAM,eACNuY,SAAAA,MACAC,OAAO,SACPrY,IA5EF,SAAqBsY,IAAAA;AACnB,YAAIC,KAAQD,GAAKC;AACjBhV,eAAOrH,KAAKqc,GAAMC,QAAAA,EAAUC,QAAQ,SAAU5Y,IAAAA;AAC5C,cAAI2V,KAAQ+C,GAAMG,OAAO7Y,EAAAA,KAAS,CAAA,GAC9BoI,KAAasQ,GAAMtQ,WAAWpI,EAAAA,KAAS,CAAA,GACvCtE,KAAUgd,GAAMC,SAAS3Y,EAAAA;AAExBmY,aAAczc,EAAAA,KAAamc,GAAYnc,EAAAA,MAO5CgI,OAAOoV,OAAOpd,GAAQia,OAAOA,EAAAA,GAC7BjS,OAAOrH,KAAK+L,EAAAA,EAAYwQ,QAAQ,SAAU5Y,IAAAA;AACxC,gBAAIkH,KAAQkB,GAAWpI,EAAAA;AAAAA,sBAEnBkH,KACFxL,GAAQwM,gBAAgBlI,EAAAA,IAExBtE,GAAQsM,aAAahI,IAAAA,SAAMkH,KAAiB,KAAKA,EAAAA;UAEzD,CAAA;QACA,CAAA;MACA,GAoDE6R,QAlDF,SAAgBC,IAAAA;AACd,YAAIN,KAAQM,GAAMN,OACdO,KAAgB,EAClBrC,QAAQ,EACNsC,UAAUR,GAAMS,QAAQC,UACxB/C,MAAM,KACNH,KAAK,KACLmD,QAAQ,IAAA,GAEVC,OAAO,EACLJ,UAAU,WAAA,GAEZrC,WAAW,CAAA,EAAA;AASb,eAPAnT,OAAOoV,OAAOJ,GAAMC,SAAS/B,OAAOjB,OAAOsD,GAAcrC,MAAAA,GACzD8B,GAAMG,SAASI,IAEXP,GAAMC,SAASW,SACjB5V,OAAOoV,OAAOJ,GAAMC,SAASW,MAAM3D,OAAOsD,GAAcK,KAAAA,GAGnD,WAAA;AACL5V,iBAAOrH,KAAKqc,GAAMC,QAAAA,EAAUC,QAAQ,SAAU5Y,IAAAA;AAC5C,gBAAItE,KAAUgd,GAAMC,SAAS3Y,EAAAA,GACzBoI,KAAasQ,GAAMtQ,WAAWpI,EAAAA,KAAS,CAAA,GAGvC2V,KAFkBjS,OAAOrH,KAAKqc,GAAMG,OAAOU,eAAevZ,EAAAA,IAAQ0Y,GAAMG,OAAO7Y,EAAAA,IAAQiZ,GAAcjZ,EAAAA,CAAAA,EAE7E+W,OAAO,SAAUpB,IAAOpM,IAAAA;AAElD,qBADAoM,GAAMpM,EAAAA,IAAY,IACXoM;YACf,GAAS,CAAA,CAAA;AAEEwC,eAAczc,EAAAA,KAAamc,GAAYnc,EAAAA,MAI5CgI,OAAOoV,OAAOpd,GAAQia,OAAOA,EAAAA,GAC7BjS,OAAOrH,KAAK+L,EAAAA,EAAYwQ,QAAQ,SAAUY,IAAAA;AACxC9d,cAAAA,GAAQwM,gBAAgBsR,EAAAA;YAChC,CAAA;UACA,CAAA;QACA;MACA,GASEC,UAAU,CAAC,eAAA,EAAA;ACjFE,eAASC,GAAiBzC,IAAAA;AACvC,eAAOA,GAAUzV,MAAM,GAAA,EAAK,CAAA;MAC9B;ACHO,UAAIe,KAAMD,KAAKC,KACXC,KAAMF,KAAKE,KACXmX,KAAQrX,KAAKqX;ACFT,eAASC,KAAAA;AACtB,YAAIC,KAAS5K,UAAU6K;AAEvB,eAAc,QAAVD,MAAkBA,GAAOE,UAAU5d,MAAM6d,QAAQH,GAAOE,MAAAA,IACnDF,GAAOE,OAAOhP,IAAI,SAAUkP,IAAAA;AACjC,iBAAOA,GAAKC,QAAQ,MAAMD,GAAKE;QACrC,CAAA,EAAOlP,KAAK,GAAA,IAGHgE,UAAUmL;MACnB;ACTe,eAASC,KAAAA;AACtB,eAAA,CAAQ,iCAAiCzQ,KAAKgQ,GAAAA,CAAAA;MAChD;ACCe,eAAS9D,GAAsBpa,IAAS4e,IAAcC,IAAAA;AAAAA,mBAC/DD,OACFA,KAAAA,QAAe,WAGbC,OACFA,KAAAA;AAGF,YAAIC,KAAa9e,GAAQoa,sBAAAA,GACrB2E,KAAS,GACTC,KAAS;AAETJ,QAAAA,MAAgBnC,GAAczc,EAAAA,MAChC+e,KAAS/e,GAAQif,cAAc,KAAIhB,GAAMa,GAAWI,KAAAA,IAASlf,GAAQif,eAAmB,GACxFD,KAAShf,GAAQ2D,eAAe,KAAIsa,GAAMa,GAAWK,MAAAA,IAAUnf,GAAQ2D,gBAAoB;AAG7F,YACIyb,MADO1d,GAAU1B,EAAAA,IAAWqc,GAAUrc,EAAAA,IAAWiB,QAC3Bme,gBAEtBC,KAAAA,CAAoBV,GAAAA,KAAsBE,IAC1CS,MAAKR,GAAWnE,QAAQ0E,MAAoBD,KAAiBA,GAAeG,aAAa,MAAMR,IAC/FS,MAAKV,GAAWtE,OAAO6E,MAAoBD,KAAiBA,GAAeK,YAAY,MAAMT,IAC7FE,KAAQJ,GAAWI,QAAQH,IAC3BI,KAASL,GAAWK,SAASH;AACjC,eAAO,EACLE,OAAOA,IACPC,QAAQA,IACR3E,KAAKgF,IACL9E,OAAO4E,KAAIJ,IACXzE,QAAQ+E,KAAIL,IACZxE,MAAM2E,IACNA,GAAGA,IACHE,GAAGA,GAAAA;MAEP;ACrCe,eAASE,GAAc1f,IAAAA;AACpC,YAAI8e,KAAa1E,GAAsBpa,EAAAA,GAGnCkf,KAAQlf,GAAQif,aAChBE,KAASnf,GAAQ2D;AAUrB,eARIiD,KAAKuM,IAAI2L,GAAWI,QAAQA,EAAAA,KAAU,MACxCA,KAAQJ,GAAWI,QAGjBtY,KAAKuM,IAAI2L,GAAWK,SAASA,EAAAA,KAAW,MAC1CA,KAASL,GAAWK,SAGf,EACLG,GAAGtf,GAAQuf,YACXC,GAAGxf,GAAQyf,WACXP,OAAOA,IACPC,QAAQA,GAAAA;MAEZ;ACvBe,eAASpc,GAASgW,IAAQlJ,IAAAA;AACvC,YAAI8P,KAAW9P,GAAMvM,eAAeuM,GAAMvM,YAAAA;AAE1C,YAAIyV,GAAOhW,SAAS8M,EAAAA,EAClB,QAAA;AAEG,YAAI8P,MAAYhD,GAAagD,EAAAA,GAAW;AACzC,cAAIvP,KAAOP;AAEX,aAAG;AACD,gBAAIO,MAAQ2I,GAAO6G,WAAWxP,EAAAA,EAC5B,QAAA;AAIFA,YAAAA,KAAOA,GAAK1N,cAAc0N,GAAKyP;UACvC,SAAezP;QACf;AAGE,eAAA;MACF;ACrBe,eAAS/N,GAAiBrC,IAAAA;AACvC,eAAOqc,GAAUrc,EAAAA,EAASqC,iBAAiBrC,EAAAA;MAC7C;ACFe,eAAS8f,GAAe9f,IAAAA;AACrC,eAAO,CAAC,SAAS,MAAM,IAAA,EAAM2G,QAAQwV,GAAYnc,EAAAA,CAAAA,KAAa;MAChE;ACFe,eAAS+f,GAAmB/f,IAAAA;AAEzC,iBAAS0B,GAAU1B,EAAAA,IAAWA,GAAQuc,gBACtCvc,GAAQgC,aAAaf,OAAOe,UAAUoB;MACxC;ACFe,eAAS4c,GAAchgB,IAAAA;AACpC,eAA6B,WAAzBmc,GAAYnc,EAAAA,IACPA,KAMPA,GAAQigB,gBACRjgB,GAAQ0C,eACRia,GAAa3c,EAAAA,IAAWA,GAAQ6f,OAAO,SAEvCE,GAAmB/f,EAAAA;MAGvB;ACVA,eAASkgB,GAAoBlgB,IAAAA;AAC3B,eAAKyc,GAAczc,EAAAA,KACoB,YAAvCqC,GAAiBrC,EAAAA,EAASwd,WAInBxd,GAAQmgB,eAHN;MAIX;AAwCe,eAASC,GAAgBpgB,IAAAA;AAItC,iBAHIiB,KAASob,GAAUrc,EAAAA,GACnBmgB,KAAeD,GAAoBlgB,EAAAA,GAEhCmgB,MAAgBL,GAAeK,EAAAA,KAA6D,aAA5C9d,GAAiB8d,EAAAA,EAAc3C,WACpF2C,CAAAA,KAAeD,GAAoBC,EAAAA;AAGrC,eAAIA,OAA+C,WAA9BhE,GAAYgE,EAAAA,KAA0D,WAA9BhE,GAAYgE,EAAAA,KAAwE,aAA5C9d,GAAiB8d,EAAAA,EAAc3C,YAC3Hvc,KAGFkf,MAhDT,SAA4BngB,IAAAA;AAC1B,cAAIqgB,KAAY,WAAWnS,KAAKgQ,GAAAA,CAAAA;AAGhC,cAFW,WAAWhQ,KAAKgQ,GAAAA,CAAAA,KAEfzB,GAAczc,EAAAA,KAII,YAFXqC,GAAiBrC,EAAAA,EAEnBwd,SACb,QAAO;AAIX,cAAI8C,KAAcN,GAAchgB,EAAAA;AAMhC,eAJI2c,GAAa2D,EAAAA,MACfA,KAAcA,GAAYT,OAGrBpD,GAAc6D,EAAAA,KAAgB,CAAC,QAAQ,MAAA,EAAQ3Z,QAAQwV,GAAYmE,EAAAA,CAAAA,IAAgB,KAAG;AAC3F,gBAAIC,KAAMle,GAAiBie,EAAAA;AAI3B,gBAAsB,WAAlBC,GAAIC,aAA4C,WAApBD,GAAIE,eAA0C,YAAhBF,GAAIG,WAAAA,OAAuB,CAAC,aAAa,aAAA,EAAe/Z,QAAQ4Z,GAAII,UAAAA,KAAsBN,MAAgC,aAAnBE,GAAII,cAA2BN,MAAaE,GAAI1T,UAAyB,WAAf0T,GAAI1T,OACjO,QAAOyT;AAEPA,YAAAA,KAAcA,GAAY5d;UAEhC;AAEE,iBAAO;QACT,EAgB4C1C,EAAAA,KAAYiB;MACxD;ACpEe,eAAS2f,GAAyBrF,IAAAA;AAC/C,eAAO,CAAC,OAAO,QAAA,EAAU5U,QAAQ4U,EAAAA,KAAc,IAAI,MAAM;MAC3D;ACDO,eAASsF,GAAO/Z,IAAK0E,IAAO3E,IAAAA;AACjC,eAAOia,GAAQha,IAAKia,GAAQvV,IAAO3E,EAAAA,CAAAA;MACrC;ACFe,eAASma,GAAmBC,IAAAA;AACzC,eAAOjZ,OAAOoV,OAAO,CAAA,GCDd,EACL5C,KAAK,GACLE,OAAO,GACPD,QAAQ,GACRE,MAAM,EAAA,GDHuCsG,EAAAA;MACjD;AEHe,eAASC,GAAgB1V,IAAO7K,IAAAA;AAC7C,eAAOA,GAAK0a,OAAO,SAAU8F,IAASlhB,IAAAA;AAEpC,iBADAkhB,GAAQlhB,EAAAA,IAAOuL,IACR2V;QACX,GAAK,CAAA,CAAA;MACL;AC4EA,YAAAC,KAAe,EACb9c,MAAM,SACNuY,SAAAA,MACAC,OAAO,QACPrY,IApEF,SAAesY,IAAAA;AACb,YAAIsE,IAEArE,KAAQD,GAAKC,OACb1Y,KAAOyY,GAAKzY,MACZmZ,KAAUV,GAAKU,SACf6D,KAAetE,GAAMC,SAASW,OAC9B2D,KAAgBvE,GAAMwE,cAAcD,eACpCE,KAAgBzD,GAAiBhB,GAAMzB,SAAAA,GACvCmG,KAAOd,GAAyBa,EAAAA,GAEhCE,KADa,CAAChH,IAAMD,EAAAA,EAAO/T,QAAQ8a,EAAAA,KAAkB,IAClC,WAAW;AAElC,YAAKH,MAAiBC,IAAtB;AAIA,cAAIN,KAxBgB,SAAyBW,IAAS5E,IAAAA;AAItD,mBAAOgE,GAAsC,YAAA,QAH7CY,KAA6B,cAAA,OAAZA,KAAyBA,GAAQ5Z,OAAOoV,OAAO,CAAA,GAAIJ,GAAM6E,OAAO,EAC/EtG,WAAWyB,GAAMzB,UAAAA,CAAAA,CAAAA,IACbqG,MACkDA,KAAUV,GAAgBU,IAAS/G,EAAAA,CAAAA;UAC7F,EAmBsC4C,GAAQmE,SAAS5E,EAAAA,GACjD8E,KAAYpC,GAAc4B,EAAAA,GAC1BS,KAAmB,QAATL,KAAelH,KAAMG,IAC/BqH,KAAmB,QAATN,KAAejH,KAASC,IAClCuH,KAAUjF,GAAM6E,MAAM1G,UAAUwG,EAAAA,IAAO3E,GAAM6E,MAAM1G,UAAUuG,EAAAA,IAAQH,GAAcG,EAAAA,IAAQ1E,GAAM6E,MAAM3G,OAAOyG,EAAAA,GAC9GO,KAAYX,GAAcG,EAAAA,IAAQ1E,GAAM6E,MAAM1G,UAAUuG,EAAAA,GACxDS,KAAoB/B,GAAgBkB,EAAAA,GACpCc,KAAaD,KAA6B,QAATT,KAAeS,GAAkBE,gBAAgB,IAAIF,GAAkBG,eAAe,IAAI,GAC3HC,KAAoBN,KAAU,IAAIC,KAAY,GAG9Cpb,KAAMma,GAAcc,EAAAA,GACpBlb,KAAMub,KAAaN,GAAUH,EAAAA,IAAOV,GAAce,EAAAA,GAClDQ,KAASJ,KAAa,IAAIN,GAAUH,EAAAA,IAAO,IAAIY,IAC/CE,KAAS5B,GAAO/Z,IAAK0b,IAAQ3b,EAAAA,GAE7B6b,KAAWhB;AACf1E,UAAAA,GAAMwE,cAAcld,EAAAA,MAAS+c,KAAwB,CAAA,GAA0BqB,EAAAA,IAAYD,IAAQpB,GAAsBsB,eAAeF,KAASD,IAAQnB;QAnB3J;MAoBA,GAkCEhE,QAhCF,SAAgBC,IAAAA;AACd,YAAIN,KAAQM,GAAMN,OAEd4F,KADUtF,GAAMG,QACWzd,SAC3BshB,KAAAA,WAAesB,KAA8B,wBAAwBA;AAErD,gBAAhBtB,OAKwB,YAAA,OAAjBA,OACTA,KAAetE,GAAMC,SAAS/B,OAAOjZ,cAAcqf,EAAAA,OAOhDve,GAASia,GAAMC,SAAS/B,QAAQoG,EAAAA,MAIrCtE,GAAMC,SAASW,QAAQ0D;MACzB,GASEvD,UAAU,CAAC,eAAA,GACX8E,kBAAkB,CAAC,iBAAA,EAAA;ACxFN,eAASC,GAAavH,IAAAA;AACnC,eAAOA,GAAUzV,MAAM,GAAA,EAAK,CAAA;MAC9B;ACOA,UAAIid,KAAa,EACfvI,KAAK,QACLE,OAAO,QACPD,QAAQ,QACRE,MAAM,OAAA;AAeD,eAASqI,GAAY1F,IAAAA;AAC1B,YAAI2F,IAEA/H,KAASoC,GAAMpC,QACfgI,KAAa5F,GAAM4F,YACnB3H,KAAY+B,GAAM/B,WAClB4H,KAAY7F,GAAM6F,WAClBC,KAAU9F,GAAM8F,SAChB5F,KAAWF,GAAME,UACjB6F,KAAkB/F,GAAM+F,iBACxBC,KAAWhG,GAAMgG,UACjBC,KAAejG,GAAMiG,cACrBC,KAAUlG,GAAMkG,SAChBC,KAAaL,GAAQ9D,GACrBA,KAAAA,WAAImE,KAAwB,IAAIA,IAChCC,KAAaN,GAAQ5D,GACrBA,KAAAA,WAAIkE,KAAwB,IAAIA,IAEhCC,KAAgC,cAAA,OAAjBJ,KAA8BA,GAAa,EAC5DjE,GAAGA,IACHE,GAAGA,GAAAA,CAAAA,IACA,EACHF,GAAGA,IACHE,GAAGA,GAAAA;AAGLF,QAAAA,KAAIqE,GAAMrE,GACVE,KAAImE,GAAMnE;AACV,YAAIoE,KAAOR,GAAQvF,eAAe,GAAA,GAC9BgG,KAAOT,GAAQvF,eAAe,GAAA,GAC9BiG,KAAQnJ,IACRoJ,KAAQvJ,IACRwJ,KAAM/iB;AAEV,YAAIqiB,IAAU;AACZ,cAAInD,KAAeC,GAAgBlF,EAAAA,GAC/B+I,KAAa,gBACbC,KAAY;AAEZ/D,UAAAA,OAAiB9D,GAAUnB,EAAAA,KAGmB,aAA5C7Y,GAFJ8d,KAAeJ,GAAmB7E,EAAAA,CAAAA,EAECsC,YAAsC,eAAbA,OAC1DyG,KAAa,gBACbC,KAAY,iBAOZ3I,OAAcf,OAAQe,OAAcZ,MAAQY,OAAcb,OAAUyI,OAAcpI,QACpFgJ,KAAQtJ,IAGR+E,OAFcgE,MAAWrD,OAAiB6D,MAAOA,GAAI5E,iBAAiB4E,GAAI5E,eAAeD,SACzFgB,GAAa8D,EAAAA,KACEf,GAAW/D,QAC1BK,MAAK6D,KAAkB,IAAA,KAGrB9H,OAAcZ,OAASY,OAAcf,MAAOe,OAAcd,MAAW0I,OAAcpI,QACrF+I,KAAQpJ,IAGR4E,OAFckE,MAAWrD,OAAiB6D,MAAOA,GAAI5E,iBAAiB4E,GAAI5E,eAAeF,QACzFiB,GAAa+D,EAAAA,KACEhB,GAAWhE,OAC1BI,MAAK+D,KAAkB,IAAA;QAE7B;AAEE,YAgBMc,IAhBFC,KAAepc,OAAOoV,OAAO,EAC/BI,UAAUA,GAAAA,GACT8F,MAAYP,EAAAA,GAEXsB,KAAAA,SAAQd,KAlFd,SAA2BxG,IAAMiH,IAAAA;AAC/B,cAAI1E,KAAIvC,GAAKuC,GACTE,KAAIzC,GAAKyC,GACT8E,KAAMN,GAAIO,oBAAoB;AAClC,iBAAO,EACLjF,GAAGrB,GAAMqB,KAAIgF,EAAAA,IAAOA,MAAO,GAC3B9E,GAAGvB,GAAMuB,KAAI8E,EAAAA,IAAOA,MAAO,EAAA;QAE/B,EA0EwD,EACpDhF,GAAGA,IACHE,GAAGA,GAAAA,GACFnD,GAAUnB,EAAAA,CAAAA,IAAW,EACtBoE,GAAGA,IACHE,GAAGA,GAAAA;AAML,eAHAF,KAAI+E,GAAM/E,GACVE,KAAI6E,GAAM7E,GAEN6D,KAGKrb,OAAOoV,OAAO,CAAA,GAAIgH,MAAeD,KAAiB,CAAA,GAAmBJ,EAAAA,IAASF,KAAO,MAAM,IAAIM,GAAeL,EAAAA,IAASF,KAAO,MAAM,IAAIO,GAAe3D,aAAawD,GAAIO,oBAAoB,MAAM,IAAI,eAAejF,KAAI,SAASE,KAAI,QAAQ,iBAAiBF,KAAI,SAASE,KAAI,UAAU2E,GAAAA,IAG5Rnc,OAAOoV,OAAO,CAAA,GAAIgH,MAAenB,KAAkB,CAAA,GAAoBc,EAAAA,IAASF,KAAOrE,KAAI,OAAO,IAAIyD,GAAgBa,EAAAA,IAASF,KAAOtE,KAAI,OAAO,IAAI2D,GAAgBzC,YAAY,IAAIyC,GAAAA;MAC9L;AA4CA,YAAAuB,KAAe,EACblgB,MAAM,iBACNuY,SAAAA,MACAC,OAAO,eACPrY,IA9CF,SAAuBggB,IAAAA;AACrB,YAAIzH,KAAQyH,GAAMzH,OACdS,KAAUgH,GAAMhH,SAChBiH,KAAwBjH,GAAQ4F,iBAChCA,KAAAA,WAAkBqB,MAA0CA,IAC5DC,KAAoBlH,GAAQ6F,UAC5BA,KAAAA,WAAWqB,MAAsCA,IACjDC,KAAwBnH,GAAQ8F,cAChCA,KAAAA,WAAeqB,MAA0CA,IACzDR,KAAe,EACjB7I,WAAWyC,GAAiBhB,GAAMzB,SAAAA,GAClC4H,WAAWL,GAAa9F,GAAMzB,SAAAA,GAC9BL,QAAQ8B,GAAMC,SAAS/B,QACvBgI,YAAYlG,GAAM6E,MAAM3G,QACxBmI,iBAAiBA,IACjBG,SAAoC,YAA3BxG,GAAMS,QAAQC,SAAAA;AAGgB,gBAArCV,GAAMwE,cAAcD,kBACtBvE,GAAMG,OAAOjC,SAASlT,OAAOoV,OAAO,CAAA,GAAIJ,GAAMG,OAAOjC,QAAQ8H,GAAYhb,OAAOoV,OAAO,CAAA,GAAIgH,IAAc,EACvGhB,SAASpG,GAAMwE,cAAcD,eAC7B/D,UAAUR,GAAMS,QAAQC,UACxB4F,UAAUA,IACVC,cAAcA,GAAAA,CAAAA,CAAAA,CAAAA,IAIe,QAA7BvG,GAAMwE,cAAc5D,UACtBZ,GAAMG,OAAOS,QAAQ5V,OAAOoV,OAAO,CAAA,GAAIJ,GAAMG,OAAOS,OAAOoF,GAAYhb,OAAOoV,OAAO,CAAA,GAAIgH,IAAc,EACrGhB,SAASpG,GAAMwE,cAAc5D,OAC7BJ,UAAU,YACV8F,UAAAA,OACAC,cAAcA,GAAAA,CAAAA,CAAAA,CAAAA,IAIlBvG,GAAMtQ,WAAWwO,SAASlT,OAAOoV,OAAO,CAAA,GAAIJ,GAAMtQ,WAAWwO,QAAQ,EACnE,yBAAyB8B,GAAMzB,UAAAA,CAAAA;MAEnC,GAQE/J,MAAM,CAAA,EAAA;ACrKR,UAAIqT,KAAU,EACZA,SAAAA,KAAS;AAsCX,YAAAC,KAAe,EACbxgB,MAAM,kBACNuY,SAAAA,MACAC,OAAO,SACPrY,IAAI,WAAA;MAAc,GAClB4Y,QAxCF,SAAgBN,IAAAA;AACd,YAAIC,KAAQD,GAAKC,OACb9c,KAAW6c,GAAK7c,UAChBud,KAAUV,GAAKU,SACfsH,KAAkBtH,GAAQuH,QAC1BA,KAAAA,WAASD,MAAoCA,IAC7CE,KAAkBxH,GAAQyH,QAC1BA,KAAAA,WAASD,MAAoCA,IAC7ChkB,KAASob,GAAUW,GAAMC,SAAS/B,MAAAA,GAClCiK,KAAgB,CAAA,EAAG1V,OAAOuN,GAAMmI,cAAchK,WAAW6B,GAAMmI,cAAcjK,MAAAA;AAYjF,eAVI8J,MACFG,GAAcjI,QAAQ,SAAUkI,IAAAA;AAC9BA,UAAAA,GAAatgB,iBAAiB,UAAU5E,GAASmlB,QAAQR,EAAAA;QAC/D,CAAA,GAGMK,MACFjkB,GAAO6D,iBAAiB,UAAU5E,GAASmlB,QAAQR,EAAAA,GAG9C,WAAA;AACDG,UAAAA,MACFG,GAAcjI,QAAQ,SAAUkI,IAAAA;AAC9BA,YAAAA,GAAalf,oBAAoB,UAAUhG,GAASmlB,QAAQR,EAAAA;UACpE,CAAA,GAGQK,MACFjkB,GAAOiF,oBAAoB,UAAUhG,GAASmlB,QAAQR,EAAAA;QAE5D;MACA,GASErT,MAAM,CAAA,EAAA;AC/CR,UAAI8T,KAAO,EACT3K,MAAM,SACND,OAAO,QACPD,QAAQ,OACRD,KAAK,SAAA;AAEQ,eAAS+K,GAAqBhK,IAAAA;AAC3C,eAAOA,GAAUna,QAAQ,0BAA0B,SAAUokB,IAAAA;AAC3D,iBAAOF,GAAKE,EAAAA;QAChB,CAAA;MACA;ACVA,UAAIF,KAAO,EACTxK,OAAO,OACPC,KAAK,QAAA;AAEQ,eAAS0K,GAA8BlK,IAAAA;AACpD,eAAOA,GAAUna,QAAQ,cAAc,SAAUokB,IAAAA;AAC/C,iBAAOF,GAAKE,EAAAA;QAChB,CAAA;MACA;ACPe,eAASE,GAAgBpJ,IAAAA;AACtC,YAAI0H,KAAM3H,GAAUC,EAAAA;AAGpB,eAAO,EACLqJ,YAHe3B,GAAI4B,aAInBC,WAHc7B,GAAI8B,YAAAA;MAKtB;ACNe,eAASC,GAAoB/lB,IAAAA;AAQ1C,eAAOoa,GAAsB2F,GAAmB/f,EAAAA,CAAAA,EAAU2a,OAAO+K,GAAgB1lB,EAAAA,EAAS2lB;MAC5F;ACXe,eAASK,GAAehmB,IAAAA;AAErC,YAAIimB,KAAoB5jB,GAAiBrC,EAAAA,GACrCkmB,KAAWD,GAAkBC,UAC7BC,KAAYF,GAAkBE,WAC9BC,KAAYH,GAAkBG;AAElC,eAAO,6BAA6BlY,KAAKgY,KAAWE,KAAYD,EAAAA;MAClE;ACLe,eAASE,GAAgB/J,IAAAA;AACtC,eAAI,CAAC,QAAQ,QAAQ,WAAA,EAAa3V,QAAQwV,GAAYG,EAAAA,CAAAA,KAAU,IAEvDA,GAAKC,cAAczY,OAGxB2Y,GAAcH,EAAAA,KAAS0J,GAAe1J,EAAAA,IACjCA,KAGF+J,GAAgBrG,GAAc1D,EAAAA,CAAAA;MACvC;ACJe,eAASgK,GAAkBtmB,IAASqG,IAAAA;AACjD,YAAIkgB;AAAAA,mBAEAlgB,OACFA,KAAO,CAAA;AAGT,YAAI+e,KAAeiB,GAAgBrmB,EAAAA,GAC/BwmB,KAASpB,QAAqE,SAAlDmB,KAAwBvmB,GAAQuc,iBAAAA,SAAkCgK,GAAsBziB,OACpHkgB,KAAM3H,GAAU+I,EAAAA,GAChBnf,KAASugB,KAAS,CAACxC,EAAAA,EAAKvU,OAAOuU,GAAI5E,kBAAkB,CAAA,GAAI4G,GAAeZ,EAAAA,IAAgBA,KAAe,CAAA,CAAA,IAAMA,IAC7GqB,KAAcpgB,GAAKoJ,OAAOxJ,EAAAA;AAC9B,eAAOugB,KAASC,KAChBA,GAAYhX,OAAO6W,GAAkBtG,GAAc/Z,EAAAA,CAAAA,CAAAA;MACrD;ACzBe,eAASygB,GAAiBC,IAAAA;AACvC,eAAO3e,OAAOoV,OAAO,CAAA,GAAIuJ,IAAM,EAC7BhM,MAAMgM,GAAKrH,GACX9E,KAAKmM,GAAKnH,GACV9E,OAAOiM,GAAKrH,IAAIqH,GAAKzH,OACrBzE,QAAQkM,GAAKnH,IAAImH,GAAKxH,OAAAA,CAAAA;MAE1B;ACqBA,eAASyH,GAA2B5mB,IAAS6mB,IAAgBnJ,IAAAA;AAC3D,eAAOmJ,OAAmB5L,KAAWyL,GCzBxB,SAAyB1mB,IAAS0d,IAAAA;AAC/C,cAAIsG,KAAM3H,GAAUrc,EAAAA,GAChB8mB,KAAO/G,GAAmB/f,EAAAA,GAC1Bof,KAAiB4E,GAAI5E,gBACrBF,KAAQ4H,GAAKxE,aACbnD,KAAS2H,GAAKzE,cACd/C,KAAI,GACJE,KAAI;AAER,cAAIJ,IAAgB;AAClBF,YAAAA,KAAQE,GAAeF,OACvBC,KAASC,GAAeD;AACxB,gBAAI4H,KAAiBpI,GAAAA;AAAAA,aAEjBoI,MAAAA,CAAmBA,MAA+B,YAAbrJ,QACvC4B,KAAIF,GAAeG,YACnBC,KAAIJ,GAAeK;UAEzB;AAEE,iBAAO,EACLP,OAAOA,IACPC,QAAQA,IACRG,GAAGA,KAAIyG,GAAoB/lB,EAAAA,GAC3Bwf,GAAGA,GAAAA;QAEP,EDDwExf,IAAS0d,EAAAA,CAAAA,IAAahc,GAAUmlB,EAAAA,IAdxG,SAAoC7mB,IAAS0d,IAAAA;AAC3C,cAAIiJ,KAAOvM,GAAsBpa,IAAAA,OAA6B,YAAb0d,EAAAA;AASjD,iBARAiJ,GAAKnM,MAAMmM,GAAKnM,MAAMxa,GAAQgnB,WAC9BL,GAAKhM,OAAOgM,GAAKhM,OAAO3a,GAAQinB,YAChCN,GAAKlM,SAASkM,GAAKnM,MAAMxa,GAAQqiB,cACjCsE,GAAKjM,QAAQiM,GAAKhM,OAAO3a,GAAQsiB,aACjCqE,GAAKzH,QAAQlf,GAAQsiB,aACrBqE,GAAKxH,SAASnf,GAAQqiB,cACtBsE,GAAKrH,IAAIqH,GAAKhM,MACdgM,GAAKnH,IAAImH,GAAKnM,KACPmM;QACT,EAGqJE,IAAgBnJ,EAAAA,IAAYgJ,GEtBlK,SAAyB1mB,IAAAA;AACtC,cAAIumB,IAEAO,KAAO/G,GAAmB/f,EAAAA,GAC1BknB,KAAYxB,GAAgB1lB,EAAAA,GAC5B8D,KAA0D,SAAlDyiB,KAAwBvmB,GAAQuc,iBAAAA,SAAkCgK,GAAsBziB,MAChGob,KAAQrY,GAAIigB,GAAKK,aAAaL,GAAKxE,aAAaxe,KAAOA,GAAKqjB,cAAc,GAAGrjB,KAAOA,GAAKwe,cAAc,CAAA,GACvGnD,KAAStY,GAAIigB,GAAKM,cAAcN,GAAKzE,cAAcve,KAAOA,GAAKsjB,eAAe,GAAGtjB,KAAOA,GAAKue,eAAe,CAAA,GAC5G/C,KAAAA,CAAK4H,GAAUvB,aAAaI,GAAoB/lB,EAAAA,GAChDwf,KAAAA,CAAK0H,GAAUrB;AAMnB,iBAJiD,UAA7CxjB,GAAiByB,MAAQgjB,EAAAA,EAAM1T,cACjCkM,MAAKzY,GAAIigB,GAAKxE,aAAaxe,KAAOA,GAAKwe,cAAc,CAAA,IAAKpD,KAGrD,EACLA,OAAOA,IACPC,QAAQA,IACRG,GAAGA,IACHE,GAAGA,GAAAA;QAEP,EFCkNO,GAAmB/f,EAAAA,CAAAA,CAAAA;MACrO;AG1Be,eAASqnB,GAAetK,IAAAA;AACrC,YAOIqG,IAPAjI,KAAY4B,GAAK5B,WACjBnb,KAAU+c,GAAK/c,SACfub,KAAYwB,GAAKxB,WACjBkG,KAAgBlG,KAAYyC,GAAiBzC,EAAAA,IAAa,MAC1D4H,KAAY5H,KAAYuH,GAAavH,EAAAA,IAAa,MAClD+L,KAAUnM,GAAUmE,IAAInE,GAAU+D,QAAQ,IAAIlf,GAAQkf,QAAQ,GAC9DqI,KAAUpM,GAAUqE,IAAIrE,GAAUgE,SAAS,IAAInf,GAAQmf,SAAS;AAGpE,gBAAQsC,IAAAA;UACN,KAAKjH;AACH4I,YAAAA,KAAU,EACR9D,GAAGgI,IACH9H,GAAGrE,GAAUqE,IAAIxf,GAAQmf,OAAAA;AAE3B;UAEF,KAAK1E;AACH2I,YAAAA,KAAU,EACR9D,GAAGgI,IACH9H,GAAGrE,GAAUqE,IAAIrE,GAAUgE,OAAAA;AAE7B;UAEF,KAAKzE;AACH0I,YAAAA,KAAU,EACR9D,GAAGnE,GAAUmE,IAAInE,GAAU+D,OAC3BM,GAAG+H,GAAAA;AAEL;UAEF,KAAK5M;AACHyI,YAAAA,KAAU,EACR9D,GAAGnE,GAAUmE,IAAItf,GAAQkf,OACzBM,GAAG+H,GAAAA;AAEL;UAEF;AACEnE,YAAAA,KAAU,EACR9D,GAAGnE,GAAUmE,GACbE,GAAGrE,GAAUqE,EAAAA;QAAAA;AAInB,YAAIgI,KAAW/F,KAAgBb,GAAyBa,EAAAA,IAAiB;AAEzE,YAAgB,QAAZ+F,IAAkB;AACpB,cAAI7F,KAAmB,QAAb6F,KAAmB,WAAW;AAExC,kBAAQrE,IAAAA;YACN,KAAKrI;AACHsI,cAAAA,GAAQoE,EAAAA,IAAYpE,GAAQoE,EAAAA,KAAarM,GAAUwG,EAAAA,IAAO,IAAI3hB,GAAQ2hB,EAAAA,IAAO;AAC7E;YAEF,KAAK5G;AACHqI,cAAAA,GAAQoE,EAAAA,IAAYpE,GAAQoE,EAAAA,KAAarM,GAAUwG,EAAAA,IAAO,IAAI3hB,GAAQ2hB,EAAAA,IAAO;UAAA;QAKrF;AAEE,eAAOyB;MACT;AC3De,eAASqE,GAAezK,IAAOS,IAAAA;AAAAA,mBACxCA,OACFA,KAAU,CAAA;AAGZ,YAAIiK,KAAWjK,IACXkK,KAAqBD,GAASnM,WAC9BA,KAAAA,WAAYoM,KAAgC3K,GAAMzB,YAAYoM,IAC9DC,KAAoBF,GAAShK,UAC7BA,KAAAA,WAAWkK,KAA+B5K,GAAMU,WAAWkK,IAC3DC,KAAoBH,GAASI,UAC7BA,KAAAA,WAAWD,KAA+B7M,KAAkB6M,IAC5DE,KAAwBL,GAASM,cACjCA,KAAAA,WAAeD,KAAmC9M,KAAW8M,IAC7DE,KAAwBP,GAASQ,gBACjCA,KAAAA,WAAiBD,KAAmC/M,KAAS+M,IAC7DE,KAAuBT,GAASU,aAChCA,KAAAA,WAAcD,MAA0CA,IACxDE,KAAmBX,GAAS9F,SAC5BA,KAAAA,WAAUyG,KAA8B,IAAIA,IAC5CpH,KAAgBD,GAAsC,YAAA,OAAZY,KAAuBA,KAAUV,GAAgBU,IAAS/G,EAAAA,CAAAA,GACpGyN,KAAaJ,OAAmBhN,KAASC,KAAYD,IACrDgI,KAAalG,GAAM6E,MAAM3G,QACzBlb,KAAUgd,GAAMC,SAASmL,KAAcE,KAAaJ,EAAAA,GACpDK,KJkBS,SAAyBvoB,IAAS8nB,IAAUE,IAActK,IAAAA;AACvE,cAAI8K,KAAmC,sBAAbV,KAlB5B,SAA4B9nB,IAAAA;AAC1B,gBAAIgb,KAAkBsL,GAAkBtG,GAAchgB,EAAAA,CAAAA,GAElDyoB,KADoB,CAAC,YAAY,OAAA,EAAS9hB,QAAQtE,GAAiBrC,EAAAA,EAASwd,QAAAA,KAAa,KACnDf,GAAczc,EAAAA,IAAWogB,GAAgBpgB,EAAAA,IAAWA;AAE9F,mBAAK0B,GAAU+mB,EAAAA,IAKRzN,GAAgBnO,OAAO,SAAUga,IAAAA;AACtC,qBAAOnlB,GAAUmlB,EAAAA,KAAmB9jB,GAAS8jB,IAAgB4B,EAAAA,KAAmD,WAAhCtM,GAAY0K,EAAAA;YAChG,CAAA,IANW,CAAA;UAOX,EAKgF7mB,EAAAA,IAAW,CAAA,EAAGyP,OAAOqY,EAAAA,GAC/F9M,KAAkB,CAAA,EAAGvL,OAAO+Y,IAAqB,CAACR,EAAAA,CAAAA,GAClDU,KAAsB1N,GAAgB,CAAA,GACtC2N,KAAe3N,GAAgBK,OAAO,SAAUuN,IAAS/B,IAAAA;AAC3D,gBAAIF,KAAOC,GAA2B5mB,IAAS6mB,IAAgBnJ,EAAAA;AAK/D,mBAJAkL,GAAQpO,MAAM3T,GAAI8f,GAAKnM,KAAKoO,GAAQpO,GAAAA,GACpCoO,GAAQlO,QAAQ5T,GAAI6f,GAAKjM,OAAOkO,GAAQlO,KAAAA,GACxCkO,GAAQnO,SAAS3T,GAAI6f,GAAKlM,QAAQmO,GAAQnO,MAAAA,GAC1CmO,GAAQjO,OAAO9T,GAAI8f,GAAKhM,MAAMiO,GAAQjO,IAAAA,GAC/BiO;UACX,GAAKhC,GAA2B5mB,IAAS0oB,IAAqBhL,EAAAA,CAAAA;AAK5D,iBAJAiL,GAAazJ,QAAQyJ,GAAajO,QAAQiO,GAAahO,MACvDgO,GAAaxJ,SAASwJ,GAAalO,SAASkO,GAAanO,KACzDmO,GAAarJ,IAAIqJ,GAAahO,MAC9BgO,GAAanJ,IAAImJ,GAAanO,KACvBmO;QACT,EInC2CjnB,GAAU1B,EAAAA,IAAWA,KAAUA,GAAQ6oB,kBAAkB9I,GAAmB/C,GAAMC,SAAS/B,MAAAA,GAAS4M,IAAUE,IAActK,EAAAA,GACjKoL,KAAsB1O,GAAsB4C,GAAMC,SAAS9B,SAAAA,GAC3DoG,KAAgB8F,GAAe,EACjClM,WAAW2N,IACX9oB,SAASkjB,IACTxF,UAAU,YACVnC,WAAWA,GAAAA,CAAAA,GAETwN,KAAmBrC,GAAiB1e,OAAOoV,OAAO,CAAA,GAAI8F,IAAY3B,EAAAA,CAAAA,GAClEyH,KAAoBd,OAAmBhN,KAAS6N,KAAmBD,IAGnEG,KAAkB,EACpBzO,KAAK+N,GAAmB/N,MAAMwO,GAAkBxO,MAAMyG,GAAczG,KACpEC,QAAQuO,GAAkBvO,SAAS8N,GAAmB9N,SAASwG,GAAcxG,QAC7EE,MAAM4N,GAAmB5N,OAAOqO,GAAkBrO,OAAOsG,GAActG,MACvED,OAAOsO,GAAkBtO,QAAQ6N,GAAmB7N,QAAQuG,GAAcvG,MAAAA,GAExEwO,KAAalM,GAAMwE,cAAciB;AAErC,YAAIyF,OAAmBhN,MAAUgO,IAAY;AAC3C,cAAIzG,KAASyG,GAAW3N,EAAAA;AACxBvT,iBAAOrH,KAAKsoB,EAAAA,EAAiB/L,QAAQ,SAAUjd,IAAAA;AAC7C,gBAAIkpB,KAAW,CAACzO,IAAOD,EAAAA,EAAQ9T,QAAQ1G,EAAAA,KAAQ,IAAI,IAAA,IAC/CyhB,KAAO,CAAClH,IAAKC,EAAAA,EAAQ9T,QAAQ1G,EAAAA,KAAQ,IAAI,MAAM;AACnDgpB,YAAAA,GAAgBhpB,EAAAA,KAAQwiB,GAAOf,EAAAA,IAAQyH;UAC7C,CAAA;QACA;AAEE,eAAOF;MACT;AC5De,eAASG,GAAqBpM,IAAOS,IAAAA;AAAAA,mBAC9CA,OACFA,KAAU,CAAA;AAGZ,YAAIiK,KAAWjK,IACXlC,KAAYmM,GAASnM,WACrBuM,KAAWJ,GAASI,UACpBE,KAAeN,GAASM,cACxBpG,KAAU8F,GAAS9F,SACnByH,KAAiB3B,GAAS2B,gBAC1BC,KAAwB5B,GAAS6B,uBACjCA,KAAAA,WAAwBD,KAAmCE,KAAgBF,IAC3EnG,KAAYL,GAAavH,EAAAA,GACzBC,KAAa2H,KAAYkG,KAAiBjO,KAAsBA,GAAoBvO,OAAO,SAAU0O,IAAAA;AACvG,iBAAOuH,GAAavH,EAAAA,MAAe4H;QACvC,CAAA,IAAOtI,IACD4O,KAAoBjO,GAAW3O,OAAO,SAAU0O,IAAAA;AAClD,iBAAOgO,GAAsB5iB,QAAQ4U,EAAAA,KAAc;QACvD,CAAA;AAEmC,cAA7BkO,GAAkB1nB,WACpB0nB,KAAoBjO;AAItB,YAAIkO,KAAYD,GAAkBpO,OAAO,SAAUC,IAAKC,IAAAA;AAOtD,iBANAD,GAAIC,EAAAA,IAAakM,GAAezK,IAAO,EACrCzB,WAAWA,IACXuM,UAAUA,IACVE,cAAcA,IACdpG,SAASA,GAAAA,CAAAA,EACR5D,GAAiBzC,EAAAA,CAAAA,GACbD;QACX,GAAK,CAAA,CAAA;AACH,eAAOtT,OAAOrH,KAAK+oB,EAAAA,EAAWC,KAAK,SAAUC,IAAGC,IAAAA;AAC9C,iBAAOH,GAAUE,EAAAA,IAAKF,GAAUG,EAAAA;QACpC,CAAA;MACA;AC+FA,YAAAC,KAAe,EACbxlB,MAAM,QACNuY,SAAAA,MACAC,OAAO,QACPrY,IA5HF,SAAcsY,IAAAA;AACZ,YAAIC,KAAQD,GAAKC,OACbS,KAAUV,GAAKU,SACfnZ,KAAOyY,GAAKzY;AAEhB,YAAA,CAAI0Y,GAAMwE,cAAcld,EAAAA,EAAMylB,OAA9B;AAoCA,mBAhCIC,KAAoBvM,GAAQ+J,UAC5ByC,KAAAA,WAAgBD,MAAsCA,IACtDE,KAAmBzM,GAAQ0M,SAC3BC,KAAAA,WAAeF,MAAqCA,IACpDG,KAA8B5M,GAAQ6M,oBACtC1I,KAAUnE,GAAQmE,SAClBkG,KAAWrK,GAAQqK,UACnBE,KAAevK,GAAQuK,cACvBI,KAAc3K,GAAQ2K,aACtBmC,KAAwB9M,GAAQ4L,gBAChCA,KAAAA,WAAiBkB,MAA0CA,IAC3DhB,KAAwB9L,GAAQ8L,uBAChCiB,KAAqBxN,GAAMS,QAAQlC,WACnCkG,KAAgBzD,GAAiBwM,EAAAA,GAEjCF,KAAqBD,OADH5I,OAAkB+I,MACqCnB,KAjC/E,SAAuC9N,IAAAA;AACrC,gBAAIyC,GAAiBzC,EAAAA,MAAeX,GAClC,QAAO,CAAA;AAGT,gBAAI6P,KAAoBlF,GAAqBhK,EAAAA;AAC7C,mBAAO,CAACkK,GAA8BlK,EAAAA,GAAYkP,IAAmBhF,GAA8BgF,EAAAA,CAAAA;UACrG,EA0B2KD,EAAAA,IAA3E,CAACjF,GAAqBiF,EAAAA,CAAAA,IAChHhP,KAAa,CAACgP,EAAAA,EAAoB/a,OAAO6a,EAAAA,EAAoBjP,OAAO,SAAUC,IAAKC,IAAAA;AACrF,mBAAOD,GAAI7L,OAAOuO,GAAiBzC,EAAAA,MAAeX,KAAOwO,GAAqBpM,IAAO,EACnFzB,WAAWA,IACXuM,UAAUA,IACVE,cAAcA,IACdpG,SAASA,IACTyH,gBAAgBA,IAChBE,uBAAuBA,GAAAA,CAAAA,IACpBhO,EAAAA;UACT,GAAK,CAAA,CAAA,GACCmP,KAAgB1N,GAAM6E,MAAM1G,WAC5B+H,KAAalG,GAAM6E,MAAM3G,QACzByP,KAAY,oBAAI9qB,OAChB+qB,KAAAA,MACAC,KAAwBrP,GAAW,CAAA,GAE9BsP,KAAI,GAAGA,KAAItP,GAAWzZ,QAAQ+oB,MAAK;AAC1C,gBAAIvP,KAAYC,GAAWsP,EAAAA,GAEvBC,KAAiB/M,GAAiBzC,EAAAA,GAElCyP,KAAmBlI,GAAavH,EAAAA,MAAeT,IAC/CmQ,KAAa,CAACzQ,IAAKC,EAAAA,EAAQ9T,QAAQokB,EAAAA,KAAmB,GACtDpJ,KAAMsJ,KAAa,UAAU,UAC7B/E,KAAWuB,GAAezK,IAAO,EACnCzB,WAAWA,IACXuM,UAAUA,IACVE,cAAcA,IACdI,aAAaA,IACbxG,SAASA,GAAAA,CAAAA,GAEPsJ,KAAoBD,KAAaD,KAAmBtQ,KAAQC,KAAOqQ,KAAmBvQ,KAASD;AAE/FkQ,YAAAA,GAAc/I,EAAAA,IAAOuB,GAAWvB,EAAAA,MAClCuJ,KAAoB3F,GAAqB2F,EAAAA;AAG3C,gBAAIC,KAAmB5F,GAAqB2F,EAAAA,GACxCE,KAAS,CAAA;AAUb,gBARInB,MACFmB,GAAOrmB,KAAKmhB,GAAS6E,EAAAA,KAAmB,CAAA,GAGtCX,MACFgB,GAAOrmB,KAAKmhB,GAASgF,EAAAA,KAAsB,GAAGhF,GAASiF,EAAAA,KAAqB,CAAA,GAG1EC,GAAOC,MAAM,SAAUC,IAAAA;AACzB,qBAAOA;YACb,CAAA,GAAQ;AACFT,cAAAA,KAAwBtP,IACxBqP,KAAAA;AACA;YACN;AAEID,YAAAA,GAAU5qB,IAAIwb,IAAW6P,EAAAA;UAC7B;AAEE,cAAIR,GAqBF,UAjBIW,KAAQ,SAAeC,IAAAA;AACzB,gBAAIC,KAAmBjQ,GAAWtT,KAAK,SAAUqT,IAAAA;AAC/C,kBAAI6P,KAAST,GAAUtqB,IAAIkb,EAAAA;AAE3B,kBAAI6P,GACF,QAAOA,GAAO3gB,MAAM,GAAG+gB,EAAAA,EAAIH,MAAM,SAAUC,IAAAA;AACzC,uBAAOA;cACnB,CAAA;YAEA,CAAA;AAEM,gBAAIG,GAEF,QADAZ,KAAwBY,IACjB;UAEf,GAEaD,KAnBYnC,KAAiB,IAAI,GAmBZmC,KAAK,KAGpB,YAFFD,GAAMC,EAAAA,GADmBA,KAAAA;AAOpCxO,UAAAA,GAAMzB,cAAcsP,OACtB7N,GAAMwE,cAAcld,EAAAA,EAAMylB,QAAAA,MAC1B/M,GAAMzB,YAAYsP,IAClB7N,GAAM0O,QAAAA;QA5GV;MA8GA,GAQE7I,kBAAkB,CAAC,QAAA,GACnBrR,MAAM,EACJuY,OAAAA,MAAO,EAAA;AC7IX,eAAS4B,GAAezF,IAAUS,IAAMiF,IAAAA;AAQtC,eAAA,WAPIA,OACFA,KAAmB,EACjBtM,GAAG,GACHE,GAAG,EAAA,IAIA,EACLhF,KAAK0L,GAAS1L,MAAMmM,GAAKxH,SAASyM,GAAiBpM,GACnD9E,OAAOwL,GAASxL,QAAQiM,GAAKzH,QAAQ0M,GAAiBtM,GACtD7E,QAAQyL,GAASzL,SAASkM,GAAKxH,SAASyM,GAAiBpM,GACzD7E,MAAMuL,GAASvL,OAAOgM,GAAKzH,QAAQ0M,GAAiBtM,EAAAA;MAExD;AAEA,eAASuM,GAAsB3F,IAAAA;AAC7B,eAAO,CAAC1L,IAAKE,IAAOD,IAAQE,EAAAA,EAAMmR,KAAK,SAAUC,IAAAA;AAC/C,iBAAO7F,GAAS6F,EAAAA,KAAS;QAC7B,CAAA;MACA;AA+BA,YAAAC,KAAe,EACb1nB,MAAM,QACNuY,SAAAA,MACAC,OAAO,QACP+F,kBAAkB,CAAC,iBAAA,GACnBpe,IAlCF,SAAcsY,IAAAA;AACZ,YAAIC,KAAQD,GAAKC,OACb1Y,KAAOyY,GAAKzY,MACZomB,KAAgB1N,GAAM6E,MAAM1G,WAC5B+H,KAAalG,GAAM6E,MAAM3G,QACzB0Q,KAAmB5O,GAAMwE,cAAcyK,iBACvCC,KAAoBzE,GAAezK,IAAO,EAC5CkL,gBAAgB,YAAA,CAAA,GAEdiE,KAAoB1E,GAAezK,IAAO,EAC5CoL,aAAAA,KAAa,CAAA,GAEXgE,KAA2BT,GAAeO,IAAmBxB,EAAAA,GAC7D2B,KAAsBV,GAAeQ,IAAmBjJ,IAAY0I,EAAAA,GACpEU,KAAoBT,GAAsBO,EAAAA,GAC1CG,KAAmBV,GAAsBQ,EAAAA;AAC7CrP,QAAAA,GAAMwE,cAAcld,EAAAA,IAAQ,EAC1B8nB,0BAA0BA,IAC1BC,qBAAqBA,IACrBC,mBAAmBA,IACnBC,kBAAkBA,GAAAA,GAEpBvP,GAAMtQ,WAAWwO,SAASlT,OAAOoV,OAAO,CAAA,GAAIJ,GAAMtQ,WAAWwO,QAAQ,EACnE,gCAAgCoR,IAChC,uBAAuBC,GAAAA,CAAAA;MAE3B,EAAA,GCJAC,KAAe,EACbloB,MAAM,UACNuY,SAAAA,MACAC,OAAO,QACPiB,UAAU,CAAC,eAAA,GACXtZ,IA5BF,SAAgB6Y,IAAAA;AACd,YAAIN,KAAQM,GAAMN,OACdS,KAAUH,GAAMG,SAChBnZ,KAAOgZ,GAAMhZ,MACbmoB,KAAkBhP,GAAQgF,QAC1BA,KAAAA,WAASgK,KAA6B,CAAC,GAAG,CAAA,IAAKA,IAC/Cjb,KAAOgK,GAAWH,OAAO,SAAUC,IAAKC,IAAAA;AAE1C,iBADAD,GAAIC,EAAAA,IA5BD,SAAiCA,IAAWsG,IAAOY,IAAAA;AACxD,gBAAIhB,KAAgBzD,GAAiBzC,EAAAA,GACjCmR,KAAiB,CAAC/R,IAAMH,EAAAA,EAAK7T,QAAQ8a,EAAAA,KAAkB,IAAA,KAAS,GAEhE1E,KAAyB,cAAA,OAAX0F,KAAwBA,GAAOza,OAAOoV,OAAO,CAAA,GAAIyE,IAAO,EACxEtG,WAAWA,GAAAA,CAAAA,CAAAA,IACPkH,IACFkK,KAAW5P,GAAK,CAAA,GAChB6P,KAAW7P,GAAK,CAAA;AAIpB,mBAFA4P,KAAWA,MAAY,GACvBC,MAAYA,MAAY,KAAKF,IACtB,CAAC/R,IAAMD,EAAAA,EAAO/T,QAAQ8a,EAAAA,KAAkB,IAAI,EACjDnC,GAAGsN,IACHpN,GAAGmN,GAAAA,IACD,EACFrN,GAAGqN,IACHnN,GAAGoN,GAAAA;UAEP,EAS6CrR,IAAWyB,GAAM6E,OAAOY,EAAAA,GAC1DnH;QACX,GAAK,CAAA,CAAA,GACCuR,KAAwBrb,GAAKwL,GAAMzB,SAAAA,GACnC+D,KAAIuN,GAAsBvN,GAC1BE,KAAIqN,GAAsBrN;AAEW,gBAArCxC,GAAMwE,cAAcD,kBACtBvE,GAAMwE,cAAcD,cAAcjC,KAAKA,IACvCtC,GAAMwE,cAAcD,cAAc/B,KAAKA,KAGzCxC,GAAMwE,cAAcld,EAAAA,IAAQkN;MAC9B,EAAA,GC1BAsb,KAAe,EACbxoB,MAAM,iBACNuY,SAAAA,MACAC,OAAO,QACPrY,IApBF,SAAuBsY,IAAAA;AACrB,YAAIC,KAAQD,GAAKC,OACb1Y,KAAOyY,GAAKzY;AAKhB0Y,QAAAA,GAAMwE,cAAcld,EAAAA,IAAQ+iB,GAAe,EACzClM,WAAW6B,GAAM6E,MAAM1G,WACvBnb,SAASgd,GAAM6E,MAAM3G,QACrBwC,UAAU,YACVnC,WAAWyB,GAAMzB,UAAAA,CAAAA;MAErB,GAQE/J,MAAM,CAAA,EAAA,GCgHRub,KAAe,EACbzoB,MAAM,mBACNuY,SAAAA,MACAC,OAAO,QACPrY,IA/HF,SAAyBsY,IAAAA;AACvB,YAAIC,KAAQD,GAAKC,OACbS,KAAUV,GAAKU,SACfnZ,KAAOyY,GAAKzY,MACZ0lB,KAAoBvM,GAAQ+J,UAC5ByC,KAAAA,WAAgBD,MAAsCA,IACtDE,KAAmBzM,GAAQ0M,SAC3BC,KAAAA,WAAeF,MAAsCA,IACrDpC,KAAWrK,GAAQqK,UACnBE,KAAevK,GAAQuK,cACvBI,KAAc3K,GAAQ2K,aACtBxG,KAAUnE,GAAQmE,SAClBoL,KAAkBvP,GAAQwP,QAC1BA,KAAAA,WAASD,MAAoCA,IAC7CE,KAAwBzP,GAAQ0P,cAChCA,KAAAA,WAAeD,KAAmC,IAAIA,IACtDhH,KAAWuB,GAAezK,IAAO,EACnC8K,UAAUA,IACVE,cAAcA,IACdpG,SAASA,IACTwG,aAAaA,GAAAA,CAAAA,GAEX3G,KAAgBzD,GAAiBhB,GAAMzB,SAAAA,GACvC4H,KAAYL,GAAa9F,GAAMzB,SAAAA,GAC/B6R,KAAAA,CAAmBjK,IACnBqE,KAAW5G,GAAyBa,EAAAA,GACpC0I,KCrCY,QDqCS3C,KCrCH,MAAM,KDsCxBjG,KAAgBvE,GAAMwE,cAAcD,eACpCmJ,KAAgB1N,GAAM6E,MAAM1G,WAC5B+H,KAAalG,GAAM6E,MAAM3G,QACzBmS,KAA4C,cAAA,OAAjBF,KAA8BA,GAAanlB,OAAOoV,OAAO,CAAA,GAAIJ,GAAM6E,OAAO,EACvGtG,WAAWyB,GAAMzB,UAAAA,CAAAA,CAAAA,IACb4R,IACFG,KAA2D,YAAA,OAAtBD,KAAiC,EACxE7F,UAAU6F,IACVlD,SAASkD,GAAAA,IACPrlB,OAAOoV,OAAO,EAChBoK,UAAU,GACV2C,SAAS,EAAA,GACRkD,EAAAA,GACCE,KAAsBvQ,GAAMwE,cAAciB,SAASzF,GAAMwE,cAAciB,OAAOzF,GAAMzB,SAAAA,IAAa,MACjG/J,KAAO,EACT8N,GAAG,GACHE,GAAG,EAAA;AAGL,YAAK+B,IAAL;AAIA,cAAI0I,IAAe;AACjB,gBAAIuD,IAEAC,KAAwB,QAAbjG,KAAmBhN,KAAMG,IACpC+S,KAAuB,QAAblG,KAAmB/M,KAASC,IACtCiH,KAAmB,QAAb6F,KAAmB,WAAW,SACpC/E,KAASlB,GAAciG,EAAAA,GACvB1gB,KAAM2b,KAASyD,GAASuH,EAAAA,GACxB5mB,KAAM4b,KAASyD,GAASwH,EAAAA,GACxBC,KAAWV,KAAAA,CAAU/J,GAAWvB,EAAAA,IAAO,IAAI,GAC3CiM,KAASzK,OAAcrI,KAAQ4P,GAAc/I,EAAAA,IAAOuB,GAAWvB,EAAAA,GAC/DkM,KAAS1K,OAAcrI,KAAAA,CAASoI,GAAWvB,EAAAA,IAAAA,CAAQ+I,GAAc/I,EAAAA,GAGjEL,KAAetE,GAAMC,SAASW,OAC9BkE,KAAYmL,MAAU3L,KAAe5B,GAAc4B,EAAAA,IAAgB,EACrEpC,OAAO,GACPC,QAAQ,EAAA,GAEN2O,KAAqB9Q,GAAMwE,cAAc,kBAAA,IAAsBxE,GAAMwE,cAAc,kBAAA,EAAoBI,UxBhFtG,EACLpH,KAAK,GACLE,OAAO,GACPD,QAAQ,GACRE,MAAM,EAAA,GwB6EFoT,KAAkBD,GAAmBL,EAAAA,GACrCO,KAAkBF,GAAmBJ,EAAAA,GAMrCO,KAAWpN,GAAO,GAAG6J,GAAc/I,EAAAA,GAAMG,GAAUH,EAAAA,CAAAA,GACnDuM,KAAYd,KAAkB1C,GAAc/I,EAAAA,IAAO,IAAIgM,KAAWM,KAAWF,KAAkBT,GAA4B9F,WAAWoG,KAASK,KAAWF,KAAkBT,GAA4B9F,UACxM2G,KAAYf,KAAAA,CAAmB1C,GAAc/I,EAAAA,IAAO,IAAIgM,KAAWM,KAAWD,KAAkBV,GAA4B9F,WAAWqG,KAASI,KAAWD,KAAkBV,GAA4B9F,UACzMrF,KAAoBnF,GAAMC,SAASW,SAASwC,GAAgBpD,GAAMC,SAASW,KAAAA,GAC3EwQ,KAAejM,KAAiC,QAAbqF,KAAmBrF,GAAkB6E,aAAa,IAAI7E,GAAkB8E,cAAc,IAAI,GAC7HoH,KAAwH,SAAjGb,KAA+C,QAAvBD,KAAAA,SAAuCA,GAAoB/F,EAAAA,KAAqBgG,KAAwB,GAEvJc,KAAY7L,KAAS0L,KAAYE,IACjCE,KAAkB1N,GAAOoM,KAASlM,GAAQja,IAF9B2b,KAASyL,KAAYG,KAAsBD,EAAAA,IAEKtnB,IAAK2b,IAAQwK,KAASnM,GAAQja,IAAKynB,EAAAA,IAAaznB,EAAAA;AAChH0a,YAAAA,GAAciG,EAAAA,IAAY+G,IAC1B/c,GAAKgW,EAAAA,IAAY+G,KAAkB9L;UACvC;AAEE,cAAI2H,IAAc;AAChB,gBAAIoE,IAEAC,KAAyB,QAAbjH,KAAmBhN,KAAMG,IAErC+T,MAAwB,QAAblH,KAAmB/M,KAASC,IAEvCiU,MAAUpN,GAAc4I,EAAAA,GAExByE,MAAmB,QAAZzE,KAAkB,WAAW,SAEpC0E,MAAOF,MAAUzI,GAASuI,EAAAA,GAE1BK,MAAOH,MAAUzI,GAASwI,GAAAA,GAE1BK,MAAAA,OAAe,CAACvU,IAAKG,EAAAA,EAAMhU,QAAQ8a,EAAAA,GAEnCuN,MAAyH,SAAjGR,KAAgD,QAAvBjB,KAAAA,SAAuCA,GAAoBpD,EAAAA,KAAoBqE,KAAyB,GAEzJS,MAAaF,MAAeF,MAAOF,MAAUjE,GAAckE,GAAAA,IAAQ1L,GAAW0L,GAAAA,IAAQI,MAAuB1B,GAA4BnD,SAEzI+E,MAAaH,MAAeJ,MAAUjE,GAAckE,GAAAA,IAAQ1L,GAAW0L,GAAAA,IAAQI,MAAuB1B,GAA4BnD,UAAU2E,KAE5IK,MAAmBlC,MAAU8B,M1BzH9B,SAAwBjoB,IAAK0E,IAAO3E,IAAAA;AACzC,kBAAIuoB,KAAIvO,GAAO/Z,IAAK0E,IAAO3E,EAAAA;AAC3B,qBAAOuoB,KAAIvoB,KAAMA,KAAMuoB;YACzB,E0BsHmEH,KAAYN,KAASO,GAAAA,IAAcrO,GAAOoM,KAASgC,MAAaJ,KAAMF,KAAS1B,KAASiC,MAAaJ,GAAAA;AAEpKvN,YAAAA,GAAc4I,EAAAA,IAAWgF,KACzB3d,GAAK2Y,EAAAA,IAAWgF,MAAmBR;UACvC;AAEE3R,UAAAA,GAAMwE,cAAcld,EAAAA,IAAQkN;QAvE9B;MAwEA,GAQEqR,kBAAkB,CAAC,QAAA,EAAA;AE1HN,eAASwM,GAAiBC,IAAyBnP,IAAcqD,IAAAA;AAAAA,mBAC1EA,OACFA,KAAAA;AAGF,YCnBoClH,ICJOtc,IFuBvCuvB,KAA0B9S,GAAc0D,EAAAA,GACxCqP,KAAuB/S,GAAc0D,EAAAA,KAf3C,SAAyBngB,IAAAA;AACvB,cAAI2mB,KAAO3mB,GAAQoa,sBAAAA,GACf2E,KAASd,GAAM0I,GAAKzH,KAAAA,IAASlf,GAAQif,eAAe,GACpDD,KAASf,GAAM0I,GAAKxH,MAAAA,IAAUnf,GAAQ2D,gBAAgB;AAC1D,iBAAkB,MAAXob,MAA2B,MAAXC;QACzB,EAU4EmB,EAAAA,GACtE/c,KAAkB2c,GAAmBI,EAAAA,GACrCwG,KAAOvM,GAAsBkV,IAAyBE,IAAsBhM,EAAAA,GAC5EwB,KAAS,EACXW,YAAY,GACZE,WAAW,EAAA,GAETzC,KAAU,EACZ9D,GAAG,GACHE,GAAG,EAAA;AAkBL,gBAfI+P,MAAAA,CAA4BA,MAAAA,CAA4B/L,SACxB,WAA9BrH,GAAYgE,EAAAA,KAChB6F,GAAe5iB,EAAAA,OACb4hB,MCnCgC1I,KDmCT6D,QClCd9D,GAAUC,EAAAA,KAAUG,GAAcH,EAAAA,ICJxC,EACLqJ,aAFyC3lB,KDQbsc,ICNRqJ,YACpBE,WAAW7lB,GAAQ6lB,UAAAA,IDGZH,GAAgBpJ,EAAAA,IDoCnBG,GAAc0D,EAAAA,MAChBiD,KAAUhJ,GAAsB+F,IAAAA,IAAc,GACtCb,KAAKa,GAAa8G,YAC1B7D,GAAQ5D,KAAKW,GAAa6G,aACjB5jB,OACTggB,GAAQ9D,IAAIyG,GAAoB3iB,EAAAA,KAI7B,EACLkc,GAAGqH,GAAKhM,OAAOqK,GAAOW,aAAavC,GAAQ9D,GAC3CE,GAAGmH,GAAKnM,MAAMwK,GAAOa,YAAYzC,GAAQ5D,GACzCN,OAAOyH,GAAKzH,OACZC,QAAQwH,GAAKxH,OAAAA;MAEjB;AGvDA,eAASzI,GAAM+Y,IAAAA;AACb,YAAIpgB,KAAM,oBAAIxP,OACV6vB,KAAU,oBAAIloB,OACdmoB,KAAS,CAAA;AAKb,iBAAShG,GAAKiG,IAAAA;AACZF,UAAAA,GAAQrc,IAAIuc,GAAStrB,IAAAA,GACN,CAAA,EAAGmL,OAAOmgB,GAAS7R,YAAY,CAAA,GAAI6R,GAAS/M,oBAAoB,CAAA,CAAA,EACtE3F,QAAQ,SAAU2S,IAAAA;AACzB,gBAAA,CAAKH,GAAQvvB,IAAI0vB,EAAAA,GAAM;AACrB,kBAAIC,KAAczgB,GAAIhP,IAAIwvB,EAAAA;AAEtBC,cAAAA,MACFnG,GAAKmG,EAAAA;YAEf;UACA,CAAA,GACIH,GAAO5qB,KAAK6qB,EAAAA;QAChB;AAQE,eAzBAH,GAAUvS,QAAQ,SAAU0S,IAAAA;AAC1BvgB,UAAAA,GAAItP,IAAI6vB,GAAStrB,MAAMsrB,EAAAA;QAC3B,CAAA,GAiBEH,GAAUvS,QAAQ,SAAU0S,IAAAA;AACrBF,UAAAA,GAAQvvB,IAAIyvB,GAAStrB,IAAAA,KAExBqlB,GAAKiG,EAAAA;QAEX,CAAA,GACSD;MACT;ACvBA,UAAII,KAAkB,EACpBxU,WAAW,UACXkU,WAAW,CAAA,GACX/R,UAAU,WAAA;AAGZ,eAASsS,KAAAA;AACP,iBAASpB,KAAOqB,UAAUluB,QAAQmD,KAAO,IAAIzE,MAAMmuB,EAAAA,GAAOsB,KAAO,GAAGA,KAAOtB,IAAMsB,KAC/EhrB,CAAAA,GAAKgrB,EAAAA,IAAQD,UAAUC,EAAAA;AAGzB,eAAA,CAAQhrB,GAAK4mB,KAAK,SAAU9rB,IAAAA;AAC1B,iBAAA,EAASA,MAAoD,cAAA,OAAlCA,GAAQoa;QACvC,CAAA;MACA;AAEO,eAAS+V,GAAgBC,IAAAA;AAAAA,mBAC1BA,OACFA,KAAmB,CAAA;AAGrB,YAAIC,KAAoBD,IACpBE,KAAwBD,GAAkBE,kBAC1CA,KAAAA,WAAmBD,KAAmC,CAAA,IAAKA,IAC3DE,KAAyBH,GAAkBI,gBAC3CA,KAAAA,WAAiBD,KAAoCT,KAAkBS;AAC3E,eAAO,SAAsBrV,IAAWD,IAAQuC,IAAAA;AAAAA,qBAC1CA,OACFA,KAAUgT;AAGZ,cCxC6BhsB,IAC3BisB,IDuCE1T,KAAQ,EACVzB,WAAW,UACXoV,kBAAkB,CAAA,GAClBlT,SAASzV,OAAOoV,OAAO,CAAA,GAAI2S,IAAiBU,EAAAA,GAC5CjP,eAAe,CAAA,GACfvE,UAAU,EACR9B,WAAWA,IACXD,QAAQA,GAAAA,GAEVxO,YAAY,CAAA,GACZyQ,QAAQ,CAAA,EAAA,GAENyT,KAAmB,CAAA,GACnBC,KAAAA,OACA3wB,KAAW,EACb8c,OAAOA,IACP8T,YAAY,SAAoBC,IAAAA;AAC9B,gBAAItT,KAAsC,cAAA,OAArBsT,KAAkCA,GAAiB/T,GAAMS,OAAAA,IAAWsT;AACzFC,YAAAA,GAAAA,GACAhU,GAAMS,UAAUzV,OAAOoV,OAAO,CAAA,GAAIqT,IAAgBzT,GAAMS,SAASA,EAAAA,GACjET,GAAMmI,gBAAgB,EACpBhK,WAAWzZ,GAAUyZ,EAAAA,IAAamL,GAAkBnL,EAAAA,IAAaA,GAAU0N,iBAAiBvC,GAAkBnL,GAAU0N,cAAAA,IAAkB,CAAA,GAC1I3N,QAAQoL,GAAkBpL,EAAAA,EAAAA;AAI5B,gBElE4BuU,IAC9BwB,IFiEMN,KDhCG,SAAwBlB,IAAAA;AAErC,kBAAIkB,KAAmBja,GAAM+Y,EAAAA;AAE7B,qBAAOvT,GAAeb,OAAO,SAAUC,IAAKwB,IAAAA;AAC1C,uBAAOxB,GAAI7L,OAAOkhB,GAAiB9jB,OAAO,SAAU+iB,IAAAA;AAClD,yBAAOA,GAAS9S,UAAUA;gBAChC,CAAA,CAAA;cACA,GAAK,CAAA,CAAA;YACL,GG3CoC2S,KFkEsB,CAAA,EAAGhgB,OAAO8gB,IAAkBvT,GAAMS,QAAQgS,SAAAA,GEjE9FwB,KAASxB,GAAUpU,OAAO,SAAU4V,IAAQC,IAAAA;AAC9C,kBAAIC,KAAWF,GAAOC,GAAQ5sB,IAAAA;AAK9B,qBAJA2sB,GAAOC,GAAQ5sB,IAAAA,IAAQ6sB,KAAWnpB,OAAOoV,OAAO,CAAA,GAAI+T,IAAUD,IAAS,EACrEzT,SAASzV,OAAOoV,OAAO,CAAA,GAAI+T,GAAS1T,SAASyT,GAAQzT,OAAAA,GACrDjM,MAAMxJ,OAAOoV,OAAO,CAAA,GAAI+T,GAAS3f,MAAM0f,GAAQ1f,IAAAA,EAAAA,CAAAA,IAC5C0f,IACED;YACX,GAAK,CAAA,CAAA,GAEIjpB,OAAOrH,KAAKswB,EAAAA,EAAQ5hB,IAAI,SAAUpP,IAAAA;AACvC,qBAAOgxB,GAAOhxB,EAAAA;YAClB,CAAA,EAAA;AF4DQ,mBAJA+c,GAAM2T,mBAAmBA,GAAiB9jB,OAAO,SAAUukB,IAAAA;AACzD,qBAAOA,GAAEvU;YACnB,CAAA,GA+FMG,GAAM2T,iBAAiBzT,QAAQ,SAAUH,IAAAA;AACvC,kBAAIzY,KAAOyY,GAAKzY,MACZ+sB,KAAetU,GAAKU,SACpBA,KAAAA,WAAU4T,KAA0B,CAAA,IAAKA,IACzChU,KAASN,GAAKM;AAElB,kBAAsB,cAAA,OAAXA,IAAuB;AAChC,oBAAIiU,KAAYjU,GAAO,EACrBL,OAAOA,IACP1Y,MAAMA,IACNpE,UAAUA,IACVud,SAASA,GAAAA,CAAAA;AAKXmT,gBAAAA,GAAiB7rB,KAAKusB,MAFT,WAAA;gBAAkB,CAAA;cAGzC;YACA,CAAA,GA/GepxB,GAASmlB,OAAAA;UACxB,GAMMkM,aAAa,WAAA;AACX,gBAAA,CAAIV,IAAJ;AAIA,kBAAIW,KAAkBxU,GAAMC,UACxB9B,KAAYqW,GAAgBrW,WAC5BD,KAASsW,GAAgBtW;AAG7B,kBAAK8U,GAAiB7U,IAAWD,EAAAA,GAAjC;AAKA8B,gBAAAA,GAAM6E,QAAQ,EACZ1G,WAAWkU,GAAiBlU,IAAWiF,GAAgBlF,EAAAA,GAAoC,YAA3B8B,GAAMS,QAAQC,QAAAA,GAC9ExC,QAAQwE,GAAcxE,EAAAA,EAAAA,GAOxB8B,GAAM0O,QAAAA,OACN1O,GAAMzB,YAAYyB,GAAMS,QAAQlC,WAKhCyB,GAAM2T,iBAAiBzT,QAAQ,SAAU0S,IAAAA;AACvC,yBAAO5S,GAAMwE,cAAcoO,GAAStrB,IAAAA,IAAQ0D,OAAOoV,OAAO,CAAA,GAAIwS,GAASpe,IAAAA;gBACjF,CAAA;AAEQ,yBAAS9K,KAAQ,GAAGA,KAAQsW,GAAM2T,iBAAiB5uB,QAAQ2E,KACzD,KAAA,SAAIsW,GAAM0O,OAAV;AAMA,sBAAI+F,KAAwBzU,GAAM2T,iBAAiBjqB,EAAAA,GAC/CjC,KAAKgtB,GAAsBhtB,IAC3BitB,KAAyBD,GAAsBhU,SAC/CiK,KAAAA,WAAWgK,KAAoC,CAAA,IAAKA,IACpDptB,KAAOmtB,GAAsBntB;AAEf,gCAAA,OAAPG,OACTuY,KAAQvY,GAAG,EACTuY,OAAOA,IACPS,SAASiK,IACTpjB,MAAMA,IACNpE,UAAUA,GAAAA,CAAAA,KACN8c;gBAdlB,MAHYA,CAAAA,GAAM0O,QAAAA,OACNhlB,KAAAA;cAzBZ;YATA;UAqDA,GAGM2e,SC1I2B5gB,KD0IV,WAAA;AACf,mBAAO,IAAIktB,QAAQ,SAAUC,IAAAA;AAC3B1xB,cAAAA,GAASqxB,YAAAA,GACTK,GAAQ5U,EAAAA;YAClB,CAAA;UACA,GC7IS,WAAA;AAUL,mBATK0T,OACHA,KAAU,IAAIiB,QAAQ,SAAUC,IAAAA;AAC9BD,sBAAQC,QAAAA,EAAUC,KAAK,WAAA;AACrBnB,gBAAAA,KAAAA,QACAkB,GAAQntB,GAAAA,CAAAA;cAClB,CAAA;YACA,CAAA,IAGWisB;UACX,IDmIMoB,SAAS,WAAA;AACPd,YAAAA,GAAAA,GACAH,KAAAA;UACR,EAAA;AAGI,cAAA,CAAKb,GAAiB7U,IAAWD,EAAAA,EAC/B,QAAOhb;AAmCT,mBAAS8wB,KAAAA;AACPJ,YAAAA,GAAiB1T,QAAQ,SAAUzY,IAAAA;AACjC,qBAAOA,GAAAA;YACf,CAAA,GACMmsB,KAAmB,CAAA;UACzB;AAEI,iBAvCA1wB,GAAS4wB,WAAWrT,EAAAA,EAASoU,KAAK,SAAU7U,IAAAA;AAAAA,aACrC6T,MAAepT,GAAQsU,iBAC1BtU,GAAQsU,cAAc/U,EAAAA;UAE9B,CAAA,GAmCW9c;QACX;MACA;AACO,UAAI8xB,KAA4B7B,GAAAA,GG9LnC6B,KAA4B7B,GAAgB,EAC9CI,kBAFqB,CAACzL,IAAgBvD,IAAe0Q,IAAeC,EAAAA,EAAAA,CAAAA,GCMlEF,KAA4B7B,GAAgB,EAC9CI,kBAFqB,CAACzL,IAAgBvD,IAAe0Q,IAAeC,IAAazP,IAAQ0P,IAAMlG,IAAiBrO,IAAOlE,EAAAA,EAAAA,CAAAA;AAAAA,YAAAA,KAAAA,OAAAA,OAAAA,OAAAA,eAAAA,EAAAA,WAAAA,MAAAA,WAAAA,IAAAA,WAAAA,IAAAA,YAAAA,IAAAA,aAAAA,IAAAA,OAAAA,IAAAA,MAAAA,IAAAA,gBAAAA,IAAAA,YAAAA,IAAAA,YAAAA,IAAAA,aAAAA,IAAAA,QAAAA,IAAAA,iBAAAA,IAAAA,eAAAA,IAAAA,cAAAA,IAAAA,kBAAAA,IAAAA,kBAAAA,IAAAA,gBAAAA,IAAAA,KAAAA,IAAAA,gBAAAA,IAAAA,MAAAA,IAAAA,MAAAA,IAAAA,MAAAA,IAAAA,MAAAA,IAAAA,gBAAAA,IAAAA,QAAAA,IAAAA,YAAAA,IAAAA,QAAAA,IAAAA,iBAAAA,IAAAA,eAAAA,IAAAA,iBAAAA,IAAAA,MAAAA,IAAAA,WAAAA,IAAAA,OAAAA,IAAAA,OAAAA,IAAAA,KAAAA,IAAAA,qBAAAA,IAAAA,UAAAA,IAAAA,OAAAA,GAAAA,GAAAA,OAAAA,aAAAA,EAAAA,OAAAA,SAAAA,CAAAA,CAAAA,GCkBnHnV,KAAO,YAEPoK,KAAa,gBACb8E,KAAe,aAIf2e,KAAe,WACfC,KAAiB,aAGjB5Z,KAAc,OAAM9J,EAAAA,IACpB+J,KAAgB,SAAQ/J,EAAAA,IACxB4J,KAAc,OAAM5J,EAAAA,IACpB6J,KAAe,QAAO7J,EAAAA,IACtB0F,KAAwB,QAAO1F,EAAAA,GAAY8E,EAAAA,IAC3C6e,KAA0B,UAAS3jB,EAAAA,GAAY8E,EAAAA,IAC/C8e,KAAwB,QAAO5jB,EAAAA,GAAY8E,EAAAA,IAE3CkF,KAAkB,QAOlBlH,KAAuB,6DACvB+gB,KAA8B,GAAE/gB,EAAAA,IAAwBkH,EAAAA,IACxD8Z,KAAgB,kBAKhBC,KAAgB1uB,EAAAA,IAAU,YAAY,aACtC2uB,KAAmB3uB,EAAAA,IAAU,cAAc,WAC3C4uB,KAAmB5uB,EAAAA,IAAU,eAAe,gBAC5C6uB,KAAsB7uB,EAAAA,IAAU,iBAAiB,cACjD8uB,KAAkB9uB,EAAAA,IAAU,eAAe,eAC3C+uB,KAAiB/uB,EAAAA,IAAU,gBAAgB,cAI3CkJ,KAAU,EACd8lB,WAAAA,MACAlL,UAAU,mBACVmL,SAAS,WACTxQ,QAAQ,CAAC,GAAG,CAAA,GACZyQ,cAAc,MACd/X,WAAW,SAAA,GAGPhO,KAAc,EAClB6lB,WAAW,oBACXlL,UAAU,oBACVmL,SAAS,UACTxQ,QAAQ,2BACRyQ,cAAc,0BACd/X,WAAW,0BAAA;MAOb,MAAMgY,WAAiB9kB,EAAAA;QACrBV,YAAY3N,IAASsN,IAAAA;AACnBgB,gBAAMtO,IAASsN,EAAAA,GAEftE,KAAKoqB,UAAU,MACfpqB,KAAKqqB,UAAUrqB,KAAKuF,SAAS7L,YAE7BsG,KAAKsqB,QAAQ9jB,EAAeY,KAAKpH,KAAKuF,UAAUkkB,EAAAA,EAAe,CAAA,KAC7DjjB,EAAeS,KAAKjH,KAAKuF,UAAUkkB,EAAAA,EAAe,CAAA,KAClDjjB,EAAeG,QAAQ8iB,IAAezpB,KAAKqqB,OAAAA,GAC7CrqB,KAAKuqB,YAAYvqB,KAAKwqB,cAAAA;QACxB;QAGA,WAAA,UAAWtmB;AACT,iBAAOA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAOA;QACT;QAEA,WAAA,OAAW5I;AACT,iBAAOA;QACT;QAGAoN,SAAAA;AACE,iBAAO3I,KAAKyQ,SAAAA,IAAazQ,KAAK0Q,KAAAA,IAAS1Q,KAAK2Q,KAAAA;QAC9C;QAEAA,OAAAA;AACE,cAAIhX,EAAWqG,KAAKuF,QAAAA,KAAavF,KAAKyQ,SAAAA,EACpC;AAGF,gBAAM5Q,KAAgB,EACpBA,eAAeG,KAAKuF,SAAAA;AAKtB,cAAA,CAFkBhF,EAAaoB,QAAQ3B,KAAKuF,UAAUgK,IAAY1P,EAAAA,EAEpDkC,kBAAd;AAUA,gBANA/B,KAAKyqB,cAAAA,GAMD,kBAAkBzxB,SAASoB,mBAAAA,CAAoB4F,KAAKqqB,QAAQ7wB,QAtFxC,aAAA,EAuFtB,YAAWxC,MAAW,CAAA,EAAGyP,OAAAA,GAAUzN,SAAS8B,KAAK8L,QAAAA,EAC/CrG,GAAaY,GAAGnK,IAAS,aAAayD,CAAAA;AAI1CuF,iBAAKuF,SAASmlB,MAAAA,GACd1qB,KAAKuF,SAASjC,aAAa,iBAAA,IAAiB,GAE5CtD,KAAKsqB,MAAMxwB,UAAUuQ,IAAIsF,EAAAA,GACzB3P,KAAKuF,SAASzL,UAAUuQ,IAAIsF,EAAAA,GAC5BpP,EAAaoB,QAAQ3B,KAAKuF,UAAUiK,IAAa3P,EAAAA;UAnBjD;QAoBF;QAEA6Q,OAAAA;AACE,cAAI/W,EAAWqG,KAAKuF,QAAAA,KAAAA,CAAcvF,KAAKyQ,SAAAA,EACrC;AAGF,gBAAM5Q,KAAgB,EACpBA,eAAeG,KAAKuF,SAAAA;AAGtBvF,eAAK2qB,cAAc9qB,EAAAA;QACrB;QAEA6F,UAAAA;AACM1F,eAAKoqB,WACPpqB,KAAKoqB,QAAQtB,QAAAA,GAGfxjB,MAAMI,QAAAA;QACR;QAEA2W,SAAAA;AACErc,eAAKuqB,YAAYvqB,KAAKwqB,cAAAA,GAClBxqB,KAAKoqB,WACPpqB,KAAKoqB,QAAQ/N,OAAAA;QAEjB;QAGAsO,cAAc9qB,IAAAA;AAEZ,cAAA,CADkBU,EAAaoB,QAAQ3B,KAAKuF,UAAUkK,IAAY5P,EAAAA,EACpDkC,kBAAd;AAMA,gBAAI,kBAAkB/I,SAASoB,gBAC7B,YAAWpD,MAAW,CAAA,EAAGyP,OAAAA,GAAUzN,SAAS8B,KAAK8L,QAAAA,EAC/CrG,GAAaC,IAAIxJ,IAAS,aAAayD,CAAAA;AAIvCuF,iBAAKoqB,WACPpqB,KAAKoqB,QAAQtB,QAAAA,GAGf9oB,KAAKsqB,MAAMxwB,UAAUlC,OAAO+X,EAAAA,GAC5B3P,KAAKuF,SAASzL,UAAUlC,OAAO+X,EAAAA,GAC/B3P,KAAKuF,SAASjC,aAAa,iBAAiB,OAAA,GAC5CF,EAAYG,oBAAoBvD,KAAKsqB,OAAO,QAAA,GAC5C/pB,EAAaoB,QAAQ3B,KAAKuF,UAAUmK,IAAc7P,EAAAA;UAlBlD;QAmBF;QAEAwE,WAAWC,IAAAA;AAGT,cAAgC,YAAA,QAFhCA,KAASgB,MAAMjB,WAAWC,EAAAA,GAER6N,aAAAA,CAA2BzZ,EAAU4L,GAAO6N,SAAAA,KACV,cAAA,OAA3C7N,GAAO6N,UAAUf,sBAGxB,OAAM,IAAIjM,UAAW,GAAE5J,GAAK6J,YAAAA,CAAAA,gGAAAA;AAG9B,iBAAOd;QACT;QAEAmmB,gBAAAA;AACE,cAAA,WAAWG,GACT,OAAM,IAAIzlB,UAAU,8DAAA;AAGtB,cAAI0lB,KAAmB7qB,KAAKuF;AAEG,uBAA3BvF,KAAKwF,QAAQ2M,YACf0Y,KAAmB7qB,KAAKqqB,UACf3xB,EAAUsH,KAAKwF,QAAQ2M,SAAAA,IAChC0Y,KAAmB/xB,EAAWkH,KAAKwF,QAAQ2M,SAAAA,IACA,YAAA,OAA3BnS,KAAKwF,QAAQ2M,cAC7B0Y,KAAmB7qB,KAAKwF,QAAQ2M;AAGlC,gBAAM+X,KAAelqB,KAAK8qB,iBAAAA;AAC1B9qB,eAAKoqB,UAAUQ,GAAoBC,IAAkB7qB,KAAKsqB,OAAOJ,EAAAA;QACnE;QAEAzZ,WAAAA;AACE,iBAAOzQ,KAAKsqB,MAAMxwB,UAAUC,SAAS4V,EAAAA;QACvC;QAEAob,gBAAAA;AACE,gBAAMC,KAAiBhrB,KAAKqqB;AAE5B,cAAIW,GAAelxB,UAAUC,SAzMN,SAAA,EA0MrB,QAAO+vB;AAGT,cAAIkB,GAAelxB,UAAUC,SA5MJ,WAAA,EA6MvB,QAAOgwB;AAGT,cAAIiB,GAAelxB,UAAUC,SA/MA,eAAA,EAgN3B,QAhMsB;AAmMxB,cAAIixB,GAAelxB,UAAUC,SAlNE,iBAAA,EAmN7B,QAnMyB;AAuM3B,gBAAMkxB,KAAkF,UAA1E5xB,iBAAiB2G,KAAKsqB,KAAAA,EAAOhxB,iBAAiB,eAAA,EAAiB8M,KAAAA;AAE7E,iBAAI4kB,GAAelxB,UAAUC,SA7NP,QAAA,IA8NbkxB,KAAQtB,KAAmBD,KAG7BuB,KAAQpB,KAAsBD;QACvC;QAEAY,gBAAAA;AACE,iBAAkD,SAA3CxqB,KAAKuF,SAAS/L,QA5ND,SAAA;QA6NtB;QAEA0xB,aAAAA;AACE,gBAAA,EAAMzR,QAAEA,GAAAA,IAAWzZ,KAAKwF;AAExB,iBAAsB,YAAA,OAAXiU,KACFA,GAAO3c,MAAM,GAAA,EAAKuJ,IAAI7D,CAAAA,OAAS7F,OAAO4R,SAAS/L,IAAO,EAAA,CAAA,IAGzC,cAAA,OAAXiX,KACF0R,CAAAA,OAAc1R,GAAO0R,IAAYnrB,KAAKuF,QAAAA,IAGxCkU;QACT;QAEAqR,mBAAAA;AACE,gBAAMM,KAAwB,EAC5B7Y,WAAWvS,KAAK+qB,cAAAA,GAChBtE,WAAW,CAAC,EACVnrB,MAAM,mBACNmZ,SAAS,EACPqK,UAAU9e,KAAKwF,QAAQsZ,SAAAA,EAAAA,GAG3B,EACExjB,MAAM,UACNmZ,SAAS,EACPgF,QAAQzZ,KAAKkrB,WAAAA,EAAAA,EAAAA,CAAAA,EAAAA;AAcnB,kBARIlrB,KAAKuqB,aAAsC,aAAzBvqB,KAAKwF,QAAQykB,aACjC7mB,EAAYC,iBAAiBrD,KAAKsqB,OAAO,UAAU,QAAA,GACnDc,GAAsB3E,YAAY,CAAC,EACjCnrB,MAAM,eACNuY,SAAAA,MAAS,CAAA,IAIN,EAAA,GACFuX,IAAAA,GACApvB,EAAQgE,KAAKwF,QAAQ0kB,cAAc,CAACkB,EAAAA,CAAAA,EAAAA;QAE3C;QAEAC,gBAAAA,EAAgBp0B,KAAEA,IAAGgG,QAAEA,GAAAA,GAAAA;AACrB,gBAAMoQ,KAAQ7G,EAAetH,KA5QF,+DA4Q+Bc,KAAKsqB,KAAAA,EAAOzmB,OAAO7M,CAAAA,OAAWkC,EAAUlC,EAAAA,CAAAA;AAE7FqW,UAAAA,GAAMtU,UAMXqE,EAAqBiQ,IAAOpQ,IAAQhG,OAAQoyB,IAAAA,CAAiBhc,GAAMnM,SAASjE,EAAAA,CAAAA,EAASytB,MAAAA;QACvF;QAGA,OAAA,gBAAuBpmB,IAAAA;AACrB,iBAAOtE,KAAKuI,KAAK,WAAA;AACf,kBAAMC,KAAO2hB,GAASliB,oBAAoBjI,MAAMsE,EAAAA;AAEhD,gBAAsB,YAAA,OAAXA,IAAX;AAIA,kBAAA,WAAWkE,GAAKlE,EAAAA,EACd,OAAM,IAAIa,UAAW,oBAAmBb,EAAAA,GAAAA;AAG1CkE,cAAAA,GAAKlE,EAAAA,EAAAA;YANL;UAOF,CAAA;QACF;QAEA,OAAA,WAAkBnF,IAAAA;AAChB,cA/TuB,MA+TnBA,GAAMyJ,UAAiD,YAAfzJ,GAAMsB,QAlUtC,UAkU0DtB,GAAMlI,IAC1E;AAGF,gBAAMq0B,KAAc9kB,EAAetH,KAAKsqB,EAAAA;AAExC,qBAAW7gB,MAAU2iB,IAAa;AAChC,kBAAMC,KAAUpB,GAASnkB,YAAY2C,EAAAA;AACrC,gBAAA,CAAK4iB,MAAAA,UAAWA,GAAQ/lB,QAAQwkB,UAC9B;AAGF,kBAAMwB,KAAersB,GAAMqsB,aAAAA,GACrBC,KAAeD,GAAatqB,SAASqqB,GAAQjB,KAAAA;AACnD,gBACEkB,GAAatqB,SAASqqB,GAAQhmB,QAAAA,KACC,aAA9BgmB,GAAQ/lB,QAAQwkB,aAAAA,CAA2ByB,MACb,cAA9BF,GAAQ/lB,QAAQwkB,aAA2ByB,GAE5C;AAIF,gBAAIF,GAAQjB,MAAMvwB,SAASoF,GAAMlC,MAAAA,MAA4B,YAAfkC,GAAMsB,QAzV1C,UAyV8DtB,GAAMlI,OAAoB,qCAAqCiO,KAAK/F,GAAMlC,OAAO+K,OAAAA,GACvJ;AAGF,kBAAMnI,KAAgB,EAAEA,eAAe0rB,GAAQhmB,SAAAA;AAE5B,wBAAfpG,GAAMsB,SACRZ,GAAckI,aAAa5I,KAG7BosB,GAAQZ,cAAc9qB,EAAAA;UACxB;QACF;QAEA,OAAA,sBAA6BV,IAAAA;AAI3B,gBAAMusB,KAAU,kBAAkBxmB,KAAK/F,GAAMlC,OAAO+K,OAAAA,GAC9C2jB,KA7WS,aA6WOxsB,GAAMlI,KACtB20B,KAAkB,CAACxC,IAAcC,EAAAA,EAAgBnoB,SAAS/B,GAAMlI,GAAAA;AAEtE,cAAA,CAAK20B,MAAAA,CAAoBD,GACvB;AAGF,cAAID,MAAAA,CAAYC,GACd;AAGFxsB,UAAAA,GAAMkD,eAAAA;AAGN,gBAAMwpB,KAAkB7rB,KAAK8G,QAAQ2B,EAAAA,IACnCzI,OACCwG,EAAeS,KAAKjH,MAAMyI,EAAAA,EAAsB,CAAA,KAC/CjC,EAAeY,KAAKpH,MAAMyI,EAAAA,EAAsB,CAAA,KAChDjC,EAAeG,QAAQ8B,IAAsBtJ,GAAMW,eAAepG,UAAAA,GAEhExC,KAAWizB,GAASliB,oBAAoB4jB,EAAAA;AAE9C,cAAID,GAIF,QAHAzsB,GAAM2sB,gBAAAA,GACN50B,GAASyZ,KAAAA,GAAAA,KACTzZ,GAASm0B,gBAAgBlsB,EAAAA;AAIvBjI,UAAAA,GAASuZ,SAAAA,MACXtR,GAAM2sB,gBAAAA,GACN50B,GAASwZ,KAAAA,GACTmb,GAAgBnB,MAAAA;QAEpB;MAAA;AAOFnqB,QAAaY,GAAGnI,UAAUswB,IAAwB7gB,IAAsB0hB,GAAS4B,qBAAAA,GACjFxrB,EAAaY,GAAGnI,UAAUswB,IAAwBG,IAAeU,GAAS4B,qBAAAA,GAC1ExrB,EAAaY,GAAGnI,UAAUqS,IAAsB8e,GAAS6B,UAAAA,GACzDzrB,EAAaY,GAAGnI,UAAUuwB,IAAsBY,GAAS6B,UAAAA,GACzDzrB,EAAaY,GAAGnI,UAAUqS,IAAsB5C,IAAsB,SAAUtJ,IAAAA;AAC9EA,QAAAA,GAAMkD,eAAAA,GACN8nB,GAASliB,oBAAoBjI,IAAAA,EAAM2I,OAAAA;MACrC,CAAA,GAMAzN,EAAmBivB,EAAAA;ACnbnB,YAAM5uB,KAAO,YAEPoU,KAAkB,QAClBsc,KAAmB,gBAAe1wB,EAAAA,IAElC2I,KAAU,EACdgoB,WAAW,kBACXC,eAAe,MACfpmB,YAAAA,OACA7M,WAAAA,MACAkzB,aAAa,OAAA,GAGTjoB,KAAc,EAClB+nB,WAAW,UACXC,eAAe,mBACfpmB,YAAY,WACZ7M,WAAW,WACXkzB,aAAa,mBAAA;MAOf,MAAMC,WAAiBpoB,EAAAA;QACrBU,YAAYL,IAAAA;AACVgB,gBAAAA,GACAtF,KAAKwF,UAAUxF,KAAKqE,WAAWC,EAAAA,GAC/BtE,KAAKssB,cAAAA,OACLtsB,KAAKuF,WAAW;QAClB;QAGA,WAAA,UAAWrB;AACT,iBAAOA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAOA;QACT;QAEA,WAAA,OAAW5I;AACT,iBAAOA;QACT;QAGAoV,KAAKvV,IAAAA;AACH,cAAA,CAAK4E,KAAKwF,QAAQtM,UAEhB,QAAA,KADA8C,EAAQZ,EAAAA;AAIV4E,eAAKusB,QAAAA;AAEL,gBAAMv1B,KAAUgJ,KAAKwsB,YAAAA;AACjBxsB,eAAKwF,QAAQO,cACfrL,EAAO1D,EAAAA,GAGTA,GAAQ8C,UAAUuQ,IAAIsF,EAAAA,GAEtB3P,KAAKysB,kBAAkB,MAAA;AACrBzwB,cAAQZ,EAAAA;UAAS,CAAA;QAErB;QAEAsV,KAAKtV,IAAAA;AACE4E,eAAKwF,QAAQtM,aAKlB8G,KAAKwsB,YAAAA,EAAc1yB,UAAUlC,OAAO+X,EAAAA,GAEpC3P,KAAKysB,kBAAkB,MAAA;AACrBzsB,iBAAK0F,QAAAA,GACL1J,EAAQZ,EAAAA;UAAS,CAAA,KARjBY,EAAQZ,EAAAA;QAUZ;QAEAsK,UAAAA;AACO1F,eAAKssB,gBAIV/rB,EAAaC,IAAIR,KAAKuF,UAAU0mB,EAAAA,GAEhCjsB,KAAKuF,SAAS3N,OAAAA,GACdoI,KAAKssB,cAAAA;QACP;QAGAE,cAAAA;AACE,cAAA,CAAKxsB,KAAKuF,UAAU;AAClB,kBAAMmnB,KAAW1zB,SAAS2zB,cAAc,KAAA;AACxCD,YAAAA,GAASR,YAAYlsB,KAAKwF,QAAQ0mB,WAC9BlsB,KAAKwF,QAAQO,cACf2mB,GAAS5yB,UAAUuQ,IAjGH,MAAA,GAoGlBrK,KAAKuF,WAAWmnB;UAClB;AAEA,iBAAO1sB,KAAKuF;QACd;QAEAf,kBAAkBF,IAAAA;AAGhB,iBADAA,GAAO8nB,cAActzB,EAAWwL,GAAO8nB,WAAAA,GAChC9nB;QACT;QAEAioB,UAAAA;AACE,cAAIvsB,KAAKssB,YACP;AAGF,gBAAMt1B,KAAUgJ,KAAKwsB,YAAAA;AACrBxsB,eAAKwF,QAAQ4mB,YAAYQ,OAAO51B,EAAAA,GAEhCuJ,EAAaY,GAAGnK,IAASi1B,IAAiB,MAAA;AACxCjwB,cAAQgE,KAAKwF,QAAQ2mB,aAAAA;UAAc,CAAA,GAGrCnsB,KAAKssB,cAAAA;QACP;QAEAG,kBAAkBrxB,IAAAA;AAChBgB,YAAuBhB,IAAU4E,KAAKwsB,YAAAA,GAAexsB,KAAKwF,QAAQO,UAAAA;QACpE;MAAA;ACpIF,YAEMJ,KAAa,iBACbknB,KAAiB,UAASlnB,EAAAA,IAC1BmnB,KAAqB,cAAannB,EAAAA,IAIlConB,KAAmB,YAEnB7oB,KAAU,EACd8oB,WAAAA,MACAC,aAAa,KAAA,GAGT9oB,KAAc,EAClB6oB,WAAW,WACXC,aAAa,UAAA;MAOf,MAAMC,WAAkBjpB,EAAAA;QACtBU,YAAYL,IAAAA;AACVgB,gBAAAA,GACAtF,KAAKwF,UAAUxF,KAAKqE,WAAWC,EAAAA,GAC/BtE,KAAKmtB,YAAAA,OACLntB,KAAKotB,uBAAuB;QAC9B;QAGA,WAAA,UAAWlpB;AACT,iBAAOA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAOA;QACT;QAEA,WAAA,OAAW5I;AACT,iBA1CS;QA2CX;QAGA8xB,WAAAA;AACMrtB,eAAKmtB,cAILntB,KAAKwF,QAAQwnB,aACfhtB,KAAKwF,QAAQynB,YAAYvC,MAAAA,GAG3BnqB,EAAaC,IAAIxH,UAAU2M,EAAAA,GAC3BpF,EAAaY,GAAGnI,UAAU6zB,IAAe1tB,CAAAA,OAASa,KAAKstB,eAAenuB,EAAAA,CAAAA,GACtEoB,EAAaY,GAAGnI,UAAU8zB,IAAmB3tB,CAAAA,OAASa,KAAKutB,eAAepuB,EAAAA,CAAAA,GAE1Ea,KAAKmtB,YAAAA;QACP;QAEAK,aAAAA;AACOxtB,eAAKmtB,cAIVntB,KAAKmtB,YAAAA,OACL5sB,EAAaC,IAAIxH,UAAU2M,EAAAA;QAC7B;QAGA2nB,eAAenuB,IAAAA;AACb,gBAAA,EAAM8tB,aAAEA,GAAAA,IAAgBjtB,KAAKwF;AAE7B,cAAIrG,GAAMlC,WAAWjE,YAAYmG,GAAMlC,WAAWgwB,MAAeA,GAAYlzB,SAASoF,GAAMlC,MAAAA,EAC1F;AAGF,gBAAMgX,KAAWzN,EAAec,kBAAkB2lB,EAAAA;AAE1B,gBAApBhZ,GAASlb,SACXk0B,GAAYvC,MAAAA,IACH1qB,KAAKotB,yBAAyBL,KACvC9Y,GAASA,GAASlb,SAAS,CAAA,EAAG2xB,MAAAA,IAE9BzW,GAAS,CAAA,EAAGyW,MAAAA;QAEhB;QAEA6C,eAAepuB,IAAAA;AApFD,oBAqFRA,GAAMlI,QAIV+I,KAAKotB,uBAAuBjuB,GAAMsuB,WAAWV,KAxFzB;QAyFtB;MAAA;AChGF,YAAMW,KAAyB,qDACzBC,KAA0B,eAC1BC,KAAmB,iBACnBC,KAAkB;MAMxB,MAAMC,GAAAA;QACJnpB,cAAAA;AACE3E,eAAKuF,WAAWvM,SAAS8B;QAC3B;QAGAizB,WAAAA;AAEE,gBAAMC,KAAgBh1B,SAASoB,gBAAgBkf;AAC/C,iBAAO1b,KAAKuM,IAAIlS,OAAOg2B,aAAaD,EAAAA;QACtC;QAEAtd,OAAAA;AACE,gBAAMwF,KAAQlW,KAAK+tB,SAAAA;AACnB/tB,eAAKkuB,iBAAAA,GAELluB,KAAKmuB,sBAAsBnuB,KAAKuF,UAAUqoB,IAAkBQ,CAAAA,OAAmBA,KAAkBlY,EAAAA,GAEjGlW,KAAKmuB,sBAAsBT,IAAwBE,IAAkBQ,CAAAA,OAAmBA,KAAkBlY,EAAAA,GAC1GlW,KAAKmuB,sBAAsBR,IAAyBE,IAAiBO,CAAAA,OAAmBA,KAAkBlY,EAAAA;QAC5G;QAEAwM,QAAAA;AACE1iB,eAAKquB,wBAAwBruB,KAAKuF,UAAU,UAAA,GAC5CvF,KAAKquB,wBAAwBruB,KAAKuF,UAAUqoB,EAAAA,GAC5C5tB,KAAKquB,wBAAwBX,IAAwBE,EAAAA,GACrD5tB,KAAKquB,wBAAwBV,IAAyBE,EAAAA;QACxD;QAEAS,gBAAAA;AACE,iBAAOtuB,KAAK+tB,SAAAA,IAAa;QAC3B;QAGAG,mBAAAA;AACEluB,eAAKuuB,sBAAsBvuB,KAAKuF,UAAU,UAAA,GAC1CvF,KAAKuF,SAAS0L,MAAMiM,WAAW;QACjC;QAEAiR,sBAAsBn2B,IAAUw2B,IAAepzB,IAAAA;AAC7C,gBAAMqzB,KAAiBzuB,KAAK+tB,SAAAA;AAW5B/tB,eAAK0uB,2BAA2B12B,IAVHhB,CAAAA,OAAAA;AAC3B,gBAAIA,OAAYgJ,KAAKuF,YAAYtN,OAAOg2B,aAAaj3B,GAAQsiB,cAAcmV,GACzE;AAGFzuB,iBAAKuuB,sBAAsBv3B,IAASw3B,EAAAA;AACpC,kBAAMJ,KAAkBn2B,OAAOoB,iBAAiBrC,EAAAA,EAASsC,iBAAiBk1B,EAAAA;AAC1Ex3B,YAAAA,GAAQia,MAAM0d,YAAYH,IAAgB,GAAEpzB,GAASuB,OAAOC,WAAWwxB,EAAAA,CAAAA,CAAAA,IAAAA;UAAsB,CAAA;QAIjG;QAEAG,sBAAsBv3B,IAASw3B,IAAAA;AAC7B,gBAAMI,KAAc53B,GAAQia,MAAM3X,iBAAiBk1B,EAAAA;AAC/CI,UAAAA,MACFxrB,EAAYC,iBAAiBrM,IAASw3B,IAAeI,EAAAA;QAEzD;QAEAP,wBAAwBr2B,IAAUw2B,IAAAA;AAahCxuB,eAAK0uB,2BAA2B12B,IAZHhB,CAAAA,OAAAA;AAC3B,kBAAMwL,KAAQY,EAAYY,iBAAiBhN,IAASw3B,EAAAA;AAEtC,qBAAVhsB,MAKJY,EAAYG,oBAAoBvM,IAASw3B,EAAAA,GACzCx3B,GAAQia,MAAM0d,YAAYH,IAAehsB,EAAAA,KALvCxL,GAAQia,MAAM4d,eAAeL,EAAAA;UAKgB,CAAA;QAInD;QAEAE,2BAA2B12B,IAAU82B,IAAAA;AACnC,cAAIp2B,EAAUV,EAAAA,EACZ82B,CAAAA,GAAS92B,EAAAA;cAIX,YAAWsO,MAAOE,EAAetH,KAAKlH,IAAUgI,KAAKuF,QAAAA,EACnDupB,CAAAA,GAASxoB,EAAAA;QAEb;MAAA;ACxFF,YAEMX,KAAa,aAIb8J,KAAc,OAAM9J,EAAAA,IACpBopB,KAAwB,gBAAeppB,EAAAA,IACvC+J,KAAgB,SAAQ/J,EAAAA,IACxB4J,KAAc,OAAM5J,EAAAA,IACpB6J,KAAe,QAAO7J,EAAAA,IACtBqpB,KAAgB,SAAQrpB,EAAAA,IACxBspB,KAAuB,gBAAetpB,EAAAA,IACtCupB,KAA2B,oBAAmBvpB,EAAAA,IAC9CwpB,KAAyB,kBAAiBxpB,EAAAA,IAC1C0F,KAAwB,QAAO1F,EAAAA,aAE/BypB,KAAkB,cAElBzf,KAAkB,QAClB0f,KAAoB,gBAOpBnrB,KAAU,EACdwoB,UAAAA,MACAhC,OAAAA,MACA3e,UAAAA,KAAU,GAGN5H,KAAc,EAClBuoB,UAAU,oBACVhC,OAAO,WACP3e,UAAU,UAAA;MAOZ,MAAMujB,WAAcjqB,EAAAA;QAClBV,YAAY3N,IAASsN,IAAAA;AACnBgB,gBAAMtO,IAASsN,EAAAA,GAEftE,KAAKuvB,UAAU/oB,EAAeG,QAxBV,iBAwBmC3G,KAAKuF,QAAAA,GAC5DvF,KAAKwvB,YAAYxvB,KAAKyvB,oBAAAA,GACtBzvB,KAAK0vB,aAAa1vB,KAAK2vB,qBAAAA,GACvB3vB,KAAKyQ,WAAAA,OACLzQ,KAAKiQ,mBAAAA,OACLjQ,KAAK4vB,aAAa,IAAI9B,MAEtB9tB,KAAK2M,mBAAAA;QACP;QAGA,WAAA,UAAWzI;AACT,iBAAOA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAOA;QACT;QAEA,WAAA,OAAW5I;AACT,iBAnES;QAoEX;QAGAoN,OAAO9I,IAAAA;AACL,iBAAOG,KAAKyQ,WAAWzQ,KAAK0Q,KAAAA,IAAS1Q,KAAK2Q,KAAK9Q,EAAAA;QACjD;QAEA8Q,KAAK9Q,IAAAA;AACCG,eAAKyQ,YAAYzQ,KAAKiQ,oBAIR1P,EAAaoB,QAAQ3B,KAAKuF,UAAUgK,IAAY,EAChE1P,eAAAA,GAAAA,CAAAA,EAGYkC,qBAId/B,KAAKyQ,WAAAA,MACLzQ,KAAKiQ,mBAAAA,MAELjQ,KAAK4vB,WAAWlf,KAAAA,GAEhB1X,SAAS8B,KAAKhB,UAAUuQ,IAAI+kB,EAAAA,GAE5BpvB,KAAK6vB,cAAAA,GAEL7vB,KAAKwvB,UAAU7e,KAAK,MAAM3Q,KAAK8vB,aAAajwB,EAAAA,CAAAA;QAC9C;QAEA6Q,OAAAA;AACO1Q,eAAKyQ,YAAAA,CAAYzQ,KAAKiQ,qBAIT1P,EAAaoB,QAAQ3B,KAAKuF,UAAUkK,EAAAA,EAExC1N,qBAId/B,KAAKyQ,WAAAA,OACLzQ,KAAKiQ,mBAAAA,MACLjQ,KAAK0vB,WAAWlC,WAAAA,GAEhBxtB,KAAKuF,SAASzL,UAAUlC,OAAO+X,EAAAA,GAE/B3P,KAAK8F,eAAe,MAAM9F,KAAK+vB,WAAAA,GAAc/vB,KAAKuF,UAAUvF,KAAKkP,YAAAA,CAAAA;QACnE;QAEAxJ,UAAAA;AACEnF,YAAaC,IAAIvI,QAAQ0N,EAAAA,GACzBpF,EAAaC,IAAIR,KAAKuvB,SAAS5pB,EAAAA,GAE/B3F,KAAKwvB,UAAU9pB,QAAAA,GACf1F,KAAK0vB,WAAWlC,WAAAA,GAEhBloB,MAAMI,QAAAA;QACR;QAEAsqB,eAAAA;AACEhwB,eAAK6vB,cAAAA;QACP;QAGAJ,sBAAAA;AACE,iBAAO,IAAIpD,GAAS,EAClBnzB,WAAW0H,QAAQZ,KAAKwF,QAAQknB,QAAAA,GAChC3mB,YAAY/F,KAAKkP,YAAAA,EAAAA,CAAAA;QAErB;QAEAygB,uBAAAA;AACE,iBAAO,IAAIzC,GAAU,EACnBD,aAAajtB,KAAKuF,SAAAA,CAAAA;QAEtB;QAEAuqB,aAAajwB,IAAAA;AAEN7G,mBAAS8B,KAAKf,SAASiG,KAAKuF,QAAAA,KAC/BvM,SAAS8B,KAAK8xB,OAAO5sB,KAAKuF,QAAAA,GAG5BvF,KAAKuF,SAAS0L,MAAMgZ,UAAU,SAC9BjqB,KAAKuF,SAAS/B,gBAAgB,aAAA,GAC9BxD,KAAKuF,SAASjC,aAAa,cAAA,IAAc,GACzCtD,KAAKuF,SAASjC,aAAa,QAAQ,QAAA,GACnCtD,KAAKuF,SAASsX,YAAY;AAE1B,gBAAMoT,KAAYzpB,EAAeG,QAxIT,eAwIsC3G,KAAKuvB,OAAAA;AAC/DU,UAAAA,OACFA,GAAUpT,YAAY,IAGxBniB,EAAOsF,KAAKuF,QAAAA,GAEZvF,KAAKuF,SAASzL,UAAUuQ,IAAIsF,EAAAA,GAa5B3P,KAAK8F,eAXsBoqB,MAAAA;AACrBlwB,iBAAKwF,QAAQklB,SACf1qB,KAAK0vB,WAAWrC,SAAAA,GAGlBrtB,KAAKiQ,mBAAAA,OACL1P,EAAaoB,QAAQ3B,KAAKuF,UAAUiK,IAAa,EAC/C3P,eAAAA,GAAAA,CAAAA;UACA,GAGoCG,KAAKuvB,SAASvvB,KAAKkP,YAAAA,CAAAA;QAC7D;QAEAvC,qBAAAA;AACEpM,YAAaY,GAAGnB,KAAKuF,UAAU4pB,IAAuBhwB,CAAAA,OAAAA;AApLvC,yBAqLTA,GAAMlI,QAIN+I,KAAKwF,QAAQuG,WACf/L,KAAK0Q,KAAAA,IAIP1Q,KAAKmwB,2BAAAA;UAA4B,CAAA,GAGnC5vB,EAAaY,GAAGlJ,QAAQ+2B,IAAc,MAAA;AAChChvB,iBAAKyQ,YAAAA,CAAazQ,KAAKiQ,oBACzBjQ,KAAK6vB,cAAAA;UACP,CAAA,GAGFtvB,EAAaY,GAAGnB,KAAKuF,UAAU2pB,IAAyB/vB,CAAAA,OAAAA;AAEtDoB,cAAaa,IAAIpB,KAAKuF,UAAU0pB,IAAqBmB,CAAAA,OAAAA;AAC/CpwB,mBAAKuF,aAAapG,GAAMlC,UAAU+C,KAAKuF,aAAa6qB,GAAOnzB,WAIjC,aAA1B+C,KAAKwF,QAAQknB,WAKb1sB,KAAKwF,QAAQknB,YACf1sB,KAAK0Q,KAAAA,IALL1Q,KAAKmwB,2BAAAA;YAMP,CAAA;UACA,CAAA;QAEN;QAEAJ,aAAAA;AACE/vB,eAAKuF,SAAS0L,MAAMgZ,UAAU,QAC9BjqB,KAAKuF,SAASjC,aAAa,eAAA,IAAe,GAC1CtD,KAAKuF,SAAS/B,gBAAgB,YAAA,GAC9BxD,KAAKuF,SAAS/B,gBAAgB,MAAA,GAC9BxD,KAAKiQ,mBAAAA,OAELjQ,KAAKwvB,UAAU9e,KAAK,MAAA;AAClB1X,qBAAS8B,KAAKhB,UAAUlC,OAAOw3B,EAAAA,GAC/BpvB,KAAKqwB,kBAAAA,GACLrwB,KAAK4vB,WAAWlN,MAAAA,GAChBniB,EAAaoB,QAAQ3B,KAAKuF,UAAUmK,EAAAA;UAAa,CAAA;QAErD;QAEAR,cAAAA;AACE,iBAAOlP,KAAKuF,SAASzL,UAAUC,SA5NX,MAAA;QA6NtB;QAEAo2B,6BAAAA;AAEE,cADkB5vB,EAAaoB,QAAQ3B,KAAKuF,UAAUwpB,EAAAA,EACxChtB,iBACZ;AAGF,gBAAMuuB,KAAqBtwB,KAAKuF,SAAS6Y,eAAeplB,SAASoB,gBAAgBif,cAC3EkX,KAAmBvwB,KAAKuF,SAAS0L,MAAMmM;AAEpB,uBAArBmT,MAAiCvwB,KAAKuF,SAASzL,UAAUC,SAASs1B,EAAAA,MAIjEiB,OACHtwB,KAAKuF,SAAS0L,MAAMmM,YAAY,WAGlCpd,KAAKuF,SAASzL,UAAUuQ,IAAIglB,EAAAA,GAC5BrvB,KAAK8F,eAAe,MAAA;AAClB9F,iBAAKuF,SAASzL,UAAUlC,OAAOy3B,EAAAA,GAC/BrvB,KAAK8F,eAAe,MAAA;AAClB9F,mBAAKuF,SAAS0L,MAAMmM,YAAYmT;YAAgB,GAC/CvwB,KAAKuvB,OAAAA;UAAQ,GACfvvB,KAAKuvB,OAAAA,GAERvvB,KAAKuF,SAASmlB,MAAAA;QAChB;QAMAmF,gBAAAA;AACE,gBAAMS,KAAqBtwB,KAAKuF,SAAS6Y,eAAeplB,SAASoB,gBAAgBif,cAC3EoV,KAAiBzuB,KAAK4vB,WAAW7B,SAAAA,GACjCyC,KAAoB/B,KAAiB;AAE3C,cAAI+B,MAAAA,CAAsBF,IAAoB;AAC5C,kBAAMzrB,KAAW7J,EAAAA,IAAU,gBAAgB;AAC3CgF,iBAAKuF,SAAS0L,MAAMpM,EAAAA,IAAa,GAAE4pB,EAAAA;UACrC;AAEA,cAAA,CAAK+B,MAAqBF,IAAoB;AAC5C,kBAAMzrB,KAAW7J,EAAAA,IAAU,iBAAiB;AAC5CgF,iBAAKuF,SAAS0L,MAAMpM,EAAAA,IAAa,GAAE4pB,EAAAA;UACrC;QACF;QAEA4B,oBAAAA;AACErwB,eAAKuF,SAAS0L,MAAMwf,cAAc,IAClCzwB,KAAKuF,SAAS0L,MAAMyf,eAAe;QACrC;QAGA,OAAA,gBAAuBpsB,IAAQzE,IAAAA;AAC7B,iBAAOG,KAAKuI,KAAK,WAAA;AACf,kBAAMC,KAAO8mB,GAAMrnB,oBAAoBjI,MAAMsE,EAAAA;AAE7C,gBAAsB,YAAA,OAAXA,IAAX;AAIA,kBAAA,WAAWkE,GAAKlE,EAAAA,EACd,OAAM,IAAIa,UAAW,oBAAmBb,EAAAA,GAAAA;AAG1CkE,cAAAA,GAAKlE,EAAAA,EAAQzE,EAAAA;YANb;UAOF,CAAA;QACF;MAAA;AAOFU,QAAaY,GAAGnI,UAAUqS,IAnSG,4BAmSyC,SAAUlM,IAAAA;AAC9E,cAAMlC,KAASuJ,EAAekB,uBAAuB1H,IAAAA;AAEjD,SAAC,KAAK,MAAA,EAAQkB,SAASlB,KAAKgI,OAAAA,KAC9B7I,GAAMkD,eAAAA,GAGR9B,EAAaa,IAAInE,IAAQsS,IAAYohB,CAAAA,OAAAA;AAC/BA,UAAAA,GAAU5uB,oBAKdxB,EAAaa,IAAInE,IAAQyS,IAAc,MAAA;AACjCxW,cAAU8G,IAAAA,KACZA,KAAK0qB,MAAAA;UACP,CAAA;QACA,CAAA;AAIJ,cAAMkG,KAAcpqB,EAAeG,QA3Tf,aAAA;AA4ThBiqB,QAAAA,MACFtB,GAAMtpB,YAAY4qB,EAAAA,EAAalgB,KAAAA,GAGpB4e,GAAMrnB,oBAAoBhL,EAAAA,EAElC0L,OAAO3I,IAAAA;MACd,CAAA,GAEA4H,EAAqB0nB,EAAAA,GAMrBp0B,EAAmBo0B,EAAAA;AC/VnB,YAEM3pB,KAAa,iBACb8E,KAAe,aACfW,KAAuB,OAAMzF,EAAAA,GAAY8E,EAAAA,IAGzCkF,KAAkB,QAClBkhB,KAAqB,WACrBC,KAAoB,UAEpBC,KAAgB,mBAEhBxhB,KAAc,OAAM5J,EAAAA,IACpB6J,KAAe,QAAO7J,EAAAA,IACtB8J,KAAc,OAAM9J,EAAAA,IACpBopB,KAAwB,gBAAeppB,EAAAA,IACvC+J,KAAgB,SAAQ/J,EAAAA,IACxBqpB,KAAgB,SAAQrpB,EAAAA,IACxB0F,KAAwB,QAAO1F,EAAAA,GAAY8E,EAAAA,IAC3C0kB,KAAyB,kBAAiBxpB,EAAAA,IAI1CzB,KAAU,EACdwoB,UAAAA,MACA3gB,UAAAA,MACAiQ,QAAAA,MAAQ,GAGJ7X,KAAc,EAClBuoB,UAAU,oBACV3gB,UAAU,WACViQ,QAAQ,UAAA;MAOV,MAAMgV,WAAkB3rB,EAAAA;QACtBV,YAAY3N,IAASsN,IAAAA;AACnBgB,gBAAMtO,IAASsN,EAAAA,GAEftE,KAAKyQ,WAAAA,OACLzQ,KAAKwvB,YAAYxvB,KAAKyvB,oBAAAA,GACtBzvB,KAAK0vB,aAAa1vB,KAAK2vB,qBAAAA,GACvB3vB,KAAK2M,mBAAAA;QACP;QAGA,WAAA,UAAWzI;AACT,iBAAOA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAOA;QACT;QAEA,WAAA,OAAW5I;AACT,iBA5DS;QA6DX;QAGAoN,OAAO9I,IAAAA;AACL,iBAAOG,KAAKyQ,WAAWzQ,KAAK0Q,KAAAA,IAAS1Q,KAAK2Q,KAAK9Q,EAAAA;QACjD;QAEA8Q,KAAK9Q,IAAAA;AACCG,eAAKyQ,YAISlQ,EAAaoB,QAAQ3B,KAAKuF,UAAUgK,IAAY,EAAE1P,eAAAA,GAAAA,CAAAA,EAEtDkC,qBAId/B,KAAKyQ,WAAAA,MACLzQ,KAAKwvB,UAAU7e,KAAAA,GAEV3Q,KAAKwF,QAAQwW,UAChB,IAAI8R,KAAkBpd,KAAAA,GAGxB1Q,KAAKuF,SAASjC,aAAa,cAAA,IAAc,GACzCtD,KAAKuF,SAASjC,aAAa,QAAQ,QAAA,GACnCtD,KAAKuF,SAASzL,UAAUuQ,IAAIwmB,EAAAA,GAY5B7wB,KAAK8F,eAVoBmJ,MAAAA;AAClBjP,iBAAKwF,QAAQwW,UAAAA,CAAUhc,KAAKwF,QAAQknB,YACvC1sB,KAAK0vB,WAAWrC,SAAAA,GAGlBrtB,KAAKuF,SAASzL,UAAUuQ,IAAIsF,EAAAA,GAC5B3P,KAAKuF,SAASzL,UAAUlC,OAAOi5B,EAAAA,GAC/BtwB,EAAaoB,QAAQ3B,KAAKuF,UAAUiK,IAAa,EAAE3P,eAAAA,GAAAA,CAAAA;UAAgB,GAG/BG,KAAKuF,UAAAA,IAAU;QACvD;QAEAmL,OAAAA;AACO1Q,eAAKyQ,aAIQlQ,EAAaoB,QAAQ3B,KAAKuF,UAAUkK,EAAAA,EAExC1N,qBAId/B,KAAK0vB,WAAWlC,WAAAA,GAChBxtB,KAAKuF,SAAS0rB,KAAAA,GACdjxB,KAAKyQ,WAAAA,OACLzQ,KAAKuF,SAASzL,UAAUuQ,IAAIymB,EAAAA,GAC5B9wB,KAAKwvB,UAAU9e,KAAAA,GAcf1Q,KAAK8F,eAZoBorB,MAAAA;AACvBlxB,iBAAKuF,SAASzL,UAAUlC,OAAO+X,IAAiBmhB,EAAAA,GAChD9wB,KAAKuF,SAAS/B,gBAAgB,YAAA,GAC9BxD,KAAKuF,SAAS/B,gBAAgB,MAAA,GAEzBxD,KAAKwF,QAAQwW,UAChB,IAAI8R,KAAkBpL,MAAAA,GAGxBniB,EAAaoB,QAAQ3B,KAAKuF,UAAUmK,EAAAA;UAAa,GAGb1P,KAAKuF,UAAAA,IAAU;QACvD;QAEAG,UAAAA;AACE1F,eAAKwvB,UAAU9pB,QAAAA,GACf1F,KAAK0vB,WAAWlC,WAAAA,GAChBloB,MAAMI,QAAAA;QACR;QAGA+pB,sBAAAA;AACE,gBAUMv2B,KAAY0H,QAAQZ,KAAKwF,QAAQknB,QAAAA;AAEvC,iBAAO,IAAIL,GAAS,EAClBH,WAlJsB,sBAmJtBhzB,WAAAA,IACA6M,YAAAA,MACAqmB,aAAapsB,KAAKuF,SAAS7L,YAC3ByyB,eAAejzB,KAjBKizB,MAAAA;AACU,yBAA1BnsB,KAAKwF,QAAQknB,WAKjB1sB,KAAK0Q,KAAAA,IAJHnQ,EAAaoB,QAAQ3B,KAAKuF,UAAUwpB,EAAAA;UAI3B,IAWgC,KAAA,CAAA;QAE/C;QAEAY,uBAAAA;AACE,iBAAO,IAAIzC,GAAU,EACnBD,aAAajtB,KAAKuF,SAAAA,CAAAA;QAEtB;QAEAoH,qBAAAA;AACEpM,YAAaY,GAAGnB,KAAKuF,UAAU4pB,IAAuBhwB,CAAAA,OAAAA;AAtKvC,yBAuKTA,GAAMlI,QAIN+I,KAAKwF,QAAQuG,WACf/L,KAAK0Q,KAAAA,IAIPnQ,EAAaoB,QAAQ3B,KAAKuF,UAAUwpB,EAAAA;UAAqB,CAAA;QAE7D;QAGA,OAAA,gBAAuBzqB,IAAAA;AACrB,iBAAOtE,KAAKuI,KAAK,WAAA;AACf,kBAAMC,KAAOwoB,GAAU/oB,oBAAoBjI,MAAMsE,EAAAA;AAEjD,gBAAsB,YAAA,OAAXA,IAAX;AAIA,kBAAA,WAAIkE,GAAKlE,EAAAA,KAAyBA,GAAO/C,WAAW,GAAA,KAAmB,kBAAX+C,GAC1D,OAAM,IAAIa,UAAW,oBAAmBb,EAAAA,GAAAA;AAG1CkE,cAAAA,GAAKlE,EAAAA,EAAQtE,IAAAA;YANb;UAOF,CAAA;QACF;MAAA;AAOFO,QAAaY,GAAGnI,UAAUqS,IAzLG,gCAyLyC,SAAUlM,IAAAA;AAC9E,cAAMlC,KAASuJ,EAAekB,uBAAuB1H,IAAAA;AAMrD,YAJI,CAAC,KAAK,MAAA,EAAQkB,SAASlB,KAAKgI,OAAAA,KAC9B7I,GAAMkD,eAAAA,GAGJ1I,EAAWqG,IAAAA,EACb;AAGFO,UAAaa,IAAInE,IAAQyS,IAAc,MAAA;AAEjCxW,YAAU8G,IAAAA,KACZA,KAAK0qB,MAAAA;QACP,CAAA;AAIF,cAAMkG,KAAcpqB,EAAeG,QAAQoqB,EAAAA;AACvCH,QAAAA,MAAeA,OAAgB3zB,MACjC+zB,GAAUhrB,YAAY4qB,EAAAA,EAAalgB,KAAAA,GAGxBsgB,GAAU/oB,oBAAoBhL,EAAAA,EACtC0L,OAAO3I,IAAAA;MACd,CAAA,GAEAO,EAAaY,GAAGlJ,QAAQmT,IAAqB,MAAA;AAC3C,mBAAWpT,MAAYwO,EAAetH,KAAK6xB,EAAAA,EACzCC,IAAU/oB,oBAAoBjQ,EAAAA,EAAU2Y,KAAAA;MAC1C,CAAA,GAGFpQ,EAAaY,GAAGlJ,QAAQ+2B,IAAc,MAAA;AACpC,mBAAWh4B,MAAWwP,EAAetH,KAAK,8CAAA,EACG,aAAvC7F,iBAAiBrC,EAAAA,EAASwd,YAC5Bwc,GAAU/oB,oBAAoBjR,EAAAA,EAAS0Z,KAAAA;MAE3C,CAAA,GAGF9I,EAAqBopB,EAAAA,GAMrB91B,EAAmB81B,EAAAA;AC/QnB,YAEaG,KAAmB,EAE9B,KAAK,CAAC,SAAS,OAAO,MAAM,QAAQ,QAJP,gBAAA,GAK7BvQ,GAAG,CAAC,UAAU,QAAQ,SAAS,KAAA,GAC/BwQ,MAAM,CAAA,GACNvQ,GAAG,CAAA,GACHwQ,IAAI,CAAA,GACJC,KAAK,CAAA,GACLC,MAAM,CAAA,GACNC,IAAI,CAAA,GACJC,KAAK,CAAA,GACLC,IAAI,CAAA,GACJC,IAAI,CAAA,GACJC,IAAI,CAAA,GACJC,IAAI,CAAA,GACJC,IAAI,CAAA,GACJC,IAAI,CAAA,GACJC,IAAI,CAAA,GACJC,IAAI,CAAA,GACJC,IAAI,CAAA,GACJC,IAAI,CAAA,GACJrQ,GAAG,CAAA,GACHhU,KAAK,CAAC,OAAO,UAAU,OAAO,SAAS,SAAS,QAAA,GAChDskB,IAAI,CAAA,GACJC,IAAI,CAAA,GACJC,GAAG,CAAA,GACHC,KAAK,CAAA,GACLC,GAAG,CAAA,GACHC,OAAO,CAAA,GACPC,MAAM,CAAA,GACNC,KAAK,CAAA,GACLC,KAAK,CAAA,GACLC,QAAQ,CAAA,GACRC,GAAG,CAAA,GACHC,IAAI,CAAA,EAAA,GAIAC,KAAgB,oBAAIx0B,IAAI,CAC5B,cACA,QACA,QACA,YACA,YACA,UACA,OACA,YAAA,CAAA,GAUIy0B,KAAmB,2DAEnBC,KAAmBA,CAACpe,IAAWqe,OAAAA;AACnC,cAAMC,KAAgBte,GAAU1B,SAASjQ,YAAAA;AAEzC,eAAIgwB,GAAqBjyB,SAASkyB,EAAAA,IAAAA,CAC5BJ,GAAc77B,IAAIi8B,EAAAA,KACbxyB,QAAQqyB,GAAiB/tB,KAAK4P,GAAUue,SAAAA,CAAAA,IAO5CF,GAAqBtvB,OAAOyvB,CAAAA,OAAkBA,cAA0BruB,MAAAA,EAC5E6d,KAAKyQ,CAAAA,OAASA,GAAMruB,KAAKkuB,EAAAA,CAAAA;MAAe,GC/DvClvB,KAAU,EACdsvB,WAAWrC,IACXsC,SAAS,CAAA,GACTC,YAAY,IACZ5V,MAAAA,OACA6V,UAAAA,MACAC,YAAY,MACZC,UAAU,cAAA,GAGN1vB,KAAc,EAClBqvB,WAAW,UACXC,SAAS,UACTC,YAAY,qBACZ5V,MAAM,WACN6V,UAAU,WACVC,YAAY,mBACZC,UAAU,SAAA,GAGNC,KAAqB,EACzBC,OAAO,kCACP/7B,UAAU,mBAAA;MAOZ,MAAMg8B,WAAwB/vB,EAAAA;QAC5BU,YAAYL,IAAAA;AACVgB,gBAAAA,GACAtF,KAAKwF,UAAUxF,KAAKqE,WAAWC,EAAAA;QACjC;QAGA,WAAA,UAAWJ;AACT,iBAAOA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAOA;QACT;QAEA,WAAA,OAAW5I;AACT,iBA/CS;QAgDX;QAGA04B,aAAAA;AACE,iBAAOj1B,OAAOC,OAAOe,KAAKwF,QAAQiuB,OAAAA,EAC/BptB,IAAI/B,CAAAA,OAAUtE,KAAKk0B,yBAAyB5vB,EAAAA,CAAAA,EAC5CT,OAAOjD,OAAAA;QACZ;QAEAuzB,aAAAA;AACE,iBAAOn0B,KAAKi0B,WAAAA,EAAal7B,SAAS;QACpC;QAEAq7B,cAAcX,IAAAA;AAGZ,iBAFAzzB,KAAKq0B,cAAcZ,EAAAA,GACnBzzB,KAAKwF,QAAQiuB,UAAU,EAAA,GAAKzzB,KAAKwF,QAAQiuB,SAAAA,GAAYA,GAAAA,GAC9CzzB;QACT;QAEAs0B,SAAAA;AACE,gBAAMC,KAAkBv7B,SAAS2zB,cAAc,KAAA;AAC/C4H,UAAAA,GAAgBC,YAAYx0B,KAAKy0B,eAAez0B,KAAKwF,QAAQquB,QAAAA;AAE7D,qBAAK,CAAO77B,IAAU08B,EAAAA,KAAS11B,OAAOiC,QAAQjB,KAAKwF,QAAQiuB,OAAAA,EACzDzzB,MAAK20B,YAAYJ,IAAiBG,IAAM18B,EAAAA;AAG1C,gBAAM67B,KAAWU,GAAgB3tB,SAAS,CAAA,GACpC8sB,KAAa1zB,KAAKk0B,yBAAyBl0B,KAAKwF,QAAQkuB,UAAAA;AAM9D,iBAJIA,MACFG,GAAS/5B,UAAUuQ,IAAAA,GAAOqpB,GAAW52B,MAAM,GAAA,CAAA,GAGtC+2B;QACT;QAGApvB,iBAAiBH,IAAAA;AACfgB,gBAAMb,iBAAiBH,EAAAA,GACvBtE,KAAKq0B,cAAc/vB,GAAOmvB,OAAAA;QAC5B;QAEAY,cAAcO,IAAAA;AACZ,qBAAK,CAAO58B,IAAUy7B,EAAAA,KAAYz0B,OAAOiC,QAAQ2zB,EAAAA,EAC/CtvB,OAAMb,iBAAiB,EAAEzM,UAAAA,IAAU+7B,OAAON,GAAAA,GAAWK,EAAAA;QAEzD;QAEAa,YAAYd,IAAUJ,IAASz7B,IAAAA;AAC7B,gBAAM68B,KAAkBruB,EAAeG,QAAQ3O,IAAU67B,EAAAA;AAEpDgB,UAAAA,QAILpB,KAAUzzB,KAAKk0B,yBAAyBT,EAAAA,KAOpC/6B,EAAU+6B,EAAAA,IACZzzB,KAAK80B,sBAAsBh8B,EAAW26B,EAAAA,GAAUoB,EAAAA,IAI9C70B,KAAKwF,QAAQsY,OACf+W,GAAgBL,YAAYx0B,KAAKy0B,eAAehB,EAAAA,IAIlDoB,GAAgBE,cAActB,KAd5BoB,GAAgBj9B,OAAAA;QAepB;QAEA68B,eAAeG,IAAAA;AACb,iBAAO50B,KAAKwF,QAAQmuB,WDzDjB,SAAsBqB,IAAYxB,IAAWyB,IAAAA;AAClD,gBAAA,CAAKD,GAAWj8B,OACd,QAAOi8B;AAGT,gBAAIC,MAAgD,cAAA,OAArBA,GAC7B,QAAOA,GAAiBD,EAAAA;AAG1B,kBACME,KADY,IAAIj9B,OAAOk9B,YACKC,gBAAgBJ,IAAY,WAAA,GACxD/gB,KAAW,CAAA,EAAGxN,OAAAA,GAAUyuB,GAAgBp6B,KAAKsF,iBAAiB,GAAA,CAAA;AAEpE,uBAAWpJ,MAAWid,IAAU;AAC9B,oBAAMohB,KAAcr+B,GAAQoc,SAASjQ,YAAAA;AAErC,kBAAA,CAAKnE,OAAOrH,KAAK67B,EAAAA,EAAWtyB,SAASm0B,EAAAA,GAAc;AACjDr+B,gBAAAA,GAAQY,OAAAA;AACR;cACF;AAEA,oBAAM09B,KAAgB,CAAA,EAAG7uB,OAAAA,GAAUzP,GAAQ0M,UAAAA,GACrC6xB,KAAoB,CAAA,EAAG9uB,OAAO+sB,GAAU,GAAA,KAAQ,CAAA,GAAIA,GAAU6B,EAAAA,KAAgB,CAAA,CAAA;AAEpF,yBAAWvgB,MAAawgB,GACjBpC,IAAiBpe,IAAWygB,EAAAA,KAC/Bv+B,GAAQwM,gBAAgBsR,GAAU1B,QAAAA;YAGxC;AAEA,mBAAO8hB,GAAgBp6B,KAAK05B;UAC9B,ECyBgDI,IAAK50B,KAAKwF,QAAQguB,WAAWxzB,KAAKwF,QAAQouB,UAAAA,IAAcgB;QACtG;QAEAV,yBAAyBU,IAAAA;AACvB,iBAAO54B,EAAQ44B,IAAK,CAAC50B,IAAAA,CAAAA;QACvB;QAEA80B,sBAAsB99B,IAAS69B,IAAAA;AAC7B,cAAI70B,KAAKwF,QAAQsY,KAGf,QAFA+W,GAAgBL,YAAY,IAAA,KAC5BK,GAAgBjI,OAAO51B,EAAAA;AAIzB69B,UAAAA,GAAgBE,cAAc/9B,GAAQ+9B;QACxC;MAAA;ACvIF,YACMS,KAAwB,oBAAIh3B,IAAI,CAAC,YAAY,aAAa,YAAA,CAAA,GAE1Di3B,KAAkB,QAElB9lB,KAAkB,QAGlB+lB,KAAkB,UAElBC,KAAmB,iBAEnBC,KAAgB,SAChBC,KAAgB,SAehBC,KAAgB,EACpBC,MAAM,QACNC,KAAK,OACLC,OAAOj7B,EAAAA,IAAU,SAAS,SAC1Bk7B,QAAQ,UACRC,MAAMn7B,EAAAA,IAAU,UAAU,OAAA,GAGtBkJ,KAAU,EACdsvB,WAAWrC,IACXiF,WAAAA,MACAtX,UAAU,mBACVuX,WAAAA,OACAC,aAAa,IACbC,OAAO,GACPjV,oBAAoB,CAAC,OAAO,SAAS,UAAU,MAAA,GAC/CxD,MAAAA,OACArE,QAAQ,CAAC,GAAG,CAAA,GACZlH,WAAW,OACX2X,cAAc,MACdyJ,UAAAA,MACAC,YAAY,MACZ57B,UAAAA,OACA67B,UAAU,gHAIV2C,OAAO,IACP70B,SAAS,cAAA,GAGLwC,KAAc,EAClBqvB,WAAW,UACX4C,WAAW,WACXtX,UAAU,oBACVuX,WAAW,4BACXC,aAAa,qBACbC,OAAO,mBACPjV,oBAAoB,SACpBxD,MAAM,WACNrE,QAAQ,2BACRlH,WAAW,qBACX2X,cAAc,0BACdyJ,UAAU,WACVC,YAAY,mBACZ57B,UAAU,oBACV67B,UAAU,UACV2C,OAAO,6BACP70B,SAAS,SAAA;MAOX,MAAM80B,WAAgBpxB,EAAAA;QACpBV,YAAY3N,IAASsN,IAAAA;AACnB,cAAA,WAAWsmB,GACT,OAAM,IAAIzlB,UAAU,6DAAA;AAGtBG,gBAAMtO,IAASsN,EAAAA,GAGftE,KAAK02B,aAAAA,MACL12B,KAAK22B,WAAW,GAChB32B,KAAK42B,aAAa,MAClB52B,KAAK62B,iBAAiB,CAAA,GACtB72B,KAAKoqB,UAAU,MACfpqB,KAAK82B,mBAAmB,MACxB92B,KAAK+2B,cAAc,MAGnB/2B,KAAKg3B,MAAM,MAEXh3B,KAAKi3B,cAAAA,GAEAj3B,KAAKwF,QAAQxN,YAChBgI,KAAKk3B,UAAAA;QAET;QAGA,WAAA,UAAWhzB;AACT,iBAAOA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAOA;QACT;QAEA,WAAA,OAAW5I;AACT,iBAxHS;QAyHX;QAGA47B,SAAAA;AACEn3B,eAAK02B,aAAAA;QACP;QAEAU,UAAAA;AACEp3B,eAAK02B,aAAAA;QACP;QAEAW,gBAAAA;AACEr3B,eAAK02B,aAAAA,CAAc12B,KAAK02B;QAC1B;QAEA/tB,SAAAA;AACO3I,eAAK02B,eAIV12B,KAAK62B,eAAeS,QAAAA,CAASt3B,KAAK62B,eAAeS,OAC7Ct3B,KAAKyQ,SAAAA,IACPzQ,KAAKu3B,OAAAA,IAIPv3B,KAAKw3B,OAAAA;QACP;QAEA9xB,UAAAA;AACEwI,uBAAalO,KAAK22B,QAAAA,GAElBp2B,EAAaC,IAAIR,KAAKuF,SAAS/L,QAAQk8B,EAAAA,GAAiBC,IAAkB31B,KAAKy3B,iBAAAA,GAE3Ez3B,KAAKuF,SAASrL,aAAa,wBAAA,KAC7B8F,KAAKuF,SAASjC,aAAa,SAAStD,KAAKuF,SAASrL,aAAa,wBAAA,CAAA,GAGjE8F,KAAK03B,eAAAA,GACLpyB,MAAMI,QAAAA;QACR;QAEAiL,OAAAA;AACE,cAAoC,WAAhC3Q,KAAKuF,SAAS0L,MAAMgZ,QACtB,OAAM,IAAI7lB,MAAM,qCAAA;AAGlB,cAAA,CAAMpE,KAAK23B,eAAAA,KAAAA,CAAoB33B,KAAK02B,WAClC;AAGF,gBAAM/F,KAAYpwB,EAAaoB,QAAQ3B,KAAKuF,UAAUvF,KAAK2E,YAAYiK,UAzJxD,MAAA,CAAA,GA2JTgpB,MADaz9B,EAAe6F,KAAKuF,QAAAA,KACLvF,KAAKuF,SAASgO,cAAcnZ,iBAAiBL,SAASiG,KAAKuF,QAAAA;AAE7F,cAAIorB,GAAU5uB,oBAAAA,CAAqB61B,GACjC;AAIF53B,eAAK03B,eAAAA;AAEL,gBAAMV,KAAMh3B,KAAK63B,eAAAA;AAEjB73B,eAAKuF,SAASjC,aAAa,oBAAoB0zB,GAAI98B,aAAa,IAAA,CAAA;AAEhE,gBAAA,EAAMm8B,WAAEA,GAAAA,IAAcr2B,KAAKwF;AAe3B,cAbKxF,KAAKuF,SAASgO,cAAcnZ,gBAAgBL,SAASiG,KAAKg3B,GAAAA,MAC7DX,GAAUzJ,OAAOoK,EAAAA,GACjBz2B,EAAaoB,QAAQ3B,KAAKuF,UAAUvF,KAAK2E,YAAYiK,UA1KpC,UAAA,CAAA,IA6KnB5O,KAAKoqB,UAAUpqB,KAAKyqB,cAAcuM,EAAAA,GAElCA,GAAIl9B,UAAUuQ,IAAIsF,EAAAA,GAMd,kBAAkB3W,SAASoB,gBAC7B,YAAWpD,MAAW,CAAA,EAAGyP,OAAAA,GAAUzN,SAAS8B,KAAK8L,QAAAA,EAC/CrG,GAAaY,GAAGnK,IAAS,aAAayD,CAAAA;AAc1CuF,eAAK8F,eAVYqL,MAAAA;AACf5Q,cAAaoB,QAAQ3B,KAAKuF,UAAUvF,KAAK2E,YAAYiK,UA7LvC,OAAA,CAAA,GAAA,UA+LV5O,KAAK42B,cACP52B,KAAKu3B,OAAAA,GAGPv3B,KAAK42B,aAAAA;UAAkB,GAGK52B,KAAKg3B,KAAKh3B,KAAKkP,YAAAA,CAAAA;QAC/C;QAEAwB,OAAAA;AACE,cAAK1Q,KAAKyQ,SAAAA,KAAAA,CAIQlQ,EAAaoB,QAAQ3B,KAAKuF,UAAUvF,KAAK2E,YAAYiK,UAjNxD,MAAA,CAAA,EAkND7M,kBAAd;AASA,gBALY/B,KAAK63B,eAAAA,EACb/9B,UAAUlC,OAAO+X,EAAAA,GAIjB,kBAAkB3W,SAASoB,gBAC7B,YAAWpD,MAAW,CAAA,EAAGyP,OAAAA,GAAUzN,SAAS8B,KAAK8L,QAAAA,EAC/CrG,GAAaC,IAAIxJ,IAAS,aAAayD,CAAAA;AAI3CuF,iBAAK62B,eAA4B,QAAA,OACjC72B,KAAK62B,eAAehB,EAAAA,IAAAA,OACpB71B,KAAK62B,eAAejB,EAAAA,IAAAA,OACpB51B,KAAK42B,aAAa,MAelB52B,KAAK8F,eAbYqL,MAAAA;AACXnR,mBAAK83B,qBAAAA,MAIJ93B,KAAK42B,cACR52B,KAAK03B,eAAAA,GAGP13B,KAAKuF,SAAS/B,gBAAgB,kBAAA,GAC9BjD,EAAaoB,QAAQ3B,KAAKuF,UAAUvF,KAAK2E,YAAYiK,UA/OtC,QAAA,CAAA;YA+O8D,GAGjD5O,KAAKg3B,KAAKh3B,KAAKkP,YAAAA,CAAAA;UA/B7C;QAgCF;QAEAmN,SAAAA;AACMrc,eAAKoqB,WACPpqB,KAAKoqB,QAAQ/N,OAAAA;QAEjB;QAGAsb,iBAAAA;AACE,iBAAO/2B,QAAQZ,KAAK+3B,UAAAA,CAAAA;QACtB;QAEAF,iBAAAA;AAKE,iBAJK73B,KAAKg3B,QACRh3B,KAAKg3B,MAAMh3B,KAAKg4B,kBAAkBh4B,KAAK+2B,eAAe/2B,KAAKi4B,uBAAAA,CAAAA,IAGtDj4B,KAAKg3B;QACd;QAEAgB,kBAAkBvE,IAAAA;AAChB,gBAAMuD,KAAMh3B,KAAKk4B,oBAAoBzE,EAAAA,EAASa,OAAAA;AAG9C,cAAA,CAAK0C,GACH,QAAO;AAGTA,UAAAA,GAAIl9B,UAAUlC,OAAO69B,IAAiB9lB,EAAAA,GAEtCqnB,GAAIl9B,UAAUuQ,IAAK,MAAKrK,KAAK2E,YAAYpJ,IAAAA,OAAAA;AAEzC,gBAAM48B,M3ErRKC,CAAAA,OAAAA;AACb,eAAA;AACEA,cAAAA,MAAUx6B,KAAKy6B,MAjCH,MAiCSz6B,KAAK06B,OAAAA,CAAAA;YAAAA,SACnBt/B,SAASu/B,eAAeH,EAAAA;AAEjC,mBAAOA;UAAM,G2EgRUp4B,KAAK2E,YAAYpJ,IAAAA,EAAMsH,SAAAA;AAQ5C,iBANAm0B,GAAI1zB,aAAa,MAAM60B,EAAAA,GAEnBn4B,KAAKkP,YAAAA,KACP8nB,GAAIl9B,UAAUuQ,IAAIorB,EAAAA,GAGbuB;QACT;QAEAwB,WAAW/E,IAAAA;AACTzzB,eAAK+2B,cAActD,IACfzzB,KAAKyQ,SAAAA,MACPzQ,KAAK03B,eAAAA,GACL13B,KAAK2Q,KAAAA;QAET;QAEAunB,oBAAoBzE,IAAAA;AAalB,iBAZIzzB,KAAK82B,mBACP92B,KAAK82B,iBAAiB1C,cAAcX,EAAAA,IAEpCzzB,KAAK82B,mBAAmB,IAAI9C,GAAgB,EAAA,GACvCh0B,KAAKwF,SAGRiuB,SAAAA,IACAC,YAAY1zB,KAAKk0B,yBAAyBl0B,KAAKwF,QAAQ8wB,WAAAA,EAAAA,CAAAA,GAIpDt2B,KAAK82B;QACd;QAEAmB,yBAAAA;AACE,iBAAO,EACL,kBAA0Bj4B,KAAK+3B,UAAAA,EAAAA;QAEnC;QAEAA,YAAAA;AACE,iBAAO/3B,KAAKk0B,yBAAyBl0B,KAAKwF,QAAQgxB,KAAAA,KAAUx2B,KAAKuF,SAASrL,aAAa,wBAAA;QACzF;QAGAu+B,6BAA6Bt5B,IAAAA;AAC3B,iBAAOa,KAAK2E,YAAYsD,oBAAoB9I,GAAMW,gBAAgBE,KAAK04B,mBAAAA,CAAAA;QACzE;QAEAxpB,cAAAA;AACE,iBAAOlP,KAAKwF,QAAQ4wB,aAAcp2B,KAAKg3B,OAAOh3B,KAAKg3B,IAAIl9B,UAAUC,SAAS07B,EAAAA;QAC5E;QAEAhlB,WAAAA;AACE,iBAAOzQ,KAAKg3B,OAAOh3B,KAAKg3B,IAAIl9B,UAAUC,SAAS4V,EAAAA;QACjD;QAEA8a,cAAcuM,IAAAA;AACZ,gBAAMzkB,KAAYvW,EAAQgE,KAAKwF,QAAQ+M,WAAW,CAACvS,MAAMg3B,IAAKh3B,KAAKuF,QAAAA,CAAAA,GAC7DozB,KAAa7C,GAAcvjB,GAAUnN,YAAAA,CAAAA;AAC3C,iBAAOwlB,GAAoB5qB,KAAKuF,UAAUyxB,IAAKh3B,KAAK8qB,iBAAiB6N,EAAAA,CAAAA;QACvE;QAEAzN,aAAAA;AACE,gBAAA,EAAMzR,QAAEA,GAAAA,IAAWzZ,KAAKwF;AAExB,iBAAsB,YAAA,OAAXiU,KACFA,GAAO3c,MAAM,GAAA,EAAKuJ,IAAI7D,CAAAA,OAAS7F,OAAO4R,SAAS/L,IAAO,EAAA,CAAA,IAGzC,cAAA,OAAXiX,KACF0R,CAAAA,OAAc1R,GAAO0R,IAAYnrB,KAAKuF,QAAAA,IAGxCkU;QACT;QAEAya,yBAAyBU,IAAAA;AACvB,iBAAO54B,EAAQ44B,IAAK,CAAC50B,KAAKuF,QAAAA,CAAAA;QAC5B;QAEAulB,iBAAiB6N,IAAAA;AACf,gBAAMvN,KAAwB,EAC5B7Y,WAAWomB,IACXlS,WAAW,CACT,EACEnrB,MAAM,QACNmZ,SAAS,EACP6M,oBAAoBthB,KAAKwF,QAAQ8b,mBAAAA,EAAAA,GAGrC,EACEhmB,MAAM,UACNmZ,SAAS,EACPgF,QAAQzZ,KAAKkrB,WAAAA,EAAAA,EAAAA,GAGjB,EACE5vB,MAAM,mBACNmZ,SAAS,EACPqK,UAAU9e,KAAKwF,QAAQsZ,SAAAA,EAAAA,GAG3B,EACExjB,MAAM,SACNmZ,SAAS,EACPzd,SAAU,IAAGgJ,KAAK2E,YAAYpJ,IAAAA,SAAAA,EAAAA,GAGlC,EACED,MAAM,mBACNuY,SAAAA,MACAC,OAAO,cACPrY,IAAI+M,CAAAA,OAAAA;AAGFxI,iBAAK63B,eAAAA,EAAiBv0B,aAAa,yBAAyBkF,GAAKwL,MAAMzB,SAAAA;UAAU,EAAA,CAAA,EAAA;AAMzF,iBAAO,EAAA,GACF6Y,IAAAA,GACApvB,EAAQgE,KAAKwF,QAAQ0kB,cAAc,CAACkB,EAAAA,CAAAA,EAAAA;QAE3C;QAEA6L,gBAAAA;AACE,gBAAM2B,KAAW54B,KAAKwF,QAAQ7D,QAAQ7E,MAAM,GAAA;AAE5C,qBAAW6E,MAAWi3B,GACpB,KAAgB,YAAZj3B,GACFpB,GAAaY,GAAGnB,KAAKuF,UAAUvF,KAAK2E,YAAYiK,UAtZpC,OAAA,GAsZ4D5O,KAAKwF,QAAQxN,UAAUmH,CAAAA,OAAAA;AAC7Ea,iBAAKy4B,6BAA6Bt5B,EAAAA,EAC1CwJ,OAAAA;UAAQ,CAAA;mBA/ZH,aAiaNhH,IAA4B;AACrC,kBAAMk3B,KAAUl3B,OAAYi0B,KAC1B51B,KAAK2E,YAAYiK,UAzZF,YAAA,IA0Zf5O,KAAK2E,YAAYiK,UA5ZL,SAAA,GA6ZRkqB,KAAWn3B,OAAYi0B,KAC3B51B,KAAK2E,YAAYiK,UA3ZF,YAAA,IA4Zf5O,KAAK2E,YAAYiK,UA9ZJ,UAAA;AAgafrO,cAAaY,GAAGnB,KAAKuF,UAAUszB,IAAS74B,KAAKwF,QAAQxN,UAAUmH,CAAAA,OAAAA;AAC7D,oBAAMosB,KAAUvrB,KAAKy4B,6BAA6Bt5B,EAAAA;AAClDosB,cAAAA,GAAQsL,eAA8B,cAAf13B,GAAMsB,OAAqBo1B,KAAgBD,EAAAA,IAAAA,MAClErK,GAAQiM,OAAAA;YAAQ,CAAA,GAElBj3B,EAAaY,GAAGnB,KAAKuF,UAAUuzB,IAAU94B,KAAKwF,QAAQxN,UAAUmH,CAAAA,OAAAA;AAC9D,oBAAMosB,KAAUvrB,KAAKy4B,6BAA6Bt5B,EAAAA;AAClDosB,cAAAA,GAAQsL,eAA8B,eAAf13B,GAAMsB,OAAsBo1B,KAAgBD,EAAAA,IACjErK,GAAQhmB,SAASxL,SAASoF,GAAMU,aAAAA,GAElC0rB,GAAQgM,OAAAA;YAAQ,CAAA;UAEpB;AAGFv3B,eAAKy3B,oBAAoB,MAAA;AACnBz3B,iBAAKuF,YACPvF,KAAK0Q,KAAAA;UACP,GAGFnQ,EAAaY,GAAGnB,KAAKuF,SAAS/L,QAAQk8B,EAAAA,GAAiBC,IAAkB31B,KAAKy3B,iBAAAA;QAChF;QAEAP,YAAAA;AACE,gBAAMV,KAAQx2B,KAAKuF,SAASrL,aAAa,OAAA;AAEpCs8B,UAAAA,OAIAx2B,KAAKuF,SAASrL,aAAa,YAAA,KAAkB8F,KAAKuF,SAASwvB,YAAY3uB,KAAAA,KAC1EpG,KAAKuF,SAASjC,aAAa,cAAckzB,EAAAA,GAG3Cx2B,KAAKuF,SAASjC,aAAa,0BAA0BkzB,EAAAA,GACrDx2B,KAAKuF,SAAS/B,gBAAgB,OAAA;QAChC;QAEAg0B,SAAAA;AACMx3B,eAAKyQ,SAAAA,KAAczQ,KAAK42B,aAC1B52B,KAAK42B,aAAAA,QAIP52B,KAAK42B,aAAAA,MAEL52B,KAAK+4B,YAAY,MAAA;AACX/4B,iBAAK42B,cACP52B,KAAK2Q,KAAAA;UACP,GACC3Q,KAAKwF,QAAQ+wB,MAAM5lB,IAAAA;QACxB;QAEA4mB,SAAAA;AACMv3B,eAAK83B,qBAAAA,MAIT93B,KAAK42B,aAAAA,OAEL52B,KAAK+4B,YAAY,MAAA;AACV/4B,iBAAK42B,cACR52B,KAAK0Q,KAAAA;UACP,GACC1Q,KAAKwF,QAAQ+wB,MAAM7lB,IAAAA;QACxB;QAEAqoB,YAAY/7B,IAASg8B,IAAAA;AACnB9qB,uBAAalO,KAAK22B,QAAAA,GAClB32B,KAAK22B,WAAWx5B,WAAWH,IAASg8B,EAAAA;QACtC;QAEAlB,uBAAAA;AACE,iBAAO94B,OAAOC,OAAOe,KAAK62B,cAAAA,EAAgB31B,SAAAA,IAAS;QACrD;QAEAmD,WAAWC,IAAAA;AACT,gBAAM20B,KAAiB71B,EAAYK,kBAAkBzD,KAAKuF,QAAAA;AAE1D,qBAAW2zB,MAAiBl6B,OAAOrH,KAAKshC,EAAAA,EAClCzD,IAAsBr+B,IAAI+hC,EAAAA,KAAAA,OACrBD,GAAeC,EAAAA;AAW1B,iBAPA50B,KAAS,EAAA,GACJ20B,IAAAA,GACmB,YAAA,OAAX30B,MAAuBA,KAASA,KAAS,CAAA,EAAA,GAEtDA,KAAStE,KAAKuE,gBAAgBD,EAAAA,GAC9BA,KAAStE,KAAKwE,kBAAkBF,EAAAA,GAChCtE,KAAKyE,iBAAiBH,EAAAA,GACfA;QACT;QAEAE,kBAAkBF,IAAAA;AAkBhB,iBAjBAA,GAAO+xB,YAAAA,UAAY/xB,GAAO+xB,YAAsBr9B,SAAS8B,OAAOhC,EAAWwL,GAAO+xB,SAAAA,GAEtD,YAAA,OAAjB/xB,GAAOiyB,UAChBjyB,GAAOiyB,QAAQ,EACb5lB,MAAMrM,GAAOiyB,OACb7lB,MAAMpM,GAAOiyB,MAAAA,IAIW,YAAA,OAAjBjyB,GAAOkyB,UAChBlyB,GAAOkyB,QAAQlyB,GAAOkyB,MAAM3zB,SAAAA,IAGA,YAAA,OAAnByB,GAAOmvB,YAChBnvB,GAAOmvB,UAAUnvB,GAAOmvB,QAAQ5wB,SAAAA,IAG3ByB;QACT;QAEAo0B,qBAAAA;AACE,gBAAMp0B,KAAS,CAAA;AAEf,qBAAK,CAAOrN,IAAKuL,EAAAA,KAAUxD,OAAOiC,QAAQjB,KAAKwF,OAAAA,EACzCxF,MAAK2E,YAAYT,QAAQjN,EAAAA,MAASuL,OACpC8B,GAAOrN,EAAAA,IAAOuL;AAUlB,iBANA8B,GAAOtM,WAAAA,OACPsM,GAAO3C,UAAU,UAKV2C;QACT;QAEAozB,iBAAAA;AACM13B,eAAKoqB,YACPpqB,KAAKoqB,QAAQtB,QAAAA,GACb9oB,KAAKoqB,UAAU,OAGbpqB,KAAKg3B,QACPh3B,KAAKg3B,IAAIp/B,OAAAA,GACToI,KAAKg3B,MAAM;QAEf;QAGA,OAAA,gBAAuB1yB,IAAAA;AACrB,iBAAOtE,KAAKuI,KAAK,WAAA;AACf,kBAAMC,KAAOiuB,GAAQxuB,oBAAoBjI,MAAMsE,EAAAA;AAE/C,gBAAsB,YAAA,OAAXA,IAAX;AAIA,kBAAA,WAAWkE,GAAKlE,EAAAA,EACd,OAAM,IAAIa,UAAW,oBAAmBb,EAAAA,GAAAA;AAG1CkE,cAAAA,GAAKlE,EAAAA,EAAAA;YANL;UAOF,CAAA;QACF;MAAA;AAOFpJ,QAAmBu7B,EAAAA;ACxmBnB,YAKMvyB,KAAU,EAAA,GACXuyB,GAAQvyB,SACXuvB,SAAS,IACTha,QAAQ,CAAC,GAAG,CAAA,GACZlH,WAAW,SACXshB,UAAU,+IAKVlyB,SAAS,QAAA,GAGLwC,KAAc,EAAA,GACfsyB,GAAQtyB,aACXsvB,SAAS,iCAAA;MAOX,MAAM0F,WAAgB1C,GAAAA;QAEpB,WAAA,UAAWvyB;AACT,iBAAOA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAOA;QACT;QAEA,WAAA,OAAW5I;AACT,iBAtCS;QAuCX;QAGAo8B,iBAAAA;AACE,iBAAO33B,KAAK+3B,UAAAA,KAAe/3B,KAAKo5B,YAAAA;QAClC;QAGAnB,yBAAAA;AACE,iBAAO,EACL,mBAAkBj4B,KAAK+3B,UAAAA,GACvB,iBAAoB/3B,KAAKo5B,YAAAA,EAAAA;QAE7B;QAEAA,cAAAA;AACE,iBAAOp5B,KAAKk0B,yBAAyBl0B,KAAKwF,QAAQiuB,OAAAA;QACpD;QAGA,OAAA,gBAAuBnvB,IAAAA;AACrB,iBAAOtE,KAAKuI,KAAK,WAAA;AACf,kBAAMC,KAAO2wB,GAAQlxB,oBAAoBjI,MAAMsE,EAAAA;AAE/C,gBAAsB,YAAA,OAAXA,IAAX;AAIA,kBAAA,WAAWkE,GAAKlE,EAAAA,EACd,OAAM,IAAIa,UAAW,oBAAmBb,EAAAA,GAAAA;AAG1CkE,cAAAA,GAAKlE,EAAAA,EAAAA;YANL;UAOF,CAAA;QACF;MAAA;AAOFpJ,QAAmBi+B,EAAAA;AC5EnB,YAEMxzB,KAAa,iBAGb0zB,KAAkB,WAAU1zB,EAAAA,IAC5B2zB,KAAe,QAAO3zB,EAAAA,IACtByF,KAAuB,OAAMzF,EAAAA,aAG7B4F,KAAoB,UAGpBguB,KAAwB,UAExBC,KAAqB,aAGrBC,KAAuB,GAAED,EAAAA,iBAA+CA,EAAAA,sBAIxEt1B,KAAU,EACduV,QAAQ,MACRigB,YAAY,gBACZC,cAAAA,OACA18B,QAAQ,MACR28B,WAAW,CAAC,KAAK,KAAK,CAAA,EAAA,GAGlBz1B,KAAc,EAClBsV,QAAQ,iBACRigB,YAAY,UACZC,cAAc,WACd18B,QAAQ,WACR28B,WAAW,QAAA;MAOb,MAAMC,WAAkBx0B,EAAAA;QACtBV,YAAY3N,IAASsN,IAAAA;AACnBgB,gBAAMtO,IAASsN,EAAAA,GAGftE,KAAK85B,eAAe,oBAAIjjC,OACxBmJ,KAAK+5B,sBAAsB,oBAAIljC,OAC/BmJ,KAAKg6B,eAA6D,cAA9C3gC,iBAAiB2G,KAAKuF,QAAAA,EAAU6X,YAA0B,OAAOpd,KAAKuF,UAC1FvF,KAAKi6B,gBAAgB,MACrBj6B,KAAKk6B,YAAY,MACjBl6B,KAAKm6B,sBAAsB,EACzBC,iBAAiB,GACjBC,iBAAiB,EAAA,GAEnBr6B,KAAKs6B,QAAAA;QACP;QAGA,WAAA,UAAWp2B;AACT,iBAAOA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAOA;QACT;QAEA,WAAA,OAAW5I;AACT,iBArES;QAsEX;QAGA++B,UAAAA;AACEt6B,eAAKu6B,iCAAAA,GACLv6B,KAAKw6B,yBAAAA,GAEDx6B,KAAKk6B,YACPl6B,KAAKk6B,UAAUO,WAAAA,IAEfz6B,KAAKk6B,YAAYl6B,KAAK06B,gBAAAA;AAGxB,qBAAWC,MAAW36B,KAAK+5B,oBAAoB96B,OAAAA,EAC7Ce,MAAKk6B,UAAUU,QAAQD,EAAAA;QAE3B;QAEAj1B,UAAAA;AACE1F,eAAKk6B,UAAUO,WAAAA,GACfn1B,MAAMI,QAAAA;QACR;QAGAlB,kBAAkBF,IAAAA;AAWhB,iBATAA,GAAOrH,SAASnE,EAAWwL,GAAOrH,MAAAA,KAAWjE,SAAS8B,MAGtDwJ,GAAOo1B,aAAap1B,GAAOmV,SAAU,GAAEnV,GAAOmV,MAAAA,gBAAsBnV,GAAOo1B,YAE3C,YAAA,OAArBp1B,GAAOs1B,cAChBt1B,GAAOs1B,YAAYt1B,GAAOs1B,UAAU98B,MAAM,GAAA,EAAKuJ,IAAI7D,CAAAA,OAAS7F,OAAOC,WAAW4F,EAAAA,CAAAA,IAGzE8B;QACT;QAEAk2B,2BAAAA;AACOx6B,eAAKwF,QAAQm0B,iBAKlBp5B,EAAaC,IAAIR,KAAKwF,QAAQvI,QAAQq8B,EAAAA,GAEtC/4B,EAAaY,GAAGnB,KAAKwF,QAAQvI,QAAQq8B,IAAaC,IAAuBp6B,CAAAA,OAAAA;AACvE,kBAAM07B,KAAoB76B,KAAK+5B,oBAAoB1iC,IAAI8H,GAAMlC,OAAOqf,IAAAA;AACpE,gBAAIue,IAAmB;AACrB17B,cAAAA,GAAMkD,eAAAA;AACN,oBAAM9H,KAAOyF,KAAKg6B,gBAAgB/hC,QAC5Bke,KAAS0kB,GAAkBpkB,YAAYzW,KAAKuF,SAASkR;AAC3D,kBAAIlc,GAAKugC,SAEP,QAAA,KADAvgC,GAAKugC,SAAS,EAAEtpB,KAAK2E,IAAQ4kB,UAAU,SAAA,CAAA;AAKzCxgC,cAAAA,GAAKsiB,YAAY1G;YACnB;UAAA,CAAA;QAEJ;QAEAukB,kBAAAA;AACE,gBAAMjmB,KAAU,EACdla,MAAMyF,KAAKg6B,cACXJ,WAAW55B,KAAKwF,QAAQo0B,WACxBF,YAAY15B,KAAKwF,QAAQk0B,WAAAA;AAG3B,iBAAO,IAAIsB,qBAAqB/5B,CAAAA,OAAWjB,KAAKi7B,kBAAkBh6B,EAAAA,GAAUwT,EAAAA;QAC9E;QAGAwmB,kBAAkBh6B,IAAAA;AAChB,gBAAMi6B,KAAgBnH,CAAAA,OAAS/zB,KAAK85B,aAAaziC,IAAK,IAAG08B,GAAM92B,OAAO3E,EAAAA,EAAAA,GAChE+0B,KAAW0G,CAAAA,OAAAA;AACf/zB,iBAAKm6B,oBAAoBC,kBAAkBrG,GAAM92B,OAAOwZ,WACxDzW,KAAKm7B,SAASD,GAAcnH,EAAAA,CAAAA;UAAO,GAG/BsG,MAAmBr6B,KAAKg6B,gBAAgBhhC,SAASoB,iBAAiByiB,WAClEue,KAAkBf,MAAmBr6B,KAAKm6B,oBAAoBE;AACpEr6B,eAAKm6B,oBAAoBE,kBAAkBA;AAE3C,qBAAWtG,MAAS9yB,IAAS;AAC3B,gBAAA,CAAK8yB,GAAMsH,gBAAgB;AACzBr7B,mBAAKi6B,gBAAgB,MACrBj6B,KAAKs7B,kBAAkBJ,GAAcnH,EAAAA,CAAAA;AAErC;YACF;AAEA,kBAAMwH,KAA2BxH,GAAM92B,OAAOwZ,aAAazW,KAAKm6B,oBAAoBC;AAEpF,gBAAIgB,MAAmBG,IAAAA;AAGrB,kBAFAlO,GAAS0G,EAAAA,GAAAA,CAEJsG,GACH;YAAA,MAOCe,CAAAA,MAAoBG,MACvBlO,GAAS0G,EAAAA;UAEb;QACF;QAEAwG,mCAAAA;AACEv6B,eAAK85B,eAAe,oBAAIjjC,OACxBmJ,KAAK+5B,sBAAsB,oBAAIljC;AAE/B,gBAAM2kC,KAAch1B,EAAetH,KAAKq6B,IAAuBv5B,KAAKwF,QAAQvI,MAAAA;AAE5E,qBAAWw+B,MAAUD,IAAa;AAEhC,gBAAA,CAAKC,GAAOnf,QAAQ3iB,EAAW8hC,EAAAA,EAC7B;AAGF,kBAAMZ,KAAoBr0B,EAAeG,QAAQ+0B,UAAUD,GAAOnf,IAAAA,GAAOtc,KAAKuF,QAAAA;AAG1ErM,cAAU2hC,EAAAA,MACZ76B,KAAK85B,aAAa/iC,IAAI2kC,UAAUD,GAAOnf,IAAAA,GAAOmf,EAAAA,GAC9Cz7B,KAAK+5B,oBAAoBhjC,IAAI0kC,GAAOnf,MAAMue,EAAAA;UAE9C;QACF;QAEAM,SAASl+B,IAAAA;AACH+C,eAAKi6B,kBAAkBh9B,OAI3B+C,KAAKs7B,kBAAkBt7B,KAAKwF,QAAQvI,MAAAA,GACpC+C,KAAKi6B,gBAAgBh9B,IACrBA,GAAOnD,UAAUuQ,IAAIkB,EAAAA,GACrBvL,KAAK27B,iBAAiB1+B,EAAAA,GAEtBsD,EAAaoB,QAAQ3B,KAAKuF,UAAU8zB,IAAgB,EAAEx5B,eAAe5C,GAAAA,CAAAA;QACvE;QAEA0+B,iBAAiB1+B,IAAAA;AAEf,cAAIA,GAAOnD,UAAUC,SAlNQ,eAAA,EAmN3ByM,GAAeG,QAxMY,oBAwMsB1J,GAAOzD,QAzMpC,WAAA,CAAA,EA0MjBM,UAAUuQ,IAAIkB,EAAAA;cAInB,YAAWqwB,MAAap1B,EAAeO,QAAQ9J,IAnNnB,mBAAA,EAsN1B,YAAWsY,MAAQ/O,EAAeS,KAAK20B,IAAWnC,EAAAA,EAChDlkB,CAAAA,GAAKzb,UAAUuQ,IAAIkB,EAAAA;QAGzB;QAEA+vB,kBAAkBvrB,IAAAA;AAChBA,UAAAA,GAAOjW,UAAUlC,OAAO2T,EAAAA;AAExB,gBAAMswB,KAAcr1B,EAAetH,KAAM,GAAEq6B,EAAAA,IAAyBhuB,EAAAA,IAAqBwE,EAAAA;AACzF,qBAAWuD,MAAQuoB,GACjBvoB,CAAAA,GAAKxZ,UAAUlC,OAAO2T,EAAAA;QAE1B;QAGA,OAAA,gBAAuBjH,IAAAA;AACrB,iBAAOtE,KAAKuI,KAAK,WAAA;AACf,kBAAMC,KAAOqxB,GAAU5xB,oBAAoBjI,MAAMsE,EAAAA;AAEjD,gBAAsB,YAAA,OAAXA,IAAX;AAIA,kBAAA,WAAIkE,GAAKlE,EAAAA,KAAyBA,GAAO/C,WAAW,GAAA,KAAmB,kBAAX+C,GAC1D,OAAM,IAAIa,UAAW,oBAAmBb,EAAAA,GAAAA;AAG1CkE,cAAAA,GAAKlE,EAAAA,EAAAA;YANL;UAOF,CAAA;QACF;MAAA;AAOF/D,QAAaY,GAAGlJ,QAAQmT,IAAqB,MAAA;AAC3C,mBAAW0wB,MAAOt1B,EAAetH,KA9PT,wBAAA,EA+PtB26B,IAAU5xB,oBAAoB6zB,EAAAA;MAChC,CAAA,GAOF5gC,EAAmB2+B,EAAAA;ACrRnB,YAEMl0B,KAAa,WAEb8J,KAAc,OAAM9J,EAAAA,IACpB+J,KAAgB,SAAQ/J,EAAAA,IACxB4J,KAAc,OAAM5J,EAAAA,IACpB6J,KAAe,QAAO7J,EAAAA,IACtB0F,KAAwB,QAAO1F,EAAAA,IAC/BqF,KAAiB,UAASrF,EAAAA,IAC1ByF,KAAuB,OAAMzF,EAAAA,IAE7Bo2B,KAAiB,aACjBC,KAAkB,cAClB5S,KAAe,WACfC,KAAiB,aACjB4S,KAAW,QACXC,KAAU,OAEV3wB,KAAoB,UACpBkqB,KAAkB,QAClB9lB,KAAkB,QAGlBwsB,KAA2B,oBAE3BC,KAAgC,QAAOD,EAAAA,KAKvC1zB,KAAuB,4EACvB4zB,KAAuB,YAFMD,EAAAA,qBAAiDA,EAAAA,iBAA6CA,EAAAA,KAE/E3zB,EAAAA,IAE5C6zB,KAA+B,IAAG/wB,EAAAA,4BAA6CA,EAAAA,6BAA8CA,EAAAA;MAMnI,MAAMgxB,WAAYl3B,EAAAA;QAChBV,YAAY3N,IAAAA;AACVsO,gBAAMtO,EAAAA,GACNgJ,KAAKqqB,UAAUrqB,KAAKuF,SAAS/L,QAfN,qCAAA,GAiBlBwG,KAAKqqB,YAOVrqB,KAAKw8B,sBAAsBx8B,KAAKqqB,SAASrqB,KAAKy8B,aAAAA,CAAAA,GAE9Cl8B,EAAaY,GAAGnB,KAAKuF,UAAUyF,IAAe7L,CAAAA,OAASa,KAAK4N,SAASzO,EAAAA,CAAAA;QACvE;QAGA,WAAA,OAAW5D;AACT,iBA3DS;QA4DX;QAGAoV,OAAAA;AACE,gBAAM+rB,KAAY18B,KAAKuF;AACvB,cAAIvF,KAAK28B,cAAcD,EAAAA,EACrB;AAIF,gBAAME,KAAS58B,KAAK68B,eAAAA,GAEdC,KAAYF,KAChBr8B,EAAaoB,QAAQi7B,IAAQntB,IAAY,EAAE5P,eAAe68B,GAAAA,CAAAA,IAC1D;AAEgBn8B,YAAaoB,QAAQ+6B,IAAWntB,IAAY,EAAE1P,eAAe+8B,GAAAA,CAAAA,EAEjE76B,oBAAqB+6B,MAAaA,GAAU/6B,qBAI1D/B,KAAK+8B,YAAYH,IAAQF,EAAAA,GACzB18B,KAAKg9B,UAAUN,IAAWE,EAAAA;QAC5B;QAGAI,UAAUhmC,IAASimC,IAAAA;AACZjmC,UAAAA,OAILA,GAAQ8C,UAAUuQ,IAAIkB,EAAAA,GAEtBvL,KAAKg9B,UAAUx2B,EAAekB,uBAAuB1Q,EAAAA,CAAAA,GAgBrDgJ,KAAK8F,eAdYqL,MAAAA;AACsB,sBAAjCna,GAAQkD,aAAa,MAAA,KAKzBlD,GAAQwM,gBAAgB,UAAA,GACxBxM,GAAQsM,aAAa,iBAAA,IAAiB,GACtCtD,KAAKk9B,gBAAgBlmC,IAAAA,IAAS,GAC9BuJ,EAAaoB,QAAQ3K,IAASwY,IAAa,EACzC3P,eAAeo9B,GAAAA,CAAAA,KARfjmC,GAAQ8C,UAAUuQ,IAAIsF,EAAAA;UAStB,GAG0B3Y,IAASA,GAAQ8C,UAAUC,SAAS07B,EAAAA,CAAAA;QACpE;QAEAsH,YAAY/lC,IAASimC,IAAAA;AACdjmC,UAAAA,OAILA,GAAQ8C,UAAUlC,OAAO2T,EAAAA,GACzBvU,GAAQi6B,KAAAA,GAERjxB,KAAK+8B,YAAYv2B,EAAekB,uBAAuB1Q,EAAAA,CAAAA,GAcvDgJ,KAAK8F,eAZYqL,MAAAA;AACsB,sBAAjCna,GAAQkD,aAAa,MAAA,KAKzBlD,GAAQsM,aAAa,iBAAA,KAAiB,GACtCtM,GAAQsM,aAAa,YAAY,IAAA,GACjCtD,KAAKk9B,gBAAgBlmC,IAAAA,KAAS,GAC9BuJ,EAAaoB,QAAQ3K,IAAS0Y,IAAc,EAAE7P,eAAeo9B,GAAAA,CAAAA,KAP3DjmC,GAAQ8C,UAAUlC,OAAO+X,EAAAA;UAOgD,GAG/C3Y,IAASA,GAAQ8C,UAAUC,SAAS07B,EAAAA,CAAAA;QACpE;QAEA7nB,SAASzO,IAAAA;AACP,cAAA,CAAM,CAAC48B,IAAgBC,IAAiB5S,IAAcC,IAAgB4S,IAAUC,EAAAA,EAASh7B,SAAS/B,GAAMlI,GAAAA,EACtG;AAGFkI,UAAAA,GAAM2sB,gBAAAA,GACN3sB,GAAMkD,eAAAA;AAEN,gBAAMuE,KAAW5G,KAAKy8B,aAAAA,EAAe54B,OAAO7M,CAAAA,OAAAA,CAAY2C,EAAW3C,EAAAA,CAAAA;AACnE,cAAImmC;AAEJ,cAAI,CAAClB,IAAUC,EAAAA,EAASh7B,SAAS/B,GAAMlI,GAAAA,EACrCkmC,CAAAA,KAAoBv2B,GAASzH,GAAMlI,QAAQglC,KAAW,IAAIr1B,GAAS7N,SAAS,CAAA;eACvE;AACL,kBAAMyV,KAAS,CAACwtB,IAAiB3S,EAAAA,EAAgBnoB,SAAS/B,GAAMlI,GAAAA;AAChEkmC,YAAAA,KAAoB//B,EAAqBwJ,IAAUzH,GAAMlC,QAAQuR,IAAAA,IAAQ;UAC3E;AAEI2uB,UAAAA,OACFA,GAAkBzS,MAAM,EAAE0S,eAAAA,KAAe,CAAA,GACzCb,GAAIt0B,oBAAoBk1B,EAAAA,EAAmBxsB,KAAAA;QAE/C;QAEA8rB,eAAAA;AACE,iBAAOj2B,EAAetH,KAAKm9B,IAAqBr8B,KAAKqqB,OAAAA;QACvD;QAEAwS,iBAAAA;AACE,iBAAO78B,KAAKy8B,aAAAA,EAAev9B,KAAK2H,CAAAA,OAAS7G,KAAK28B,cAAc91B,EAAAA,CAAAA,KAAW;QACzE;QAEA21B,sBAAsBzsB,IAAQnJ,IAAAA;AAC5B5G,eAAKq9B,yBAAyBttB,IAAQ,QAAQ,SAAA;AAE9C,qBAAWlJ,MAASD,GAClB5G,MAAKs9B,6BAA6Bz2B,EAAAA;QAEtC;QAEAy2B,6BAA6Bz2B,IAAAA;AAC3BA,UAAAA,KAAQ7G,KAAKu9B,iBAAiB12B,EAAAA;AAC9B,gBAAM22B,KAAWx9B,KAAK28B,cAAc91B,EAAAA,GAC9B42B,KAAYz9B,KAAK09B,iBAAiB72B,EAAAA;AACxCA,UAAAA,GAAMvD,aAAa,iBAAiBk6B,EAAAA,GAEhCC,OAAc52B,MAChB7G,KAAKq9B,yBAAyBI,IAAW,QAAQ,cAAA,GAG9CD,MACH32B,GAAMvD,aAAa,YAAY,IAAA,GAGjCtD,KAAKq9B,yBAAyBx2B,IAAO,QAAQ,KAAA,GAG7C7G,KAAK29B,mCAAmC92B,EAAAA;QAC1C;QAEA82B,mCAAmC92B,IAAAA;AACjC,gBAAM5J,KAASuJ,EAAekB,uBAAuBb,EAAAA;AAEhD5J,UAAAA,OAIL+C,KAAKq9B,yBAAyBpgC,IAAQ,QAAQ,UAAA,GAE1C4J,GAAMvO,MACR0H,KAAKq9B,yBAAyBpgC,IAAQ,mBAAoB,GAAE4J,GAAMvO,EAAAA,EAAAA;QAEtE;QAEA4kC,gBAAgBlmC,IAAS4mC,IAAAA;AACvB,gBAAMH,KAAYz9B,KAAK09B,iBAAiB1mC,EAAAA;AACxC,cAAA,CAAKymC,GAAU3jC,UAAUC,SAhMN,UAAA,EAiMjB;AAGF,gBAAM4O,KAASA,CAAC3Q,IAAUk0B,OAAAA;AACxB,kBAAMl1B,KAAUwP,EAAeG,QAAQ3O,IAAUylC,EAAAA;AAC7CzmC,YAAAA,MACFA,GAAQ8C,UAAU6O,OAAOujB,IAAW0R,EAAAA;UACtC;AAGFj1B,UAAAA,GAAOwzB,IAA0B5wB,EAAAA,GACjC5C,GAzM2B,kBAyMIgH,EAAAA,GAC/B8tB,GAAUn6B,aAAa,iBAAiBs6B,EAAAA;QAC1C;QAEAP,yBAAyBrmC,IAAS8d,IAAWtS,IAAAA;AACtCxL,UAAAA,GAAQiD,aAAa6a,EAAAA,KACxB9d,GAAQsM,aAAawR,IAAWtS,EAAAA;QAEpC;QAEAm6B,cAAcvsB,IAAAA;AACZ,iBAAOA,GAAKtW,UAAUC,SAASwR,EAAAA;QACjC;QAGAgyB,iBAAiBntB,IAAAA;AACf,iBAAOA,GAAKtJ,QAAQu1B,EAAAA,IAAuBjsB,KAAO5J,EAAeG,QAAQ01B,IAAqBjsB,EAAAA;QAChG;QAGAstB,iBAAiBttB,IAAAA;AACf,iBAAOA,GAAK5W,QA1NO,6BAAA,KA0NoB4W;QACzC;QAGA,OAAA,gBAAuB9L,IAAAA;AACrB,iBAAOtE,KAAKuI,KAAK,WAAA;AACf,kBAAMC,KAAO+zB,GAAIt0B,oBAAoBjI,IAAAA;AAErC,gBAAsB,YAAA,OAAXsE,IAAX;AAIA,kBAAA,WAAIkE,GAAKlE,EAAAA,KAAyBA,GAAO/C,WAAW,GAAA,KAAmB,kBAAX+C,GAC1D,OAAM,IAAIa,UAAW,oBAAmBb,EAAAA,GAAAA;AAG1CkE,cAAAA,GAAKlE,EAAAA,EAAAA;YANL;UAOF,CAAA;QACF;MAAA;AAOF/D,QAAaY,GAAGnI,UAAUqS,IAAsB5C,IAAsB,SAAUtJ,IAAAA;AAC1E,SAAC,KAAK,MAAA,EAAQ+B,SAASlB,KAAKgI,OAAAA,KAC9B7I,GAAMkD,eAAAA,GAGJ1I,EAAWqG,IAAAA,KAIfu8B,GAAIt0B,oBAAoBjI,IAAAA,EAAM2Q,KAAAA;MAChC,CAAA,GAKApQ,EAAaY,GAAGlJ,QAAQmT,IAAqB,MAAA;AAC3C,mBAAWpU,MAAWwP,EAAetH,KAAKo9B,EAAAA,EACxCC,IAAIt0B,oBAAoBjR,EAAAA;MAC1B,CAAA,GAMFkE,EAAmBqhC,EAAAA;ACxSnB,YAEM52B,KAAa,aAEbk4B,KAAmB,YAAWl4B,EAAAA,IAC9Bm4B,KAAkB,WAAUn4B,EAAAA,IAC5BknB,KAAiB,UAASlnB,EAAAA,IAC1Bo4B,KAAkB,WAAUp4B,EAAAA,IAC5B8J,KAAc,OAAM9J,EAAAA,IACpB+J,KAAgB,SAAQ/J,EAAAA,IACxB4J,KAAc,OAAM5J,EAAAA,IACpB6J,KAAe,QAAO7J,EAAAA,IAGtBq4B,KAAkB,QAClBruB,KAAkB,QAClBkhB,KAAqB,WAErB1sB,KAAc,EAClBiyB,WAAW,WACX6H,UAAU,WACV1H,OAAO,SAAA,GAGHryB,KAAU,EACdkyB,WAAAA,MACA6H,UAAAA,MACA1H,OAAO,IAAA;MAOT,MAAM2H,WAAc74B,EAAAA;QAClBV,YAAY3N,IAASsN,IAAAA;AACnBgB,gBAAMtO,IAASsN,EAAAA,GAEftE,KAAK22B,WAAW,MAChB32B,KAAKm+B,uBAAAA,OACLn+B,KAAKo+B,0BAAAA,OACLp+B,KAAKi3B,cAAAA;QACP;QAGA,WAAA,UAAW/yB;AACT,iBAAOA;QACT;QAEA,WAAA,cAAWC;AACT,iBAAOA;QACT;QAEA,WAAA,OAAW5I;AACT,iBAtDS;QAuDX;QAGAoV,OAAAA;AACoBpQ,YAAaoB,QAAQ3B,KAAKuF,UAAUgK,EAAAA,EAExCxN,qBAId/B,KAAKq+B,cAAAA,GAEDr+B,KAAKwF,QAAQ4wB,aACfp2B,KAAKuF,SAASzL,UAAUuQ,IAvDN,MAAA,GAiEpBrK,KAAKuF,SAASzL,UAAUlC,OAAOomC,EAAAA,GAC/BtjC,EAAOsF,KAAKuF,QAAAA,GACZvF,KAAKuF,SAASzL,UAAUuQ,IAAIsF,IAAiBkhB,EAAAA,GAE7C7wB,KAAK8F,eAXYqL,MAAAA;AACfnR,iBAAKuF,SAASzL,UAAUlC,OAAOi5B,EAAAA,GAC/BtwB,EAAaoB,QAAQ3B,KAAKuF,UAAUiK,EAAAA,GAEpCxP,KAAKs+B,mBAAAA;UAAoB,GAOGt+B,KAAKuF,UAAUvF,KAAKwF,QAAQ4wB,SAAAA;QAC5D;QAEA1lB,OAAAA;AACO1Q,eAAKu+B,QAAAA,MAIQh+B,EAAaoB,QAAQ3B,KAAKuF,UAAUkK,EAAAA,EAExC1N,qBAUd/B,KAAKuF,SAASzL,UAAUuQ,IAAIwmB,EAAAA,GAC5B7wB,KAAK8F,eAPYqL,MAAAA;AACfnR,iBAAKuF,SAASzL,UAAUuQ,IAAI2zB,EAAAA,GAC5Bh+B,KAAKuF,SAASzL,UAAUlC,OAAOi5B,IAAoBlhB,EAAAA,GACnDpP,EAAaoB,QAAQ3B,KAAKuF,UAAUmK,EAAAA;UAAa,GAIrB1P,KAAKuF,UAAUvF,KAAKwF,QAAQ4wB,SAAAA;QAC5D;QAEA1wB,UAAAA;AACE1F,eAAKq+B,cAAAA,GAEDr+B,KAAKu+B,QAAAA,KACPv+B,KAAKuF,SAASzL,UAAUlC,OAAO+X,EAAAA,GAGjCrK,MAAMI,QAAAA;QACR;QAEA64B,UAAAA;AACE,iBAAOv+B,KAAKuF,SAASzL,UAAUC,SAAS4V,EAAAA;QAC1C;QAIA2uB,qBAAAA;AACOt+B,eAAKwF,QAAQy4B,aAIdj+B,KAAKm+B,wBAAwBn+B,KAAKo+B,4BAItCp+B,KAAK22B,WAAWx5B,WAAW,MAAA;AACzB6C,iBAAK0Q,KAAAA;UAAM,GACV1Q,KAAKwF,QAAQ+wB,KAAAA;QAClB;QAEAiI,eAAer/B,IAAOs/B,IAAAA;AACpB,kBAAQt/B,GAAMsB,MAAAA;YACZ,KAAK;YACL,KAAK;AACHT,mBAAKm+B,uBAAuBM;AAC5B;YAGF,KAAK;YACL,KAAK;AACHz+B,mBAAKo+B,0BAA0BK;UAAAA;AASnC,cAAIA,GAEF,QAAA,KADAz+B,KAAKq+B,cAAAA;AAIP,gBAAM5vB,KAActP,GAAMU;AACtBG,eAAKuF,aAAakJ,MAAezO,KAAKuF,SAASxL,SAAS0U,EAAAA,KAI5DzO,KAAKs+B,mBAAAA;QACP;QAEArH,gBAAAA;AACE12B,YAAaY,GAAGnB,KAAKuF,UAAUs4B,IAAiB1+B,CAAAA,OAASa,KAAKw+B,eAAer/B,IAAAA,IAAO,CAAA,GACpFoB,EAAaY,GAAGnB,KAAKuF,UAAUu4B,IAAgB3+B,CAAAA,OAASa,KAAKw+B,eAAer/B,IAAAA,KAAO,CAAA,GACnFoB,EAAaY,GAAGnB,KAAKuF,UAAUsnB,IAAe1tB,CAAAA,OAASa,KAAKw+B,eAAer/B,IAAAA,IAAO,CAAA,GAClFoB,EAAaY,GAAGnB,KAAKuF,UAAUw4B,IAAgB5+B,CAAAA,OAASa,KAAKw+B,eAAer/B,IAAAA,KAAO,CAAA;QACrF;QAEAk/B,gBAAAA;AACEnwB,uBAAalO,KAAK22B,QAAAA,GAClB32B,KAAK22B,WAAW;QAClB;QAGA,OAAA,gBAAuBryB,IAAAA;AACrB,iBAAOtE,KAAKuI,KAAK,WAAA;AACf,kBAAMC,KAAO01B,GAAMj2B,oBAAoBjI,MAAMsE,EAAAA;AAE7C,gBAAsB,YAAA,OAAXA,IAAqB;AAC9B,kBAAA,WAAWkE,GAAKlE,EAAAA,EACd,OAAM,IAAIa,UAAW,oBAAmBb,EAAAA,GAAAA;AAG1CkE,cAAAA,GAAKlE,EAAAA,EAAQtE,IAAAA;YACf;UACF,CAAA;QACF;MAAA;AAAA,aAOF4H,EAAqBs2B,EAAAA,GAMrBhjC,EAAmBgjC,EAAAA,GC1MJ,EACb91B,OAAAA,GACAM,QAAAA,GACA0D,UAAAA,IACA4D,UAAAA,IACAma,UAAAA,IACAmF,OAAAA,IACA0B,WAAAA,IACAmI,SAAAA,IACAU,WAAAA,IACA0C,KAAAA,IACA2B,OAAAA,IACAzH,SAAAA,GAAAA;IAAAA,CAAAA;;;", "names": ["elementMap", "Map", "Data", "set", "element", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "keys", "remove", "delete", "TRANSITION_END", "parseSelector", "selector", "window", "CSS", "escape", "replace", "match", "id", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "object", "j<PERSON>y", "nodeType", "getElement", "length", "document", "querySelector", "isVisible", "getClientRects", "elementIsVisible", "getComputedStyle", "getPropertyValue", "closedDetails", "closest", "summary", "parentNode", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "getAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "push", "execute", "<PERSON><PERSON><PERSON><PERSON>", "args", "defaultValue", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "split", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "listLength", "index", "indexOf", "Math", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "makeEventUid", "uid", "getElementEvents", "<PERSON><PERSON><PERSON><PERSON>", "events", "callable", "delegationSelector", "Object", "values", "find", "event", "normalizeParameters", "originalTypeEvent", "delegationFunction", "isDelegated", "typeEvent", "getTypeEvent", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFunction", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "call", "this", "handlers", "previousFunction", "dom<PERSON><PERSON>s", "querySelectorAll", "dom<PERSON>lement", "hydrateObj", "EventHandler", "off", "type", "apply", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "entries", "includes", "on", "one", "inNamespace", "isNamespace", "startsWith", "elementEvent", "slice", "keyHandlers", "trigger", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "preventDefault", "obj", "meta", "value", "_unused", "defineProperty", "configurable", "normalizeData", "toString", "JSON", "parse", "decodeURIComponent", "normalizeDataKey", "chr", "toLowerCase", "Manipulator", "setDataAttribute", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "bs<PERSON><PERSON>s", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "Config", "<PERSON><PERSON><PERSON>", "DefaultType", "Error", "_getConfig", "config", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "jsonConfig", "constructor", "configTypes", "property", "expectedTypes", "valueType", "prototype", "RegExp", "test", "TypeError", "toUpperCase", "BaseComponent", "super", "_element", "_config", "DATA_KEY", "dispose", "EVENT_KEY", "propertyName", "getOwnPropertyNames", "_queueCallback", "isAnimated", "getInstance", "VERSION", "getSelector", "hrefAttribute", "trim", "map", "sel", "join", "SelectorEngine", "concat", "Element", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "el", "getSelectorFromElement", "getElementFromSelector", "getMultipleElementsFromSelector", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "getOrCreateInstance", "EVENT_CLOSE", "EVENT_CLOSED", "<PERSON><PERSON>", "close", "_destroyElement", "each", "data", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "button", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "endCallback", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "Swipe", "isSupported", "_deltaX", "_supportPointerEvents", "PointerEvent", "_initEvents", "_start", "_eventIsPointerPenTouch", "clientX", "touches", "_end", "_handleSwipe", "_move", "absDeltaX", "abs", "direction", "add", "pointerType", "navigator", "maxTouchPoints", "DATA_API_KEY", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "EVENT_CLICK_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "SELECTOR_ACTIVE", "SELECTOR_ITEM", "SELECTOR_ACTIVE_ITEM", "KEY_TO_DIRECTION", "ArrowLeft", "ArrowRight", "interval", "keyboard", "pause", "ride", "touch", "wrap", "Carousel", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "_addEventListeners", "cycle", "_slide", "nextWhenVisible", "hidden", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "to", "items", "_getItems", "activeIndex", "_getItemIndex", "_getActive", "order", "defaultInterval", "_keydown", "_addTouchEventListeners", "img", "swipeConfig", "_directionToOrder", "endCallBack", "clearTimeout", "_setActiveIndicatorElement", "activeIndicator", "newActiveIndicator", "elementInterval", "parseInt", "isNext", "nextElement", "nextElementIndex", "triggerEvent", "eventName", "_orderToDirection", "isCycling", "directionalClassName", "orderClassName", "completeCallBack", "_isAnimated", "clearInterval", "carousel", "slideIndex", "carousels", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_DEEPER_CHILDREN", "parent", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElement", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "activeInstance", "dimension", "_getDimension", "style", "scrollSize", "complete", "getBoundingClientRect", "selected", "trigger<PERSON><PERSON>y", "isOpen", "top", "bottom", "right", "left", "auto", "basePlacements", "start", "end", "clippingParents", "viewport", "popper", "reference", "variationPlacements", "reduce", "acc", "placement", "placements", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases", "getNodeName", "nodeName", "getWindow", "node", "ownerDocument", "defaultView", "isHTMLElement", "HTMLElement", "isShadowRoot", "applyStyles$1", "enabled", "phase", "_ref", "state", "elements", "for<PERSON>ach", "styles", "assign", "effect", "_ref2", "initialStyles", "position", "options", "strategy", "margin", "arrow", "hasOwnProperty", "attribute", "requires", "getBasePlacement", "round", "getUAString", "uaData", "userAgentData", "brands", "isArray", "item", "brand", "version", "userAgent", "isLayoutViewport", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "height", "visualViewport", "addVisualOffsets", "x", "offsetLeft", "y", "offsetTop", "getLayoutRect", "rootNode", "isSameNode", "host", "isTableElement", "getDocumentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "getOffsetParent", "isFirefox", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "getMainAxisFromPlacement", "within", "mathMax", "mathMin", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "arrow$1", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "len", "padding", "rects", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "offset", "axisProp", "centerOffset", "_options$element", "requiresIfExists", "getVariation", "unsetSides", "mapToStyles", "_Object$assign2", "popperRect", "variation", "offsets", "gpuAcceleration", "adaptive", "roundOffsets", "isFixed", "_offsets$x", "_offsets$y", "_ref3", "hasX", "hasY", "sideX", "sideY", "win", "heightProp", "widthProp", "_Object$assign", "commonStyles", "_ref4", "dpr", "devicePixelRatio", "computeStyles$1", "_ref5", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "passive", "eventListeners", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "update", "hash", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getWindowScroll", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "getWindowScrollBarX", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "getScrollParent", "listScrollParents", "_element$ownerDocumen", "isBody", "updatedList", "rectToClientRect", "rect", "getClientRectFromMixedType", "clippingParent", "html", "layoutViewport", "clientTop", "clientLeft", "winScroll", "scrollWidth", "scrollHeight", "computeOffsets", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$strategy", "_options$boundary", "boundary", "_options$rootBoundary", "rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "clippingClientRect", "mainClippingParents", "clipperElement", "firstClippingParent", "clippingRect", "accRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "flip$1", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "oppositePlacement", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "i", "_basePlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "_loop", "_i", "fittingPlacement", "reset", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide$1", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "offset$1", "_options$offset", "invertDistance", "skidding", "distance", "_data$state$placement", "popperOffsets$1", "preventOverflow$1", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "normalizedTetherOffsetValue", "offsetModifierState", "_offsetModifierState$", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMax", "preventedOffset", "_offsetModifierState$2", "_mainSide", "_altSide", "_offset", "_len", "_min", "_max", "isOriginSide", "_offsetModifierValue", "_tetherMin", "_tetherMax", "_preventedOffset", "v", "getCompositeRect", "elementOrVirtualElement", "isOffsetParentAnElement", "offsetParentIsScaled", "modifiers", "visited", "result", "modifier", "dep", "depModifier", "DEFAULT_OPTIONS", "areValidElements", "arguments", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "pending", "orderedModifiers", "effectCleanupFns", "isDestroyed", "setOptions", "setOptionsAction", "cleanupModifierEffects", "merged", "current", "existing", "m", "_ref$options", "cleanupFn", "forceUpdate", "_state$elements", "_state$orderedModifie", "_state$orderedModifie2", "Promise", "resolve", "then", "destroy", "onFirstUpdate", "createPopper", "computeStyles", "applyStyles", "flip", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "autoClose", "display", "popperConfig", "Dropdown", "_popper", "_parent", "_menu", "_inNavbar", "_detectNavbar", "_createPopper", "focus", "_completeHide", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "_selectMenuItem", "openToggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "isInput", "isEscapeEvent", "isUpOrDownEvent", "getToggleButton", "stopPropagation", "dataApiKeydownHandler", "clearMenus", "EVENT_MOUSEDOWN", "className", "clickCallback", "rootElement", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "EVENT_FOCUSIN", "EVENT_KEYDOWN_TAB", "TAB_NAV_BACKWARD", "autofocus", "trapElement", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "shift<PERSON>ey", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "getWidth", "documentWidth", "innerWidth", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "styleProperty", "scrollbarWidth", "_applyManipulationCallback", "setProperty", "actualValue", "removeProperty", "callBack", "EVENT_HIDE_PREVENTED", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "EVENT_KEYDOWN_DISMISS", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_scrollBar", "_adjustDialog", "_showElement", "_hideModal", "handleUpdate", "modalBody", "transitionComplete", "_triggerBackdropTransition", "event2", "_resetAdjustments", "isModalOverflowing", "initialOverflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "alreadyOpen", "CLASS_NAME_SHOWING", "CLASS_NAME_HIDING", "OPEN_SELECTOR", "<PERSON><PERSON><PERSON>", "blur", "completeCallback", "DefaultAllowlist", "area", "br", "col", "code", "dd", "div", "dl", "dt", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "uriAttributes", "SAFE_URL_PATTERN", "allowedAttribute", "allowedAttributeList", "attributeName", "nodeValue", "attributeRegex", "regex", "allowList", "content", "extraClass", "sanitize", "sanitizeFn", "template", "DefaultContentType", "entry", "TemplateFactory", "get<PERSON>ontent", "_resolvePossibleFunction", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "_checkContent", "toHtml", "templateWrapper", "innerHTML", "_maybeSanitize", "text", "_setContent", "arg", "templateElement", "_putElementInTemplate", "textContent", "unsafeHtml", "sanitizeFunction", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "elementName", "attributeList", "allowedAttributes", "DISALLOWED_ATTRIBUTES", "CLASS_NAME_FADE", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "animation", "container", "customClass", "delay", "title", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_templateFactory", "_newContent", "tip", "_setListeners", "_fixTitle", "enable", "disable", "toggle<PERSON>nabled", "click", "_leave", "_enter", "_hideModalHandler", "_disposePopper", "_isWithContent", "isInTheDom", "_getTipElement", "_isWithActiveTrigger", "_getTitle", "_createTipElement", "_getContentForTemplate", "_getTemplateFactory", "tipId", "prefix", "floor", "random", "getElementById", "<PERSON><PERSON><PERSON><PERSON>", "_initializeOnDelegatedTarget", "_getDelegateConfig", "attachment", "triggers", "eventIn", "eventOut", "_setTimeout", "timeout", "dataAttributes", "dataAttribute", "Popover", "_getContent", "EVENT_ACTIVATE", "EVENT_CLICK", "SELECTOR_TARGET_LINKS", "SELECTOR_NAV_LINKS", "SELECTOR_LINK_ITEMS", "rootMargin", "smoothScroll", "threshold", "ScrollSpy", "_targetLinks", "_observableSections", "_rootElement", "_activeTarget", "_observer", "_previousScrollData", "visibleEntryTop", "parentScrollTop", "refresh", "_initializeTargetsAndObservables", "_maybeEnableSmoothScroll", "disconnect", "_getNewObserver", "section", "observe", "observableSection", "scrollTo", "behavior", "IntersectionObserver", "_<PERSON><PERSON><PERSON><PERSON>", "targetElement", "_process", "userScrollsDown", "isIntersecting", "_clearActiveClass", "entryIsLowerThanPrevious", "targetLinks", "anchor", "decodeURI", "_activateParents", "listGroup", "activeNodes", "spy", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "HOME_KEY", "END_KEY", "SELECTOR_DROPDOWN_TOGGLE", "NOT_SELECTOR_DROPDOWN_TOGGLE", "SELECTOR_INNER_ELEM", "SELECTOR_DATA_TOGGLE_ACTIVE", "Tab", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "innerElem", "_elemIsActive", "active", "_getActiveElem", "hideEvent", "_deactivate", "_activate", "relatedElem", "_toggleDropDown", "nextActiveElement", "preventScroll", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "open", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "EVENT_FOCUSOUT", "CLASS_NAME_HIDE", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "isShown", "_onInteraction", "isInteracting"]}
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { StringNGrams } from '@tensorflow/tfjs-core';
import { stringNGramsImpl } from './StringNGrams_impl';
export function stringNGrams(args) {
    const { inputs, backend, attrs } = args;
    const { separator, nGramWidths, leftPad, rightPad, padWidth, preserveShortSequences } = attrs;
    const { data, dataSplits } = inputs;
    const $data = backend.data.get(data.dataId).values;
    const $dataSplits = backend.data.get(dataSplits.dataId).values;
    const [nGrams, nGramsSplits] = stringNGramsImpl($data, $dataSplits, separator, nGramWidths, leftPad, rightPad, padWidth, preserveShortSequences);
    return [
        backend.makeTensorInfo([nGrams.length], 'string', nGrams),
        backend.makeTensorInfo(dataSplits.shape, 'int32', nGramsSplits),
    ];
}
export const stringNGramsConfig = {
    kernelName: StringNGrams,
    backendName: 'cpu',
    kernelFunc: stringNGrams,
};
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiU3RyaW5nTkdyYW1zLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vdGZqcy1iYWNrZW5kLWNwdS9zcmMva2VybmVscy9TdHJpbmdOR3JhbXMudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7Ozs7Ozs7Ozs7OztHQWVHO0FBRUgsT0FBTyxFQUEyQixZQUFZLEVBQW9ELE1BQU0sdUJBQXVCLENBQUM7QUFJaEksT0FBTyxFQUFDLGdCQUFnQixFQUFDLE1BQU0scUJBQXFCLENBQUM7QUFFckQsTUFBTSxVQUFVLFlBQVksQ0FBQyxJQUk1QjtJQUNDLE1BQU0sRUFBQyxNQUFNLEVBQUUsT0FBTyxFQUFFLEtBQUssRUFBQyxHQUFHLElBQUksQ0FBQztJQUN0QyxNQUFNLEVBQ0osU0FBUyxFQUNULFdBQVcsRUFDWCxPQUFPLEVBQ1AsUUFBUSxFQUNSLFFBQVEsRUFDUixzQkFBc0IsRUFDdkIsR0FBRyxLQUFLLENBQUM7SUFDVixNQUFNLEVBQUMsSUFBSSxFQUFFLFVBQVUsRUFBQyxHQUFHLE1BQU0sQ0FBQztJQUNsQyxNQUFNLEtBQUssR0FBRyxPQUFPLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUMsTUFBc0IsQ0FBQztJQUNuRSxNQUFNLFdBQVcsR0FBRyxPQUFPLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsTUFBTSxDQUFDLENBQUMsTUFBb0IsQ0FBQztJQUU3RSxNQUFNLENBQUMsTUFBTSxFQUFFLFlBQVksQ0FBQyxHQUFHLGdCQUFnQixDQUMzQyxLQUFLLEVBQUUsV0FBVyxFQUFFLFNBQVMsRUFBRSxXQUFXLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxRQUFRLEVBQ3ZFLHNCQUFzQixDQUFDLENBQUM7SUFDNUIsT0FBTztRQUNMLE9BQU8sQ0FBQyxjQUFjLENBQUMsQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLEVBQUUsUUFBUSxFQUFFLE1BQU0sQ0FBQztRQUN6RCxPQUFPLENBQUMsY0FBYyxDQUFDLFVBQVUsQ0FBQyxLQUFLLEVBQUUsT0FBTyxFQUFFLFlBQVksQ0FBQztLQUNoRSxDQUFDO0FBQ0osQ0FBQztBQUVELE1BQU0sQ0FBQyxNQUFNLGtCQUFrQixHQUFpQjtJQUM5QyxVQUFVLEVBQUUsWUFBWTtJQUN4QixXQUFXLEVBQUUsS0FBSztJQUNsQixVQUFVLEVBQUUsWUFBZ0M7Q0FDN0MsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCAyMDIxIEdvb2dsZSBMTEMuIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICogWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4gKlxuICogaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuICogZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gKiBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4gKiBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbiAqID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4gKi9cblxuaW1wb3J0IHtLZXJuZWxDb25maWcsIEtlcm5lbEZ1bmMsIFN0cmluZ05HcmFtcywgU3RyaW5nTkdyYW1zQXR0cnMsIFN0cmluZ05HcmFtc0lucHV0cywgVGVuc29ySW5mb30gZnJvbSAnQHRlbnNvcmZsb3cvdGZqcy1jb3JlJztcblxuaW1wb3J0IHtNYXRoQmFja2VuZENQVX0gZnJvbSAnLi4vYmFja2VuZF9jcHUnO1xuXG5pbXBvcnQge3N0cmluZ05HcmFtc0ltcGx9IGZyb20gJy4vU3RyaW5nTkdyYW1zX2ltcGwnO1xuXG5leHBvcnQgZnVuY3Rpb24gc3RyaW5nTkdyYW1zKGFyZ3M6IHtcbiAgaW5wdXRzOiBTdHJpbmdOR3JhbXNJbnB1dHMsXG4gIGJhY2tlbmQ6IE1hdGhCYWNrZW5kQ1BVLFxuICBhdHRyczogU3RyaW5nTkdyYW1zQXR0cnNcbn0pOiBbVGVuc29ySW5mbywgVGVuc29ySW5mb10ge1xuICBjb25zdCB7aW5wdXRzLCBiYWNrZW5kLCBhdHRyc30gPSBhcmdzO1xuICBjb25zdCB7XG4gICAgc2VwYXJhdG9yLFxuICAgIG5HcmFtV2lkdGhzLFxuICAgIGxlZnRQYWQsXG4gICAgcmlnaHRQYWQsXG4gICAgcGFkV2lkdGgsXG4gICAgcHJlc2VydmVTaG9ydFNlcXVlbmNlc1xuICB9ID0gYXR0cnM7XG4gIGNvbnN0IHtkYXRhLCBkYXRhU3BsaXRzfSA9IGlucHV0cztcbiAgY29uc3QgJGRhdGEgPSBiYWNrZW5kLmRhdGEuZ2V0KGRhdGEuZGF0YUlkKS52YWx1ZXMgYXMgVWludDhBcnJheVtdO1xuICBjb25zdCAkZGF0YVNwbGl0cyA9IGJhY2tlbmQuZGF0YS5nZXQoZGF0YVNwbGl0cy5kYXRhSWQpLnZhbHVlcyBhcyBJbnQzMkFycmF5O1xuXG4gIGNvbnN0IFtuR3JhbXMsIG5HcmFtc1NwbGl0c10gPSBzdHJpbmdOR3JhbXNJbXBsKFxuICAgICAgJGRhdGEsICRkYXRhU3BsaXRzLCBzZXBhcmF0b3IsIG5HcmFtV2lkdGhzLCBsZWZ0UGFkLCByaWdodFBhZCwgcGFkV2lkdGgsXG4gICAgICBwcmVzZXJ2ZVNob3J0U2VxdWVuY2VzKTtcbiAgcmV0dXJuIFtcbiAgICBiYWNrZW5kLm1ha2VUZW5zb3JJbmZvKFtuR3JhbXMubGVuZ3RoXSwgJ3N0cmluZycsIG5HcmFtcyksXG4gICAgYmFja2VuZC5tYWtlVGVuc29ySW5mbyhkYXRhU3BsaXRzLnNoYXBlLCAnaW50MzInLCBuR3JhbXNTcGxpdHMpLFxuICBdO1xufVxuXG5leHBvcnQgY29uc3Qgc3RyaW5nTkdyYW1zQ29uZmlnOiBLZXJuZWxDb25maWcgPSB7XG4gIGtlcm5lbE5hbWU6IFN0cmluZ05HcmFtcyxcbiAgYmFja2VuZE5hbWU6ICdjcHUnLFxuICBrZXJuZWxGdW5jOiBzdHJpbmdOR3JhbXMgYXMge30gYXMgS2VybmVsRnVuYyxcbn07XG4iXX0=
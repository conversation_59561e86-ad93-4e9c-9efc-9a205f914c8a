/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { DenseBincount } from '@tensorflow/tfjs-core';
import { bincountImplCPU, bincountReduceImplCPU } from '../kernel_utils/shared';
export function denseBincount(args) {
    const { inputs, backend, attrs } = args;
    const { x, weights } = inputs;
    const { size, binaryOutput } = attrs;
    if (x.shape.length === 1) {
        const xVals = backend.readSync(x.dataId);
        const weightsVals = backend.readSync(weights.dataId);
        const outVals = bincountImplCPU(xVals, weightsVals, weights.dtype, weights.shape, size);
        return backend.makeTensorInfo([size], weights.dtype, outVals);
    }
    else if (x.shape.length === 2) {
        const xBuf = backend.bufferSync(x);
        const weightsBuf = backend.bufferSync(weights);
        const outBuf = bincountReduceImplCPU(xBuf, weightsBuf, size, binaryOutput);
        return backend.makeTensorInfo(outBuf.shape, weights.dtype, outBuf.values);
    }
    throw new Error(`Error in denseBincount: input must be at most rank 2, but got rank` +
        `${x.shape.length}.`);
}
export const denseBincountConfig = {
    kernelName: DenseBincount,
    backendName: 'webgl',
    kernelFunc: denseBincount
};
//# sourceMappingURL=data:application/json;base64,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
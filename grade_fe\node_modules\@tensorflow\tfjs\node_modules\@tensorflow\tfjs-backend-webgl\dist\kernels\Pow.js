/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { Pow } from '@tensorflow/tfjs-core';
import { CHECK_NAN_SNIPPET_PACKED } from '../binaryop_packed_gpu';
import { binaryKernelFunc } from '../kernel_utils/kernel_funcs_utils';
const POW = `
  if(a < 0.0 && floor(b) < b){
    return NAN;
  }
  if (b == 0.0) {
    return 1.0;
  }
  return (round(mod(b, 2.0)) != 1) ?
      pow(abs(a), b) : sign(a) * pow(abs(a), b);
`;
const POW_PACKED = `
  // isModRound1 has 1 for components with round(mod(b, 2.0)) == 1, 0 otherwise.
  vec4 isModRound1 = vec4(equal(round(mod(b, 2.0)), ivec4(1)));
  vec4 multiplier = sign(a) * isModRound1 + (vec4(1.0) - isModRound1);
  vec4 result = multiplier * pow(abs(a), b);

  // Ensure that a^0 = 1, including 0^0 = 1 as this correspond to TF and JS
  bvec4 isExpZero = equal(b, vec4(0.0));
  result.r = isExpZero.r ? 1.0 : result.r;
  result.g = isExpZero.g ? 1.0 : result.g;
  result.b = isExpZero.b ? 1.0 : result.b;
  result.a = isExpZero.a ? 1.0 : result.a;

  bvec4 isNaN1 = lessThan(a, vec4(0.0));
  bvec4 isNaN2 = lessThan(floor(b), b);
  bvec4 isNaN = bvec4(isNaN1.x && isNaN2.x, isNaN1.y && isNaN2.y, isNaN1.z && isNaN2.z, isNaN1.w && isNaN2.w);
  ` +
    CHECK_NAN_SNIPPET_PACKED + `
  return result;
`;
export const pow = binaryKernelFunc({ opSnippet: POW, packedOpSnippet: POW_PACKED });
export const powConfig = {
    kernelName: Pow,
    backendName: 'webgl',
    kernelFunc: pow
};
//# sourceMappingURL=data:application/json;base64,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
/**
 * @license
 * Copyright 2022 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
!function(e,a){"object"==typeof exports&&"undefined"!=typeof module?a(exports,require("@tensorflow/tfjs-core"),require("seedrandom")):"function"==typeof define&&define.amd?define(["exports","@tensorflow/tfjs-core","seedrandom"],a):a((e="undefined"!=typeof globalThis?globalThis:e||self).tf=e.tf||{},e.tf,e.seedrandom)}(this,(function(e,a,t){"use strict";function r(e){if(e&&e.__esModule)return e;var a=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(a,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}})),a.default=e,a}var n=r(t),i=function(e,a){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,a){e.__proto__=a}||function(e,a){for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(e[t]=a[t])},i(e,a)};function o(e,a,t,r){return new(t||(t=Promise))((function(n,i){function o(e){try{u(r.next(e))}catch(e){i(e)}}function s(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var a;e.done?n(e.value):(a=e.value,a instanceof t?a:new t((function(e){e(a)}))).then(o,s)}u((r=r.apply(e,a||[])).next())}))}function s(e,a){var t,r,n,i,o={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return i={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function s(i){return function(s){return function(i){if(t)throw new TypeError("Generator is already executing.");for(;o;)try{if(t=1,r&&(n=2&i[0]?r.return:i[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,i[1])).done)return n;switch(r=0,n&&(i=[2&i[0],n.value]),i[0]){case 0:case 1:n=i;break;case 4:return o.label++,{value:i[1],done:!1};case 5:o.label++,r=i[1],i=[0];continue;case 7:i=o.ops.pop(),o.trys.pop();continue;default:if(!(n=o.trys,(n=n.length>0&&n[n.length-1])||6!==i[0]&&2!==i[0])){o=0;continue}if(3===i[0]&&(!n||i[1]>n[0]&&i[1]<n[3])){o.label=i[1];break}if(6===i[0]&&o.label<n[1]){o.label=n[1],n=i;break}if(n&&o.label<n[2]){o.label=n[2],o.ops.push(i);break}n[2]&&o.ops.pop(),o.trys.pop();continue}i=a.call(e,o)}catch(e){i=[6,e],r=0}finally{t=n=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,s])}}}function u(e){var a="function"==typeof Symbol&&Symbol.iterator,t=a&&e[a],r=0;if(t)return t.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(a?"Object is not iterable.":"Symbol.iterator is not defined.")}function d(e,a){var t="function"==typeof Symbol&&e[Symbol.iterator];if(!t)return e;var r,n,i=t.call(e),o=[];try{for(;(void 0===a||a-- >0)&&!(r=i.next()).done;)o.push(r.value)}catch(e){n={error:e}}finally{try{r&&!r.done&&(t=i.return)&&t.call(i)}finally{if(n)throw n.error}}return o}function l(){for(var e=[],a=0;a<arguments.length;a++)e=e.concat(d(arguments[a]));return e}function p(e,t){Array.isArray(e)||(e=[e]),e.forEach((function(e){null!=e&&a.util.assert("complex64"!==e.dtype,(function(){return t+" does not support complex64 tensors in the CPU backend."}))}))}var c=a.kernel_impls.whereImpl,h=function(e){function t(){var t=e.call(this)||this;return t.blockSize=48,t.firstUse=!0,t.data=new a.DataStorage(t,a.engine()),t}return function(e,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function t(){this.constructor=e}i(e,a),e.prototype=null===a?Object.create(a):(t.prototype=a.prototype,new t)}(t,e),t.prototype.nextDataId=function(){return t.nextDataId++},t.prototype.write=function(e,t,r){this.firstUse&&(this.firstUse=!1,a.env().get("IS_NODE")&&a.backend_util.warn("\n============================\nHi, looks like you are running TensorFlow.js in Node.js. To speed things up dramatically, install our node backend, visit https://github.com/tensorflow/tfjs-node for more details. \n============================"));var n={id:this.nextDataId()};return this.data.set(n,{values:e,dtype:r,refCount:1}),n},t.prototype.makeTensorInfo=function(e,t,r){var n;if("string"===t&&null!=r&&r.length>0&&a.util.isString(r[0])){var i=r.map((function(e){return a.util.encodeString(e)}));n=this.write(i,e,t)}else n=this.write(r,e,t);return{dataId:n,shape:e,dtype:t}},t.prototype.refCount=function(e){return this.data.has(e)?this.data.get(e).refCount:0},t.prototype.incRef=function(e){this.data.get(e).refCount++},t.prototype.decRef=function(e){this.data.has(e)&&this.data.get(e).refCount--},t.prototype.move=function(e,a,t,r,n){this.data.set(e,{values:a,dtype:r,refCount:n})},t.prototype.numDataIds=function(){return this.data.numDataIds()},t.prototype.read=function(e){return o(this,void 0,void 0,(function(){return s(this,(function(a){return[2,this.readSync(e)]}))}))},t.prototype.readSync=function(e){var t=this.data.get(e),r=t.dtype,n=t.complexTensorInfos;if("complex64"===r){var i=this.readSync(n.real.dataId),o=this.readSync(n.imag.dataId);return a.backend_util.mergeRealAndImagArrays(i,o)}return this.data.get(e).values},t.prototype.bufferSync=function(e){var t=this.readSync(e.dataId);if("string"===e.dtype)try{var r=t.map((function(e){return a.util.decodeString(e)}));return a.buffer(e.shape,e.dtype,r)}catch(e){throw new Error("Failed to decode encoded string bytes into utf-8")}return a.buffer(e.shape,e.dtype,t)},t.prototype.makeOutput=function(e,t,r){return a.engine().makeTensorFromTensorInfo(this.makeTensorInfo(t,r,e),this)},t.prototype.disposeData=function(e,a){if(void 0===a&&(a=!1),this.data.has(e)){if(this.data.get(e).refCount--,!a&&this.data.get(e).refCount>0)return!1;var t=this.data.get(e).complexTensorInfos;null!=t&&(this.disposeData(t.real.dataId,!0),this.disposeData(t.imag.dataId,!0)),this.data.delete(e)}return!0},t.prototype.disposeIntermediateTensorInfo=function(e){this.disposeData(e.dataId)},t.prototype.time=function(e){return o(this,void 0,void 0,(function(){var t;return s(this,(function(r){return t=a.util.now(),e(),[2,{kernelMs:a.util.now()-t}]}))}))},t.prototype.memory=function(){return{unreliable:!0,reasons:["The reported memory is an upper bound. Due to automatic garbage collection, the true allocated memory may be less."]}},t.prototype.where=function(e){p([e],"where");var a=this.readSync(e.dataId);return c(e.shape,a)},t.prototype.dispose=function(){},t.prototype.floatPrecision=function(){return 32},t.prototype.epsilon=function(){return e.prototype.epsilon.call(this)},t}(a.KernelBackend);function f(e){for(var a=new Float32Array(e.length),t=0;t<e.length;++t)a[t]=Math.abs(e[t]);return a}h.nextDataId=0;var m={kernelName:a.Abs,backendName:"cpu",kernelFunc:function(e){var t=e.inputs.x,r=e.backend;p(t,"abs");var n=new Float32Array(a.util.sizeFromShape(t.shape));return n=f(r.data.get(t.dataId).values),r.makeOutput(n,t.shape,t.dtype)}};function v(e){return function(t,r,n,i,o){var s=a.backend_util.assertAndGetBroadcastShape(t,r),u=s.length,d=a.util.computeStrides(s),l=a.util.sizeFromShape(s),p=a.util.getTypedArrayFromDType(o,l),c=t.length,h=r.length,f=a.util.computeStrides(t),m=a.util.computeStrides(r),v=a.backend_util.getBroadcastDims(t,s),k=a.backend_util.getBroadcastDims(r,s);if(v.length+k.length===0)for(var g=0;g<p.length;++g)p[g]=e(n[g%n.length],i[g%i.length]);else{var b=function(t){var r=a.util.indexToLoc(t,u,d),o=r.slice(-c);v.forEach((function(e){return o[e]=0}));var s=a.util.locToIndex(o,c,f),l=r.slice(-h);k.forEach((function(e){return l[e]=0}));var g=a.util.locToIndex(l,h,m);p[t]=e(n[s],i[g])};for(g=0;g<p.length;++g)b(g)}return[p,s]}}function k(e){var a=e.inputs,t=e.backend,r=a.real,n=a.imag,i=t.data.get(r.dataId).values,o=t.data.get(n.dataId).values,s=t.makeTensorInfo(r.shape,"complex64");return t.data.get(s.dataId).complexTensorInfos={real:t.makeTensorInfo(r.shape,"float32",i),imag:t.makeTensorInfo(n.shape,"float32",o)},s}var g={kernelName:a.Complex,backendName:"cpu",kernelFunc:k};function b(e,t,r){if(void 0===r&&(r="float32"),"complex64"===r)return k({inputs:{real:b(e,t,"float32"),imag:b(e,t,"float32")},backend:e});var n=a.util.makeZerosTypedArray(a.util.sizeFromShape(t),r);return e.makeTensorInfo(t,r,n)}function I(e){var a=e.inputs,t=e.backend,r=a.x;return t.incRef(r.dataId),{dataId:r.dataId,shape:r.shape,dtype:r.dtype}}var y={kernelName:a.Identity,backendName:"cpu",kernelFunc:I};function S(e){var a=e.inputs,t=e.backend,r=a.input,n=t.data.get(r.dataId).complexTensorInfos.real,i=t.data.get(n.dataId).values;return t.makeTensorInfo(n.shape,n.dtype,i)}var T={kernelName:a.Real,backendName:"cpu",kernelFunc:S};function N(e,t,r,n){if("int32"===n)return[t,"int32",Int32Array.from(e)];if("bool"===n){var i=a.util.toTypedArray([0],r),o=d(v((function(e,a){return e!==a?1:0}))(t,[],e,i,"bool"),2),s=o[0];return[o[1],"bool",s]}throw new Error("Error in Cast: failed to cast "+r+" to "+n)}function x(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.dtype;if("complex64"===o){if("complex64"===i.dtype)return I({inputs:{x:i},backend:r});var s=b(r,i.shape,i.dtype),u=x({inputs:{x:i},backend:r,attrs:{dtype:"float32"}}),l=k({inputs:{real:u,imag:s},backend:r});return r.disposeIntermediateTensorInfo(s),r.disposeIntermediateTensorInfo(u),l}if("complex64"===i.dtype){var p=S({inputs:{input:i},backend:r}),l=x({inputs:{x:p},backend:r,attrs:{dtype:o}});return r.disposeIntermediateTensorInfo(p),l}if(!a.util.hasEncodingLoss(i.dtype,o))return{dataId:(l=I({inputs:{x:i},backend:r})).dataId,shape:l.shape,dtype:o};var c=d(N(r.data.get(i.dataId).values,i.shape,i.dtype,o),3),h=c[0],f=c[1],m=c[2];return r.makeTensorInfo(h,f,m)}var F={kernelName:a.Cast,backendName:"cpu",kernelFunc:x};function w(e,t,r,n){return null==r?function(r){var i=r.inputs,o=r.backend,s=i.a,u=i.b,l=o;p([s,u],e);var c=l.data.get(s.dataId).values,h=l.data.get(u.dataId).values,f="string"===s.dtype?a.backend_util.fromUint8ToStringArray(c):c,m="string"===s.dtype?a.backend_util.fromUint8ToStringArray(h):h,v=n||s.dtype,k=d(t(s.shape,u.shape,f,m,v),2),g=k[0],b=k[1];return l.makeTensorInfo(b,v,g)}:function(e){var a=e.inputs,i=e.backend,o=a.a,s=a.b,u=i;if("complex64"===o.dtype||"complex64"===s.dtype){var l=x({inputs:{x:o},backend:u,attrs:{dtype:"complex64"}}),p=u.data.get(l.dataId),c=p.complexTensorInfos.real,h=p.complexTensorInfos.imag,f=u.data.get(c.dataId).values,m=u.data.get(h.dataId).values,v=x({inputs:{x:s},backend:u,attrs:{dtype:"complex64"}}),g=u.data.get(v.dataId),b=g.complexTensorInfos.real,I=g.complexTensorInfos.imag,y=u.data.get(b.dataId).values,S=u.data.get(I.dataId).values,T=d(r(o.shape,s.shape,f,m,y,S),3),N=T[0],F=T[1],w=T[2],M=u.makeTensorInfo(w,"float32",N),A=u.makeTensorInfo(w,"float32",F),D=k({inputs:{real:M,imag:A},backend:u});return u.disposeIntermediateTensorInfo(l),u.disposeIntermediateTensorInfo(v),u.disposeIntermediateTensorInfo(M),u.disposeIntermediateTensorInfo(A),D}var _=u.data.get(o.dataId).values,E=u.data.get(s.dataId).values,z=n||o.dtype,R=d(t(o.shape,s.shape,_,E,z),2),W=R[0];w=R[1];return u.makeTensorInfo(w,z,W)}}function M(e){return function(t,r,n,i,o,s){var u=a.backend_util.assertAndGetBroadcastShape(t,r),d=a.util.sizeFromShape(u),l=u.length,p=a.util.computeStrides(u),c=a.util.getTypedArrayFromDType("float32",d),h=a.util.getTypedArrayFromDType("float32",d),f=a.backend_util.getBroadcastDims(t,u),m=a.backend_util.getBroadcastDims(r,u),v=a.backend_util.mergeRealAndImagArrays(n,i),k=a.backend_util.mergeRealAndImagArrays(o,s),g=t.length,b=a.util.computeStrides(t),I=r.length,y=a.util.computeStrides(r);if(f.length+m.length===0)for(var S=0;S<c.length;S++){var T=S%v.length,N=S%k.length,x=e(v[2*T],v[2*T+1],k[2*N],k[2*N+1]);c[S]=x.real,h[S]=x.imag}else{var F=function(t){var r=a.util.indexToLoc(t,l,p),n=r.slice(-g);f.forEach((function(e){return n[e]=0}));var i=a.util.locToIndex(n,g,b),o=r.slice(-I);m.forEach((function(e){return o[e]=0}));var s=a.util.locToIndex(o,I,y),u=e(v[2*i],v[2*i+1],k[2*s],k[2*s+1]);c[t]=u.real,h[t]=u.imag};for(S=0;S<c.length;S++)F(S)}return[c,h,u]}}var A=v((function(e,a){return e+a})),D=M((function(e,a,t,r){return{real:e+t,imag:a+r}})),_=w(a.Add,A,D),E={kernelName:a.Add,backendName:"cpu",kernelFunc:_};function z(e,t,r,n,i){for(var o=a.util.sizeFromShape(n),s=a.util.makeZerosTypedArray(i,r),u=0;u<e.length;u++){var d=e[u];if(d<0)throw new Error("Input x must be non-negative!");d>=i||(s[d]+=o>0?t[u]:1)}return s}function R(e,t,r,n){void 0===n&&(n=!1);for(var i=e.shape[0],o=e.shape[1],s=a.buffer([i,r],t.dtype),u=0;u<i;u++)for(var d=0;d<o;d++){var l=e.get(u,d);if(l<0)throw new Error("Input x must be non-negative!");l>=r||(n?s.set(1,u,l):t.size>0?s.set(s.get(u,l)+t.get(u,d),u,l):s.set(s.get(u,l)+1,u,l))}return s}function W(e){return function(t,r,n){for(var i=a.util.getTypedArrayFromDType(r,t.length),o=0;o<t.length;++o)i[o]=e(t[o],n);return i}}function P(e,t,r){return function(n){var i=n.inputs,o=n.attrs,s=n.backend,u=i.x;if(p(u,e),"string"===u.dtype||"string"===r)throw new Error("unaryKernelFunc does not support string input/output");for(var d=s,l=d.data.get(u.dataId).values,c=a.util.sizeFromShape(u.shape),h=r||u.dtype,f=a.util.getArrayFromDType(h,c),m=0;m<c;++m)f[m]=t(l[m],o);return d.makeTensorInfo(u.shape,h,f)}}function C(e,a,t){return function(r){var n=r.inputs,i=r.attrs,o=r.backend,s=n.x;if(p(s,e),"string"===s.dtype||"string"===t)throw new Error("unaryKernelFunc does not support string input/output");var u=o,d=u.data.get(s.dataId).values,l=t||s.dtype,c=a(d,l,i);return u.makeTensorInfo(s.shape,l,c)}}var H=W((function(e){return Math.ceil(e)})),O=C(a.Ceil,H),V={kernelName:a.Ceil,backendName:"cpu",kernelFunc:O};function B(e,t,r,n){var i=a.util.getArrayFromDType(r,a.util.sizeFromShape(t));if(n&&"string"!==r){var o=0;e.forEach((function(e){var t=a.util.sizeFromShape(e.shape);i.set(e.vals,o),o+=t}))}else{var s=0;e.forEach((function(e){for(var n="string"===r?a.backend_util.fromUint8ToStringArray(e.vals):e.vals,o=0,u=0;u<e.shape[0];++u)for(var d=u*t[1]+s,l=0;l<e.shape[1];++l)i[d+l]=n[o++];s+=e.shape[1]}))}return i}var G=v((function(e,a){return e===a?1:0})),L=w(a.Equal,G,null,"bool"),q={kernelName:a.Equal,backendName:"cpu",kernelFunc:L},U=W((function(e){return Math.exp(e)})),Z=C(a.Exp,U,"float32"),j={kernelName:a.Exp,backendName:"cpu",kernelFunc:Z},K=W((function(e){return Math.expm1(e)})),Y=C(a.Expm1,K),J={kernelName:a.Expm1,backendName:"cpu",kernelFunc:Y},Q=W((function(e){return Math.floor(e)})),X=C(a.Floor,Q),$={kernelName:a.Floor,backendName:"cpu",kernelFunc:X};function ee(e,t,r,n,i,o,s,u,d){for(var p=a.buffer([n,o],r),c=0;c<n;c++){for(var h=[],f=0,m=0;m<i;m++){var v=e[c*i+m];f+=v*s[m],h.push(v)}if(f<0||f>=d/o)throw new Error("Invalid indices: "+h+" does not index into "+u);for(var k=0;k<o;k++)p.values[c*o+k]=t.get.apply(t,l(t.indexToLoc(f*o+k)))}return p}function ae(e,t,r){for(var n=a.buffer(r,e.dtype),i=0;i<n.size;++i){var o=n.indexToLoc(i).slice(),s=o[0],u=o[2],d=t.locToIndex([s,u]);o[2]=t.values[d];var l=e.locToIndex(o);0<=l&&l<e.values.length&&(n.values[i]=e.values[l])}return n}var te=v((function(e,a){return e>a?1:0})),re=w(a.Greater,te,null,"bool"),ne={kernelName:a.Greater,backendName:"cpu",kernelFunc:re},ie=v((function(e,a){return e>=a?1:0})),oe=w(a.GreaterEqual,ie,null,"bool"),se={kernelName:a.GreaterEqual,backendName:"cpu",kernelFunc:oe},ue=v((function(e,a){return e<a?1:0})),de=w(a.Less,ue,null,"bool"),le={kernelName:a.Less,backendName:"cpu",kernelFunc:de},pe=v((function(e,a){return e<=a?1:0})),ce=w(a.LessEqual,pe,null,"bool"),he={kernelName:a.LessEqual,backendName:"cpu",kernelFunc:ce};function fe(e,t,r){var n=(t-e)/(r-1),i=a.util.makeZerosTypedArray(r,"float32");i[0]=e;for(var o=1;o<i.length;o++)i[o]=i[o-1]+n;return i}var me=W((function(e){return Math.log(e)})),ve=C(a.Log,me),ke={kernelName:a.Log,backendName:"cpu",kernelFunc:ve};function ge(e,t,r,n){for(var i=a.util.getTypedArrayFromDType(n,a.util.sizeFromShape(r)),o=0;o<i.length;++o){for(var s=o*t,u=e[s],d=0;d<t;++d){var l=e[s+d];(Number.isNaN(l)||l>u)&&(u=l)}i[o]=u}return i}var be=v((function(e,a){return Math.max(e,a)})),Ie=w(a.Maximum,be),ye={kernelName:a.Maximum,backendName:"cpu",kernelFunc:Ie},Se=v((function(e,a){return Math.min(e,a)})),Te=w(a.Minimum,Se),Ne={kernelName:a.Minimum,backendName:"cpu",kernelFunc:Te},xe=v((function(e,a){return e*a})),Fe=M((function(e,a,t,r){return{real:e*t-a*r,imag:e*r+a*t}})),we=w(a.Multiply,xe,Fe),Me={kernelName:a.Multiply,backendName:"cpu",kernelFunc:we};function Ae(e,t,r){var n=a.util.createScalarValue(-1,r);return xe([],t,n,e,r)}var De={kernelName:a.Neg,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=a.x;p(r,"neg");var n=d(Ae(t.data.get(r.dataId).values,r.shape,r.dtype),2),i=n[0],o=n[1];return t.makeTensorInfo(o,r.dtype,i)}},_e=v((function(e,a){return e!==a?1:0})),Ee=w(a.NotEqual,_e,null,"bool"),ze={kernelName:a.NotEqual,backendName:"cpu",kernelFunc:Ee};function Re(e,t,r,n,i){for(var o=t.length,s=a.util.sizeFromShape(t),u=a.util.computeStrides(t),d=a.util.computeStrides(i),l=a.util.getTypedArrayFromDType(r,a.util.sizeFromShape(i)),p=0;p<s;++p){for(var c=a.util.indexToLoc(p,o,u),h=new Array(c.length),f=0;f<h.length;f++)h[f]=c[n[f]];l[a.util.locToIndex(h,o,d)]=e[p]}return l}function We(e){var a=e.inputs,t=e.attrs,r=e.backend,n=a.x,i=t.perm;p(n,"transpose");for(var o=n.shape.length,s=new Array(o),u=0;u<s.length;u++)s[u]=n.shape[i[u]];var d=Re(r.data.get(n.dataId).values,n.shape,n.dtype,i,s);return{dataId:r.write(d,s,n.dtype),shape:s,dtype:n.dtype}}var Pe={kernelName:a.Transpose,backendName:"cpu",kernelFunc:We};function Ce(e,t,r,n){for(var i=d(a.backend_util.computeOutAndReduceShapes(e,n),2),o=i[0],s=i[1],u=a.upcastType(t,"int32"),l=a.util.makeZerosTypedArray(a.util.sizeFromShape(o),u),p=a.util.sizeFromShape(s),c=0;c<l.length;++c){for(var h=c*p,f=1,m=0;m<p;++m)f*=r[h+m];l[c]=f}return{outVals:l,outShape:o,outDtype:u}}var He={kernelName:a.Prod,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.axis,s=n.keepDims;p(i,"prod");var u=i.shape.length,d=a.util.parseAxisParam(o,i.shape),l=a.backend_util.getAxesPermutation(d,u),c=d,h=i,f=[];null!=l&&(h=We({inputs:{x:i},backend:r,attrs:{perm:l}}),f.push(h),c=a.backend_util.getInnerMostAxes(c.length,u));var m=r.data.get(h.dataId).values,v=Ce(h.shape,h.dtype,m,c),k=v.outVals,g=v.outShape,b=v.outDtype,I=g;return s&&(I=a.backend_util.expandShapeToKeepDim(g,d)),f.forEach((function(e){return r.disposeIntermediateTensorInfo(e)})),r.makeTensorInfo(I,b,k)}};function Oe(e,a,t,r){var n=[],i=0,o=a.length-1+t.length,s=new Array(o).fill(null).map((function(){return[0]}));!function(e,a){for(var t=0;t<e.length;++t){var r=e[t],n=t===e.length-1?a:e[t+1].length;if(0===r.length)throw new Error("Ragged splits may not be empty");if(r[0]<0)throw new Error("Ragged splits must be non-negative");if(r[r.length-1]>n)throw new Error("Ragged splits must not point past values");for(var i=1;i<r.length;++i)if(r[i-1]>r[i])throw new Error("Ragged splits must be sorted in ascending order")}}(t,r);for(var u=1,d=0;d<a.length-1;++d){u*=a[d];for(var l=a[d+1],p=1;p<u+1;++p)s[d].push(p*l)}for(p=0;p<e.length;++p){var c=e[p],h=e[p]+1;for(d=0;d<t.length;++d){var f=t[d],m=d+a.length-1;if(m>=0)for(var v=s[m],k=v[v.length-1]-f[c],g=c;g<h;++g)s[m].push(f[g+1]+k);c=f[c],h=f[h]}h!==c&&(n.push([c,h]),i+=h-c)}return{outSplits:s,valueSlices:n,numValues:i}}function Ve(e,a){for(var t=e.slice(0,a);t.length<a;)t.push(1);for(var r=a;r<e.length;r++)t[a-1]*=e[r];return t}function Be(e,t,r,n,i){var o=t.slice();o[0]=i;var s=a.util.getArrayFromDType(r,a.util.sizeFromShape(o)),d=e.length;return function(e,a,t,r,n,i){var o,s,d=Ve(a,2)[1],l=Ve(i,2)[1],p=0;try{for(var c=u(t),h=c.next();!h.done;h=c.next())for(var f=h.value,m=f[0];m<f[1];++m){for(var v=0;v<r;++v)n[p*l+v]=e[m*d+v];++p}}catch(e){o={error:e}}finally{try{h&&!h.done&&(s=c.return)&&s.call(c)}finally{if(o)throw o.error}}}(e,t,n,0===d?0:d/t[0],s,o),[s,o]}function Ge(e,t,r,n,i,o,s,u){if(0===e.length)throw new Error("paramsNestedSplits must be non empty");if(0===t[0].length)throw new Error("Split tensors must not be scalars");if(function(e,t,r){e.forEach((function(e,n){if(e<0||e>=r){var i=a.util.indexToLoc(n,t.length,a.util.computeStrides(t)).join(",");throw new Error("indices["+i+"] = "+e+" is not in [0, "+r+")")}}))}(o,s,t[0][0]-1),0===n.length)throw new Error("params.rank must be nonzero");var d=Oe(o,s,e,n[0]),l=d.outSplits,p=d.valueSlices,c=d.numValues,h=function(e){for(var t=[],r=function(r){var n=e[r].length,i=a.util.getArrayFromDType("int32",n);t.push(i),e[r].forEach((function(e,a){return i[a]=e}))},n=0;n<e.length;++n)r(n);return t}(l),f=Be(r,n,i,p,c);return[h,f[0],f[1]]}var Le=a.backend_util.RowPartitionType,qe=function(){function e(e,t,r,n,i,o,s,u,d,l){this.shape=e,this.shapeShape=t,this.values=r,this.valuesShape=n,this.valuesDType=i,this.defaultValue=o,this.defaultValueShape=s,this.rowPartitionValues=u,this.rowPartitionValuesShapes=d,this.rowPartitionTypes=a.backend_util.getRowPartitionTypesHelper(l),this.raggedRank=a.backend_util.getRaggedRank(this.rowPartitionTypes)}return e.prototype.getRowPartitionTypeByDimension=function(e){return this.rowPartitionTypes[0]===Le.FIRST_DIM_SIZE?this.rowPartitionTypes[e+1]:this.rowPartitionTypes[e]},e.prototype.getRowPartitionTensor=function(e){return this.rowPartitionTypes[0]===Le.FIRST_DIM_SIZE?this.rowPartitionValues[e+1]:this.rowPartitionValues[e]},e.prototype.getMaxWidth=function(a){var t=this.getRowPartitionTensor(a-1);switch(this.getRowPartitionTypeByDimension(a-1)){case Le.VALUE_ROWIDS:return e.getMaxWidthValueRowID(t);case Le.ROW_SPLITS:return e.getMaxWidthRowSplit(t);default:throw new Error("Cannot handle partition type "+Le[this.getRowPartitionTypeByDimension(a-1)])}},e.getMaxWidthRowSplit=function(e){var a=e.length;if(0===a||1===a)return 0;for(var t=0,r=0;r<a-1;++r){var n=e[r+1]-e[r];n>t&&(t=n)}return t},e.getMaxWidthValueRowID=function(e){var a=e.length;if(0===a)return 0;for(var t=0,r=e[0],n=0,i=1;i<a;++i){var o=e[i];o!==r&&(r=o,n=Math.max(i-t,n),t=i)}return Math.max(a-t,n)},e.prototype.tensorShapeFromTensor=function(e,a,t){if(void 0===t&&(t=!0),0===a.length){if(-1===e[0])return[];throw new Error("The only valid scalar shape tensor is the fully unknown shape specified as -1.")}return Ze(e,t)},e.prototype.calculateOutputSize=function(e){var t=this.valuesShape,r=this.defaultValueShape;a.backend_util.validateDefaultValueShape(r,t);var n=this.tensorShapeFromTensor(this.shape,this.shapeShape),i=a.backend_util.combineRaggedTensorToTensorShapes(this.raggedRank,n,t);i[0]<0&&(i[0]=e);for(var o=1;o<=this.raggedRank;++o)i[o]<0&&(i[o]=this.getMaxWidth(o));return i},e.prototype.calculateFirstParentOutputIndex=function(e,t,r){for(var n=Math.min(e,r),i=[],o=0,s=0;s<n;++s,o+=t)i.push(o);for(s=n;s<e;++s)i.push(-1);return a.util.assert(i.length===e,(function(){return"Final length of result must be equal to firstDimension."})),i},e.prototype.calculateOutputIndexRowSplit=function(e,a,t,r){for(var n=e.length,i=[],o=0;o<n-1;++o){var s=e[o+1]-e[o],u=Math.min(r,s),d=a[o];-1===d&&(u=0);for(var l=0;l<u;++l)i.push(d),d+=t;for(l=0;l<s-u;++l)i.push(-1)}if(n>0&&i.length!==e[n-1])throw new Error("Invalid row split size.");return i},e.prototype.calculateOutputIndexValueRowID=function(e,a,t,r){var n=e.length,i=[];if(0===n)return[];var o=0,s=e[0];if(s>=a.length)throw new Error("Got currentValueRowId="+s+", which is not less than "+a.length);var u=a[s];i.push(u);for(var d=1;d<n;++d){var l=e[d];if(l===s)u>=0&&(++o<r?u+=t:u=-1);else{if(o=0,s=l,l>=a.length)throw new Error("Got nextValueRowId="+l+" which is not less than "+a.length);u=a[l]}i.push(u)}if(i.length!==e.length)throw new Error("Invalid row ids.");return i},e.prototype.calculateOutputIndex=function(e,a,t,r){var n=this.getRowPartitionTensor(e),i=this.getRowPartitionTypeByDimension(e);switch(i){case Le.VALUE_ROWIDS:return this.calculateOutputIndexValueRowID(n,a,t,r);case Le.ROW_SPLITS:if(n.length-1>a.length)throw new Error("Row partition size is greater than output size: "+(n.length-1)+" > "+a.length);return this.calculateOutputIndexRowSplit(n,a,t,r);default:throw new Error("Unsupported partition type: "+Le[i])}},e.prototype.getFirstDimensionSize=function(){var e=this.rowPartitionValues[0];if(0===this.rowPartitionTypes.length)throw new Error("No row_partition_types given.");var a=this.rowPartitionTypes[0];switch(a){case Le.FIRST_DIM_SIZE:return e[0];case Le.VALUE_ROWIDS:throw new Error("Cannot handle VALUE_ROWIDS in first dimension.");case Le.ROW_SPLITS:return this.rowPartitionValuesShapes[0][0]-1;default:throw new Error("Cannot handle type "+Le[a])}},e.prototype.compute=function(){if(this.rowPartitionValues[0].length<=0)throw new Error("Invalid first partition input. Tensor requires at least one element.");var e=this.getFirstDimensionSize(),t=this.calculateOutputSize(e),r=new Array(this.raggedRank+1);r[r.length-1]=1;for(var n=r.length-2;n>=0;--n)r[n]=r[n+1]*t[n+1];var i=Ze(t,!1),o=a.util.getArrayFromDType(this.valuesDType,a.util.sizeFromShape(i));if(r[0]*t[0]>0){var s=this.calculateFirstParentOutputIndex(e,r[0],t[0]);for(n=1;n<=this.raggedRank;++n){s=this.calculateOutputIndex(n-1,s,r[n],t[n])}this.setOutput(this.raggedRank,s,o,i)}return[i,o]},e.prototype.setOutput=function(e,t,r,n){if(0!==r.length){var i=this.values,o=r,s=n.slice();s=s.slice(e+1);var u=a.util.sizeFromShape(s),d=t.length,l=this.defaultValue;if(l.length!==u&&1!==l.length){var p=this.defaultValueShape;a.tidy((function(){var e=a.reshape(l,p),t=a.broadcastTo(e,s);l=t.dataSync()}))}for(var c=0,h=0,f=0,m=0;m<=d;++m){var v=m<d?t[m]:-1;if(v!==f){if(h<f){var k=i.subarray(c*u);Ue(o.subarray(h*u),k,(f-h)*u)}if(m>=d){var g=r.length;v=Math.floor(g/u)}if(v>f)if(1===this.defaultValue.length)o.subarray(f*u,v*u).fill(this.defaultValue[0]),f=v;else for(;v>f;){Ue(o.slice(f*u),l,u),++f}v<0?(c=m+1,h=f):(c=m,f=(h=f)+1)}else++f}}},e}();function Ue(e,a,t){for(var r=0;r<t;r++)e[r]=a[r]}function Ze(e,a){var t,r,n=[];try{for(var i=u(e),o=i.next();!o.done;o=i.next()){var s=o.value;if(s<0){if(!a)throw new Error("Dimension "+s+" must be >= 0");if(s<-1)throw new Error("Dimension "+s+" must be >= -1");s=-1}n.push(s)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return n}function je(e,a,t,r,n,i,o,s,u,d){return new qe(e,a,t,r,n,i,o,s,u,d).compute()}function Ke(e,t,r,n){if(e===t||e<t&&r<0||t<e&&r>1)return a.util.makeZerosTypedArray(0,n);var i=Math.abs(Math.ceil((t-e)/r)),o=a.util.makeZerosTypedArray(i,n);t<e&&1===r&&(r=-1),o[0]=e;for(var s=1;s<o.length;s++)o[s]=o[s-1]+r;return o}var Ye=W((function(e){return 1/Math.sqrt(e)})),Je=C(a.Rsqrt,Ye),Qe={kernelName:a.Rsqrt,backendName:"cpu",kernelFunc:Je};function Xe(e,t,r,n,i,o,s,u,d,l){var p=[n/i,i],c=e.values,h=t.values;if(0===n)return a.buffer(r,t.dtype);var f=a.buffer(p,t.dtype);"string"==typeof d||"number"==typeof d?f.values.fill(d):"boolean"==typeof d&&f.values.fill(+d);for(var m=0;m<o;m++){for(var v=[],k=0,g=0;g<s;g++){var b=c[m*s+g];v.push(b),k+=b*u[g]}if(k<0||k>=n/i)throw new Error("Invalid indices: "+v+" does not index into "+r);for(var I=0;I<i;I++)l?f.values[k*i+I]+=h[m*i+I]:f.values[k*i+I]=0===t.rank?h[0]:h[m*i+I]}return f}var $e=W((function(e){return 1/(1+Math.exp(-e))})),ea=P(a.Sigmoid,(function(e){return 1/(1+Math.exp(-e))})),aa={kernelName:a.Sigmoid,backendName:"cpu",kernelFunc:ea};function ta(e,t,r,n,i){var o=a.slice_util.isSliceContinous(n,t,r),s=a.util.sizeFromShape(r),u=a.util.computeStrides(n);if(o){var d=a.slice_util.computeFlatOffset(t,u);return"string"===i?e.slice(d,d+s):e.subarray(d,d+s)}for(var p="string"===i?a.backend_util.fromUint8ToStringArray(e):e,c=a.buffer(n,i,p),h=a.buffer(r,i),f=0;f<h.size;++f){var m=h.indexToLoc(f),v=m.map((function(e,a){return e+t[a]}));h.set.apply(h,l([c.get.apply(c,l(v))],m))}return"string"===i?a.backend_util.fromStringArrayToUint8(h.values):h.values}function ra(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.begin,s=n.size;p(i,"slice");var u=d(a.slice_util.parseSliceParams(i,o,s),2),l=u[0],c=u[1];a.slice_util.assertParamsValid(i,l,c);var h=ta(r.data.get(i.dataId).values,l,c,i.shape,i.dtype);return r.makeTensorInfo(c,i.dtype,h)}var na={kernelName:a.Slice,backendName:"cpu",kernelFunc:ra};function ia(e,t,r,n,i,o,s){var u=t[0],d=o[0],l=new Array(d),p=new Array(u),c=t[1];if(0===d){if(0!==u)throw new Error(a.backend_util.getSparseFillEmptyRowsIndicesDenseShapeMismatch(u));return[I=a.util.getArrayFromDType(r,0),[0,c],y=a.util.getArrayFromDType(i,0),l,p]}for(var h=!0,f=0,m=new Array(d).fill(0),v=0;v<u;++v){if((g=e[v*c])<0)throw new Error(a.backend_util.getSparseFillEmptyRowsNegativeIndexErrorMessage(v,g));if(g>=d)throw new Error(a.backend_util.getSparseFillEmptyRowsOutOfRangeIndexErrorMessage(v,g,d));++m[g],h=h&&g>=f,f=g}for(var k=!0,g=0;g<d;++g){var b=0===m[g];l[g]=b,k=k&&!b,m[g]=Math.max(m[g],1),g>0&&(m[g]+=m[g-1])}if(k&&h){var I=e,y=n;for(v=0;v<u;++v)p[v]=v;return[I,[u,c],y,l,p]}var S=m[d-1],T=(I=a.util.getArrayFromDType(r,S*c),y=a.util.getArrayFromDType(i,S),new Array(d).fill(0));for(v=0;v<u;++v){var N=T[g=e[v*c]],x=(0===g?0:m[g-1])+N;T[g]++;for(var F=0;F<c;++F)I[x*c+F]=e[v*c+F];y[x]=n[v],p[v]=x}for(g=0;g<d;++g){if(0===T[g]){var w=0===g?0:m[g-1];I[w*c+0]=g;for(var M=1;M<c;++M)I[w*c+M]=0;y[w]=s}}return[I,[S,c],y,l,p]}function oa(e,t,r,n,i){for(var o=a.util.sizeFromShape(n),s=t[0],u=i.length,d=[],l=1,p=-1,c=0;c<u;++c){var h=i[c];if(-1===h){if(-1!==p)throw new Error(a.backend_util.getSparseReshapeMultipleNegativeOneOutputDimErrorMessage(p,c));p=c,d.push(1)}else{if(h<0)throw new Error(a.backend_util.getSparseReshapeNegativeOutputDimErrorMessage(c,h));l*=h,d.push(h)}}if(-1!==p){if(l<=0)throw new Error(a.backend_util.getSparseReshapeEmptyTensorZeroOutputDimErrorMessage());var f=Math.trunc(o/l);if(l*f!==o)throw new Error(a.backend_util.getSparseReshapeInputOutputMultipleErrorMessage(n,d));d[p]=f}if(a.util.sizeFromShape(d)!==o)throw new Error(a.backend_util.getSparseReshapeInputOutputMismatchErrorMessage(n,d));var m=n.length,v=[];if(m>0){v[m-1]=1;for(c=m-2;c>=0;--c)v[c]=v[c+1]*n[c+1]}var k=[];if(u>0){k[u-1]=1;for(c=u-2;c>=0;--c)k[c]=k[c+1]*d[c+1]}for(var g=a.util.getArrayFromDType(r,s*u),b=0;b<s;++b){for(var I=0,y=0;y<m;++y)I+=e[b*m+y]*v[y];for(y=0;y<u;++y)g[b*u+y]=Math.trunc(I/k[y]),I%=k[y]}return[g,[s,u],d]}function sa(e,t,r,n,i,o,s){void 0===o&&(o=!1),void 0===s&&(s=0);var u=n.length,d=[t[0],e.length/t[0]],l=d[1],p=u>0?i[u-1]+1:0;if(p<0)throw new Error(a.backend_util.getSparseSegmentReductionNegativeSegmentIdsErrorMessage());var c=t.slice();c[0]=p;var h=c.reduce((function(e,a){return e*a}),1),f=a.util.getArrayFromDType(r,h);if(0===u)return p>0&&f.fill(s),[f,c];if(p<=0)throw new Error(a.backend_util.getSparseSegmentReductionNegativeSegmentIdsErrorMessage());for(var m=0,v=1,k=0,g=i[m];;){var b=0;if(v<u){if(g===(b=i[v])){++v;continue}if(g>=b)throw new Error(a.backend_util.getSparseSegmentReductionNonIncreasingSegmentIdsErrorMessage())}if(g<0||g>=p)throw new Error(a.backend_util.getSparseSegmentReductionSegmentIdOutOfRangeErrorMessage(g,p));g>k&&f.fill(s,k*l,g*l);for(var I=m;I<v;++I){var y=n[I];if(y<0||y>=d[0])throw new Error(a.backend_util.getSparseSegmentReductionIndicesOutOfRangeErrorMessage(I,n[I],d[0]));for(var S=0;S<l;S++)f[g*l+S]+=e[y*l+S]}if(o)for(S=0;S<l;S++)f[g*l+S]/=v-m;if(m=v,k=g+1,g=b,++v>u)break}return k<p&&f.fill(s,k*l,p*l),[f,c]}var ua=W((function(e){return Math.sqrt(e)})),da=P(a.Sqrt,(function(e){return Math.sqrt(e)})),la={kernelName:a.Sqrt,backendName:"cpu",kernelFunc:da},pa=v((function(e,a){var t=e-a;return t*t})),ca=w(a.SquaredDifference,pa),ha={kernelName:a.SquaredDifference,backendName:"cpu",kernelFunc:ca};function fa(e,t,r,n){for(var i=a.buffer(e,t.dtype),o=0;o<i.size;o++){for(var s=i.indexToLoc(o),u=new Array(s.length),d=0;d<u.length;d++)u[d]=s[d]*r[d]+n[d];i.set.apply(i,l([t.get.apply(t,l(u))],s))}return i}var ma=function(){function e(e,t,r,n,i,o){this.separator=a.util.encodeString(e),this.nGramWidths=t,this.leftPad=a.util.encodeString(r),this.rightPad=a.util.encodeString(n),this.padWidth=i,this.preserveShort=o}return e.prototype.getPadWidth=function(e){return Math.min(this.padWidth<0?e-1:this.padWidth,e-1)},e.prototype.getNumNGrams=function(e,a){var t=this.getPadWidth(a);return Math.max(0,e+2*t-a+1)},e.prototype.createNGrams=function(e,a,t,r,n,i){for(var o=function(o){var u=s.getPadWidth(i),d=Math.max(0,u-o),l=Math.max(0,u-(n-(o+1))),p=i-(d+l),c=a+(d>0?0:o-u),h=0;h+=d*s.leftPad.length;for(var f=0;f<p;++f)h+=e[c+f].length;h+=l*s.rightPad.length,h+=(d+l+p-1)*s.separator.length,t[r+o]=new Uint8Array(h);var m=t[r+o],v=0,k=function(e){return e.forEach((function(e){return m[v++]=e}))};for(f=0;f<d;++f)k(s.leftPad),k(s.separator);for(f=0;f<p-1;++f)k(e[c+f]),k(s.separator);if(p>0){k(e[c+p-1]);for(f=0;f<l;++f)k(s.separator),k(s.rightPad)}else{for(f=0;f<l-1;++f)k(s.rightPad),k(s.separator);k(s.rightPad)}},s=this,u=0;u<n;++u)o(u)},e.prototype.compute=function(e,t){var r=this,n=e.length,i=t.length;if(i>0){var o=t[0];if(0!==o)throw new Error("First split value must be 0, got "+o);for(var s=1;s<i;++s){var u=t[s]>=o;if(!(u=u&&t[s]<=n))throw new Error("Invalid split value "+t[s]+", must be in ["+o+", "+n+"]");o=t[s]}if(o!==n)throw new Error("Last split value must be data size. Expected "+n+", got "+o)}var d=i-1,l=a.util.getArrayFromDType("int32",i);if(0===n||0===i){var p=new Array(n);for(s=0;s<=d;++s)l[s]=0;return[p,l]}l[0]=0;var c=function(e){var a=t[e]-t[e-1],n=0;h.nGramWidths.forEach((function(e){n+=r.getNumNGrams(a,e)})),h.preserveShort&&a>0&&0===n&&(n=1),l[e]=l[e-1]+n},h=this;for(s=1;s<=d;++s)c(s);var f=new Array(l[d]),m=function(a){var n=t[a],i=l[a];if(v.nGramWidths.forEach((function(o){var s=t[a+1]-t[a],u=r.getNumNGrams(s,o);r.createNGrams(e,n,f,i,u,o),i+=u})),v.preserveShort&&i===l[a]){var o=t[a+1]-t[a];if(0===o)return"continue";var s=o+2*v.padWidth;v.createNGrams(e,n,f,i,1,s)}},v=this;for(s=0;s<d;++s)m(s);return[f,l]},e}();function va(e,a,t,r,n,i,o,s){return new ma(t,r,n,i,o,s).compute(e,a)}function ka(e,a,t,r){if(e.length)if(0!==a.length)if(1!==a.length){var n=0;for(u=0;u<e.length+1;u++)if(u===e.length||-1!==a.indexOf(e[u])){s=e.subarray(n,u);t&&0===s.length||r.push(s),n=u+1}}else{for(var i=a[0],o=e.indexOf(i);-1!==o;){var s=e.subarray(0,o);t&&0===s.length||r.push(s),o=(e=e.subarray(o+1)).indexOf(i)}t&&0===e.length||r.push(e)}else for(var u=0;u<e.length;++u)r.push(e.subarray(u,u+1))}function ga(e,t,r){for(var n=e.length,i=[],o=0,s=0,u=new Array(n),d=0;d<n;++d){var l=i.length;ka(e[d],t,r,i);var p=i.length-l;u[d]=p,o+=p,s=Math.max(s,p)}var c=a.util.getArrayFromDType("int32",2*o),h=new Array(o),f=[n,s],m=0;for(d=0;d<n;++d)for(var v=0;v<u[d];++v)c[2*m]=d,c[2*m+1]=v,h[m]=i[m],++m;return[c,h,f]}function ba(e,t){for(var r=a.util.getArrayFromDType("int32",e.length),n=0;n<e.length;++n)r[n]=a.util.fingerPrint64(e[n]).modulo(t).getLowBitsUnsigned();return r}var Ia=v((function(e,a){return e-a})),ya=M((function(e,a,t,r){return{real:e-t,imag:a-r}})),Sa=w(a.Sub,Ia,ya),Ta={kernelName:a.Sub,backendName:"cpu",kernelFunc:Sa};function Na(e,t){for(var r=new Array(e.rank),n=0;n<r.length;n++)r[n]=e.shape[n]*t[n];var i=a.buffer(r,e.dtype);for(n=0;n<i.values.length;++n){for(var o=i.indexToLoc(n),s=new Array(e.rank),u=0;u<s.length;u++)s[u]=o[u]%e.shape[u];var d=e.locToIndex(s);i.values[n]=e.values[d]}return i}var xa=function(e,a){var t=a.value-e.value;return 0===t?e.index-a.index:t};function Fa(e,t,r,n){for(void 0===r&&(r=0),void 0===n&&(n=e.length-1);n>r;){if(n-r>600){var i=n-r+1,o=t-r+1,s=Math.log(i),u=.5*Math.exp(2*s/3),d=.5*Math.sqrt(s*u*(i-u)/i)*Math.sign(o-i/2);Fa(e,t,Math.max(r,Math.floor(t-o*u/i+d)),Math.min(n,Math.floor(t+(i-o)*u/i+d)))}var l=e[t],p=r,c=n;for(a.util.swap(e,r,t),xa(e[n],l)>0&&a.util.swap(e,r,n);p<c;){for(a.util.swap(e,p,c),p++,c--;xa(e[p],l)<0;)p+=1;for(;xa(e[c],l)>0;)c-=1}0===xa(e[r],l)?a.util.swap(e,r,c):(c+=1,a.util.swap(e,c,n)),c<=t&&(r=c+1),t<=c&&(n=c-1)}}function wa(e,t,r,n,i){for(var o=t[t.length-1],s=d([e.length/o,o],2),u=s[0],l=s[1],p=a.util.getTypedArrayFromDType(r,u*n),c=a.util.getTypedArrayFromDType("int32",u*n),h=function(a){var t=a*l,r=e.subarray(t,t+l),o=new Array(r.length);r.forEach((function(e,a){return o[a]={value:e,index:a}})),n<o.length&&(Fa(o,n),o=o.slice(0,n)),i&&o.sort(xa);for(var s=a*n,u=p.subarray(s,s+n),d=c.subarray(s,s+n),h=0;h<n;h++)u[h]=o[h].value,d[h]=o[h].index},f=0;f<u;f++)h(f);var m=t.slice();return m[m.length-1]=n,[a.buffer(m,r,p),a.buffer(m,"int32",c)]}function Ma(e,t,r,n){for(var i=a.util.parseAxisParam(t,r)[0],o=[1,r[0],1],s=0;s<i;s++)o[0]*=r[s];o[1]=r[i];for(s=i+1;s<r.length;s++)o[2]*=r[s];var u={},d=new Int32Array(r[i]),l=new a.TensorBuffer(o,n,e),p=[],c=1===o[0]&&1===o[2];for(s=0;s<r[i];s++){var h=void 0;if(c)h=e[s].toString();else{for(var f=[],m=0;m<o[0];m++)for(var v=0;v<o[2];v++)f.push(l.get(m,s,v));h=f.join(",")}if(void 0!==u[h])d[s]=u[h];else{var k=Object.keys(u).length;u[h]=k,d[s]=k,p.push(s)}}var g=o.slice();g[1]=Object.keys(u).length;var b=new a.TensorBuffer(g,n);p.forEach((function(e,a){for(var t=0;t<o[0];t++)for(var r=0;r<o[2];r++)b.set(l.get(t,e,r),t,a,r)}));var I=r.slice();return I[i]=g[1],{outputValues:b.values,outputShape:I,indices:d}}var Aa={__proto__:null,simpleAbsImpl:f,addImpl:A,bincountImpl:z,bincountReduceImpl:R,castImpl:N,ceilImpl:H,concatImpl:B,equalImpl:G,expImpl:U,expm1Impl:K,floorImpl:Q,gatherNdImpl:ee,gatherV2Impl:ae,greaterImpl:te,greaterEqualImpl:ie,lessImpl:ue,lessEqualImpl:pe,linSpaceImpl:fe,logImpl:me,maxImpl:ge,maximumImpl:be,minimumImpl:Se,multiplyImpl:xe,negImpl:Ae,notEqualImpl:_e,prodImpl:Ce,raggedGatherImpl:Ge,raggedTensorToTensorImpl:je,rangeImpl:Ke,rsqrtImpl:Ye,scatterImpl:Xe,sigmoidImpl:$e,sliceImpl:ta,sparseFillEmptyRowsImpl:ia,sparseReshapeImpl:oa,sparseSegmentReductionImpl:sa,sqrtImpl:ua,squaredDifferenceImpl:pa,stridedSliceImpl:fa,stringNGramsImpl:va,stringSplitImpl:ga,stringToHashBucketFastImpl:ba,subImpl:Ia,tileImpl:Na,topKImpl:wa,transposeImpl:Re,uniqueImpl:Ma};a.registerBackend("cpu",(function(){return new h}),1);var Da=P(a.Elu,(function(e){return e>=0?e:Math.exp(e)-1})),_a={kernelName:a.Elu,backendName:"cpu",kernelFunc:Da};function Ea(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.alpha;p([i],"leakyRelu");for(var s=a.util.sizeFromShape(i.shape),u=r.data.get(i.dataId).values,d=a.util.getTypedArrayFromDType("float32",s),l=0;l<u.length;l++)d[l]=u[l]<0?o*u[l]:u[l];return r.makeTensorInfo(i.shape,"float32",d)}var za={kernelName:a.LeakyRelu,backendName:"cpu",kernelFunc:Ea},Ra=v((function(e,a){return e<0?a*e:e}));function Wa(e){var a=e.inputs,t=e.backend,r=a.x,n=a.alpha;p([r,n],"prelu");var i=t.data.get(r.dataId).values,o=t.data.get(n.dataId).values,s=d(Ra(r.shape,n.shape,i,o,"float32"),2),u=s[0],l=s[1];return t.makeTensorInfo(l,"float32",u)}var Pa={kernelName:a.Prelu,backendName:"cpu",kernelFunc:Wa},Ca=P(a.Relu,(function(e){return Math.max(0,e)})),Ha={kernelName:a.Relu,backendName:"cpu",kernelFunc:Ca},Oa=P(a.Relu6,(function(e){return Math.min(Math.max(0,e),6)})),Va={kernelName:a.Relu6,backendName:"cpu",kernelFunc:Oa};function Ba(e,a,t,r,n){if("linear"===t)return I({inputs:{x:a},backend:e});if("relu"===t)return Ca({inputs:{x:a},backend:e});if("elu"===t)return Da({inputs:{x:a},backend:e});if("relu6"===t)return Oa({inputs:{x:a},backend:e});if("prelu"===t)return Wa({inputs:{x:a,alpha:r},backend:e});if("leakyrelu"===t)return Ea({inputs:{x:a},backend:e,attrs:{alpha:n}});if("sigmoid"===t)return ea({inputs:{x:a},backend:e});throw new Error("Activation "+t+" has not been implemented for the CPU backend.")}function Ga(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.shape,s=a.util.sizeFromShape(i.shape),u=a.util.inferFromImplicitShape(o,s),d=a.util.sizeFromShape(u);a.util.assert(s===d,(function(){return"The new shape ("+u+") has "+d+" elements and the old shape ("+i.shape+") has "+s+" elements. The new shape and old shape must have the same number of elements."})),r.incRef(i.dataId);var l=r.data.get(i.dataId);if(null!=l.complexTensorInfos){var p=l.complexTensorInfos.real,c=l.complexTensorInfos.imag;p.shape=u,c.shape=u}return{dataId:i.dataId,shape:u,dtype:i.dtype}}var La={kernelName:a.Reshape,backendName:"cpu",kernelFunc:Ga};function qa(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.a,o=t.b,s=n.transposeA,u=n.transposeB;p([i,o],"matMul");var l=i.shape.length,c=o.shape.length,h=s?i.shape[l-2]:i.shape[l-1],f=u?o.shape[c-1]:o.shape[c-2],m=s?i.shape[l-1]:i.shape[l-2],v=u?o.shape[c-2]:o.shape[c-1],k=i.shape.slice(0,-2),g=o.shape.slice(0,-2),b=a.util.sizeFromShape(k),I=a.util.sizeFromShape(g),y=a.broadcast_util.assertAndGetBroadcastShape(i.shape.slice(0,-2),o.shape.slice(0,-2)).concat([m,v]);a.util.assert(h===f,(function(){return"Error in matMul: inner shapes ("+h+") and ("+f+") of Tensors with shapes "+i.shape+" and "+o.shape+" and transposeA="+s+" and transposeB="+u+" must match."}));for(var S=u?[I,v,f]:[I,f,v],T=Ga({inputs:{x:i},backend:r,attrs:{shape:s?[b,h,m]:[b,m,h]}}),N=Ga({inputs:{x:o},backend:r,attrs:{shape:S}}),x=s?T.shape[1]:T.shape[2],F=s?T.shape[2]:T.shape[1],w=u?N.shape[1]:N.shape[2],M=Math.max(b,I),A=r.data.get(T.dataId).values,D=r.data.get(N.dataId).values,_=a.util.computeStrides(T.shape),E=a.util.computeStrides(N.shape),z=d(s?[_[0],1,_[1]]:[_[0],_[1],1],3),R=z[0],W=z[1],P=z[2],C=d(u?[1,E[1],E[0]]:[E[1],1,E[0]],3),H=C[0],O=C[1],V=C[2],B=F*w,G=a.buffer([M,F,w],T.dtype),L=G.values,q=r.blockSize,U=0;U<M;U++)for(var Z=0;Z<F;Z+=q)for(var j=0;j<w;j+=q)for(var K=0;K<x;K+=q)for(var Y=Math.min(Z+q,F),J=Math.min(j+q,w),Q=Math.min(K+q,x),X=Z;X<Y;X++)for(var $=j;$<J;$++){for(var ee=0,ae=K;ae<Q;ae++){var te=Math.min(U,b-1)*R,re=Math.min(U,I-1)*V;ee+=A[te+X*W+ae*P]*D[ae*H+$*O+re]}L[U*B+(X*w+$)]+=ee}return r.disposeIntermediateTensorInfo(T),r.disposeIntermediateTensorInfo(N),r.makeTensorInfo(y,G.dtype,G.values)}var Ua={kernelName:a.BatchMatMul,backendName:"cpu",kernelFunc:qa};var Za={kernelName:a._FusedMatMul,backendName:"cpu",kernelFunc:function(e){var a,t,r,n,i,o=e.inputs,s=e.backend,d=e.attrs,l=o.a,p=o.b,c=o.bias,h=o.preluActivationWeights,f=d.transposeA,m=d.transposeB,v=d.activation,k=d.leakyreluAlpha,g=[];r=qa({inputs:{a:l,b:p},attrs:{transposeA:f,transposeB:m},backend:s}),c&&(n=_({inputs:{a:r,b:c},backend:s}),g.push(r),r=n),v&&(i=Ba(s,r,v,h,k),g.push(r),r=i);try{for(var b=u(g),I=b.next();!I.done;I=b.next()){var y=I.value;s.disposeIntermediateTensorInfo(y)}}catch(e){a={error:e}}finally{try{I&&!I.done&&(t=b.return)&&t.call(b)}finally{if(a)throw a.error}}return r}},ja=P(a.Acos,(function(e){return Math.acos(e)})),Ka={kernelName:a.Acos,backendName:"cpu",kernelFunc:ja},Ya=P(a.Acosh,(function(e){return Math.acosh(e)})),Ja={kernelName:a.Acosh,backendName:"cpu",kernelFunc:Ya};var Qa={kernelName:a.AddN,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=t;p(t,"addN");for(var i=n.map((function(e){return r.data.get(e.dataId).values})),o=a.buffer(n[0].shape,n[0].dtype),s=o.values,u=0;u<n.length;u++)for(var d=i[u],l=0;l<s.length;l++)s[l]+=d[l];return r.makeTensorInfo(o.shape,o.dtype,o.values)}};var Xa={kernelName:a.All,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.axis,s=n.keepDims;p(i,"all");var u=a.util.parseAxisParam(o,i.shape),l=u,c=a.backend_util.getAxesPermutation(l,i.shape.length),h=i;null!=c&&(h=We({inputs:{x:i},backend:r,attrs:{perm:c}}),l=a.backend_util.getInnerMostAxes(l.length,i.shape.length)),a.backend_util.assertAxesAreInnerMostDims("all",l,h.shape.length);for(var f=d(a.backend_util.computeOutAndReduceShapes(h.shape,l),2),m=f[0],v=f[1],k=a.util.sizeFromShape(v),g=a.util.makeZerosTypedArray(a.util.sizeFromShape(m),h.dtype),b=r.data.get(h.dataId).values,I=0;I<g.length;++I){for(var y=I*k,S=b[y],T=0;T<k;++T){var N=b[y+T];S=S&&N}g[I]=S}null!=c&&r.disposeIntermediateTensorInfo(h);var x=r.makeTensorInfo(m,h.dtype,g);if(s){var F=Ga({inputs:{x:x},backend:r,attrs:{shape:a.backend_util.expandShapeToKeepDim(m,u)}});return r.disposeIntermediateTensorInfo(x),F}return x}};var $a={kernelName:a.Any,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.axis,s=n.keepDims;p(i,"any");var u=a.util.parseAxisParam(o,i.shape),l=u,c=a.backend_util.getAxesPermutation(l,i.shape.length),h=i;null!=c&&(h=We({inputs:{x:i},backend:r,attrs:{perm:c}}),l=a.backend_util.getInnerMostAxes(l.length,i.shape.length)),a.backend_util.assertAxesAreInnerMostDims("any",l,h.shape.length);for(var f=d(a.backend_util.computeOutAndReduceShapes(h.shape,l),2),m=f[0],v=f[1],k=a.util.sizeFromShape(v),g=a.util.makeZerosTypedArray(a.util.sizeFromShape(m),h.dtype),b=r.data.get(h.dataId).values,I=0;I<g.length;++I){for(var y=I*k,S=b[y],T=0;T<k;++T){var N=b[y+T];S=S||N}g[I]=S}null!=c&&r.disposeIntermediateTensorInfo(h);var x=r.makeTensorInfo(m,h.dtype,g);if(s){var F=Ga({inputs:{x:x},backend:r,attrs:{shape:a.backend_util.expandShapeToKeepDim(m,u)}});return r.disposeIntermediateTensorInfo(x),F}return x}};var et={kernelName:a.ArgMax,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.axis;p(i,"argMax");var s=a.util.parseAxisParam(o,i.shape),u=a.backend_util.getAxesPermutation(s,i.shape.length),l=i,c=[];null!=u&&(l=We({inputs:{x:i},backend:r,attrs:{perm:u}}),c.push(l),s=a.backend_util.getInnerMostAxes(s.length,l.shape.length)),s=[s[0]],a.backend_util.assertAxesAreInnerMostDims("argMax",s,l.shape.length);for(var h=d(a.backend_util.computeOutAndReduceShapes(l.shape,s),2),f=h[0],m=h[1],v=a.util.sizeFromShape(f),k=a.util.makeZerosTypedArray(v,"int32"),g=a.util.sizeFromShape(m),b=r.data.get(l.dataId).values,I=0;I<k.length;++I){for(var y=I*g,S=b[y],T=0,N=0;N<g;++N){var x=b[y+N];x>S&&(S=x,T=N)}k[I]=T}return c.forEach((function(e){return r.disposeIntermediateTensorInfo(e)})),r.makeTensorInfo(f,"int32",k)}};var at={kernelName:a.ArgMin,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.axis;p(i,"argMin");var s=a.util.parseAxisParam(o,i.shape),u=a.backend_util.getAxesPermutation(s,i.shape.length),l=i,c=[];null!=u&&(l=We({inputs:{x:i},backend:r,attrs:{perm:u}}),c.push(l),s=a.backend_util.getInnerMostAxes(s.length,l.shape.length)),s=[s[0]],a.backend_util.assertAxesAreInnerMostDims("argMin",s,l.shape.length);for(var h=d(a.backend_util.computeOutAndReduceShapes(l.shape,s),2),f=h[0],m=h[1],v=a.util.sizeFromShape(f),k=a.util.makeZerosTypedArray(v,"int32"),g=a.util.sizeFromShape(m),b=r.data.get(l.dataId).values,I=0;I<k.length;++I){for(var y=I*g,S=b[y],T=0,N=0;N<g;++N){var x=b[y+N];x<S&&(S=x,T=N)}k[I]=T}return c.forEach((function(e){return r.disposeIntermediateTensorInfo(e)})),r.makeTensorInfo(f,"int32",k)}},tt=P(a.Asin,(function(e){return Math.asin(e)})),rt={kernelName:a.Asin,backendName:"cpu",kernelFunc:tt},nt=P(a.Asinh,(function(e){return Math.asinh(e)})),it={kernelName:a.Asinh,backendName:"cpu",kernelFunc:nt},ot=P(a.Atan,(function(e){return Math.atan(e)})),st={kernelName:a.Atan,backendName:"cpu",kernelFunc:ot},ut=v((function(e,a){return Math.atan2(e,a)})),dt=w(a.Atan2,ut),lt={kernelName:a.Atan2,backendName:"cpu",kernelFunc:dt},pt=P(a.Atanh,(function(e){return Math.atanh(e)})),ct={kernelName:a.Atanh,backendName:"cpu",kernelFunc:pt};function ht(e,t,r,n,i,o){for(var s=i.strideHeight,u=i.strideWidth,d=i.dilationHeight,l=i.dilationWidth,p=i.effectiveFilterHeight,c=i.effectiveFilterWidth,h=i.padInfo.top,f=i.padInfo.left,m="max"===o?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,v=a.buffer(i.outShape,r),k=v.values,g=i.outShape[1]*i.outShape[2]*i.outShape[3],b=i.outShape[2]*i.outShape[3],I=i.outShape[3],y=0;y<i.batchSize;++y)for(var S=y*g,T=y*n[0],N=0;N<i.inChannels;++N)for(var x=0;x<i.outHeight;++x)for(var F=x*s-h,w=Math.max(0,F),M=Math.min(i.inHeight,p+F),A=S+x*b,D=0;D<i.outWidth;++D){for(var _=D*u-f,E=Math.max(0,_),z=Math.min(i.inWidth,c+_),R=m,W=0,P=0,C=w;C<M;C+=d){for(var H=T+C*n[1],O=E;O<z;O+=l){var V=e[H+O*n[2]+N];"max"===o&&V>R?R=V:"avg"===o&&(W+=V,P++)}if(isNaN(R))break}k[A+D*I+N]="avg"===o?W/P:R}return v}function ft(e,t,r,n,i,o){void 0===i&&(i=!1),void 0===o&&(o=!1);for(var s=a.buffer(n.outShape,"int32"),u=n.strideHeight,d=n.strideWidth,l=n.dilationHeight,p=n.dilationWidth,c=n.effectiveFilterHeight,h=n.effectiveFilterWidth,f=n.padInfo.top,m=n.padInfo.left,v=a.buffer(t,r,e),k=0;k<n.batchSize;++k)for(var g=0;g<n.inChannels;++g)for(var b=0;b<n.outHeight;++b){for(var I=b*u-f,y=I;y<0;)y+=l;for(var S=Math.min(n.inHeight,c+I),T=0;T<n.outWidth;++T){for(var N=T*d-m,x=N;x<0;)x+=p;for(var F=Math.min(n.inWidth,h+N),w=Number.NEGATIVE_INFINITY,M=-1,A=y;A<S;A+=l)for(var D=A-I,_=x;_<F;_+=p){var E=_-N,z=v.get(k,A,_,g);z>w&&(w=z,M=i?o?((k*n.inHeight+A)*n.inWidth+_)*n.inChannels+g:(A*n.inWidth+_)*n.inChannels+g:D*h+E)}s.set(M,k,b,T,g)}}return s}function mt(e,t,r,n,i,o){for(var s=i.strideDepth,u=i.strideHeight,d=i.strideWidth,l=i.dilationDepth,p=i.dilationHeight,c=i.dilationWidth,h=i.effectiveFilterDepth,f=i.effectiveFilterHeight,m=i.effectiveFilterWidth,v=i.padInfo.front,k=i.padInfo.top,g=i.padInfo.left,b="max"===o?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,I=a.buffer(i.outShape,r),y=I.values,S=i.outShape[1]*i.outShape[2]*i.outShape[3]*i.outShape[4],T=i.outShape[2]*i.outShape[3]*i.outShape[4],N=i.outShape[3]*i.outShape[4],x=i.outShape[4],F=0;F<i.batchSize;++F)for(var w=F*S,M=F*n[0],A=0;A<i.inChannels;++A)for(var D=0;D<i.outDepth;++D){for(var _=D*s-v,E=_;E<0;)E+=l;for(var z=Math.min(i.inDepth,h+_),R=w+D*T,W=0;W<i.outHeight;++W){for(var P=W*u-k,C=P;C<0;)C+=p;for(var H=Math.min(i.inHeight,f+P),O=R+W*N,V=0;V<i.outWidth;++V){for(var B=V*d-g,G=B;G<0;)G+=c;for(var L=Math.min(i.inWidth,m+B),q=O+V*x,U=b,Z=0,j=0,K=E;K<z;K+=l){for(var Y=M+K*n[1],J=C;J<H;J+=p){for(var Q=Y+J*n[2],X=G;X<L;X+=c){var $=e[Q+X*n[3]+A];if("max"===o&&$>U?U=$:"avg"===o&&(Z+=$,j++),isNaN(U))break}if(isNaN(U))break}if(isNaN(U))break}y[q+A]="avg"===o?Z/j:U}}}return I}var vt={kernelName:a.AvgPool,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x;p(i,"avgPool");var o=n.filterSize,s=n.strides,u=n.pad,d=n.dimRoundingMode;a.util.assert(a.backend_util.eitherStridesOrDilationsAreOne(s,1),(function(){return"Error in avgPool: Either strides or dilations must be 1. Got strides "+s+" and dilations '1'"}));var l,c=a.backend_util.computePool2DInfo(i.shape,o,s,1,u,d);if(1===c.filterWidth&&1===c.filterHeight&&a.util.arraysEqual(c.inShape,c.outShape))l=I({inputs:{x:i},backend:r});else{var h=r.data.get(i.dataId).values,f=a.util.computeStrides(i.shape),m=ht(h,i.shape,i.dtype,f,c,"avg");l=r.makeTensorInfo(c.outShape,i.dtype,m.values)}return l}};var kt={kernelName:a.AvgPool3D,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.filterSize,s=n.strides,u=n.pad,d=n.dimRoundingMode,l=n.dataFormat;p(i,"avgPool3d");var c=a.backend_util.computePool3DInfo(i.shape,o,s,1,u,d,l),h=mt(r.data.get(i.dataId).values,i.shape,i.dtype,a.util.computeStrides(i.shape),c,"avg");return r.makeTensorInfo(h.shape,"float32",h.values)}};var gt={kernelName:a.AvgPool3DGrad,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.dy,o=t.input,s=n.filterSize,u=n.strides,d=n.pad,l=n.dimRoundingMode;p([i,o],"avgPool3DGrad");for(var c=a.backend_util.computePool3DInfo(o.shape,s,u,1,d,l),h=c.strideDepth,f=c.strideHeight,m=c.strideWidth,v=c.filterDepth,k=c.filterHeight,g=c.filterWidth,b=c.dilationDepth,I=c.dilationHeight,y=c.dilationWidth,S=c.effectiveFilterDepth,T=c.effectiveFilterHeight,N=c.effectiveFilterWidth,x=S-1-c.padInfo.front,F=N-1-c.padInfo.left,w=T-1-c.padInfo.top,M=a.buffer(o.shape,"float32"),A=1/(v*k*g),D=r.bufferSync(i),_=0;_<c.batchSize;++_)for(var E=0;E<c.inChannels;++E)for(var z=0;z<c.inDepth;++z)for(var R=0;R<c.inHeight;++R)for(var W=0;W<c.inWidth;++W){for(var P=z-x,C=R-w,H=W-F,O=0,V=0;V<S;V+=b){var B=(P+V)/h;if(!(B<0||B>=c.outDepth||Math.floor(B)!==B))for(var G=0;G<T;G+=I){var L=(C+G)/f;if(!(L<0||L>=c.outHeight||Math.floor(L)!==L))for(var q=0;q<N;q+=y){var U=(H+q)/m;if(!(U<0||U>=c.outWidth||Math.floor(U)!==U))O+=D.get(_,B,L,U,E)}}}M.set(O*A,_,z,R,W,E)}return r.makeTensorInfo(M.shape,M.dtype,M.values)}};var bt={kernelName:a.AvgPoolGrad,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.dy,o=t.input,s=o;p([i,o],"avgPoolGrad");for(var u=n.filterSize,d=n.strides,l=n.pad,c=a.backend_util.computePool2DInfo(s.shape,u,d,1,l),h=c.strideHeight,f=c.strideWidth,m=c.filterHeight,v=c.filterWidth,k=c.dilationHeight,g=c.dilationWidth,b=c.effectiveFilterHeight,I=c.effectiveFilterWidth,y=I-1-c.padInfo.left,S=b-1-c.padInfo.top,T=a.buffer(s.shape,"float32"),N=1/(m*v),x=r.data.get(i.dataId).values,F=a.buffer(i.shape,"float32",x),w=0;w<c.batchSize;++w)for(var M=0;M<c.inChannels;++M)for(var A=0;A<c.inHeight;++A)for(var D=0;D<c.inWidth;++D){for(var _=A-S,E=D-y,z=0,R=0;R<b;R+=k){var W=(_+R)/h;if(!(W<0||W>=c.outHeight||Math.floor(W)!==W))for(var P=0;P<I;P+=g){var C=(E+P)/f;if(!(C<0||C>=c.outWidth||Math.floor(C)!==C))z+=F.get(w,W,C,M)}}T.set(z*N,w,A,D,M)}return r.makeTensorInfo(T.shape,T.dtype,T.values)}};var It={kernelName:a.FusedBatchNorm,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=t.scale,s=t.offset,u=t.mean,d=t.variance;a.util.assert(u.shape.length===d.shape.length,(function(){return"Batch normalization gradient requires mean and variance to have equal ranks."})),a.util.assert(null==s||u.shape.length===s.shape.length,(function(){return"Batch normalization gradient requires mean and offset to have equal ranks."})),a.util.assert(null==o||u.shape.length===o.shape.length,(function(){return"Batch normalization gradient requires mean and scale to have equal ranks."})),p([i,u,d,o,s],"batchNorm");var l=n.varianceEpsilon;null==l&&(l=.001);for(var c=r.data.get(i.dataId).values,h=r.data.get(u.dataId).values,f=r.data.get(d.dataId).values,m=o?r.data.get(o.dataId).values:new Float32Array([1]),v=s?r.data.get(s.dataId).values:new Float32Array([0]),k=new Float32Array(c.length),g=v.length,b=m.length,I=f.length,y=h.length,S=0,T=0,N=0,x=0,F=0;F<c.length;++F)k[F]=v[S++]+(c[F]-h[T++])*m[N++]/Math.sqrt(f[x++]+l),S>=g&&(S=0),T>=y&&(T=0),N>=b&&(N=0),x>=I&&(x=0);return r.makeTensorInfo(i.shape,i.dtype,k)}};var yt={kernelName:a.BatchToSpaceND,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.blockShape,s=n.crops;p([i],"batchToSpaceND");var u=o.reduce((function(e,a){return e*a})),d=a.backend_util.getReshaped(i.shape,o,u),l=a.backend_util.getPermuted(d.length,o.length),c=a.backend_util.getReshapedPermuted(i.shape,o,u),h=a.backend_util.getSliceBeginCoords(s,o.length),f=a.backend_util.getSliceSize(c,s,o.length),m=Ga({inputs:{x:i},backend:r,attrs:{shape:d}}),v=We({inputs:{x:m},backend:r,attrs:{perm:l}}),k=Ga({inputs:{x:v},backend:r,attrs:{shape:c}}),g=ra({inputs:{x:k},backend:r,attrs:{begin:h,size:f}});return r.disposeIntermediateTensorInfo(m),r.disposeIntermediateTensorInfo(v),r.disposeIntermediateTensorInfo(k),g}};var St={kernelName:a.Bincount,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs,n=a.x,i=a.weights,o=r.size,s=z(t.data.get(n.dataId).values,t.data.get(i.dataId).values,i.dtype,i.shape,o);return t.makeTensorInfo([o],i.dtype,s)}};var Tt={kernelName:a.BroadcastArgs,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=t.s0,i=t.s1,o=r.data.get(n.dataId).values,s=r.data.get(i.dataId).values,u=a.backend_util.assertAndGetBroadcastShape(Array.from(o),Array.from(s));return r.makeTensorInfo([u.length],"int32",Int32Array.from(u))}},Nt=P(a.ClipByValue,(function(e,a){var t=a;return e>t.clipValueMax?t.clipValueMax:e<t.clipValueMin?t.clipValueMin:e})),xt={kernelName:a.ClipByValue,backendName:"cpu",kernelFunc:Nt},Ft={kernelName:a.ComplexAbs,backendName:"cpu",kernelFunc:function(e){for(var t=e.inputs.x,r=e.backend,n=new Float32Array(a.util.sizeFromShape(t.shape)),i=r.data.get(t.dataId),o=i.complexTensorInfos.real,s=i.complexTensorInfos.imag,u=r.data.get(o.dataId).values,d=r.data.get(s.dataId).values,l=0;l<u.length;l++){var p=u[l],c=d[l];n[l]=Math.hypot(p,c)}return r.makeOutput(n,t.shape,"float32")}};function wt(e){var a=e.inputs,t=e.backend,r=a.input,n=t.data.get(r.dataId).complexTensorInfos.imag,i=t.data.get(n.dataId).values;return t.makeTensorInfo(n.shape,n.dtype,i)}var Mt={kernelName:a.Imag,backendName:"cpu",kernelFunc:wt};function At(e){var t=e.inputs,r=e.backend,n=e.attrs.axis,i=a.util.parseAxisParam(n,t[0].shape)[0],o=t.map((function(e){return e.shape}));a.backend_util.assertParamsConsistent(o,i);var s=a.backend_util.computeOutShape(t.map((function(e){return e.shape})),i);if(0===a.util.sizeFromShape(s))return r.makeTensorInfo(s,t[0].dtype,[]);var u=t.filter((function(e){return a.util.sizeFromShape(e.shape)>0}));if(1===u.length)return I({inputs:{x:u[0]},backend:r});if("complex64"===u[0].dtype){var d=u.map((function(e){return S({inputs:{input:e},backend:r})})),l=u.map((function(e){return wt({inputs:{input:e},backend:r})})),p=At({inputs:d,backend:r,attrs:{axis:i}}),c=At({inputs:l,backend:r,attrs:{axis:i}}),h=k({inputs:{real:p,imag:c},backend:r});return d.forEach((function(e){return r.disposeIntermediateTensorInfo(e)})),l.forEach((function(e){return r.disposeIntermediateTensorInfo(e)})),r.disposeIntermediateTensorInfo(p),r.disposeIntermediateTensorInfo(c),h}var f=u.map((function(e){var t=a.util.sizeFromShape(e.shape.slice(i));return Ga({inputs:{x:e},backend:r,attrs:{shape:[-1,t]}})})),m=f.map((function(e){return{vals:r.data.get(e.dataId).values,shape:e.shape}}));s=a.backend_util.computeOutShape(f.map((function(e){return e.shape})),1);var v=1===f[0].shape[0],g=B(m,s,t[0].dtype,v),b=a.backend_util.computeOutShape(u.map((function(e){return e.shape})),i),y=r.makeTensorInfo(b,t[0].dtype,g);return f.forEach((function(e){return r.disposeIntermediateTensorInfo(e)})),y}var Dt={kernelName:a.Concat,backendName:"cpu",kernelFunc:At};function _t(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=t.filter,s=n.strides,u=n.pad,d=n.dataFormat,l=n.dilations,c=n.dimRoundingMode;p([i,o],"conv2d");for(var h=a.backend_util.convertConv2DDataFormat(d),f=a.backend_util.computeConv2DInfo(i.shape,o.shape,s,l,u,c,!1,h),m=f.filterHeight,v=f.filterWidth,k=f.dilationHeight,g=f.dilationWidth,b=f.padInfo.left,I=f.padInfo.top,y="channelsLast"===f.dataFormat,S=new a.TensorBuffer(f.outShape,i.dtype),T=a.util.computeStrides(i.shape),N=a.util.computeStrides(o.shape),x=T[0],F=y?T[1]:T[2],w=y?T[2]:1,M=y?1:T[1],A=S.strides[0],D=y?S.strides[1]:S.strides[2],_=y?S.strides[2]:1,E=y?1:S.strides[1],z=r.data.get(i.dataId).values,R=r.data.get(o.dataId).values,W=S.values,P=0;P<f.batchSize;++P)for(var C=P*x,H=P*A,O=0;O<f.outHeight;++O)for(var V=H+O*D,B=O*f.strideHeight-I,G=0;G<m;++G){var L=B+G*k;if(!(L<0||L>=f.inHeight))for(var q=G*N[0],U=C+L*F,Z=0;Z<f.outWidth;++Z)for(var j=V+Z*_,K=Z*f.strideWidth-b,Y=0;Y<v;++Y){var J=K+Y*g;if(!(J<0||J>=f.inWidth))for(var Q=U+J*w,X=q+Y*N[1],$=0;$<f.inChannels;++$){for(var ee=z[Q+$*M],ae=0;ae<f.outChannels;++ae)W[j+ae*E]+=ee*R[X+ae];X+=f.outChannels}}}return r.makeTensorInfo(S.shape,S.dtype,W)}var Et={kernelName:a.Conv2D,backendName:"cpu",kernelFunc:_t};var zt={kernelName:a.Conv2DBackpropFilter,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=t.dy,s=n.strides,u=n.pad,d=n.dataFormat,l=n.dimRoundingMode,c=n.filterShape;p([i,o],"conv2dBackpropFilter");for(var h=a.backend_util.convertConv2DDataFormat(d),f=a.backend_util.computeConv2DInfo(i.shape,c,s,1,u,l,!1,h),m=f.strideHeight,v=f.strideWidth,k=f.filterHeight,g=f.filterWidth,b="channelsLast"===f.dataFormat,I=new a.TensorBuffer(f.filterShape,"float32"),y=f.padInfo.left,S=f.padInfo.top,T=r.data.get(i.dataId).values,N=r.data.get(o.dataId).values,x=new a.TensorBuffer(i.shape,i.dtype,T),F=new a.TensorBuffer(o.shape,o.dtype,N),w=0;w<k;++w)for(var M=Math.max(0,Math.ceil((S-w)/m)),A=Math.min(f.outHeight,(f.inHeight+S-w)/m),D=0;D<g;++D)for(var _=Math.max(0,Math.ceil((y-D)/v)),E=Math.min(f.outWidth,(f.inWidth+y-D)/v),z=0;z<f.inChannels;++z)for(var R=0;R<f.outChannels;++R){for(var W=0,P=0;P<f.batchSize;++P)for(var C=M;C<A;++C)for(var H=w+C*m-S,O=_;O<E;++O){var V=D+O*v-y;W+=b?x.get(P,H,V,z)*F.get(P,C,O,R):x.get(P,z,H,V)*F.get(P,R,C,O)}I.set(W,w,D,z,R)}return r.makeTensorInfo(I.shape,I.dtype,I.values)}};var Rt={kernelName:a.Conv2DBackpropInput,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.dy,o=t.filter,s=n.inputShape,u=n.strides,l=n.pad,c=n.dataFormat,h=n.dimRoundingMode;p([i,o],"conv2dBackpropInput");var f=a.util.computeStrides(o.shape),m=a.util.computeStrides(i.shape),v=a.backend_util.convertConv2DDataFormat(c),k=a.backend_util.computeConv2DInfo(s,o.shape,u,1,l,h,!1,v),g=new a.TensorBuffer(k.inShape,"float32"),b=g.values,I=r.data.get(i.dataId).values,y=r.data.get(o.dataId).values,S=d(f,3),T=S[0],N=S[1],x=S[2],F=k.batchSize,w=k.filterHeight,M=k.filterWidth,A=k.inChannels,D=k.inHeight,_=k.inWidth,E=k.outChannels,z=k.outHeight,R=k.outWidth,W=k.strideHeight,P=k.strideWidth;v=k.dataFormat;for(var C=w-1-k.padInfo.top,H=M-1-k.padInfo.left,O="channelsLast"===v,V=g.strides[0],B=O?g.strides[1]:g.strides[2],G=O?g.strides[2]:1,L=O?1:g.strides[1],q=m[0],U=O?m[1]:m[2],Z=O?m[2]:1,j=O?1:m[1],K=0;K<F;++K)for(var Y=0;Y<A;++Y)for(var J=0;J<D;++J)for(var Q=J-C,X=Math.max(0,Math.ceil(Q/W)),$=Math.min(z,(w+Q)/W),ee=0;ee<_;++ee){for(var ae=ee-H,te=Math.max(0,Math.ceil(ae/P)),re=Math.min(R,(M+ae)/P),ne=0,ie=X;ie<$;++ie)for(var oe=ie*W-Q,se=te;se<re;++se)for(var ue=q*K+U*ie+Z*se,de=T*(w-1-oe)+N*(M-1-(se*P-ae))+x*Y,le=0;le<E;++le){ne+=I[ue+j*le]*y[de+le]}b[V*K+B*J+G*ee+L*Y]=ne}return r.makeTensorInfo(g.shape,g.dtype,g.values)}};var Wt={kernelName:a.Conv3D,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=t.filter,s=n.strides,u=n.pad,d=n.dilations;p([i,o],"conv3d");for(var l=a.backend_util.computeConv3DInfo(i.shape,o.shape,s,d,u),c=l.filterDepth,h=l.filterHeight,f=l.filterWidth,m=l.dilationDepth,v=l.dilationHeight,k=l.dilationWidth,g=l.padInfo,b=g.front,I=g.left,y=g.top,S=new a.TensorBuffer(l.outShape,i.dtype),T=r.data.get(i.dataId).values,N=r.data.get(o.dataId).values,x=S.values,F=a.util.computeStrides(i.shape),w=a.util.computeStrides(o.shape),M=0;M<l.batchSize;++M)for(var A=M*F[0],D=M*S.strides[0],_=0;_<l.outDepth;++_)for(var E=D+_*S.strides[1],z=_*l.strideDepth-b,R=0;R<c;++R){var W=z+R*m;if(!(W<0||W>=l.inDepth))for(var P=R*w[0],C=A+W*F[1],H=0;H<l.outHeight;++H)for(var O=E+H*S.strides[2],V=H*l.strideHeight-y,B=0;B<h;++B){var G=V+B*v;if(!(G<0||G>=l.inHeight))for(var L=P+B*w[1],q=C+G*F[2],U=0;U<l.outWidth;++U)for(var Z=O+U*l.outChannels,j=U*l.strideWidth-I,K=0;K<f;++K){var Y=j+K*k;if(!(Y<0||Y>=l.inWidth))for(var J=L+K*w[2],Q=q+Y*l.inChannels,X=J,$=0;$<l.inChannels;++$){for(var ee=T[Q+$],ae=0;ae<l.outChannels;++ae)x[Z+ae]+=ee*N[X+ae];X+=l.outChannels}}}}return r.makeTensorInfo(S.shape,S.dtype,S.values)}};var Pt={kernelName:a.Conv3DBackpropFilterV2,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=t.dy,s=n.strides,u=n.pad,l=n.filterShape;p([i,o],"conv3dBackpropFilterV2");for(var c=a.util.computeStrides(i.shape),h=a.util.computeStrides(o.shape),f=a.backend_util.computeConv3DInfo(i.shape,l,s,1,u),m=f.strideDepth,v=f.strideHeight,k=f.strideWidth,g=f.filterDepth,b=f.filterHeight,I=f.filterWidth,y=new a.TensorBuffer(f.filterShape,"float32"),S=y.values,T=d(y.strides,4),N=T[0],x=T[1],F=T[2],w=T[3],M=r.data.get(o.dataId).values,A=d(h,4),D=A[0],_=A[1],E=A[2],z=A[3],R=r.data.get(i.dataId).values,W=d(c,4),P=W[0],C=W[1],H=W[2],O=W[3],V=f.padInfo.front,B=f.padInfo.left,G=f.padInfo.top,L=0;L<g;++L)for(var q=Math.max(0,Math.ceil((V-L)/m)),U=Math.min(f.outDepth,(f.inDepth+V-L)/m),Z=L*N,j=0;j<b;++j)for(var K=Math.max(0,Math.ceil((G-j)/v)),Y=Math.min(f.outHeight,(f.inHeight+G-j)/v),J=j*x+Z,Q=0;Q<I;++Q)for(var X=Math.max(0,Math.ceil((B-Q)/k)),$=Math.min(f.outWidth,(f.inWidth+B-Q)/k),ee=Q*F+J,ae=0;ae<f.inChannels;++ae)for(var te=ae*w+ee,re=0;re<f.outChannels;++re){for(var ne=0,ie=0;ie<f.batchSize;++ie)for(var oe=ie*P,se=ie*D,ue=q;ue<U;++ue)for(var de=(L+ue*m-V)*C+oe,le=ue*_+se,pe=K;pe<Y;++pe)for(var ce=(j+pe*v-G)*H+de,he=pe*E+le,fe=X;fe<$;++fe){var me=fe*z+he;ne+=R[(Q+fe*k-B)*O+ce+ae]*M[me+re]}S[te+re]=ne}return r.makeTensorInfo(y.shape,y.dtype,y.values)}};var Ct={kernelName:a.Conv3DBackpropInputV2,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.dy,o=t.filter,s=n.pad,u=n.strides,l=n.inputShape;p([i],"conv3dBackpropInputV2");for(var c=a.util.computeStrides(i.shape),h=a.util.computeStrides(o.shape),f=a.backend_util.computeConv3DInfo(l,o.shape,u,1,s),m=new a.TensorBuffer(f.inShape,"float32"),v=m.values,k=d(m.strides,4),g=k[0],b=k[1],I=k[2],y=k[3],S=r.data.get(i.dataId).values,T=d(c,4),N=T[0],x=T[1],F=T[2],w=T[3],M=r.data.get(o.dataId).values,A=d(h,4),D=A[0],_=A[1],E=A[2],z=A[3],R=f.batchSize,W=f.filterDepth,P=f.filterHeight,C=f.filterWidth,H=f.inChannels,O=f.inDepth,V=f.inHeight,B=f.inWidth,G=f.outChannels,L=f.outDepth,q=f.outHeight,U=f.outWidth,Z=f.strideDepth,j=f.strideHeight,K=f.strideWidth,Y=W-1-f.padInfo.front,J=P-1-f.padInfo.top,Q=C-1-f.padInfo.left,X=0;X<R;++X)for(var $=0;$<H;++$)for(var ee=0;ee<O;++ee)for(var ae=ee-Y,te=Math.max(0,Math.ceil(ae/Z)),re=Math.min(L,(W+ae)/Z),ne=0;ne<V;++ne)for(var ie=ne-J,oe=Math.max(0,Math.ceil(ie/j)),se=Math.min(q,(P+ie)/j),ue=0;ue<B;++ue){for(var de=ue-Q,le=Math.max(0,Math.ceil(de/K)),pe=Math.min(U,(C+de)/K),ce=0,he=te;he<re;++he)for(var fe=he*Z-ae,me=oe;me<se;++me)for(var ve=me*j-ie,ke=le;ke<pe;++ke)for(var ge=N*X+x*he+F*me+w*ke,be=D*(W-1-fe)+_*(P-1-ve)+E*(C-1-(ke*K-de))+z*$,Ie=0;Ie<G;++Ie){ce+=S[ge+Ie]*M[be+Ie]}v[g*X+b*ee+I*ne+y*ue+$]=ce}return r.makeTensorInfo(m.shape,m.dtype,m.values)}},Ht=P(a.Cos,(function(e){return Math.cos(e)})),Ot={kernelName:a.Cos,backendName:"cpu",kernelFunc:Ht},Vt=P(a.Cosh,(function(e){return Math.cosh(e)})),Bt={kernelName:a.Cosh,backendName:"cpu",kernelFunc:Vt};var Gt={kernelName:a.CropAndResize,backendName:"cpu",kernelFunc:function(e){for(var t=e.inputs,r=e.backend,n=e.attrs,i=t.image,o=t.boxes,s=t.boxInd,u=n.cropSize,l=n.method,p=n.extrapolationValue,c=d(i.shape,4),h=c[0],f=c[1],m=c[2],v=c[3],k=o.shape[0],g=d(u,2),b=g[0],I=g[1],y=a.buffer([k,b,I,v],"float32"),S=r.data.get(o.dataId).values,T=r.data.get(s.dataId).values,N=r.data.get(i.dataId).values,x=a.util.computeStrides(i.shape),F=a.util.computeStrides(y.shape),w=0;w<k;w++){var M=4*w,A=S[M],D=S[M+1],_=S[M+2],E=S[M+3],z=T[w];if(!(z>=h))for(var R=b>1?(_-A)*(f-1)/(b-1):0,W=I>1?(E-D)*(m-1)/(I-1):0,P=0;P<b;P++){var C=b>1?A*(f-1)+P*R:.5*(A+_)*(f-1);if(C<0||C>f-1)for(var H=0;H<I;H++)for(var O=0;O<v;O++){var V=O+H*F[2]+P*F[1]+w*F[0];y.values[V]=p}else if("bilinear"===l){var B=Math.floor(C),G=Math.ceil(C),L=C-B;for(H=0;H<I;H++){if((X=I>1?D*(m-1)+H*W:.5*(D+E)*(m-1))<0||X>m-1)for(O=0;O<v;O++){V=O+H*F[2]+P*F[1]+w*F[0];y.values[V]=p}else{var q=Math.floor(X),U=Math.ceil(X),Z=X-q;for(O=0;O<v;O++){var j=N[V=O+q*x[2]+B*x[1]+z*x[0]],K=N[V=O+U*x[2]+B*x[1]+z*x[0]],Y=N[V=O+q*x[2]+G*x[1]+z*x[0]],J=j+(K-j)*Z,Q=Y+(N[V=O+U*x[2]+G*x[1]+z*x[0]]-Y)*Z;V=O+H*F[2]+P*F[1]+w*F[0],y.values[V]=J+(Q-J)*L}}}}else for(H=0;H<I;++H){var X;if((X=I>1?D*(m-1)+H*W:.5*(D+E)*(m-1))<0||X>m-1)for(O=0;O<v;O++){V=O+H*F[2]+P*F[1]+w*F[0];y.values[V]=p}else{var $=Math.round(X),ee=Math.round(C);for(O=0;O<v;O++){var ae=O+$*x[2]+ee*x[1]+z*x[0],te=O+H*F[2]+P*F[1]+w*F[0];y.values[te]=N[ae]}}}}}return r.makeTensorInfo(y.shape,y.dtype,y.values)}};var Lt={kernelName:a.Cumprod,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.axis,s=n.exclusive,u=n.reverse;p(i,"cumprod");var d=a.backend_util.getAxesPermutation([o],i.shape.length),l=i;null!=d&&(l=We({inputs:{x:i},backend:r,attrs:{perm:d}}));var c=a.backend_util.getInnerMostAxes(1,i.shape.length)[0];if(c!==l.shape.length-1)throw new Error("backend.cumprod in CPU expects an inner-most axis="+(l.shape.length-1)+" but got axis="+c);for(var h=a.upcastType(l.dtype,"int32"),f=a.util.makeOnesTypedArray(a.util.sizeFromShape(l.shape),h),m=r.data.get(l.dataId).values,v=l.shape[l.shape.length-1],k=u?function(e,a){return e+v-a-1}:function(e,a){return e+a},g=0;g<m.length;g+=v)for(var b=0;b<v;b++){var I=k(g,b);if(0===b)f[I]=s?1:m[I];else{var y=k(g,b-1);f[I]=s?m[y]*f[y]:m[I]*f[y]}}var S=r.makeTensorInfo(l.shape,h,f);if(null!=d){var T=We({inputs:{x:S},backend:r,attrs:{perm:a.backend_util.getUndoAxesPermutation(d)}});return r.disposeIntermediateTensorInfo(S),r.disposeIntermediateTensorInfo(l),T}return S}};var qt={kernelName:a.Cumsum,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.axis,s=n.exclusive,u=n.reverse;p(i,"cumsum");var d=a.backend_util.getAxesPermutation([o],i.shape.length),l=i;null!=d&&(l=We({inputs:{x:i},backend:r,attrs:{perm:d}}));var c=a.backend_util.getInnerMostAxes(1,i.shape.length)[0];if(c!==l.shape.length-1)throw new Error("backend.cumsum in CPU expects an inner-most axis="+(l.shape.length-1)+" but got axis="+c);for(var h=a.upcastType(l.dtype,"int32"),f=a.util.makeZerosTypedArray(a.util.sizeFromShape(l.shape),h),m=r.data.get(l.dataId).values,v=l.shape[l.shape.length-1],k=u?function(e,a){return e+v-a-1}:function(e,a){return e+a},g=0;g<m.length;g+=v)for(var b=0;b<v;b++){var I=k(g,b);if(0===b)f[I]=s?0:m[I];else{var y=k(g,b-1);f[I]=s?m[y]+f[y]:m[I]+f[y]}}var S=r.makeTensorInfo(l.shape,h,f);if(null!=d){var T=We({inputs:{x:S},backend:r,attrs:{perm:a.backend_util.getUndoAxesPermutation(d)}});return r.disposeIntermediateTensorInfo(S),r.disposeIntermediateTensorInfo(l),T}return S}};var Ut={kernelName:a.DenseBincount,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs,n=a.x,i=a.weights,o=r.size,s=r.binaryOutput;if(1===n.shape.length){var u=z(t.data.get(n.dataId).values,t.data.get(i.dataId).values,i.dtype,i.shape,o);return t.makeTensorInfo([o],i.dtype,u)}if(2===n.shape.length){var d=R(t.bufferSync(n),t.bufferSync(i),o,s);return t.makeTensorInfo(d.shape,i.dtype,d.values)}throw new Error("Error in denseBincount: input must be at most rank 2, but got rank"+n.shape.length+".")}};var Zt={kernelName:a.DepthToSpace,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.blockSize,s=n.dataFormat;a.util.assert("NHWC"===s,(function(){return"Only NHWC dataFormat supported on CPU for depthToSpace. Got "+s}));for(var u=i.shape[0],d=i.shape[1],l=i.shape[2],p=i.shape[3],c=d*o,h=l*o,f=p/(o*o),m=r.data.get(i.dataId).values,v=new Float32Array(u*c*h*f),k=0,g=0;g<u;++g)for(var b=0;b<c;++b)for(var I=Math.floor(b/o),y=b%o,S=0;S<h;++S)for(var T=Math.floor(S/o),N=(y*o+S%o)*f,x=0;x<f;++x){var F=x+N+p*(T+l*(I+d*g));v[k++]=m[F]}return r.makeTensorInfo([u,c,h,f],i.dtype,v)}};function jt(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=t.filter,s=n.strides,u=n.pad,d=n.dilations,l=n.dimRoundingMode;p([i,o],"depthwiseConv2DNative");var c=a.util.computeStrides(i.shape),h=a.util.computeStrides(o.shape),f=d;null==f&&(f=[1,1]),a.util.assert(a.backend_util.eitherStridesOrDilationsAreOne(s,f),(function(){return"Error in depthwiseConv2d: Either strides or dilations must be 1. Got strides "+s+" and dilations '"+f+"'"}));for(var m=a.backend_util.computeConv2DInfo(i.shape,o.shape,s,f,u,l,!0),v=m.filterHeight,k=m.filterWidth,g=m.dilationHeight,b=m.dilationWidth,I=m.padInfo,y=I.left,S=I.top,T=m.outChannels/m.inChannels,N=new a.TensorBuffer(m.outShape,i.dtype),x=r.data.get(i.dataId).values,F=r.data.get(o.dataId).values,w=N.values,M=0;M<m.batchSize;++M)for(var A=M*c[0],D=M*N.strides[0],_=0;_<m.outHeight;++_)for(var E=D+_*N.strides[1],z=_*m.strideHeight-S,R=0;R<v;++R){var W=z+R*g;if(!(W<0||W>=m.inHeight))for(var P=R*h[0],C=A+W*c[1],H=0;H<m.outWidth;++H)for(var O=E+H*N.strides[2],V=H*m.strideWidth-y,B=0;B<k;++B){var G=V+B*b;if(!(G<0||G>=m.inWidth))for(var L=P+B*h[1],q=C+G*m.inChannels,U=O,Z=L,j=0;j<m.inChannels;++j){for(var K=x[q+j],Y=0;Y<T;++Y)w[U+Y]+=K*F[Z+Y];U+=T,Z+=T}}}return r.makeTensorInfo(N.shape,N.dtype,N.values)}var Kt={kernelName:a.DepthwiseConv2dNative,backendName:"cpu",kernelFunc:jt};var Yt={kernelName:a.DepthwiseConv2dNativeBackpropFilter,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=t.dy,s=n.strides,u=n.dilations,d=n.pad,l=n.dimRoundingMode,c=n.filterShape;p([i,o],"depthwiseConv2dNativeBackpropFilter");for(var h=a.backend_util.computeConv2DInfo(i.shape,c,s,u,d,l,!0),f=h.strideHeight,m=h.strideWidth,v=h.filterHeight,k=h.filterWidth,g=new a.TensorBuffer(h.filterShape,"float32"),b=h.padInfo.left,I=h.padInfo.top,y=h.outChannels/h.inChannels,S=r.data.get(i.dataId).values,T=new a.TensorBuffer(i.shape,i.dtype,S),N=r.data.get(o.dataId).values,x=new a.TensorBuffer(o.shape,o.dtype,N),F=0;F<v;++F)for(var w=Math.max(0,Math.ceil((I-F)/f)),M=Math.min(h.outHeight,(h.inHeight+I-F)/f),A=0;A<k;++A)for(var D=Math.max(0,Math.ceil((b-A)/m)),_=Math.min(h.outWidth,(h.inWidth+b-A)/m),E=0;E<h.outChannels;++E){for(var z=Math.trunc(E/y),R=E%y,W=0,P=0;P<h.batchSize;++P)for(var C=w;C<M;++C)for(var H=F+C*f-I,O=D;O<_;++O){var V=A+O*m-b;W+=T.get(P,H,V,z)*x.get(P,C,O,E)}g.set(W,F,A,z,R)}return r.makeTensorInfo(g.shape,g.dtype,g.values)}};var Jt={kernelName:a.DepthwiseConv2dNativeBackpropInput,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.dy,o=t.filter,s=n.strides,u=n.dilations,l=n.pad,c=n.dimRoundingMode,h=n.inputShape;p([i,o],"depthwiseConv2DNativeBackpropInput");for(var f=a.util.computeStrides(i.shape),m=a.util.computeStrides(o.shape),v=a.backend_util.computeConv2DInfo(h,o.shape,s,u,l,c,!0),k=new a.TensorBuffer(v.inShape,"float32"),g=k.values,b=d(k.strides,3),I=b[0],y=b[1],S=b[2],T=r.data.get(i.dataId).values,N=d(f,3),x=N[0],F=N[1],w=N[2],M=r.data.get(o.dataId).values,A=d(m,3),D=A[0],_=A[1],E=A[2],z=v.batchSize,R=v.filterHeight,W=v.filterWidth,P=v.inChannels,C=v.inHeight,H=v.inWidth,O=v.outChannels,V=v.outHeight,B=v.outWidth,G=v.strideHeight,L=v.strideWidth,q=R-1-v.padInfo.top,U=W-1-v.padInfo.left,Z=O/P,j=0;j<z;++j)for(var K=0;K<P;++K)for(var Y=0;Y<C;++Y)for(var J=Y-q,Q=Math.max(0,Math.ceil(J/G)),X=Math.min(V,(R+J)/G),$=0;$<H;++$){for(var ee=$-U,ae=Math.max(0,Math.ceil(ee/L)),te=Math.min(B,(W+ee)/L),re=0,ne=Q;ne<X;++ne)for(var ie=ne*G-J,oe=ae;oe<te;++oe)for(var se=x*j+F*ne+w*oe,ue=D*(R-1-ie)+_*(W-1-(oe*L-ee))+E*K,de=0;de<Z;++de){re+=T[se+(K*Z+de)]*M[ue+de]}g[I*j+y*Y+S*$+K]=re}return r.makeTensorInfo(k.shape,k.dtype,k.values)}};var Qt={kernelName:a.Diag,backendName:"cpu",kernelFunc:function(e){for(var t=e.inputs,r=e.backend,n=t.x,i=a.util.sizeFromShape(n.shape),o=r.data.get(n.dataId).values,s=a.buffer([i,i],n.dtype),u=s.values,d=0;d<o.length;d++)u[d*i+d]=o[d];var p=l(n.shape,n.shape);return r.makeTensorInfo(p,s.dtype,s.values)}},Xt={kernelName:a.Dilation2D,backendName:"cpu",kernelFunc:function(e){for(var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=t.filter,s=n.strides,u=n.pad,d=n.dilations,l=r,p=l.data.get(i.dataId).values,c=i.shape.length,h=l.data.get(o.dataId).values,f=o.shape.length,m=a.backend_util.computeDilation2DInfo(i.shape,o.shape,s,u,"NHWC",d),v=m.batchSize,k=m.inHeight,g=m.inWidth,b=m.inChannels,I=m.outHeight,y=m.outWidth,S=m.padInfo,T=m.strideHeight,N=m.strideWidth,x=m.filterHeight,F=m.filterWidth,w=m.dilationHeight,M=m.dilationWidth,A=m.outShape,D=a.util.sizeFromShape(A),_=A.length,E=a.util.getArrayFromDType(i.dtype,D),z=0;z<v;++z)for(var R=0;R<I;++R)for(var W=R*T-S.top,P=0;P<y;++P)for(var C=P*N-S.left,H=0;H<b;++H){for(var O=Number.MIN_SAFE_INTEGER,V=0;V<x;++V){var B=W+V*w;if(B>=0&&B<k)for(var G=0;G<F;++G){var L=C+G*M;if(L>=0&&L<g){var q=a.util.locToIndex([z,B,L,H],c,a.util.computeStrides(i.shape)),U=a.util.locToIndex([V,G,H],f,a.util.computeStrides(o.shape)),Z=p[q]+h[U];Z>O&&(O=Z)}}}E[a.util.locToIndex([z,R,P,H],_,a.util.computeStrides(A))]=O}return{dataId:l.write(a.util.toTypedArray(E,i.dtype),A,i.dtype),shape:A,dtype:i.dtype}}},$t={kernelName:a.Dilation2DBackpropFilter,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=t.filter,s=t.dy,u=n.strides,d=n.pad,l=n.dilations,p=r,c=a.util.toNestedArray(i.shape,p.data.get(i.dataId).values),h=a.util.toNestedArray(o.shape,p.data.get(o.dataId).values),f=a.backend_util.computeDilation2DInfo(i.shape,o.shape,u,d,"NHWC",l),m=f.batchSize,v=f.inHeight,k=f.inWidth,g=f.inChannels,b=f.outHeight,I=f.outWidth,y=f.padInfo,S=f.strideHeight,T=f.strideWidth,N=f.filterHeight,x=f.filterWidth,F=f.dilationHeight,w=f.dilationWidth,M=f.outShape;a.util.assert(s.rank===M.length,(function(){return"Error in "+a.Dilation2DBackpropFilter+", dy must have the same rank as output "+M.length+", but got "+s.rank}));for(var A=a.util.toNestedArray(M,p.data.get(s.dataId).values),D=a.util.makeZerosNestedTypedArray(o.shape,o.dtype),_=0;_<m;++_)for(var E=0;E<b;++E)for(var z=E*S-y.top,R=0;R<I;++R)for(var W=R*T-y.left,P=0;P<g;++P){for(var C=Number.MIN_SAFE_INTEGER,H=0,O=0,V=0;V<N;++V){var B=z+V*F;if(B>=0&&B<v)for(var G=0;G<x;++G){var L=W+G*w;if(L>=0&&L<k){var q=c[_][B][L][P]+h[V][G][P];q>C&&(C=q,H=V,O=G)}}}D[H][O][P]+=A[_][E][R][P]}return{dataId:p.write(a.util.toTypedArray(D,i.dtype),o.shape,o.dtype),shape:o.shape,dtype:o.dtype}}},er={kernelName:a.Dilation2DBackpropInput,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=t.filter,s=t.dy,u=n.strides,d=n.pad,l=n.dilations,p=r,c=a.util.toNestedArray(i.shape,p.data.get(i.dataId).values),h=a.util.toNestedArray(o.shape,p.data.get(o.dataId).values),f=a.backend_util.computeDilation2DInfo(i.shape,o.shape,u,d,"NHWC",l),m=f.batchSize,v=f.inHeight,k=f.inWidth,g=f.inChannels,b=f.outHeight,I=f.outWidth,y=f.padInfo,S=f.strideHeight,T=f.strideWidth,N=f.filterHeight,x=f.filterWidth,F=f.dilationHeight,w=f.dilationWidth,M=f.outShape;a.util.assert(s.rank===M.length,(function(){return"Error in "+a.Dilation2DBackpropInput+", dy must have the same rank as output "+M.length+", but got "+s.rank}));for(var A=a.util.toNestedArray(M,p.data.get(s.dataId).values),D=a.util.makeZerosNestedTypedArray(i.shape,i.dtype),_=0;_<m;++_)for(var E=0;E<b;++E)for(var z=E*S-y.top,R=0;R<I;++R)for(var W=R*T-y.left,P=0;P<g;++P){for(var C=Number.MIN_SAFE_INTEGER,H=z<0?0:z,O=W<0?0:W,V=0;V<N;++V){var B=z+V*F;if(B>=0&&B<v)for(var G=0;G<x;++G){var L=W+G*w;if(L>=0&&L<k){var q=c[_][B][L][P]+h[V][G][P];q>C&&(C=q,H=B,O=L)}}}D[_][H][O][P]+=A[_][E][R][P]}return{dataId:p.write(a.util.toTypedArray(D,i.dtype),i.shape,i.dtype),shape:i.shape,dtype:i.dtype}}};function ar(e){var t,r=e.inputs,n=e.backend,i=e.attrs,o=r.x,s=i.axis,u=i.keepDims;p(o,"sum");var l=(t="bool"===o.dtype?x({inputs:{x:o},backend:n,attrs:{dtype:"int32"}}):I({inputs:{x:o},backend:n})).shape.length,c=a.util.parseAxisParam(s,t.shape),h=a.backend_util.getAxesPermutation(c,l),f=c,m=t;null!=h&&(m=We({inputs:{x:t},backend:n,attrs:{perm:h}}),f=a.backend_util.getInnerMostAxes(f.length,l)),a.backend_util.assertAxesAreInnerMostDims("sum",f,m.shape.length);for(var v=d(a.backend_util.computeOutAndReduceShapes(m.shape,f),2),k=v[0],g=v[1],y=b(n,k,a.backend_util.upcastType(m.dtype,"int32")),S=a.util.sizeFromShape(g),T=n.data.get(y.dataId).values,N=n.data.get(m.dataId).values,F=0;F<T.length;++F){for(var w=F*S,M=0,A=0;A<S;++A)M+=N[w+A];T[F]=M}if(u){var D=y;y=Ga({inputs:{x:y},backend:n,attrs:{shape:a.backend_util.expandShapeToKeepDim(y.shape,c)}}),n.disposeIntermediateTensorInfo(D)}return n.disposeIntermediateTensorInfo(t),null!=h&&n.disposeIntermediateTensorInfo(m),y}var tr={kernelName:a.Sum,backendName:"cpu",kernelFunc:ar};var rr={kernelName:a.Einsum,backendName:"cpu",kernelFunc:function(e){var t,r,n,i,o=e.inputs,s=e.backend,d=e.attrs.equation,l=o,p=a.backend_util.decodeEinsumEquation(d,l.length),c=p.allDims,h=p.summedDims,f=p.idDims;a.backend_util.checkEinsumDimSizes(c.length,f,l);for(var m=a.backend_util.getEinsumComputePath(h,f),v=m.path,k=m.steps,g=k.length,b=null,I=c.length,y=[],S=0;S<g;++S){try{for(var T=(t=void 0,u(k[S])),N=T.next();!N.done;N=T.next()){var x=N.value,F=a.backend_util.getEinsumPermutation(I,f[x]),w=F.permutationIndices,M=F.expandDims,A=void 0;a.backend_util.isIdentityPermutation(w)?A=l[x]:(A=We({inputs:{x:l[x]},backend:s,attrs:{perm:w}}),y.push(A));for(var D=A.shape.slice(),_=0;_<M.length;++_)D.splice(M[_],0,1);a.util.arraysEqual(A.shape,D)||(A=Ga({inputs:{x:A},backend:s,attrs:{shape:D}}),y.push(A)),null===b?b=A:(b=we({inputs:{a:A,b:b},backend:s}),y.push(b))}}catch(e){t={error:e}}finally{try{N&&!N.done&&(r=T.return)&&r.call(T)}finally{if(t)throw t.error}}S<g-1&&(v[S]>=0&&(b=ar({inputs:{x:b},backend:s,attrs:{axis:v[S]-(c.length-I),keepDims:!1}}),y.push(b)),I--)}try{for(var E=u(y),z=E.next();!z.done;z=E.next()){var R=z.value;R!==b&&s.disposeIntermediateTensorInfo(R)}}catch(e){n={error:e}}finally{try{z&&!z.done&&(i=E.return)&&i.call(E)}finally{if(n)throw n.error}}return b}};var nr={kernelName:a.EluGrad,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=t.dy,i=t.y;p([n,i],"eluGrad");for(var o=new Float32Array(a.util.sizeFromShape(i.shape)),s=r.data.get(i.dataId).values,u=r.data.get(n.dataId).values,d=0;d<s.length;++d){var l=s[d];o[d]=l>=1?u[d]:u[d]*(l+1)}return r.makeTensorInfo(i.shape,"float32",o)}},ir=a.backend_util.ERF_P,or=a.backend_util.ERF_A1,sr=a.backend_util.ERF_A2,ur=a.backend_util.ERF_A3,dr=a.backend_util.ERF_A4,lr=a.backend_util.ERF_A5,pr=P(a.Erf,(function(e){var a=Math.sign(e),t=Math.abs(e),r=1/(1+ir*t);return a*(1-((((lr*r+dr)*r+ur)*r+sr)*r+or)*r*Math.exp(-t*t))})),cr={kernelName:a.Erf,backendName:"cpu",kernelFunc:pr};function hr(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.input,o=n.dim,s=i.shape.length,u=i.shape.slice(),d=o;return o<0&&(a.util.assert(-(s+1)<=o,(function(){return"Axis must be in the interval ["+-(s+1)+", "+s+"]"})),d=s+o+1),u.splice(d,0,1),Ga({inputs:{x:i},backend:r,attrs:{shape:u}})}var fr={kernelName:a.ExpandDims,backendName:"cpu",kernelFunc:hr},mr=v((function(e,a){return e/a})),vr=w(a.RealDiv,mr),kr={kernelName:a.RealDiv,backendName:"cpu",kernelFunc:vr};function gr(e,t,r){for(var n=e.shape,i=n[0],o=n[1],s=r.data.get(e.dataId),u=s.complexTensorInfos.real,d=s.complexTensorInfos.imag,l=[i,o],p=a.util.sizeFromShape(l),c=a.util.getTypedArrayFromDType("float32",p),h=a.util.getTypedArrayFromDType("float32",p),f=0;f<i;f++){for(var m=ra({inputs:{x:u},backend:r,attrs:{begin:[f,0],size:[1,o]}}),v=ra({inputs:{x:d},backend:r,attrs:{begin:[f,0],size:[1,o]}}),g=k({inputs:{real:m,imag:v},backend:r}),b=br(g,t,r),I=b.real,y=b.imag,S=a.backend_util.mergeRealAndImagArrays(I,y),T=0;T<o;T++){var N=a.backend_util.getComplexWithIndex(S,T);c[f*o+T]=N.real,h[f*o+T]=N.imag}r.disposeIntermediateTensorInfo(m),r.disposeIntermediateTensorInfo(v),r.disposeIntermediateTensorInfo(g)}var x=r.makeTensorInfo(l,"float32",c),F=r.makeTensorInfo(l,"float32",h),w=k({inputs:{real:x,imag:F},backend:r});return r.disposeIntermediateTensorInfo(x),r.disposeIntermediateTensorInfo(F),w}function br(e,t,r){var n=a.util.sizeFromShape(e.shape),i=r.data.get(e.dataId),o=r.data.get(i.complexTensorInfos.real.dataId).values,s=r.data.get(i.complexTensorInfos.imag.dataId).values;if(0==((g=n)&g-1)){var u=Ir(o,s,n,t,r),d=[e.shape[0],e.shape[1]];if(t){var l=r.makeTensorInfo(d,"float32",u.real),p=r.makeTensorInfo(d,"float32",u.imag),c=r.makeTensorInfo([],"float32",a.util.createScalarValue(n,"float32")),h=I({inputs:{x:c},backend:r}),f=kr.kernelFunc({inputs:{a:l,b:c},backend:r}),m=kr.kernelFunc({inputs:{a:p,b:h},backend:r}),v=r.data.get(f.dataId).values,k=r.data.get(m.dataId).values;return r.disposeIntermediateTensorInfo(l),r.disposeIntermediateTensorInfo(p),r.disposeIntermediateTensorInfo(c),r.disposeIntermediateTensorInfo(h),r.disposeIntermediateTensorInfo(f),r.disposeIntermediateTensorInfo(m),{real:v,imag:k}}return u}var g,b=function(e,t,r){for(var n=new Float32Array(2*t),i=0;i<t;i++){for(var o=0,s=0,u=0;u<t;u++){var d=a.backend_util.exponent(i*u,t,r),l=a.backend_util.getComplexWithIndex(e,u);o+=l.real*d.real-l.imag*d.imag,s+=l.real*d.imag+l.imag*d.real}r&&(o/=t,s/=t),a.backend_util.assignToTypedArray(n,o,s,i)}return n}(a.backend_util.mergeRealAndImagArrays(o,s),n,t);return a.backend_util.splitRealAndImagArrays(b)}function Ir(e,t,r,n,i){if(1===r)return{real:e,imag:t};var o=a.backend_util.mergeRealAndImagArrays(e,t),s=r/2,u=a.backend_util.complexWithEvenIndex(o),d=u.real,l=u.imag,p=[d.length],c=i.makeTensorInfo(p,"float32",d),h=i.makeTensorInfo(p,"float32",l),f=k({inputs:{real:c,imag:h},backend:i}),m=a.backend_util.complexWithOddIndex(o),v=m.real,g=m.imag,b=[v.length],I=i.makeTensorInfo(b,"float32",v),y=i.makeTensorInfo(b,"float32",g),T=k({inputs:{real:I,imag:y},backend:i}),N=Ir(d,l,s,n,i),x=N.real,F=N.imag,w=[x.length],M=i.makeTensorInfo(w,"float32",x),A=i.makeTensorInfo(w,"float32",F),D=k({inputs:{real:M,imag:A},backend:i}),E=Ir(v,g,s,n,i),z=E.real,R=E.imag,W=[z.length],P=i.makeTensorInfo(W,"float32",z),C=i.makeTensorInfo(W,"float32",R),H=k({inputs:{real:P,imag:C},backend:i}),O=a.backend_util.exponents(r,n),V=[O.real.length],B=i.makeTensorInfo(V,"float32",O.real),G=i.makeTensorInfo(V,"float32",O.imag),L=k({inputs:{real:B,imag:G},backend:i}),q=we({inputs:{a:L,b:H},backend:i}),U=_({inputs:{a:D,b:q},backend:i}),Z=Sa({inputs:{a:D,b:q},backend:i}),j=S({inputs:{input:U},backend:i}),K=S({inputs:{input:Z},backend:i}),Y=wt({inputs:{input:U},backend:i}),J=wt({inputs:{input:Z},backend:i}),Q=At({inputs:[j,K],backend:i,attrs:{axis:0}}),X=At({inputs:[Y,J],backend:i,attrs:{axis:0}}),$=i.data.get(Q.dataId).values,ee=i.data.get(X.dataId).values;return i.disposeIntermediateTensorInfo(c),i.disposeIntermediateTensorInfo(h),i.disposeIntermediateTensorInfo(f),i.disposeIntermediateTensorInfo(I),i.disposeIntermediateTensorInfo(y),i.disposeIntermediateTensorInfo(T),i.disposeIntermediateTensorInfo(M),i.disposeIntermediateTensorInfo(A),i.disposeIntermediateTensorInfo(D),i.disposeIntermediateTensorInfo(P),i.disposeIntermediateTensorInfo(C),i.disposeIntermediateTensorInfo(H),i.disposeIntermediateTensorInfo(B),i.disposeIntermediateTensorInfo(G),i.disposeIntermediateTensorInfo(L),i.disposeIntermediateTensorInfo(q),i.disposeIntermediateTensorInfo(U),i.disposeIntermediateTensorInfo(Z),i.disposeIntermediateTensorInfo(j),i.disposeIntermediateTensorInfo(Y),i.disposeIntermediateTensorInfo(K),i.disposeIntermediateTensorInfo(J),i.disposeIntermediateTensorInfo(Q),i.disposeIntermediateTensorInfo(X),{real:$,imag:ee}}var yr={kernelName:a.FFT,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=t.input,i=a.util.sizeFromShape(n.shape),o=n.shape[n.shape.length-1],s=Ga({inputs:{x:n},backend:r,attrs:{shape:[i/o,o]}}),u=gr(s,!1,r),d=Ga({inputs:{x:u},backend:r,attrs:{shape:n.shape}});return r.disposeIntermediateTensorInfo(s),r.disposeIntermediateTensorInfo(u),d}};function Sr(e){var t=e.backend,r=e.attrs,n=r.shape,i=r.value,o=r.dtype||a.util.inferDtype(i),s=a.util.getArrayFromDType(o,a.util.sizeFromShape(n));return function(e,a,t){e.fill(a)}(s,i),t.makeTensorInfo(n,o,s)}var Tr={kernelName:a.Fill,backendName:"cpu",kernelFunc:Sr};var Nr={kernelName:a.FlipLeftRight,backendName:"cpu",kernelFunc:function(e){var t=e.inputs;e.attrs;for(var r=e.backend,n=t.image,i=r,o=a.util.getTypedArrayFromDType(n.dtype,a.util.sizeFromShape(n.shape)),s=d(n.shape,4),u=s[0],l=s[1],p=s[2],c=s[3],h=i.data.get(n.dataId).values,f=0;f<u;f++)for(var m=f*p*l*c,v=0;v<l;v++)for(var k=v*(p*c),g=0;g<p;g++)for(var b=g*c,I=0;I<c;I++){var y=Math.round(p-g-1),S=m+k+b+I,T=h[S];if(y>=0&&y<p)T=h[m+k+y*c+I];o[S]=T}return{dataId:i.write(o,n.shape,n.dtype),shape:n.shape,dtype:n.dtype}}},xr=v((function(e,a){return Math.floor(e/a)})),Fr=w(a.FloorDiv,xr,null,"int32"),wr={kernelName:a.FloorDiv,backendName:"cpu",kernelFunc:Fr};var Mr={kernelName:a.FusedConv2D,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs,n=a.x,i=a.filter,o=a.bias,s=a.preluActivationWeights,u=r.strides,d=r.pad,l=r.dataFormat,p=r.dilations,c=r.dimRoundingMode,h=r.activation,f=r.leakyreluAlpha,m=_t({inputs:{x:n,filter:i},backend:t,attrs:{strides:u,pad:d,dataFormat:l,dilations:p,dimRoundingMode:c}});if(o){var v=m;if("NCHW"===l&&1===o.shape.length&&1!==o.shape[0]){var k=Ga({inputs:{x:o},backend:t,attrs:{shape:[o.shape[0],1,1]}});m=_({inputs:{a:m,b:k},backend:t}),t.disposeIntermediateTensorInfo(k)}else m=_({inputs:{a:m,b:o},backend:t});t.disposeIntermediateTensorInfo(v)}if(h){v=m;if("NCHW"===l&&"prelu"===h&&1===s.shape.length&&1!==s.shape[0]){var g=Ga({inputs:{x:s},backend:t,attrs:{shape:[s.shape[0],1,1]}});m=Ba(t,m,h,g,f),t.disposeIntermediateTensorInfo(g)}else m=Ba(t,m,h,s,f);t.disposeIntermediateTensorInfo(v)}return m}};var Ar={kernelName:a.FusedDepthwiseConv2D,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs,n=a.x,i=a.filter,o=a.bias,s=a.preluActivationWeights,u=r.strides,d=r.pad,l=r.dataFormat,p=r.dilations,c=r.dimRoundingMode,h=r.activation,f=r.leakyreluAlpha,m=jt({inputs:{x:n,filter:i},backend:t,attrs:{strides:u,pad:d,dataFormat:l,dilations:p,dimRoundingMode:c}});if(o){var v=m;m=_({inputs:{a:m,b:o},backend:t}),t.disposeIntermediateTensorInfo(v)}if(h){v=m;m=Ba(t,m,h,s,f),t.disposeIntermediateTensorInfo(v)}return m}};var Dr={kernelName:a.GatherNd,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=t.params,i=t.indices,o=a.util.sizeFromShape(n.shape),s=i.shape,u=s[s.length-1],l=d(a.backend_util.prepareAndValidate(n,i),4),p=l[0],c=l[1],h=l[2],f=l[3];if(0===c)return r.makeTensorInfo(p,n.dtype,[]);var m=ee(r.data.get(i.dataId).values,r.bufferSync(n),n.dtype,c,u,h,f,n.shape,o);return r.makeTensorInfo(p,n.dtype,m.values)}};var _r={kernelName:a.GatherV2,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=t.indices,s=n.axis,u=n.batchDims;p([i,o],"gatherV2");for(var d=a.util.parseAxisParam(s,i.shape)[0],l=r.data.get(o.dataId).values,c=i.shape[d],h=function(e){var t=l[e];a.util.assert(t<=c-1&&t>=0,(function(){return"GatherV2: the index value "+t+" is not in [0, "+(c-1)+"]"}))},f=0;f<l.length;++f)h(f);var m=u;null==u&&(m=0);var v=a.util.sizeFromShape(o.shape),k=a.backend_util.segment_util.collectGatherOpShapeInfo(i,o,d,m),g=Ga({inputs:{x:i},backend:r,attrs:{shape:[k.batchSize,k.outerSize,k.dimSize,k.sliceSize]}}),b=Ga({inputs:{x:o},backend:r,attrs:{shape:[k.batchSize,v/k.batchSize]}}),I=[k.batchSize,k.outerSize,v/k.batchSize,k.sliceSize],y=r.bufferSync(b),S=ae(r.bufferSync(g),y,I);return r.disposeIntermediateTensorInfo(g),r.disposeIntermediateTensorInfo(b),r.makeTensorInfo(k.outputShape,S.dtype,S.values)}};var Er={kernelName:a.IFFT,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=t.input,i=a.util.sizeFromShape(n.shape),o=n.shape[n.shape.length-1],s=Ga({inputs:{x:n},backend:r,attrs:{shape:[i/o,o]}}),u=gr(s,!0,r),d=Ga({inputs:{x:u},backend:r,attrs:{shape:n.shape}});return r.disposeIntermediateTensorInfo(s),r.disposeIntermediateTensorInfo(u),d}},zr=P(a.IsFinite,(function(e){return Number.isFinite(e)?1:0}),"bool"),Rr={kernelName:a.IsFinite,backendName:"cpu",kernelFunc:zr},Wr=P(a.IsInf,(function(e){return Math.abs(e)===1/0?1:0}),"bool"),Pr={kernelName:a.IsInf,backendName:"cpu",kernelFunc:Wr},Cr=P(a.IsNan,(function(e){return Number.isNaN(e)?1:0}),"bool"),Hr={kernelName:a.IsNan,backendName:"cpu",kernelFunc:Cr};var Or={kernelName:a.LinSpace,backendName:"cpu",kernelFunc:function(e){var a=e.backend,t=e.attrs,r=fe(t.start,t.stop,t.num);return a.makeTensorInfo([r.length],"float32",r)}},Vr=P(a.Log1p,(function(e){return Math.log1p(e)})),Br={kernelName:a.Log1p,backendName:"cpu",kernelFunc:Vr},Gr=v((function(e,a){return e&&a})),Lr=w(a.LogicalAnd,Gr,null,"bool"),qr={kernelName:a.LogicalAnd,backendName:"cpu",kernelFunc:Lr},Ur=P(a.LogicalNot,(function(e){return e?0:1}),"bool"),Zr={kernelName:a.LogicalNot,backendName:"cpu",kernelFunc:Ur},jr=v((function(e,a){return e||a})),Kr=w(a.LogicalOr,jr,null,"bool"),Yr={kernelName:a.LogicalOr,backendName:"cpu",kernelFunc:Kr};var Jr={kernelName:a.LRN,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.depthRadius,s=n.bias,u=n.alpha,d=n.beta;p(i,"LRN");var l=i.shape[3],c=l-1,h=r.data.get(i.dataId).values,f=a.util.sizeFromShape(i.shape),m=new Float32Array(f);function v(e){for(var a=e%l,t=e-a+Math.max(0,a-o),r=e-a+Math.min(a+o,c),n=0;t<=r;t++){var i=h[t];n+=i*i}return n}for(var k=0;k<f;k++){var g=v(k),b=h[k]*Math.pow(s+u*g,-d);m[k]=b}return r.makeTensorInfo(i.shape,i.dtype,m)}};var Qr={kernelName:a.LRNGrad,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=t.y,s=t.dy,u=n.depthRadius,d=n.bias,l=n.alpha,c=n.beta;p(s,"LRNGrad");for(var h=a.util.sizeFromShape(s.shape),f=s.shape[3],m=r.data.get(s.dataId).values,v=r.data.get(i.dataId).values,k=r.data.get(o.dataId).values,g=new Float32Array(h),b=h,I=0;I<b;I++){for(var y=I%f,S=I-y+Math.max(0,y-u),T=I-y+Math.min(f,y+u+1),N=0,x=S;x<T;x++)N+=Math.pow(v[x],2);N=l*N+d;for(x=S;x<T;x++){var F=-2*l*c*v[x]*k[I]/N;I===x&&(F+=Math.pow(N,-c)),F*=m[I],g[x]+=F}}return r.makeTensorInfo(s.shape,i.dtype,g)}};function Xr(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.reductionIndices,s=n.keepDims,u=r,l=i.shape,c=l.length,h=a.util.parseAxisParam(o,l),f=h,m=a.backend_util.getAxesPermutation(f,c),v=u.data.get(i.dataId).values;if(null!=m){for(var k=new Array(c),g=0;g<k.length;g++)k[g]=l[m[g]];v=Re(v,l,i.dtype,m,k),f=a.backend_util.getInnerMostAxes(f.length,c),l=k}p(i,"max"),a.backend_util.assertAxesAreInnerMostDims("max",f,c);var b=d(a.backend_util.computeOutAndReduceShapes(l,f),2),I=b[0],y=b[1],S=ge(v,a.util.sizeFromShape(y),I,i.dtype),T=u.write(S,I,i.dtype),N=I;s&&(N=k=a.backend_util.expandShapeToKeepDim(I,h));return{dataId:T,shape:N,dtype:i.dtype}}var $r={kernelName:a.Max,backendName:"cpu",kernelFunc:Xr};var en={kernelName:a.MaxPool,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x;p(i,"maxPool");var o=n.filterSize,s=n.strides,u=n.pad,d=n.dimRoundingMode;a.util.assert(a.backend_util.eitherStridesOrDilationsAreOne(s,1),(function(){return"Error in maxPool: Either strides or dilations must be 1. Got strides "+s+" and dilations '1'"}));var l,c=a.backend_util.computePool2DInfo(i.shape,o,s,1,u,d);if(1===c.filterWidth&&1===c.filterHeight&&a.util.arraysEqual(c.inShape,c.outShape))l=I({inputs:{x:i},backend:r});else{var h=r.data.get(i.dataId).values,f=a.util.computeStrides(i.shape),m=ht(h,i.shape,i.dtype,f,c,"max");l=r.makeTensorInfo(c.outShape,i.dtype,m.values)}return l}};var an={kernelName:a.MaxPool3D,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.filterSize,s=n.strides,u=n.pad,d=n.dimRoundingMode,l=n.dataFormat;p(i,"maxPool3d");var c=a.backend_util.computePool3DInfo(i.shape,o,s,1,u,d,l),h=mt(r.data.get(i.dataId).values,i.shape,i.dtype,a.util.computeStrides(i.shape),c,"max");return r.makeTensorInfo(h.shape,"float32",h.values)}};var tn={kernelName:a.MaxPool3DGrad,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.dy,o=t.input,s=n.filterSize,u=n.strides,d=n.pad,l=n.dimRoundingMode;p([i,o],"maxPool3DGrad");for(var c=a.backend_util.computePool3DInfo(o.shape,s,u,1,d,l),h=function(e,t){for(var r=a.buffer(t.outShape,"int32"),n=t.strideDepth,i=t.strideHeight,o=t.strideWidth,s=t.dilationDepth,u=t.dilationHeight,d=t.dilationWidth,l=t.effectiveFilterDepth,p=t.effectiveFilterHeight,c=t.effectiveFilterWidth,h=t.padInfo.front,f=t.padInfo.top,m=t.padInfo.left,v=0;v<t.batchSize;++v)for(var k=0;k<t.inChannels;++k)for(var g=0;g<t.outDepth;++g){for(var b=g*n-h,I=b;I<0;)I+=s;for(var y=Math.min(t.inDepth,l+b),S=0;S<t.outHeight;++S){for(var T=S*i-f,N=T;N<0;)N+=u;for(var x=Math.min(t.inHeight,p+T),F=0;F<t.outWidth;++F){for(var w=F*o-m,M=w;M<0;)M+=d;for(var A=Math.min(t.inWidth,c+w),D=Number.NEGATIVE_INFINITY,_=-1,E=I;E<y;E+=s)for(var z=E-b,R=N;R<x;R+=u)for(var W=R-T,P=M;P<A;P+=d){var C=P-w,H=e.get(v,E,R,P,k);H>=D&&(D=H,_=z*p*c+W*p+C)}r.set(_,v,g,S,F,k)}}}return r}(r.bufferSync(o),c),f=c.strideDepth,m=c.strideHeight,v=c.strideWidth,k=c.dilationDepth,g=c.dilationHeight,b=c.dilationWidth,I=c.effectiveFilterDepth,y=c.effectiveFilterHeight,S=c.effectiveFilterWidth,T=I-1-c.padInfo.front,N=S-1-c.padInfo.left,x=y-1-c.padInfo.top,F=a.buffer(o.shape,"float32"),w=r.bufferSync(i),M=0;M<c.batchSize;++M)for(var A=0;A<c.inChannels;++A)for(var D=0;D<c.inDepth;++D)for(var _=0;_<c.inHeight;++_)for(var E=0;E<c.inWidth;++E){for(var z=D-T,R=_-x,W=E-N,P=0,C=0;C<I;C+=k){var H=(z+C)/f;if(!(H<0||H>=c.outDepth||Math.floor(H)!==H))for(var O=0;O<y;O+=g){var V=(R+O)/m;if(!(V<0||V>=c.outHeight||Math.floor(V)!==V))for(var B=0;B<S;B+=b){var G=(W+B)/v;if(!(G<0||G>=c.outWidth||Math.floor(G)!==G)){var L=I*y*S-1-h.get(M,H,V,G,A)===C*y*S+O*S+B?1:0;if(0!==L)P+=w.get(M,H,V,G,A)*L}}}}F.set(P,M,D,_,E,A)}return r.makeTensorInfo(F.shape,F.dtype,F.values)}};var rn={kernelName:a.MaxPoolGrad,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.dy,o=t.input,s=o;p([o,t.output],"maxPoolGrad");for(var u=n.filterSize,d=n.strides,l=n.pad,c=n.dimRoundingMode,h=a.backend_util.computePool2DInfo(s.shape,u,d,1,l,c),f=r.data.get(s.dataId).values,m=a.buffer(h.outShape,s.dtype,ft(f,s.shape,s.dtype,h).values),v=h.strideHeight,k=h.strideWidth,g=h.dilationHeight,b=h.dilationWidth,I=h.effectiveFilterHeight,y=h.effectiveFilterWidth,S=y-1-h.padInfo.left,T=I-1-h.padInfo.top,N=a.buffer(s.shape,"float32"),x=r.data.get(i.dataId).values,F=a.buffer(i.shape,"float32",x),w=0;w<h.batchSize;++w)for(var M=0;M<h.inChannels;++M)for(var A=0;A<h.inHeight;++A)for(var D=0;D<h.inWidth;++D){for(var _=A-T,E=D-S,z=0,R=0;R<I;R+=g){var W=(_+R)/v;if(!(W<0||W>=h.outHeight||Math.floor(W)!==W))for(var P=0;P<y;P+=b){var C=(E+P)/k;if(!(C<0||C>=h.outWidth||Math.floor(C)!==C)){var H=I*y-1-m.get(w,W,C,M)===R*y+P?1:0;if(0!==H)z+=F.get(w,W,C,M)*H}}}N.set(z,w,A,D,M)}return r.makeTensorInfo(N.shape,N.dtype,N.values)}};var nn={kernelName:a.MaxPoolWithArgmax,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.attrs,n=e.backend,i=t.x,o=r.filterSize,s=r.strides,u=r.pad,l=r.includeBatchInIndex,c=n;p(i,"MaxPoolWithArgmax");var h=c.data.get(i.dataId).values,f=a.backend_util.computePool2DInfo(i.shape,o,s,[1,1],u),m=d(function(e,t,r,n,i){var o=ht(e,0,r,a.util.computeStrides(t),i,"max"),s=ft(e,t,r,i,!0,n);return[o.values,s.values]}(h,i.shape,i.dtype,l,f),2),v=m[0],k=m[1],g=c.write(v,f.outShape,i.dtype),b=c.write(k,f.outShape,i.dtype);return[{dataId:g,shape:f.outShape,dtype:i.dtype},{dataId:b,shape:f.outShape,dtype:"int32"}]}};var on={kernelName:a.Mean,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.axis,s=n.keepDims,u=a.util.parseAxisParam(o,i.shape),d=a.backend_util.computeOutAndReduceShapes(i.shape,u)[1],l=a.util.sizeFromShape(d),p=[],c=r.makeTensorInfo([],"float32",new Float32Array([l]));p.push(c);var h=x({inputs:{x:i},backend:r,attrs:{dtype:"float32"}});p.push(h);var f=vr({inputs:{a:h,b:c},backend:r});p.push(f);var m=ar({inputs:{x:f},backend:r,attrs:{axis:o,keepDims:s}});return p.forEach((function(e){return r.disposeIntermediateTensorInfo(e)})),m}};var sn={kernelName:a.Min,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.axis,s=n.keepDims;p(i,"min");var u=a.util.parseAxisParam(o,i.shape),l=u,c=a.backend_util.getAxesPermutation(l,i.shape.length),h=i;null!=c&&(h=We({inputs:{x:i},backend:r,attrs:{perm:c}}),l=a.backend_util.getInnerMostAxes(l.length,i.shape.length)),a.backend_util.assertAxesAreInnerMostDims("min",l,h.shape.length);for(var f=d(a.backend_util.computeOutAndReduceShapes(h.shape,l),2),m=f[0],v=f[1],k=a.util.sizeFromShape(v),g=a.util.makeZerosTypedArray(a.util.sizeFromShape(m),h.dtype),b=r.data.get(h.dataId).values,I=0;I<g.length;++I){for(var y=I*k,S=b[y],T=0;T<k;++T){var N=b[y+T];(Number.isNaN(N)||N<S)&&(S=N)}g[I]=S}null!=c&&r.disposeIntermediateTensorInfo(h);var x=r.makeTensorInfo(m,h.dtype,g);if(s){var F=Ga({inputs:{x:x},backend:r,attrs:{shape:a.backend_util.expandShapeToKeepDim(m,u)}});return r.disposeIntermediateTensorInfo(x),F}return x}};var un={kernelName:a.MirrorPad,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.paddings,s=n.mode;p(i,"mirrorPad");for(var u=o.map((function(e,a){return e[0]+i.shape[a]+e[1]})),d=o.map((function(e){return e[0]})),l=o.map((function(e,a){return e[0]+i.shape[a]})),c="reflect"===s?0:1,h=r.data.get(i.dataId).values,f=i.shape.length,m=a.util.computeStrides(i.shape),v=a.util.sizeFromShape(u),k=u.length,g=a.util.computeStrides(u),b=a.util.getTypedArrayFromDType(i.dtype,v),I=0;I<v;I++){for(var y=a.util.indexToLoc(I,k,g),S=0;S<k;S++)y[S]<d[S]?y[S]=2*d[S]-y[S]-c:y[S]>=l[S]&&(y[S]=2*(l[S]-1)-y[S]+c);y=y.map((function(e,a){return e-d[a]}));var T=a.util.locToIndex(y,f,m);b[I]=h[T]}return{dataId:r.write(b,u,i.dtype),shape:u,dtype:i.dtype}}},dn=v((function(e,a){var t=e%a;return e<0&&a<0||e>=0&&a>=0?t:(t+a)%a})),ln=w(a.Mod,dn),pn={kernelName:a.Mod,backendName:"cpu",kernelFunc:ln};function cn(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.logits,o=n.dim,s=i.shape.length,u=o;if(-1===u&&(u=s-1),u!==s-1)throw Error("Softmax along a non-last dimension is not yet supported. Logits was rank "+s+" and dim was "+u);var d=a.util.parseAxisParam([u],i.shape),l=Xr({inputs:{x:i},backend:r,attrs:{reductionIndices:d,keepDims:!1}}),p=a.backend_util.expandShapeToKeepDim(l.shape,d),c=Ga({inputs:{x:l},backend:r,attrs:{shape:p}}),h=Sa({inputs:{a:i,b:c},backend:r}),f=Z({inputs:{x:h},backend:r}),m=ar({inputs:{x:f},backend:r,attrs:{axis:d,keepDims:!1}}),v=Ga({inputs:{x:m},backend:r,attrs:{shape:p}}),k=vr({inputs:{a:f,b:v},backend:r});return r.disposeIntermediateTensorInfo(l),r.disposeIntermediateTensorInfo(c),r.disposeIntermediateTensorInfo(h),r.disposeIntermediateTensorInfo(f),r.disposeIntermediateTensorInfo(m),r.disposeIntermediateTensorInfo(v),k}var hn={kernelName:a.Softmax,backendName:"cpu",kernelFunc:cn};var fn={kernelName:a.Multinomial,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,i=e.attrs,o=t.logits,s=i.numSamples,u=i.seed,d=i.normalized;p(o,"multinomial");for(var l=d?o:cn({inputs:{logits:o},backend:r,attrs:{dim:-1}}),c=l.shape[0],h=l.shape[1],f=r.data.get(l.dataId).values,m=[c,s],v=a.util.makeZerosTypedArray(a.util.sizeFromShape(m),"int32"),k=0;k<c;++k){var g=k*h,b=new Float32Array(h-1);b[0]=f[g];for(var I=1;I<b.length;++I)b[I]=b[I-1]+f[g+I];for(var y=n.alea(u.toString()),S=k*s,T=0;T<s;++T){var N=y();v[S+T]=b.length;for(I=0;I<b.length;I++)if(N<b[I]){v[S+T]=I;break}}}return d||r.disposeIntermediateTensorInfo(l),r.makeTensorInfo(m,"int32",v)}},mn=a.kernel_impls.nonMaxSuppressionV3Impl;var vn={kernelName:a.NonMaxSuppressionV3,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs,n=a.boxes,i=a.scores,o=r.maxOutputSize,s=r.iouThreshold,u=r.scoreThreshold;p(n,"NonMaxSuppression");var d=t.data.get(n.dataId).values,l=t.data.get(i.dataId).values,c=mn(d,l,o,s,u).selectedIndices;return t.makeTensorInfo([c.length],"int32",new Int32Array(c))}},kn=a.kernel_impls.nonMaxSuppressionV4Impl;var gn={kernelName:a.NonMaxSuppressionV4,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs,n=a.boxes,i=a.scores,o=r.maxOutputSize,s=r.iouThreshold,u=r.scoreThreshold,d=r.padToMaxOutputSize;p(n,"NonMaxSuppressionPadded");var l=t.data.get(n.dataId).values,c=t.data.get(i.dataId).values,h=kn(l,c,o,s,u,d),f=h.selectedIndices,m=h.validOutputs;return[t.makeTensorInfo([f.length],"int32",new Int32Array(f)),t.makeTensorInfo([],"int32",new Int32Array([m]))]}},bn=a.kernel_impls.nonMaxSuppressionV5Impl;var In={kernelName:a.NonMaxSuppressionV5,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs,n=a.boxes,i=a.scores,o=r.maxOutputSize,s=r.iouThreshold,u=r.scoreThreshold,d=r.softNmsSigma;p(n,"NonMaxSuppressionWithScore");var l=t.data.get(n.dataId).values,c=t.data.get(i.dataId).values,h=bn(l,c,o,s,u,d),f=h.selectedIndices,m=h.selectedScores;return[t.makeTensorInfo([f.length],"int32",new Int32Array(f)),t.makeTensorInfo([m.length],"float32",new Float32Array(m))]}};var yn={kernelName:a.OneHot,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.indices,o=n.dtype,s=n.depth,u=n.onValue,d=n.offValue;p(i,"oneHot");var c=a.util.sizeFromShape(i.shape),h=new Float32Array(c*s);h.fill(d);for(var f=r.data.get(i.dataId).values,m=0;m<c;++m)f[m]>=0&&f[m]<s&&(h[m*s+f[m]]=u);return r.makeTensorInfo(l(i.shape,[s]),o,h)}};function Sn(e){var a=e.inputs,t=e.backend,r=a.x;if("string"===r.dtype)throw new Error("zerosLike is not supported for string tensors");if("complex64"===r.dtype){var n=S({inputs:{input:r},backend:t}),i=Sn({inputs:{x:n},backend:t}),o=wt({inputs:{input:r},backend:t}),s=Sn({inputs:{x:o},backend:t}),u=k({inputs:{real:i,imag:s},backend:t});return t.disposeIntermediateTensorInfo(n),t.disposeIntermediateTensorInfo(i),t.disposeIntermediateTensorInfo(o),t.disposeIntermediateTensorInfo(s),u}return Sr({backend:t,attrs:{shape:r.shape,value:0,dtype:r.dtype}})}var Tn={kernelName:a.ZerosLike,backendName:"cpu",kernelFunc:Sn};var Nn={kernelName:a.OnesLike,backendName:"cpu",kernelFunc:function e(a){var t=a.inputs,r=a.backend,n=t.x;if("string"===n.dtype)throw new Error("onesLike is not supported for string tensors");if("complex64"===n.dtype){var i=S({inputs:{input:n},backend:r}),o=e({inputs:{x:i},backend:r}),s=wt({inputs:{input:n},backend:r}),u=Sn({inputs:{x:s},backend:r}),d=k({inputs:{real:o,imag:u},backend:r});return r.disposeIntermediateTensorInfo(i),r.disposeIntermediateTensorInfo(o),r.disposeIntermediateTensorInfo(s),r.disposeIntermediateTensorInfo(u),d}return Sr({backend:r,attrs:{shape:n.shape,value:1,dtype:n.dtype}})}};function xn(e){var t=e.inputs,r=e.backend,n=e.attrs.axis;if(1===t.length)return hr({inputs:{input:t[0]},backend:r,attrs:{dim:n}});var i=t[0].shape,o=t[0].dtype;t.forEach((function(e){a.util.assertShapesMatch(i,e.shape,"All tensors passed to stack must have matching shapes"),a.util.assert(o===e.dtype,(function(){return"All tensors passed to stack must have matching dtypes"}))}));var s=[],u=At({inputs:t.map((function(e){var a=hr({inputs:{input:e},backend:r,attrs:{dim:n}});return s.push(a),a})),backend:r,attrs:{axis:n}});return s.forEach((function(e){return r.disposeIntermediateTensorInfo(e)})),u}var Fn={kernelName:a.Pack,backendName:"cpu",kernelFunc:xn};var wn={kernelName:a.PadV2,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.paddings,s=n.constantValue;p(i,"pad");var u=o.map((function(e,a){return e[0]+i.shape[a]+e[1]})),d=o.map((function(e){return e[0]})),l=r.data.get(i.dataId).values,c=a.util.sizeFromShape(i.shape),h=i.shape.length,f=a.util.computeStrides(i.shape),m=a.util.sizeFromShape(u),v=u.length,k=a.util.computeStrides(u),g=a.util.getTypedArrayFromDType(i.dtype,m);0!==s&&g.fill(s);for(var b=0;b<c;b++){var I=a.util.indexToLoc(b,h,f).map((function(e,a){return e+d[a]}));g[a.util.locToIndex(I,v,k)]=l[b]}return{dataId:r.write(g,u,i.dtype),shape:u,dtype:i.dtype}}},Mn=v((function(e,a){return Math.pow(e,a)})),An=w(a.Pow,Mn),Dn={kernelName:a.Pow,backendName:"cpu",kernelFunc:An};var _n={kernelName:a.RaggedGather,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs,n=a.paramsNestedSplits,i=a.paramsDenseValues,o=a.indices;r.outputRaggedRank;var s=n.map((function(e){return t.data.get(e.dataId).values})),u=n.map((function(e){return e.shape})),l=t.data.get(i.dataId).values,p=t.data.get(o.dataId).values,c=d(Ge(s,u,l,i.shape,i.dtype,p,o.shape),3),h=c[0],f=c[1],m=c[2],v=h.map((function(e){return t.makeTensorInfo([e.length],"int32",e)})),k=t.makeTensorInfo(m,i.dtype,f);return v.concat([k])}};var En={kernelName:a.RaggedTensorToTensor,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs,n=a.shape,i=a.values,o=a.defaultValue,s=a.rowPartitionTensors,u=r.rowPartitionTypes,l=t.data.get(n.dataId).values,p=t.data.get(i.dataId).values,c=t.data.get(o.dataId).values,h=s.map((function(e){return t.data.get(e.dataId).values})),f=s.map((function(e){return e.shape})),m=d(je(l,n.shape,p,i.shape,i.dtype,c,o.shape,h,f,u),2),v=m[0],k=m[1];return t.makeTensorInfo(v,i.dtype,k)}};var zn={kernelName:a.Range,backendName:"cpu",kernelFunc:function(e){var a=e.backend,t=e.attrs,r=t.start,n=t.stop,i=t.dtype,o=Ke(r,n,t.step,i);return a.makeTensorInfo([o.length],i,o)}},Rn=P(a.Reciprocal,(function(e){return 1/e})),Wn={kernelName:a.Reciprocal,backendName:"cpu",kernelFunc:Rn};var Pn={kernelName:a.ResizeBilinear,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.images,o=n.alignCorners,s=n.halfPixelCenters,u=n.size;p(i,"resizeBilinear");for(var l=a.util.computeStrides(i.shape),c=d(u,2),h=c[0],f=c[1],m=d(i.shape,4),v=m[0],k=m[1],g=m[2],b=m[3],I=r.data.get(i.dataId).values,y=new Float32Array(a.util.sizeFromShape([v,h,f,b])),S=[o&&h>1?k-1:k,o&&f>1?g-1:g],T=[o&&h>1?h-1:h,o&&f>1?f-1:f],N=0,x=S[0]/T[0],F=S[1]/T[1],w=0;w<v;w++)for(var M=0;M<h;M++){var A=void 0;A=s?x*(M+.5)-.5:x*M;for(var D=Math.max(0,Math.floor(A)),_=A-D,E=Math.min(k-1,Math.ceil(A)),z=w*l[0]+D*l[1],R=w*l[0]+E*l[1],W=0;W<f;W++){var P=void 0;P=s?F*(W+.5)-.5:F*W;for(var C=Math.max(0,Math.floor(P)),H=P-C,O=Math.min(g-1,Math.ceil(P)),V=z+C*l[2],B=R+C*l[2],G=z+O*l[2],L=R+O*l[2],q=0;q<b;q++){var U=I[V+q],Z=I[B+q],j=U+(I[G+q]-U)*H,K=j+(Z+(I[L+q]-Z)*H-j)*_;y[N++]=K}}}return r.makeTensorInfo([v,h,f,b],"float32",y)}};var Cn={kernelName:a.ResizeBilinearGrad,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.images,o=t.dy,s=n.alignCorners;p([o,i],"resizeBilinearGrad");for(var u=a.util.computeStrides(i.shape),l=d(i.shape,4),c=l[0],h=l[1],f=l[2],m=l[3],v=d(o.shape,3),k=v[1],g=v[2],b=new Float32Array(c*h*f*m),I=[s&&k>1?h-1:h,s&&g>1?f-1:f],y=[s&&k>1?k-1:k,s&&g>1?g-1:g],S=I[0]/y[0],T=I[1]/y[1],N=r.data.get(o.dataId).values,x=0,F=0;F<c;F++)for(var w=F*u[0],M=0;M<k;M++)for(var A=M*S,D=Math.floor(A),_=Math.min(Math.ceil(A),h-1),E=w+D*u[1],z=w+_*u[1],R=A-D,W=1-R,P=0;P<g;P++)for(var C=P*T,H=Math.floor(C),O=Math.min(Math.ceil(C),f-1),V=C-H,B=1-V,G=E+H*u[2],L=E+O*u[2],q=z+H*u[2],U=z+O*u[2],Z=W*B,j=W*V,K=R*B,Y=R*V,J=0;J<m;J++){var Q=N[x++];b[G+J]+=Q*Z,b[L+J]+=Q*j,b[q+J]+=Q*K,b[U+J]+=Q*Y}return r.makeTensorInfo([c,f,h,m],"float32",b)}};var Hn={kernelName:a.ResizeNearestNeighbor,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.images,o=n.alignCorners,s=n.halfPixelCenters,u=n.size;p(i,"resizeNearestNeighbor");for(var l=a.util.computeStrides(i.shape),c=d(u,2),h=c[0],f=c[1],m=d(i.shape,4),v=m[0],k=m[1],g=m[2],b=m[3],I=r.data.get(i.dataId).values,y=new Float32Array(v*h*f*b),S=[o&&h>1?k-1:k,o&&f>1?g-1:g],T=[o&&h>1?h-1:h,o&&f>1?f-1:f],N=S[0]/T[0],x=S[1]/T[1],F=0,w=0;w<v;w++)for(var M=w*l[0],A=0;A<h;A++){var D=s?N*(A+.5):N*A,_=Math.min(k-1,o?Math.round(D):Math.floor(D));s&&(_=Math.max(0,_));for(var E=M+_*l[1],z=0;z<f;z++){var R=s?x*(z+.5):x*z,W=Math.min(g-1,o?Math.round(R):Math.floor(R));s&&(W=Math.max(0,W));for(var P=E+W*l[2],C=0;C<b;C++){var H=I[P+C];y[F++]=H}}}return r.makeTensorInfo([v,h,f,b],i.dtype,y)}};var On={kernelName:a.ResizeNearestNeighborGrad,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.images,o=t.dy,s=n.alignCorners;p([o,i],"resizeNearestNeighborGrad");for(var u=a.util.computeStrides(i.shape),l=a.util.computeStrides(o.shape),c=d(i.shape,4),h=c[0],f=c[1],m=c[2],v=c[3],k=d(o.shape,3),g=k[1],b=k[2],I=new Float32Array(h*f*m*v),y=r.data.get(o.dataId).values,S=[s&&g>1?f-1:f,s&&b>1?m-1:m],T=[s&&g>1?g-1:g,s&&b>1?b-1:b],N=S[0]/T[0],x=S[1]/T[1],F=1/N,w=1/x,M=2*Math.ceil(F)+2,A=2*Math.ceil(w)+2,D=0;D<h;D++)for(var _=D*u[0],E=0;E<f;E++)for(var z=_+E*u[1],R=Math.floor(E*F),W=Math.floor(R-M/2),P=0;P<m;P++)for(var C=z+P*u[2],H=Math.floor(P*w),O=Math.floor(H-A/2),V=0;V<v;V++){for(var B=0,G=0;G<M;G++){var L=G+W;if(!(L<0||L>=g)){var q=_+L*l[1],U=L*N;if(E===Math.min(f-1,s?Math.round(U):Math.floor(U)))for(var Z=0;Z<A;Z++){var j=Z+O;if(!(j<0||j>=b)){var K=q+j*l[2],Y=j*x;P===Math.min(m-1,s?Math.round(Y):Math.floor(Y))&&(B+=y[K+V])}}}}I[C+V]=B}return r.makeTensorInfo(i.shape,i.dtype,I)}};var Vn={kernelName:a.Reverse,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.dims;p(i,"reverse");var s=i.shape.length,u=a.util.parseAxisParam(o,i.shape);if(0===s)return I({inputs:{x:i},backend:r});for(var d=new a.TensorBuffer(i.shape,i.dtype),c=r.bufferSync(i),h=function(e){var a=d.indexToLoc(e),t=a.slice();u.forEach((function(e){return t[e]=i.shape[e]-1-t[e]})),d.set.apply(d,l([c.get.apply(c,l(t))],a))},f=0;f<d.size;f++)h(f);return r.makeTensorInfo(d.shape,d.dtype,d.values)}},Bn={kernelName:a.RotateWithOffset,backendName:"cpu",kernelFunc:function(e){for(var t=e.inputs,r=e.attrs,n=e.backend,i=t.image,o=r.radians,s=r.fillValue,u=r.center,l=n,p=a.util.getTypedArrayFromDType(i.dtype,a.util.sizeFromShape(i.shape)),c=d(i.shape,4),h=c[0],f=c[1],m=c[2],v=c[3],k=d(a.backend_util.getImageCenter(u,f,m),2),g=k[0],b=k[1],I=Math.sin(o),y=Math.cos(o),S=l.data.get(i.dataId).values,T=0;T<h;T++)for(var N=T*m*f*v,x=0;x<f;x++)for(var F=x*(m*v),w=0;w<m;w++)for(var M=w*v,A=0;A<v;A++){var D=[h,x,w,A],_=D[2],E=D[1],z=(_-g)*y-(E-b)*I,R=(_-g)*I+(E-b)*y;z=Math.round(z+g),R=Math.round(R+b);var W=s;if("number"!=typeof s&&(W=3===A?255:s[A]),z>=0&&z<m&&R>=0&&R<f)W=S[N+R*(m*v)+z*v+A];p[N+F+M+A]=W}return{dataId:l.write(p,i.shape,i.dtype),shape:i.shape,dtype:i.dtype}}},Gn=P(a.Round,(function(e){var a=Math.floor(e);return e-a<.5?Math.floor(e):e-a>.5?Math.ceil(e):a%2==0?a:a+1})),Ln={kernelName:a.Round,backendName:"cpu",kernelFunc:Gn};var qn={kernelName:a.ScatterNd,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.indices,o=t.updates,s=n.shape,u=a.backend_util.calculateShapes(o,i,s),d=u.sliceRank,l=u.numUpdates,p=u.sliceSize,c=u.strides,h=u.outputSize,f=Xe(r.bufferSync(i),r.bufferSync(o),s,h,p,l,d,c,0,!0);return r.makeTensorInfo(s,f.dtype,f.values)}};function Un(e,a){for(var t=0,r=e.length,n=0;t<r;)e[n=Math.floor((t+r)/2)]<a?t=n+1:r=n;return r}function Zn(e,a){for(var t=0,r=e.length,n=0;t<r;)e[n=Math.floor((t+r)/2)]<=a?t=n+1:r=n;return r}var jn={kernelName:a.SearchSorted,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.sortedSequence,o=t.values,s=n.side,u=function(e,t,r,n,i,o){for(var s=a.util.getArrayFromDType("int32",r*i),u=0;u<r;++u)for(var d=e.slice(u*n,(u+1)*n),l=u*i,p=0;p<i;++p)s[l+p]="left"===o?Un(d,t[p+l]):Zn(d,t[p+l]);return s}(r.data.get(i.dataId).values,r.data.get(o.dataId).values,i.shape[0],i.shape[1],o.shape[1],s);return r.makeTensorInfo(o.shape,"int32",u)}};var Kn={kernelName:a.Select,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=t.condition,i=t.t,o=t.e;p([n,i,o],"select");for(var s=n.shape.length,u=r.data.get(n.dataId).values,d=r.data.get(i.dataId).values,l=r.data.get(o.dataId).values,c=a.upcastType(i.dtype,o.dtype),h=a.util.makeZerosTypedArray(a.util.sizeFromShape(i.shape),c),f=0,m=0===s||s>1||1===i.shape.length?1:a.util.sizeFromShape(i.shape.slice(1)),v=0;v<u.length;v++)for(var k=0;k<m;k++)1===u[v]?h[f++]=d[v]:h[f++]=l[v];return r.makeTensorInfo(i.shape,c,h)}},Yn=a.backend_util.SELU_SCALEALPHA,Jn=a.backend_util.SELU_SCALE,Qn=P(a.Selu,(function(e){return e>=0?Jn*e:Yn*(Math.exp(e)-1)})),Xn={kernelName:a.Selu,backendName:"cpu",kernelFunc:Qn},$n=P(a.Sign,(function(e){return e<0?-1:e>0?1:0})),ei={kernelName:a.Sign,backendName:"cpu",kernelFunc:$n},ai=P(a.Sin,(function(e){return Math.sin(e)})),ti={kernelName:a.Sin,backendName:"cpu",kernelFunc:ai},ri=P(a.Sinh,(function(e){return Math.sinh(e)})),ni={kernelName:a.Sinh,backendName:"cpu",kernelFunc:ri},ii=Math.log(1.1920928955078125e-7)+2,oi=P(a.Softplus,(function(e){var a=e>-ii,t=e<ii,r=Math.exp(e);return t?r:a?e:Math.log(1+r)})),si={kernelName:a.Softplus,backendName:"cpu",kernelFunc:oi};var ui={kernelName:a.SpaceToBatchND,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.blockShape,s=n.paddings;p([i],"spaceToBatchND");var u=a.util.sizeFromShape(o),d=[[0,0]];d.push.apply(d,l(s));for(var c=1+o.length;c<i.shape.length;++c)d.push([0,0]);var h=wn.kernelFunc({inputs:{x:i},backend:r,attrs:{paddings:d,constantValue:0}}),f=a.backend_util.getReshaped(h.shape,o,u,!1),m=a.backend_util.getPermuted(f.length,o.length,!1),v=a.backend_util.getReshapedPermuted(h.shape,o,u,!1),k=Ga({inputs:{x:h},backend:r,attrs:{shape:f}}),g=We({inputs:{x:k},backend:r,attrs:{perm:m}}),b=Ga({inputs:{x:g},backend:r,attrs:{shape:v}});return r.disposeIntermediateTensorInfo(h),r.disposeIntermediateTensorInfo(k),r.disposeIntermediateTensorInfo(g),b}};var di={kernelName:a.SparseFillEmptyRows,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=a.indices,n=a.values,i=a.denseShape,o=a.defaultValue;if(1!==i.shape.length)throw new Error("Dense shape must be a vector, saw:\n        "+i.shape);if(2!==r.shape.length)throw new Error("Indices must be a matrix, saw:\n        "+r.shape);if(1!==n.shape.length)throw new Error("Values must be a vector, saw:\n        "+n.shape);if(0!==o.shape.length)throw new Error("Default value must be a scalar, saw:\n        "+o.shape);var s=t.data.get(r.dataId).values,u=t.data.get(n.dataId).values,l=t.data.get(i.dataId).values,p=t.data.get(o.dataId).values[0],c=d(ia(s,r.shape,r.dtype,u,n.dtype,l,p),5),h=c[0],f=c[1],m=c[2],v=c[3],k=c[4];return[t.makeTensorInfo(f,r.dtype,h),t.makeTensorInfo([f[0]],n.dtype,m),t.makeTensorInfo([v.length],"bool",new Uint8Array(v.map((function(e){return Number(e)})))),t.makeTensorInfo([k.length],r.dtype,new Int32Array(k))]}};var li={kernelName:a.SparseReshape,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=a.inputIndices,n=a.inputShape,i=a.newShape;if(2!==r.shape.length)throw new Error("Input indices should be a matrix but received shape\n        "+r.shape);if(1!==n.shape.length)throw new Error("Input shape should be a vector but received shape\n        "+n.shape);if(1!==i.shape.length)throw new Error("Target shape should be a vector but received shape "+i.shape);var o=Array.from(t.data.get(n.dataId).values),s=t.data.get(r.dataId).values,u=Array.from(t.data.get(i.dataId).values),l=d(oa(s,r.shape,r.dtype,o,u),3),p=l[0],c=l[1],h=l[2];return[t.makeTensorInfo(c,r.dtype,p),t.makeTensorInfo([h.length],i.dtype,new Int32Array(h))]}};var pi={kernelName:a.SparseSegmentMean,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=a.data,n=a.indices,i=a.segmentIds;if(r.shape.length<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==n.shape.length)throw new Error("Indices should be a vector but received shape\n          "+n.shape);if(1!==i.shape.length)throw new Error("Segment ids should be a vector but received shape\n          "+i.shape);if(n.shape[0]!==i.shape[0])throw new Error("segmentIds and indices should have same size.");var o=t.data.get(r.dataId).values,s=t.data.get(n.dataId).values,u=t.data.get(i.dataId).values,l=d(sa(o,r.shape,r.dtype,s,u,!0),2),p=l[0],c=l[1];return t.makeTensorInfo(c,r.dtype,p)}};var ci={kernelName:a.SparseSegmentSum,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=a.data,n=a.indices,i=a.segmentIds;if(r.shape.length<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==n.shape.length)throw new Error("Indices should be a vector but received shape\n         "+n.shape);if(1!==i.shape.length)throw new Error("Segment ids should be a vector but received shape\n         "+i.shape);if(n.shape[0]!==i.shape[0])throw new Error("segmentIds and indices should have same size.");var o=t.data.get(r.dataId).values,s=t.data.get(n.dataId).values,u=t.data.get(i.dataId).values,l=d(sa(o,r.shape,r.dtype,s,u),2),p=l[0],c=l[1];return t.makeTensorInfo(c,r.dtype,p)}};var hi={kernelName:a.SparseToDense,backendName:"cpu",kernelFunc:function(e){var t,r=e.inputs,n=e.backend,i=e.attrs,o=r.sparseIndices,s=r.sparseValues,u=r.defaultValue,d=i.outputShape,l=a.backend_util.calculateShapes(s,o,d),p=l.sliceRank,c=l.numUpdates,h=l.sliceSize,f=l.strides,m=l.outputSize,v=!1,k=n.bufferSync(o);switch(s.dtype){case"bool":t=Xe(k,n.bufferSync(s),d,m,h,c,p,f,Boolean(n.data.get(u.dataId).values[0]),v);break;case"float32":case"int32":t=Xe(k,n.bufferSync(s),d,m,h,c,p,f,n.data.get(u.dataId).values[0],v);break;case"string":t=Xe(k,n.bufferSync(s),d,m,h,c,p,f,a.util.decodeString(n.data.get(u.dataId).values[0]),v);break;default:throw new Error("Unsupported type "+s.dtype)}return n.makeTensorInfo(d,t.dtype,t.values)}};var fi={kernelName:a.SplitV,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.numOrSizeSplits,s=n.axis,u=a.util.parseAxisParam(s,i.shape)[0],d=a.backend_util.prepareSplitSize(i,o,u),p=new Array(i.shape.length).fill(0),c=i.shape.slice();return d.map((function(e){var a=l(c);a[u]=e;var t=ra({inputs:{x:i},backend:r,attrs:{begin:p,size:a}});return p[u]+=e,t}))}},mi={kernelName:a.Square,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=a.x,n=t;p(r,"square");for(var i=n.data.get(r.dataId).values,o=new Float32Array(i.length),s=0;s<i.length;++s){var u=i[s];o[s]=u*u}return{dataId:n.write(o,r.shape,r.dtype),shape:r.shape,dtype:r.dtype}}},vi=P(a.Step,(function(e,a){var t=a;return isNaN(e)?NaN:e>0?1:t.alpha})),ki={kernelName:a.Step,backendName:"cpu",kernelFunc:vi};var gi={kernelName:a.StridedSlice,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=n.begin,s=n.end,u=n.strides,d=n.beginMask,l=n.endMask,c=n.ellipsisMask,h=n.newAxisMask,f=n.shrinkAxisMask;p(i,"stridedSlice");var m,v=a.slice_util.sliceInfo(i.shape,o,s,u,d,l,c,h,f),k=v.finalShapeSparse,g=v.finalShape,b=v.isIdentity,I=v.sliceDim0,y=v.isSimpleSlice,S=v.begin,T=v.end,N=v.strides;if(b)m=Ga({inputs:{x:i},backend:r,attrs:{shape:g}});else if(I||y){a.util.assert(i.shape.length>=1,(function(){return"Input must have rank at least 1, got: "+i.shape.length}));var x=a.slice_util.computeOutShape(S,T,N),F=ra({inputs:{x:i},backend:r,attrs:{begin:S,size:x}});m=Ga({inputs:{x:F},backend:r,attrs:{shape:g}}),r.disposeIntermediateTensorInfo(F)}else{var w=fa(k,r.bufferSync(i),N,S);m=r.makeTensorInfo(g,w.dtype,w.values)}return m}};var bi={kernelName:a.StringNGrams,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs,n=r.separator,i=r.nGramWidths,o=r.leftPad,s=r.rightPad,u=r.padWidth,l=r.preserveShortSequences,p=a.data,c=a.dataSplits,h=d(va(t.data.get(p.dataId).values,t.data.get(c.dataId).values,n,i,o,s,u,l),2),f=h[0],m=h[1];return[t.makeTensorInfo([f.length],"string",f),t.makeTensorInfo(c.shape,"int32",m)]}};var Ii={kernelName:a.StringSplit,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs.skipEmpty,n=a.input,i=a.delimiter;if("string"!==n.dtype)throw new Error("Input must be of datatype string");if(1!==n.shape.length)throw new Error("Input must be a vector, got shape: "+n.shape);if(0!==i.shape.length)throw new Error("Delimiter must be a scalar, got shape: "+i.shape);var o=d(ga(t.data.get(n.dataId).values,t.data.get(i.dataId).values[0],r),3),s=o[0],u=o[1],l=o[2],p=u.length;return[t.makeTensorInfo([p,2],"int32",s),t.makeTensorInfo([p],"string",u),t.makeTensorInfo([2],"int32",new Int32Array(l))]}};var yi={kernelName:a.StringToHashBucketFast,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs.numBuckets,n=a.input;if("string"!==n.dtype)throw new Error("Input must be of datatype string");if(r<=0)throw new Error("Number of buckets must be at least 1");var i=ba(t.data.get(n.dataId).values,r);return t.makeTensorInfo(n.shape,"int32",i)}},Si=P(a.Tan,(function(e){return Math.tan(e)})),Ti={kernelName:a.Tan,backendName:"cpu",kernelFunc:Si},Ni=P(a.Tanh,(function(e){return Math.tanh(e)})),xi={kernelName:a.Tanh,backendName:"cpu",kernelFunc:Ni};var Fi={kernelName:a.Tile,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs,n=a.x,i=r.reps;p(n,"tile");var o=Na(t.bufferSync(n),i);return t.makeTensorInfo(o.shape,o.dtype,o.values)}};var wi={kernelName:a.TopK,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs,n=a.x,i=r.k,o=r.sorted;p(n,"topk");var s=d(wa(t.data.get(n.dataId).values,n.shape,n.dtype,i,o),2),u=s[0],l=s[1];return[t.makeTensorInfo(u.shape,u.dtype,u.values),t.makeTensorInfo(l.shape,l.dtype,l.values)]}};var Mi={kernelName:a.Transform,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.attrs,n=e.backend,i=t.image,o=t.transforms,s=r.interpolation,u=r.fillMode,l=r.fillValue,p=r.outputShape,c=d(i.shape,4),h=c[0],f=c[1],m=c[2],v=c[3],k=d(null!=p?p:[f,m],2),g=k[0],b=k[1],I=[h,g,b,v],y=a.util.computeStrides(i.shape),S=y[0],T=y[1],N=y[2],x=a.util.computeStrides(I),F=x[0],w=x[1],M=x[2],A=a.util.getTypedArrayFromDType(i.dtype,a.util.sizeFromShape(I));A.fill(l);for(var D=n.data.get(i.dataId).values,_=n.data.get(o.dataId).values,E=0;E<h;++E){for(var z=1===o.shape[0]?_:_.subarray(8*E,8*E+8),R=0;R<g;++R)for(var W=0;W<b;++W)for(var P=0;P<v;++P){var C=void 0,H=z[6]*W+z[7]*R+1;if(0!==H){var O=(z[0]*W+z[1]*R+z[2])/H,V=(z[3]*W+z[4]*R+z[5])/H,B=Ai(O,m,u),G=Ai(V,f,u);switch(s){case"nearest":C=_i(D,f,m,S,T,N,E,G,B,P,l);break;case"bilinear":C=Ei(D,f,m,S,T,N,E,G,B,P,l);break;default:throw new Error("Error in Transform: Expect 'nearest' or 'bilinear', but got "+s)}A[E*F+R*w+W*M+P]=C}}return n.makeTensorInfo(I,i.dtype,A)}return{dataId:n.write(A,I,i.dtype),shape:i.shape,dtype:i.dtype}}};function Ai(e,t,r){switch(r){case"reflect":return function(e,t){var r=e;if(r<0){if(t<=1)r=0;else r<(n=2*t)&&(r=n*Math.trunc(-r/n)+r),r=r<-t?r+n:-r-1}else if(r>t-1){var n;if(t<=1)r=0;else(r-=(n=2*t)*Math.trunc(r/n))>=t&&(r=n-r-1)}return a.util.clamp(0,r,t-1)}(e,t);case"wrap":return function(e,t){var r=e;if(r<0)if(t<=1)r=0;else{var n=t-1;r+=t*(Math.trunc(-r/n)+1)}else if(r>t-1)if(t<=1)r=0;else{n=t-1;r-=t*Math.trunc(r/n)}return a.util.clamp(0,r,t-1)}(e,t);case"nearest":return function(e,t){return a.util.clamp(0,e,t-1)}(e,t);default:return function(e,a){return e}(e)}}function Di(e,a,t,r,n,i,o,s,u,d,l){return 0<=s&&s<a&&0<=u&&u<t?e[o*r+s*n+u*i+d]:l}function _i(e,a,t,r,n,i,o,s,u,d,l){return Di(e,a,t,r,n,i,o,Math.round(s),Math.round(u),d,l)}function Ei(e,a,t,r,n,i,o,s,u,d,l){var p=Math.floor(s),c=Math.floor(u),h=p+1,f=c+1;return(h-s)*((f-u)*Di(e,a,t,r,n,i,o,p,c,d,l)+(u-c)*Di(e,a,t,r,n,i,o,p,f,d,l))+(s-p)*((f-u)*Di(e,a,t,r,n,i,o,h,c,d,l)+(u-c)*Di(e,a,t,r,n,i,o,h,f,d,l))}var zi={kernelName:a.Unique,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.attrs,r=e.backend,n=t.axis,i=a.x;p(i,"unique");var o=Ma(r.data.get(i.dataId).values,n,i.shape,i.dtype),s=o.outputValues,u=o.outputShape,d=o.indices;return[r.makeTensorInfo(u,i.dtype,s),r.makeTensorInfo([d.length],"int32",d)]}};var Ri={kernelName:a.Unpack,backendName:"cpu",kernelFunc:function(e){var a=e.inputs,t=e.backend,r=e.attrs,n=a.value,i=r.axis;i<0&&(i+=n.shape.length);for(var o=n.shape.length,s=n.shape[i],u=new Array(o-1),d=0,l=0;l<o;l++)l!==i&&(u[d++]=n.shape[l]);var p=new Array(o).fill(0),c=n.shape.slice();c[i]=1;var h=new Array(s);for(l=0;l<h.length;l++){p[i]=l;var f=ra({inputs:{x:n},backend:t,attrs:{begin:p,size:c}});h[l]=Ga({inputs:{x:f},backend:t,attrs:{shape:u}}),t.disposeIntermediateTensorInfo(f)}return h}};var Wi,Pi,Ci={kernelName:a.UnsortedSegmentSum,backendName:"cpu",kernelFunc:function(e){var t=e.inputs,r=e.backend,n=e.attrs,i=t.x,o=t.segmentIds,s=n.numSegments;p(i,"unsortedSegmentSum");for(var u=[],d=[],l=i.shape.length-o.shape.length,c=o,h=0;h<l;++h){var f=hr({inputs:{input:c},backend:r,attrs:{dim:h+1}});c=f,d.push(f)}for(h=0;h<s;++h){var m=a.util.createScalarValue(h,"int32"),v=r.makeTensorInfo([],"int32",m),k=L({inputs:{a:v,b:c},backend:r}),g=x({inputs:{x:k},backend:r,attrs:{dtype:"float32"}}),b=we({inputs:{a:g,b:i},backend:r}),I=ar({inputs:{x:b},backend:r,attrs:{axis:0,keepDims:!1}});u.push(I),d.push(v),d.push(k),d.push(g),d.push(b),d.push(I)}var y=xn({inputs:u,backend:r,attrs:{axis:0}});return d.forEach((function(e){return r.disposeIntermediateTensorInfo(e)})),y}},Hi=[Za,m,Ka,Ja,E,Qa,Xa,$a,et,at,rt,it,st,lt,ct,vt,kt,gt,bt,Ua,It,yt,St,Tt,F,V,xt,g,Ft,Dt,Et,zt,Rt,Wt,Pt,Ct,Ot,Bt,Gt,Lt,qt,Ut,Zt,Kt,Yt,Jt,Qt,Xt,$t,er,rr,_a,nr,q,cr,j,fr,J,yr,Tr,Nr,$,wr,Mr,Ar,Dr,_r,ne,se,y,Er,Mt,Rr,Pr,Hr,za,le,he,Or,ke,Br,qr,Zr,Yr,Jr,Qr,$r,ye,en,an,tn,rn,nn,on,sn,Ne,un,pn,fn,Me,De,vn,gn,In,ze,yn,Nn,Fn,wn,Dn,Pa,He,_n,En,zn,T,kr,Wn,Ha,Va,La,Pn,Cn,Hn,On,Vn,Bn,Ln,Qe,qn,jn,Kn,Xn,aa,ei,ti,ni,na,hn,si,ui,di,li,pi,ci,hi,fi,la,mi,ha,ki,gi,bi,Ii,yi,Ta,tr,Ti,xi,Fi,wi,Mi,Pe,zi,Ri,Ci,Tn];try{for(var Oi=u(Hi),Vi=Oi.next();!Vi.done;Vi=Oi.next()){var Bi=Vi.value;a.registerKernel(Bi)}}catch(e){Wi={error:e}}finally{try{Vi&&!Vi.done&&(Pi=Oi.return)&&Pi.call(Oi)}finally{if(Wi)throw Wi.error}}e.MathBackendCPU=h,e.shared=Aa,e.version_cpu="3.21.0",Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=tf-backend-cpu.min.js.map

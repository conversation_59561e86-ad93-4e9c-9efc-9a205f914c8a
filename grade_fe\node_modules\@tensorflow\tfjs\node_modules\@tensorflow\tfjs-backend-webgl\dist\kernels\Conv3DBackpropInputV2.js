/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, Conv3DBackpropInputV2 } from '@tensorflow/tfjs-core';
import { Conv3DDerInputProgram } from '../conv_backprop_gpu';
export function conv3DBackpropInput(args) {
    const { inputs, backend, attrs } = args;
    const { dy, filter } = inputs;
    const { pad, strides, inputShape } = attrs;
    const convInfo = backend_util.computeConv3DInfo(inputShape, filter.shape, strides, 1 /* dilations */, pad);
    const program = new Conv3DDerInputProgram(convInfo);
    return backend.runWebGLProgram(program, [dy, filter], 'float32');
}
export const conv3DBackpropInputConfig = {
    kernelName: Conv3DBackpropInputV2,
    backendName: 'webgl',
    kernelFunc: conv3DBackpropInput,
};
//# sourceMappingURL=data:application/json;base64,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
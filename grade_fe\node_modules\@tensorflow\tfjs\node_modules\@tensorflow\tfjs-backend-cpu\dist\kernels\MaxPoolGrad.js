/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, buffer, MaxPoolGrad } from '@tensorflow/tfjs-core';
import { assertNotComplex } from '../cpu_util';
import { maxPoolPositions } from '../utils/pool_utils';
export function maxPoolGrad(args) {
    const { inputs, backend, attrs } = args;
    const { dy, input, output } = inputs;
    const x = input;
    assertNotComplex([input, output], 'maxPoolGrad');
    const { filterSize, strides, pad, dimRoundingMode } = attrs;
    const convInfo = backend_util.computePool2DInfo(x.shape, filterSize, strides, 1 /* dilations */, pad, dimRoundingMode);
    const xValues = backend.data.get(x.dataId).values;
    const maxPosBuf = buffer(convInfo.outShape, x.dtype, maxPoolPositions(xValues, x.shape, x.dtype, convInfo).values);
    const strideHeight = convInfo.strideHeight;
    const strideWidth = convInfo.strideWidth;
    const dilationHeight = convInfo.dilationHeight;
    const dilationWidth = convInfo.dilationWidth;
    const effectiveFilterHeight = convInfo.effectiveFilterHeight;
    const effectiveFilterWidth = convInfo.effectiveFilterWidth;
    const padLeft = effectiveFilterWidth - 1 - convInfo.padInfo.left;
    const padTop = effectiveFilterHeight - 1 - convInfo.padInfo.top;
    const dx = buffer(x.shape, 'float32');
    const dyData = backend.data.get(dy.dataId).values;
    const dyBuf = buffer(dy.shape, 'float32', dyData);
    for (let b = 0; b < convInfo.batchSize; ++b) {
        for (let d = 0; d < convInfo.inChannels; ++d) {
            for (let dxR = 0; dxR < convInfo.inHeight; ++dxR) {
                for (let dxC = 0; dxC < convInfo.inWidth; ++dxC) {
                    // Shader code begins.
                    const dyRCorner = dxR - padTop;
                    const dyCCorner = dxC - padLeft;
                    let dotProd = 0;
                    for (let wR = 0; wR < effectiveFilterHeight; wR += dilationHeight) {
                        const dyR = (dyRCorner + wR) / strideHeight;
                        if (dyR < 0 || dyR >= convInfo.outHeight ||
                            Math.floor(dyR) !== dyR) {
                            continue;
                        }
                        for (let wC = 0; wC < effectiveFilterWidth; wC += dilationWidth) {
                            const dyC = (dyCCorner + wC) / strideWidth;
                            if (dyC < 0 || dyC >= convInfo.outWidth ||
                                Math.floor(dyC) !== dyC) {
                                continue;
                            }
                            const maxPos = effectiveFilterHeight * effectiveFilterWidth - 1 -
                                maxPosBuf.get(b, dyR, dyC, d);
                            const curPos = wR * effectiveFilterWidth + wC;
                            const mask = maxPos === curPos ? 1 : 0;
                            if (mask === 0) {
                                continue;
                            }
                            const pixel = dyBuf.get(b, dyR, dyC, d);
                            dotProd += pixel * mask;
                        }
                    }
                    dx.set(dotProd, b, dxR, dxC, d);
                }
            }
        }
    }
    return backend.makeTensorInfo(dx.shape, dx.dtype, dx.values);
}
export const maxPoolGradConfig = {
    kernelName: MaxPoolGrad,
    backendName: 'cpu',
    kernelFunc: maxPoolGrad
};
//# sourceMappingURL=data:application/json;base64,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
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { Max } from '@tensorflow/tfjs-core';
import { backend_util } from '@tensorflow/tfjs-core';
import { util } from '@tensorflow/tfjs-core';
import { assertNotComplex } from '../cpu_util';
import { maxImpl } from './Max_impl';
import { transposeImpl } from './Transpose_impl';
export function max(args) {
    const { inputs, backend, attrs } = args;
    const { x } = inputs;
    const { reductionIndices, keepDims } = attrs;
    const cpuBackend = backend;
    let xShape = x.shape;
    const xRank = xShape.length;
    const origAxes = util.parseAxisParam(reductionIndices, xShape);
    let axes = origAxes;
    const permutedAxes = backend_util.getAxesPermutation(axes, xRank);
    let xVals = cpuBackend.data.get(x.dataId).values;
    if (permutedAxes != null) {
        const newShape = new Array(xRank);
        for (let i = 0; i < newShape.length; i++) {
            newShape[i] = xShape[permutedAxes[i]];
        }
        xVals = transposeImpl(xVals, xShape, x.dtype, permutedAxes, newShape);
        axes = backend_util.getInnerMostAxes(axes.length, xRank);
        xShape = newShape;
    }
    assertNotComplex(x, 'max');
    backend_util.assertAxesAreInnerMostDims('max', axes, xRank);
    const [maxOutShape, reduceShape] = backend_util.computeOutAndReduceShapes(xShape, axes);
    const reduceSize = util.sizeFromShape(reduceShape);
    const result = maxImpl(xVals, reduceSize, maxOutShape, x.dtype);
    const dataId = cpuBackend.write(result, maxOutShape, x.dtype);
    let outShape = maxOutShape;
    if (keepDims) {
        // reshape
        const newShape = backend_util.expandShapeToKeepDim(maxOutShape, origAxes);
        outShape = newShape;
    }
    return { dataId, shape: outShape, dtype: x.dtype };
}
export const maxConfig = {
    kernelName: Max,
    backendName: 'cpu',
    kernelFunc: max
};
//# sourceMappingURL=data:application/json;base64,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
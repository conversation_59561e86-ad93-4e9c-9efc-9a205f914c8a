/**
 * @license
 * Copyright 2019 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { env } from '@tensorflow/tfjs-core';
import { FromPixels } from '@tensorflow/tfjs-core';
import { TextureUsage } from '../tex_util';
import { FromPixelsProgram } from './FromPixels_utils/from_pixels_gpu';
import { FromPixelsPackedProgram } from './FromPixels_utils/from_pixels_packed_gpu';
export const fromPixelsConfig = {
    kernelName: FromPixels,
    backendName: 'webgl',
    kernelFunc: fromPixels,
};
let fromPixels2DContext;
let willReadFrequently = env().getBool('CANVAS2D_WILL_READ_FREQUENTLY_FOR_GPU');
function fromPixels(args) {
    const { inputs, backend, attrs } = args;
    let { pixels } = inputs;
    const { numChannels } = attrs;
    const isVideo = typeof (HTMLVideoElement) !== 'undefined' &&
        pixels instanceof HTMLVideoElement;
    const isImage = typeof (HTMLImageElement) !== 'undefined' &&
        pixels instanceof HTMLImageElement;
    const [width, height] = isVideo ?
        [
            pixels.videoWidth,
            pixels.videoHeight
        ] :
        [pixels.width, pixels.height];
    const texShape = [height, width];
    const outShape = [height, width, numChannels];
    if (isImage || isVideo) {
        const newWillReadFrequently = env().getBool('CANVAS2D_WILL_READ_FREQUENTLY_FOR_GPU');
        if (fromPixels2DContext == null ||
            newWillReadFrequently !== willReadFrequently) {
            willReadFrequently = newWillReadFrequently;
            fromPixels2DContext =
                document.createElement('canvas').getContext('2d', { willReadFrequently });
        }
        fromPixels2DContext.canvas.width = width;
        fromPixels2DContext.canvas.height = height;
        fromPixels2DContext.drawImage(pixels, 0, 0, width, height);
        pixels = fromPixels2DContext.canvas;
    }
    const tempPixelHandle = backend.makeTensorInfo(texShape, 'int32');
    // This is a byte texture with pixels.
    backend.texData.get(tempPixelHandle.dataId).usage = TextureUsage.PIXELS;
    backend.gpgpu.uploadPixelDataToTexture(backend.getTexture(tempPixelHandle.dataId), pixels);
    const program = env().getBool('WEBGL_PACK') ?
        new FromPixelsPackedProgram(outShape) :
        new FromPixelsProgram(outShape);
    const res = backend.runWebGLProgram(program, [tempPixelHandle], 'int32');
    backend.disposeData(tempPixelHandle.dataId);
    return res;
}
//# sourceMappingURL=data:application/json;base64,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
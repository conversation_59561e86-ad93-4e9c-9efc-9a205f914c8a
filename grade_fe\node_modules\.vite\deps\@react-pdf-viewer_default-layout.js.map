{"version": 3, "sources": ["../../@react-pdf-viewer/attachment/lib/cjs/attachment.js", "../../@react-pdf-viewer/attachment/lib/index.js", "../../@react-pdf-viewer/bookmark/lib/cjs/bookmark.js", "../../@react-pdf-viewer/bookmark/lib/index.js", "../../@react-pdf-viewer/thumbnail/lib/cjs/thumbnail.js", "../../@react-pdf-viewer/thumbnail/lib/index.js", "../../@react-pdf-viewer/selection-mode/lib/cjs/selection-mode.js", "../../@react-pdf-viewer/selection-mode/lib/index.js", "../../@react-pdf-viewer/full-screen/lib/cjs/full-screen.js", "../../@react-pdf-viewer/full-screen/lib/index.js", "../../@react-pdf-viewer/get-file/lib/cjs/get-file.js", "../../@react-pdf-viewer/get-file/lib/index.js", "../../@react-pdf-viewer/open/lib/cjs/open.js", "../../@react-pdf-viewer/open/lib/index.js", "../../@react-pdf-viewer/page-navigation/lib/cjs/page-navigation.js", "../../@react-pdf-viewer/page-navigation/lib/index.js", "../../@react-pdf-viewer/print/lib/cjs/print.js", "../../@react-pdf-viewer/print/lib/index.js", "../../@react-pdf-viewer/properties/lib/cjs/properties.js", "../../@react-pdf-viewer/properties/lib/index.js", "../../@react-pdf-viewer/rotate/lib/cjs/rotate.js", "../../@react-pdf-viewer/rotate/lib/index.js", "../../@react-pdf-viewer/scroll-mode/lib/cjs/scroll-mode.js", "../../@react-pdf-viewer/scroll-mode/lib/index.js", "../../@react-pdf-viewer/search/lib/cjs/search.js", "../../@react-pdf-viewer/search/lib/index.js", "../../@react-pdf-viewer/theme/lib/cjs/theme.js", "../../@react-pdf-viewer/theme/lib/index.js", "../../@react-pdf-viewer/zoom/lib/cjs/zoom.js", "../../@react-pdf-viewer/zoom/lib/index.js", "../../@react-pdf-viewer/toolbar/lib/cjs/toolbar.js", "../../@react-pdf-viewer/toolbar/lib/index.js", "../../@react-pdf-viewer/default-layout/lib/cjs/default-layout.js", "../../@react-pdf-viewer/default-layout/lib/index.js"], "sourcesContent": ["'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar getFileName = function (url) {\n    var str = url.split('/').pop();\n    return str ? str.split('#')[0].split('?')[0] : url;\n};\n\nvar downloadFile = function (url, data) {\n    var blobUrl = typeof data === 'string' ? '' : URL.createObjectURL(new Blob([data], { type: '' }));\n    var link = document.createElement('a');\n    link.style.display = 'none';\n    link.href = blobUrl || url;\n    link.setAttribute('download', getFileName(url));\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    if (blobUrl) {\n        URL.revokeObjectURL(blobUrl);\n    }\n};\n\nvar AttachmentList = function (_a) {\n    var files = _a.files;\n    var containerRef = React__namespace.useRef();\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var direction = React__namespace.useContext(core.ThemeContext).direction;\n    var isRtl = direction === core.TextDirection.RightToLeft;\n    var attachmentItemsRef = React__namespace.useRef([]);\n    var clickDownloadLabel = l10n && l10n.attachment\n        ? l10n.attachment.clickToDownload\n        : 'Click to download';\n    var handleKeyDown = function (e) {\n        switch (e.key) {\n            case 'ArrowDown':\n                e.preventDefault();\n                moveToItem(function (items, activeEle) { return items.indexOf(activeEle) + 1; });\n                break;\n            case 'ArrowUp':\n                e.preventDefault();\n                moveToItem(function (items, activeEle) { return items.indexOf(activeEle) - 1; });\n                break;\n            case 'End':\n                e.preventDefault();\n                moveToItem(function (items, _) { return items.length - 1; });\n                break;\n            case 'Home':\n                e.preventDefault();\n                moveToItem(function (_, __) { return 0; });\n                break;\n        }\n    };\n    var moveToItem = function (getItemIndex) {\n        var container = containerRef.current;\n        var attachmentItems = [].slice.call(container.getElementsByClassName('rpv-attachment__item'));\n        if (attachmentItems.length === 0) {\n            return;\n        }\n        attachmentItems.forEach(function (item) { return item.setAttribute('tabindex', '-1'); });\n        var activeEle = document.activeElement;\n        var targetIndex = Math.min(attachmentItems.length - 1, Math.max(0, getItemIndex(attachmentItems, activeEle)));\n        var targetEle = attachmentItems[targetIndex];\n        targetEle.setAttribute('tabindex', '0');\n        targetEle.focus();\n    };\n    core.useIsomorphicLayoutEffect(function () {\n        var container = containerRef.current;\n        if (!container) {\n            return;\n        }\n        var attachmentItems = [].slice.call(container.getElementsByClassName('rpv-attachment__item'));\n        attachmentItemsRef.current = attachmentItems;\n        if (attachmentItems.length > 0) {\n            var firstItem = attachmentItems[0];\n            firstItem.focus();\n            firstItem.setAttribute('tabindex', '0');\n        }\n    }, []);\n    return (React__namespace.createElement(\"div\", { \"data-testid\": \"attachment__list\", className: core.classNames({\n            'rpv-attachment__list': true,\n            'rpv-attachment__list--rtl': isRtl,\n        }), ref: containerRef, tabIndex: -1, onKeyDown: handleKeyDown }, files.map(function (file) { return (React__namespace.createElement(\"button\", { className: \"rpv-attachment__item\", key: file.fileName, tabIndex: -1, title: clickDownloadLabel, type: \"button\", onClick: function () { return downloadFile(file.fileName, file.data); } }, file.fileName)); })));\n};\n\nvar AttachmentLoader = function (_a) {\n    var doc = _a.doc;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var direction = React__namespace.useContext(core.ThemeContext).direction;\n    var isRtl = direction === core.TextDirection.RightToLeft;\n    var noAttachmentLabel = l10n && l10n.attachment\n        ? l10n.attachment.noAttachment\n        : 'There is no attachment';\n    var _b = React__namespace.useState({\n        files: [],\n        isLoaded: false,\n    }), attachments = _b[0], setAttachments = _b[1];\n    React__namespace.useEffect(function () {\n        doc.getAttachments().then(function (response) {\n            var files = response\n                ? Object.keys(response).map(function (file) {\n                    return {\n                        data: response[file].content,\n                        fileName: response[file].filename,\n                    };\n                })\n                : [];\n            setAttachments({\n                files: files,\n                isLoaded: true,\n            });\n        });\n    }, [doc]);\n    return !attachments.isLoaded ? (React__namespace.createElement(core.Spinner, null)) : attachments.files.length === 0 ? (React__namespace.createElement(\"div\", { \"data-testid\": \"attachment__empty\", className: core.classNames({\n            'rpv-attachment__empty': true,\n            'rpv-attachment__empty--rtl': isRtl,\n        }) }, noAttachmentLabel)) : (React__namespace.createElement(AttachmentList, { files: attachments.files }));\n};\n\nvar AttachmentListWithStore = function (_a) {\n    var store = _a.store;\n    var _b = React__namespace.useState(store.get('doc')), currentDoc = _b[0], setCurrentDoc = _b[1];\n    var handleDocumentChanged = function (doc) {\n        setCurrentDoc(doc);\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('doc', handleDocumentChanged);\n        return function () {\n            store.unsubscribe('doc', handleDocumentChanged);\n        };\n    }, []);\n    return currentDoc ? (React__namespace.createElement(AttachmentLoader, { doc: currentDoc })) : (React__namespace.createElement(\"div\", { className: \"rpv-attachment__loader\" },\n        React__namespace.createElement(core.Spinner, null)));\n};\n\nvar attachmentPlugin = function () {\n    var store = React__namespace.useMemo(function () { return core.createStore({}); }, []);\n    var AttachmentsDecorator = function () { return React__namespace.createElement(AttachmentListWithStore, { store: store }); };\n    return {\n        onDocumentLoad: function (props) {\n            store.update('doc', props.doc);\n        },\n        Attachments: AttachmentsDecorator,\n    };\n};\n\nexports.attachmentPlugin = attachmentPlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/attachment.min.js');\n} else {\n    module.exports = require('./cjs/attachment.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar DownArrowIcon = function () {\n    return (React__namespace.createElement(core.Icon, { size: 16 },\n        React__namespace.createElement(\"path\", { d: \"M6.427,8.245A.5.5,0,0,1,6.862,7.5H17.138a.5.5,0,0,1,.435.749l-5.139,9a.5.5,0,0,1-.868,0Z\" })));\n};\n\nvar RightArrowIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M9.248,17.572a.5.5,0,0,1-.748-.434V6.862a.5.5,0,0,1,.748-.434l8.992,5.138a.5.5,0,0,1,0,.868Z\" }))); };\n\nvar shouldBeCollapsed = function (bookmark) {\n    var count = bookmark.count, items = bookmark.items;\n    if (count >= 0) {\n        return false;\n    }\n    var numSubItems = items.length;\n    if (numSubItems === 0) {\n        return false;\n    }\n    var subItems = items.concat([]);\n    while (subItems.length > 0) {\n        var firstChild = subItems.shift();\n        var children = firstChild.items;\n        if (firstChild.count && children && firstChild.count > 0 && children.length > 0) {\n            numSubItems += children.length;\n            subItems = subItems.concat(children);\n        }\n    }\n    return Math.abs(count) === numSubItems;\n};\n\nvar BookmarkItem = function (_a) {\n    var bookmark = _a.bookmark, depth = _a.depth, doc = _a.doc, index = _a.index, isBookmarkExpanded = _a.isBookmarkExpanded, numberOfSiblings = _a.numberOfSiblings, pathFromRoot = _a.pathFromRoot, renderBookmarkItem = _a.renderBookmarkItem, store = _a.store;\n    var path = pathFromRoot ? \"\".concat(pathFromRoot, \".\").concat(index) : \"\".concat(index);\n    var defaultIsCollapsed = React__namespace.useMemo(function () { return shouldBeCollapsed(bookmark); }, [bookmark]);\n    var bookmarkExpandedMap = store.get('bookmarkExpandedMap');\n    var defaultExpanded = isBookmarkExpanded\n        ? isBookmarkExpanded({ bookmark: bookmark, doc: doc, depth: depth, index: index })\n        : bookmarkExpandedMap.has(path)\n            ? bookmarkExpandedMap.get(path)\n            : !defaultIsCollapsed;\n    var _b = React__namespace.useState(defaultExpanded), expanded = _b[0], setExpanded = _b[1];\n    var hasSubItems = bookmark.items && bookmark.items.length > 0;\n    var toggleSubItems = function () {\n        var newState = !expanded;\n        store.updateCurrentValue('bookmarkExpandedMap', function (currentValue) { return currentValue.set(path, newState); });\n        setExpanded(newState);\n    };\n    var jumpToDest = function () {\n        var dest = bookmark.dest;\n        var jumpToDestination = store.get('jumpToDestination');\n        core.getDestination(doc, dest).then(function (target) {\n            if (jumpToDestination) {\n                jumpToDestination(__assign({ label: bookmark.title }, target));\n            }\n        });\n    };\n    var clickBookmark = function () {\n        if (hasSubItems && bookmark.dest) {\n            jumpToDest();\n        }\n    };\n    var clickItem = function () {\n        if (!hasSubItems && bookmark.dest) {\n            jumpToDest();\n        }\n    };\n    var defaultRenderItem = function (onClickItem, children) { return (React__namespace.createElement(\"div\", { className: \"rpv-bookmark__item\", style: {\n            paddingLeft: \"\".concat(depth * 1.25, \"rem\"),\n        }, onClick: onClickItem }, children)); };\n    var defaultRenderToggle = function (expandIcon, collapseIcon) {\n        return hasSubItems ? (React__namespace.createElement(\"span\", { className: \"rpv-bookmark__toggle\", \"data-testid\": \"bookmark__toggle-\".concat(depth, \"-\").concat(index), onClick: toggleSubItems }, expanded ? expandIcon : collapseIcon)) : (React__namespace.createElement(\"span\", { className: \"rpv-bookmark__toggle\" }));\n    };\n    var defaultRenderTitle = function (onClickBookmark) {\n        return bookmark.url ? (React__namespace.createElement(\"a\", { className: \"rpv-bookmark__title\", href: bookmark.url, rel: \"noopener noreferrer nofollow\", target: bookmark.newWindow ? '_blank' : '' }, bookmark.title)) : (React__namespace.createElement(\"div\", { className: \"rpv-bookmark__title\", \"aria-label\": bookmark.title, onClick: onClickBookmark }, bookmark.title));\n    };\n    return (React__namespace.createElement(\"li\", { \"aria-expanded\": expanded ? 'true' : 'false', \"aria-label\": bookmark.title, \"aria-level\": depth + 1, \"aria-posinset\": index + 1, \"aria-setsize\": numberOfSiblings, role: \"treeitem\", tabIndex: -1 },\n        renderBookmarkItem\n            ? renderBookmarkItem({\n                bookmark: bookmark,\n                depth: depth,\n                hasSubItems: hasSubItems,\n                index: index,\n                isExpanded: expanded,\n                path: path,\n                defaultRenderItem: defaultRenderItem,\n                defaultRenderTitle: defaultRenderTitle,\n                defaultRenderToggle: defaultRenderToggle,\n                onClickItem: clickItem,\n                onClickTitle: clickBookmark,\n                onToggleSubItems: toggleSubItems,\n            })\n            : defaultRenderItem(clickItem, React__namespace.createElement(React__namespace.Fragment, null,\n                defaultRenderToggle(React__namespace.createElement(DownArrowIcon, null), React__namespace.createElement(RightArrowIcon, null)),\n                defaultRenderTitle(clickBookmark))),\n        hasSubItems && expanded && (React__namespace.createElement(BookmarkList, { bookmarks: bookmark.items, depth: depth + 1, doc: doc, isBookmarkExpanded: isBookmarkExpanded, isRoot: false, pathFromRoot: path, renderBookmarkItem: renderBookmarkItem, store: store }))));\n};\n\nvar BookmarkList = function (_a) {\n    var bookmarks = _a.bookmarks, _b = _a.depth, depth = _b === void 0 ? 0 : _b, doc = _a.doc, isBookmarkExpanded = _a.isBookmarkExpanded, isRoot = _a.isRoot, pathFromRoot = _a.pathFromRoot, renderBookmarkItem = _a.renderBookmarkItem, store = _a.store;\n    return (React__namespace.createElement(\"ul\", { className: \"rpv-bookmark__list\", role: isRoot ? 'tree' : 'group', tabIndex: -1 }, bookmarks.map(function (bookmark, index) { return (React__namespace.createElement(BookmarkItem, { bookmark: bookmark, depth: depth, doc: doc, index: index, isBookmarkExpanded: isBookmarkExpanded, key: index, numberOfSiblings: bookmarks.length, pathFromRoot: pathFromRoot, renderBookmarkItem: renderBookmarkItem, store: store })); })));\n};\n\nvar Toggle;\n(function (Toggle) {\n    Toggle[Toggle[\"Collapse\"] = 0] = \"Collapse\";\n    Toggle[Toggle[\"Expand\"] = 1] = \"Expand\";\n})(Toggle || (Toggle = {}));\nvar BookmarkListRoot = function (_a) {\n    var bookmarks = _a.bookmarks, doc = _a.doc, isBookmarkExpanded = _a.isBookmarkExpanded, renderBookmarkItem = _a.renderBookmarkItem, store = _a.store;\n    var containerRef = React__namespace.useRef();\n    var handleKeyDown = function (e) {\n        var container = containerRef.current;\n        if (!container || !(e.target instanceof HTMLElement) || !container.contains(e.target)) {\n            return;\n        }\n        switch (e.key) {\n            case 'ArrowDown':\n                e.preventDefault();\n                moveToItem(function (bookmarkElements, activeEle) { return bookmarkElements.indexOf(activeEle) + 1; });\n                break;\n            case 'ArrowLeft':\n                e.preventDefault();\n                toggle(Toggle.Collapse);\n                break;\n            case 'ArrowRight':\n                e.preventDefault();\n                toggle(Toggle.Expand);\n                break;\n            case 'ArrowUp':\n                e.preventDefault;\n                moveToItem(function (bookmarkElements, activeEle) { return bookmarkElements.indexOf(activeEle) - 1; });\n                break;\n            case 'End':\n                e.preventDefault();\n                moveToItem(function (bookmarkElements, _) { return bookmarkElements.length - 1; });\n                break;\n            case ' ':\n            case 'Enter':\n            case 'Space':\n                e.preventDefault();\n                clickBookmark();\n                break;\n            case 'Home':\n                e.preventDefault();\n                moveToItem(function (_, __) { return 0; });\n                break;\n        }\n    };\n    var clickBookmark = function () {\n        var closestItem = document.activeElement.closest('.rpv-bookmark__item');\n        var titleEle = closestItem.querySelector('.rpv-bookmark__title');\n        if (titleEle) {\n            titleEle.click();\n        }\n    };\n    var moveToItem = function (getItemIndex) {\n        var container = containerRef.current;\n        var bookmarkElements = [].slice.call(container.getElementsByClassName('rpv-bookmark__item'));\n        if (bookmarkElements.length === 0) {\n            return;\n        }\n        var activeEle = document.activeElement;\n        var targetIndex = Math.min(bookmarkElements.length - 1, Math.max(0, getItemIndex(bookmarkElements, activeEle)));\n        var targetEle = bookmarkElements[targetIndex];\n        activeEle.setAttribute('tabindex', '-1');\n        targetEle.setAttribute('tabindex', '0');\n        targetEle.focus();\n    };\n    var toggle = function (toggle) {\n        var container = containerRef.current;\n        var bookmarkElements = [].slice.call(container.getElementsByClassName('rpv-bookmark__item'));\n        if (bookmarkElements.length === 0) {\n            return;\n        }\n        var closestItem = document.activeElement.closest('.rpv-bookmark__item');\n        var expanedAttribute = toggle === Toggle.Collapse ? 'true' : 'false';\n        if (closestItem && closestItem.parentElement.getAttribute('aria-expanded') === expanedAttribute) {\n            var toggleEle = closestItem.querySelector('.rpv-bookmark__toggle');\n            if (toggleEle) {\n                toggleEle.click();\n            }\n        }\n    };\n    React__namespace.useEffect(function () {\n        document.addEventListener('keydown', handleKeyDown);\n        return function () {\n            document.removeEventListener('keydown', handleKeyDown);\n        };\n    }, []);\n    React__namespace.useEffect(function () {\n        var container = containerRef.current;\n        if (!container) {\n            return;\n        }\n        var bookmarkElements = [].slice.call(container.getElementsByClassName('rpv-bookmark__item'));\n        if (bookmarkElements.length > 0) {\n            bookmarkElements[0].focus();\n            bookmarkElements[0].setAttribute('tabindex', '0');\n        }\n    }, []);\n    return (React__namespace.createElement(\"div\", { ref: containerRef },\n        React__namespace.createElement(BookmarkList, { bookmarks: bookmarks, depth: 0, doc: doc, isBookmarkExpanded: isBookmarkExpanded, isRoot: true, pathFromRoot: \"\", renderBookmarkItem: renderBookmarkItem, store: store })));\n};\n\nvar BookmarkLoader = function (_a) {\n    var doc = _a.doc, isBookmarkExpanded = _a.isBookmarkExpanded, renderBookmarkItem = _a.renderBookmarkItem, store = _a.store;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var direction = React__namespace.useContext(core.ThemeContext).direction;\n    var isRtl = direction === core.TextDirection.RightToLeft;\n    var _b = React__namespace.useState({\n        isLoaded: false,\n        items: [],\n    }), bookmarks = _b[0], setBookmarks = _b[1];\n    React__namespace.useEffect(function () {\n        setBookmarks({\n            isLoaded: false,\n            items: [],\n        });\n        doc.getOutline().then(function (outline) {\n            setBookmarks({\n                isLoaded: true,\n                items: outline || [],\n            });\n        });\n    }, [doc]);\n    return !bookmarks.isLoaded ? (React__namespace.createElement(\"div\", { className: \"rpv-bookmark__loader\" },\n        React__namespace.createElement(core.Spinner, null))) : bookmarks.items.length === 0 ? (React__namespace.createElement(\"div\", { \"data-testid\": \"bookmark__empty\", className: core.classNames({\n            'rpv-bookmark__empty': true,\n            'rpv-bookmark__empty--rtl': isRtl,\n        }) }, l10n && l10n.bookmark ? l10n.bookmark.noBookmark : 'There is no bookmark')) : (React__namespace.createElement(\"div\", { \"data-testid\": \"bookmark__container\", className: core.classNames({\n            'rpv-bookmark__container': true,\n            'rpv-bookmark__container--rtl': isRtl,\n        }) },\n        React__namespace.createElement(BookmarkListRoot, { bookmarks: bookmarks.items, doc: doc, isBookmarkExpanded: isBookmarkExpanded, renderBookmarkItem: renderBookmarkItem, store: store })));\n};\n\nvar BookmarkListWithStore = function (_a) {\n    var isBookmarkExpanded = _a.isBookmarkExpanded, renderBookmarkItem = _a.renderBookmarkItem, store = _a.store;\n    var _b = React__namespace.useState(store.get('doc')), currentDoc = _b[0], setCurrentDoc = _b[1];\n    var handleDocumentChanged = function (doc) {\n        setCurrentDoc(doc);\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('doc', handleDocumentChanged);\n        return function () {\n            store.unsubscribe('doc', handleDocumentChanged);\n        };\n    }, []);\n    return currentDoc ? (React__namespace.createElement(BookmarkLoader, { doc: currentDoc, isBookmarkExpanded: isBookmarkExpanded, renderBookmarkItem: renderBookmarkItem, store: store })) : (React__namespace.createElement(\"div\", { className: \"rpv-bookmark__loader\" },\n        React__namespace.createElement(core.Spinner, null)));\n};\n\nvar bookmarkPlugin = function () {\n    var store = React__namespace.useMemo(function () {\n        return core.createStore({\n            bookmarkExpandedMap: new Map(),\n        });\n    }, []);\n    var BookmarksDecorator = function (props) { return (React__namespace.createElement(BookmarkListWithStore, { isBookmarkExpanded: props === null || props === void 0 ? void 0 : props.isBookmarkExpanded, renderBookmarkItem: props === null || props === void 0 ? void 0 : props.renderBookmarkItem, store: store })); };\n    return {\n        install: function (pluginFunctions) {\n            store.update('jumpToDestination', pluginFunctions.jumpToDestination);\n        },\n        onDocumentLoad: function (props) {\n            store.update('doc', props.doc);\n        },\n        Bookmarks: BookmarksDecorator,\n    };\n};\n\nexports.DownArrowIcon = DownArrowIcon;\nexports.RightArrowIcon = RightArrowIcon;\nexports.bookmarkPlugin = bookmarkPlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/bookmark.min.js');\n} else {\n    module.exports = require('./cjs/bookmark.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nexports.ThumbnailDirection = void 0;\n(function (ThumbnailDirection) {\n    ThumbnailDirection[\"Horizontal\"] = \"Horizontal\";\n    ThumbnailDirection[\"Vertical\"] = \"Vertical\";\n})(exports.ThumbnailDirection || (exports.ThumbnailDirection = {}));\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar CoverInner = function (_a) {\n    var doc = _a.doc, getPageIndex = _a.getPageIndex, renderSpinner = _a.renderSpinner, store = _a.store, width = _a.width;\n    var numPages = doc.numPages;\n    var targetPage = getPageIndex ? getPageIndex({ numPages: numPages }) : 0;\n    var normalizePage = Math.max(0, Math.min(targetPage, numPages - 1));\n    var initialPagesRotation = store.get('pagesRotation') || new Map();\n    var initialTargetPageRotation = initialPagesRotation.has(normalizePage)\n        ? initialPagesRotation.get(normalizePage)\n        : 0;\n    var _b = React__namespace.useState(''), src = _b[0], setSrc = _b[1];\n    var isMounted = core.useIsMounted();\n    var renderTask = React__namespace.useRef();\n    var _c = React__namespace.useState(store.get('rotation') || 0), rotation = _c[0], setRotation = _c[1];\n    var _d = React__namespace.useState(initialTargetPageRotation), pageRotation = _d[0], setPageRotation = _d[1];\n    var _e = React__namespace.useState(false), isVisible = _e[0], setVisible = _e[1];\n    var handlePagesRotationChanged = function (rotations) {\n        var pageRotation = rotations.has(normalizePage) ? rotations.get(normalizePage) : 0;\n        setPageRotation(pageRotation);\n    };\n    var handleRotationChanged = function (currentRotation) {\n        setRotation(currentRotation);\n    };\n    var handleVisibilityChanged = function (params) {\n        setVisible(params.isVisible);\n    };\n    var containerRef = core.useIntersectionObserver({\n        onVisibilityChanged: handleVisibilityChanged,\n    });\n    React__namespace.useEffect(function () {\n        if (!isVisible) {\n            return;\n        }\n        var containerEle = containerRef.current;\n        if (!containerEle) {\n            return;\n        }\n        setSrc('');\n        core.getPage(doc, normalizePage).then(function (page) {\n            var viewport = page.getViewport({ scale: 1 });\n            var viewportRotation = viewport.rotation;\n            var rotationValue = (viewportRotation + rotation + pageRotation) % 360;\n            var isVertical = Math.abs(rotation + pageRotation) % 180 === 0;\n            var w = isVertical ? viewport.width : viewport.height;\n            var h = isVertical ? viewport.height : viewport.width;\n            var canvas = document.createElement('canvas');\n            var canvasContext = canvas.getContext('2d', { alpha: false });\n            var containerWidth = containerEle.clientWidth;\n            var containerHeight = containerEle.clientHeight;\n            var scaled = width ? width / w : Math.min(containerWidth / w, containerHeight / h);\n            var canvasWidth = scaled * w;\n            var canvasHeight = scaled * h;\n            canvas.height = canvasHeight;\n            canvas.width = canvasWidth;\n            canvas.style.opacity = '0';\n            var renderViewport = page.getViewport({\n                rotation: rotationValue,\n                scale: scaled,\n            });\n            renderTask.current = page.render({ canvasContext: canvasContext, viewport: renderViewport });\n            renderTask.current.promise.then(function () {\n                isMounted.current && setSrc(canvas.toDataURL());\n                canvas.width = 0;\n                canvas.height = 0;\n            }, function () {\n            });\n        });\n    }, [pageRotation, isVisible]);\n    React__namespace.useEffect(function () {\n        store.subscribe('pagesRotation', handlePagesRotationChanged);\n        store.subscribe('rotation', handleRotationChanged);\n        return function () {\n            store.unsubscribe('pagesRotation', handlePagesRotationChanged);\n            store.unsubscribe('rotation', handleRotationChanged);\n        };\n    }, []);\n    React__namespace.useEffect(function () {\n        return function () {\n            var _a;\n            (_a = renderTask.current) === null || _a === void 0 ? void 0 : _a.cancel();\n        };\n    }, []);\n    return (React__namespace.createElement(\"div\", { ref: containerRef, className: \"rpv-thumbnail__cover-inner\", \"data-testid\": \"thumbnail__cover-inner\" }, src ? (React__namespace.createElement(\"img\", { className: \"rpv-thumbnail__cover-image\", \"data-testid\": \"thumbnail__cover-image\", src: src })) : (React__namespace.createElement(\"div\", { className: \"rpv-thumbnail__cover-loader\", \"data-testid\": \"thumbnail__cover-loader\" }, renderSpinner ? renderSpinner() : React__namespace.createElement(core.Spinner, null)))));\n};\n\nvar Cover = function (_a) {\n    var getPageIndex = _a.getPageIndex, renderSpinner = _a.renderSpinner, store = _a.store, width = _a.width;\n    var _b = React__namespace.useState(store.get('doc')), currentDoc = _b[0], setCurrentDoc = _b[1];\n    var handleDocumentChanged = function (doc) {\n        setCurrentDoc(doc);\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('doc', handleDocumentChanged);\n        return function () {\n            store.unsubscribe('doc', handleDocumentChanged);\n        };\n    }, []);\n    return (React__namespace.createElement(\"div\", { className: \"rpv-thumbnail__cover\" }, currentDoc ? (React__namespace.createElement(CoverInner, { doc: currentDoc, getPageIndex: getPageIndex, renderSpinner: renderSpinner, store: store, width: width })) : (React__namespace.createElement(\"div\", { className: \"rpv-thumbnail__cover-loader\" }, renderSpinner ? renderSpinner() : React__namespace.createElement(core.Spinner, null)))));\n};\n\nvar defaultSpinner = function () { return React__namespace.createElement(core.Spinner, null); };\nvar SpinnerContext = React__namespace.createContext({\n    renderSpinner: defaultSpinner,\n});\n\nvar FetchLabels = function (_a) {\n    var children = _a.children, doc = _a.doc;\n    var isMounted = core.useIsMounted();\n    var _b = React__namespace.useState({\n        loading: true,\n        labels: [],\n    }), status = _b[0], setStatus = _b[1];\n    React__namespace.useEffect(function () {\n        doc.getPageLabels().then(function (result) {\n            isMounted.current && setStatus({ loading: false, labels: result || [] });\n        });\n    }, [doc.loadingTask.docId]);\n    return status.loading ? React__namespace.createElement(React__namespace.Fragment, null) : children(status.labels);\n};\n\nvar scrollToBeVisibleVertically = function (ele, container) {\n    var top = ele.getBoundingClientRect().top - container.getBoundingClientRect().top;\n    var eleHeight = ele.clientHeight;\n    var containerHeight = container.clientHeight;\n    if (top < 0) {\n        container.scrollTop += top;\n        return;\n    }\n    if (top + eleHeight <= containerHeight) {\n        return;\n    }\n    container.scrollTop += top + eleHeight - containerHeight;\n};\nvar scrollToBeVisibleHorizontally = function (ele, container) {\n    var left = ele.getBoundingClientRect().left - container.getBoundingClientRect().left;\n    var eleWidth = ele.clientWidth;\n    var containerWidth = container.clientWidth;\n    if (left < 0) {\n        container.scrollLeft += left;\n        return;\n    }\n    if (left + eleWidth <= containerWidth) {\n        return;\n    }\n    container.scrollLeft += left + eleWidth - containerWidth;\n};\n\nvar ThumbnailItem = function (_a) {\n    var page = _a.page, pageHeight = _a.pageHeight, pageIndex = _a.pageIndex, pageWidth = _a.pageWidth, rotation = _a.rotation, thumbnailHeight = _a.thumbnailHeight, thumbnailWidth = _a.thumbnailWidth, onRenderCompleted = _a.onRenderCompleted;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var renderTask = React__namespace.useRef();\n    var _b = React__namespace.useState(''), src = _b[0], setSrc = _b[1];\n    var thumbnailLabel = l10n && l10n.thumbnail\n        ? l10n.thumbnail.thumbnailLabel\n        : 'Thumbnail of page {{pageIndex}}';\n    React__namespace.useEffect(function () {\n        var task = renderTask.current;\n        if (task) {\n            task.cancel();\n        }\n        var canvas = document.createElement('canvas');\n        var canvasContext = canvas.getContext('2d', { alpha: false });\n        var w = thumbnailWidth;\n        var h = w / (pageWidth / pageHeight);\n        var scale = w / pageWidth;\n        canvas.height = h;\n        canvas.width = w;\n        canvas.style.height = \"\".concat(h, \"px\");\n        canvas.style.width = \"\".concat(w, \"px\");\n        var viewport = page.getViewport({ rotation: rotation, scale: scale });\n        renderTask.current = page.render({ canvasContext: canvasContext, viewport: viewport });\n        renderTask.current.promise.then(function () {\n            setSrc(canvas.toDataURL());\n            onRenderCompleted(pageIndex);\n        }, function () {\n            onRenderCompleted(pageIndex);\n        });\n        return function () {\n            var _a;\n            (_a = renderTask.current) === null || _a === void 0 ? void 0 : _a.cancel();\n        };\n    }, [rotation]);\n    return !src ? (React__namespace.useContext(SpinnerContext).renderSpinner()) : (React__namespace.createElement(\"img\", { \"aria-label\": thumbnailLabel.replace('{{pageIndex}}', \"\".concat(pageIndex + 1)), src: src, height: \"\".concat(thumbnailHeight, \"px\"), width: \"\".concat(thumbnailWidth, \"px\") }));\n};\n\nvar ThumbnailContainer = function (_a) {\n    var doc = _a.doc, pageHeight = _a.pageHeight, pageIndex = _a.pageIndex, pageRotation = _a.pageRotation, pageWidth = _a.pageWidth, rotation = _a.rotation, shouldRender = _a.shouldRender, thumbnailWidth = _a.thumbnailWidth, onRenderCompleted = _a.onRenderCompleted, onVisibilityChanged = _a.onVisibilityChanged;\n    var isMounted = core.useIsMounted();\n    var _b = React__namespace.useState({\n        height: pageHeight,\n        page: null,\n        viewportRotation: 0,\n        width: pageWidth,\n    }), pageSize = _b[0], setPageSize = _b[1];\n    var page = pageSize.page, height = pageSize.height, width = pageSize.width;\n    var scale = width / height;\n    var isVertical = Math.abs(rotation + pageRotation) % 180 === 0;\n    var w = isVertical ? thumbnailWidth : thumbnailWidth / scale;\n    var h = isVertical ? thumbnailWidth / scale : thumbnailWidth;\n    React__namespace.useEffect(function () {\n        if (shouldRender) {\n            core.getPage(doc, pageIndex).then(function (pdfPage) {\n                var viewport = pdfPage.getViewport({ scale: 1 });\n                isMounted.current &&\n                    setPageSize({\n                        height: viewport.height,\n                        page: pdfPage,\n                        viewportRotation: viewport.rotation,\n                        width: viewport.width,\n                    });\n            });\n        }\n    }, [shouldRender]);\n    var rotationNumber = (pageSize.viewportRotation + rotation + pageRotation) % 360;\n    var containerRef = core.useIntersectionObserver({\n        onVisibilityChanged: function (visibility) {\n            onVisibilityChanged(pageIndex, visibility);\n        },\n    });\n    return (React__namespace.createElement(\"div\", { className: \"rpv-thumbnail__container\", \"data-testid\": \"thumbnail__container-\".concat(pageIndex), ref: containerRef, style: {\n            height: \"\".concat(h, \"px\"),\n            width: \"\".concat(w, \"px\"),\n        } }, !page ? (React__namespace.useContext(SpinnerContext).renderSpinner()) : (React__namespace.createElement(ThumbnailItem, { page: page, pageHeight: isVertical ? height : width, pageIndex: pageIndex, pageWidth: isVertical ? width : height, rotation: rotationNumber, thumbnailHeight: h, thumbnailWidth: w, onRenderCompleted: onRenderCompleted }))));\n};\n\nvar ThumbnailList = function (_a) {\n    var currentPage = _a.currentPage, doc = _a.doc, labels = _a.labels, pagesRotation = _a.pagesRotation, pageHeight = _a.pageHeight, pageWidth = _a.pageWidth, renderCurrentPageLabel = _a.renderCurrentPageLabel, renderThumbnailItem = _a.renderThumbnailItem, rotatedPage = _a.rotatedPage, rotation = _a.rotation, thumbnailDirection = _a.thumbnailDirection, thumbnailWidth = _a.thumbnailWidth, viewMode = _a.viewMode, onJumpToPage = _a.onJumpToPage, onRotatePage = _a.onRotatePage;\n    var numPages = doc.numPages;\n    var docId = doc.loadingTask.docId;\n    var containerRef = React__namespace.useRef(null);\n    var thumbnailsRef = React__namespace.useRef([]);\n    var _b = React__namespace.useState(currentPage), currentFocused = _b[0], setCurrentFocused = _b[1];\n    var direction = React__namespace.useContext(core.ThemeContext).direction;\n    var isRtl = direction === core.TextDirection.RightToLeft;\n    var _c = React__namespace.useState(-1), renderPageIndex = _c[0], setRenderPageIndex = _c[1];\n    var isMounted = core.useIsMounted();\n    var previousViewMode = core.usePrevious(viewMode);\n    var hasRenderingThumbnailRef = React__namespace.useRef(false);\n    var renderQueue = core.useRenderQueue({ doc: doc });\n    var pageIndexes = React__namespace.useMemo(function () {\n        return Array(numPages)\n            .fill(0)\n            .map(function (_, pageIndex) { return pageIndex; });\n    }, [docId]);\n    var chunks = React__namespace.useMemo(function () {\n        switch (viewMode) {\n            case core.ViewMode.DualPage:\n                return core.chunk(pageIndexes, 2);\n            case core.ViewMode.DualPageWithCover:\n                return [[pageIndexes[0]]].concat(core.chunk(pageIndexes.slice(1), 2));\n            case core.ViewMode.SinglePage:\n            default:\n                return core.chunk(pageIndexes, 1);\n        }\n    }, [docId, viewMode]);\n    var handleKeyDown = function (e) {\n        switch (e.key) {\n            case 'ArrowDown':\n                activateNextItem();\n                break;\n            case 'ArrowUp':\n                activatePreviousItem();\n                break;\n            case 'Enter':\n                jumpToFocusedPage();\n                break;\n        }\n    };\n    var activateNextItem = function () {\n        var container = containerRef.current;\n        if (!container) {\n            return;\n        }\n        var items = thumbnailsRef.current;\n        var nextItem = currentFocused + 1;\n        if (nextItem < items.length) {\n            if (currentFocused >= 0) {\n                items[currentFocused].setAttribute('tabindex', '-1');\n            }\n            setCurrentFocused(nextItem);\n        }\n    };\n    var activatePreviousItem = function () {\n        var container = containerRef.current;\n        if (!container) {\n            return;\n        }\n        var items = thumbnailsRef.current;\n        var prevItem = currentFocused - 1;\n        if (prevItem >= 0) {\n            if (currentFocused >= 0) {\n                items[currentFocused].setAttribute('tabindex', '-1');\n            }\n            setCurrentFocused(prevItem);\n        }\n    };\n    var jumpToFocusedPage = function () {\n        if (currentFocused >= 0 && currentFocused < numPages) {\n            onJumpToPage(currentFocused);\n        }\n    };\n    core.useIsomorphicLayoutEffect(function () {\n        var container = containerRef.current;\n        if (!container) {\n            return;\n        }\n        thumbnailsRef.current = Array.from(container.querySelectorAll('.rpv-thumbnail__item'));\n    }, [viewMode]);\n    React__namespace.useEffect(function () {\n        var thumbnails = thumbnailsRef.current;\n        if (thumbnails.length === 0 || currentFocused < 0 || currentFocused > thumbnails.length) {\n            return;\n        }\n        var thumbnailEle = thumbnails[currentFocused];\n        thumbnailEle.setAttribute('tabindex', '0');\n        thumbnailEle.focus();\n    }, [currentFocused]);\n    core.useIsomorphicLayoutEffect(function () {\n        var container = containerRef.current;\n        var thumbnails = thumbnailsRef.current;\n        if (!container || thumbnails.length === 0 || currentPage < 0 || currentPage > thumbnails.length) {\n            return;\n        }\n        var thumbnailContainer = thumbnails[currentPage].closest('.rpv-thumbnail__items');\n        if (thumbnailContainer) {\n            thumbnailDirection === exports.ThumbnailDirection.Vertical\n                ? scrollToBeVisibleVertically(thumbnailContainer, container)\n                : scrollToBeVisibleHorizontally(thumbnailContainer, container);\n        }\n    }, [currentPage, thumbnailDirection]);\n    var handleRenderCompleted = React__namespace.useCallback(function (pageIndex) {\n        if (isMounted.current) {\n            renderQueue.markRendered(pageIndex);\n            hasRenderingThumbnailRef.current = false;\n            renderNextThumbnail();\n        }\n    }, [docId]);\n    var handleVisibilityChanged = React__namespace.useCallback(function (pageIndex, visibility) {\n        visibility.isVisible\n            ? renderQueue.setVisibility(pageIndex, visibility.ratio)\n            :\n                renderQueue.setOutOfRange(pageIndex);\n        renderNextThumbnail();\n    }, [docId]);\n    var renderNextThumbnail = React__namespace.useCallback(function () {\n        if (hasRenderingThumbnailRef.current) {\n            return;\n        }\n        var nextPage = renderQueue.getHighestPriorityPage();\n        if (nextPage > -1) {\n            renderQueue.markRendering(nextPage);\n            hasRenderingThumbnailRef.current = true;\n            setRenderPageIndex(nextPage);\n        }\n    }, [docId]);\n    React__namespace.useEffect(function () {\n        if (rotatedPage >= 0) {\n            renderQueue.markRendering(rotatedPage);\n            hasRenderingThumbnailRef.current = true;\n            setRenderPageIndex(rotatedPage);\n        }\n    }, [docId, rotatedPage]);\n    core.useIsomorphicLayoutEffect(function () {\n        if (previousViewMode !== viewMode) {\n            renderQueue.markNotRendered();\n            renderNextThumbnail();\n        }\n    }, [viewMode]);\n    var renderPageThumbnail = function (pageIndex) {\n        var isCover = viewMode === core.ViewMode.DualPageWithCover &&\n            (pageIndex === 0 || (numPages % 2 === 0 && pageIndex === numPages - 1));\n        var key = \"\".concat(doc.loadingTask.docId, \"___\").concat(pageIndex);\n        var pageLabel = labels.length === numPages ? labels[pageIndex] : \"\".concat(pageIndex + 1);\n        var label = renderCurrentPageLabel\n            ? renderCurrentPageLabel({ currentPage: currentPage, pageIndex: pageIndex, numPages: numPages, pageLabel: pageLabel })\n            : pageLabel;\n        var pageRotation = pagesRotation.has(pageIndex) ? pagesRotation.get(pageIndex) : 0;\n        var thumbnail = (React__namespace.createElement(ThumbnailContainer, { doc: doc, pageHeight: pageHeight, pageIndex: pageIndex, pageRotation: pageRotation, pageWidth: pageWidth, rotation: rotation, shouldRender: renderPageIndex === pageIndex, thumbnailWidth: thumbnailWidth, onRenderCompleted: handleRenderCompleted, onVisibilityChanged: handleVisibilityChanged }));\n        return renderThumbnailItem ? (renderThumbnailItem({\n            currentPage: currentPage,\n            key: key,\n            numPages: numPages,\n            pageIndex: pageIndex,\n            renderPageLabel: React__namespace.createElement(React__namespace.Fragment, null, label),\n            renderPageThumbnail: thumbnail,\n            onJumpToPage: function () { return onJumpToPage(pageIndex); },\n            onRotatePage: function (direction) { return onRotatePage(pageIndex, direction); },\n        })) : (React__namespace.createElement(\"div\", { key: key },\n            React__namespace.createElement(\"div\", { className: core.classNames({\n                    'rpv-thumbnail__item': true,\n                    'rpv-thumbnail__item--dual-even': viewMode === core.ViewMode.DualPage && pageIndex % 2 === 0,\n                    'rpv-thumbnail__item--dual-odd': viewMode === core.ViewMode.DualPage && pageIndex % 2 === 1,\n                    'rpv-thumbnail__item--dual-cover': isCover,\n                    'rpv-thumbnail__item--dual-cover-even': viewMode === core.ViewMode.DualPageWithCover && !isCover && pageIndex % 2 === 0,\n                    'rpv-thumbnail__item--dual-cover-odd': viewMode === core.ViewMode.DualPageWithCover && !isCover && pageIndex % 2 === 1,\n                    'rpv-thumbnail__item--single': viewMode === core.ViewMode.SinglePage,\n                    'rpv-thumbnail__item--selected': currentPage === pageIndex,\n                }), role: \"button\", tabIndex: currentPage === pageIndex ? 0 : -1, onClick: function () { return onJumpToPage(pageIndex); } }, thumbnail),\n            React__namespace.createElement(\"div\", { \"data-testid\": \"thumbnail__label-\".concat(pageIndex), className: \"rpv-thumbnail__label\" }, label)));\n    };\n    return (React__namespace.createElement(\"div\", { ref: containerRef, \"data-testid\": \"thumbnail__list\", className: core.classNames({\n            'rpv-thumbnail__list': true,\n            'rpv-thumbnail__list--horizontal': thumbnailDirection === exports.ThumbnailDirection.Horizontal,\n            'rpv-thumbnail__list--rtl': isRtl,\n            'rpv-thumbnail__list--vertical': thumbnailDirection === exports.ThumbnailDirection.Vertical,\n        }), onKeyDown: handleKeyDown }, chunks.map(function (chunkItem, index) {\n        var isSelectedChunk = false;\n        switch (viewMode) {\n            case core.ViewMode.DualPage:\n                isSelectedChunk = currentPage === 2 * index || currentPage === 2 * index + 1;\n                break;\n            case core.ViewMode.DualPageWithCover:\n                isSelectedChunk =\n                    (currentPage === 0 && index === 0) ||\n                        (index > 0 && currentPage === 2 * index - 1) ||\n                        (index > 0 && currentPage === 2 * index);\n                break;\n            case core.ViewMode.SinglePage:\n            default:\n                isSelectedChunk = currentPage === index;\n                break;\n        }\n        return (React__namespace.createElement(\"div\", { className: core.classNames({\n                'rpv-thumbnail__items': true,\n                'rpv-thumbnail__items--dual': viewMode === core.ViewMode.DualPage,\n                'rpv-thumbnail__items--dual-cover': viewMode === core.ViewMode.DualPageWithCover,\n                'rpv-thumbnail__items--single': viewMode === core.ViewMode.SinglePage,\n                'rpv-thumbnail__items--selected': isSelectedChunk,\n            }), key: \"\".concat(index, \"___\").concat(viewMode) }, chunkItem.map(function (pageIndex) { return renderPageThumbnail(pageIndex); })));\n    })));\n};\n\nvar ThumbnailListWithStore = function (_a) {\n    var renderCurrentPageLabel = _a.renderCurrentPageLabel, renderThumbnailItem = _a.renderThumbnailItem, store = _a.store, thumbnailDirection = _a.thumbnailDirection, thumbnailWidth = _a.thumbnailWidth;\n    var _b = React__namespace.useState(store.get('doc')), currentDoc = _b[0], setCurrentDoc = _b[1];\n    var _c = React__namespace.useState(store.get('currentPage') || 0), currentPage = _c[0], setCurrentPage = _c[1];\n    var _d = React__namespace.useState(store.get('pageHeight') || 0), pageHeight = _d[0], setPageHeight = _d[1];\n    var _e = React__namespace.useState(store.get('pageWidth') || 0), pageWidth = _e[0], setPageWidth = _e[1];\n    var _f = React__namespace.useState(store.get('rotation') || 0), rotation = _f[0], setRotation = _f[1];\n    var _g = React__namespace.useState(store.get('pagesRotation') || new Map()), pagesRotation = _g[0], setPagesRotation = _g[1];\n    var _h = React__namespace.useState(store.get('rotatedPage') || -1), rotatedPage = _h[0], setRotatedPage = _h[1];\n    var _j = React__namespace.useState(store.get('viewMode')), viewMode = _j[0], setViewMode = _j[1];\n    var handleCurrentPageChanged = function (currentPageIndex) {\n        setCurrentPage(currentPageIndex);\n    };\n    var handleDocumentChanged = function (doc) {\n        setCurrentDoc(doc);\n    };\n    var handlePageHeightChanged = function (height) {\n        setPageHeight(height);\n    };\n    var handlePageWidthChanged = function (width) {\n        setPageWidth(width);\n    };\n    var handleRotationChanged = function (currentRotation) {\n        setRotation(currentRotation);\n    };\n    var handlePagesRotationChanged = function (rotations) {\n        setPagesRotation(rotations);\n    };\n    var handleRotatedPage = function (rotatedPage) {\n        setRotatedPage(rotatedPage);\n    };\n    var handleViewModeChanged = function (viewMode) {\n        setViewMode(viewMode);\n    };\n    var jump = function (pageIndex) {\n        var jumpToPage = store.get('jumpToPage');\n        if (jumpToPage) {\n            jumpToPage(pageIndex);\n        }\n    };\n    var rotatePage = function (pageIndex, direction) {\n        store.get('rotatePage')(pageIndex, direction);\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('doc', handleDocumentChanged);\n        store.subscribe('pageHeight', handlePageHeightChanged);\n        store.subscribe('pageWidth', handlePageWidthChanged);\n        store.subscribe('rotatedPage', handleRotatedPage);\n        store.subscribe('rotation', handleRotationChanged);\n        store.subscribe('pagesRotation', handlePagesRotationChanged);\n        store.subscribe('viewMode', handleViewModeChanged);\n        return function () {\n            store.unsubscribe('doc', handleDocumentChanged);\n            store.unsubscribe('pageHeight', handlePageHeightChanged);\n            store.unsubscribe('pageWidth', handlePageWidthChanged);\n            store.unsubscribe('rotatedPage', handleRotatedPage);\n            store.unsubscribe('rotation', handleRotationChanged);\n            store.unsubscribe('pagesRotation', handlePagesRotationChanged);\n            store.unsubscribe('viewMode', handleViewModeChanged);\n        };\n    }, []);\n    core.useIsomorphicLayoutEffect(function () {\n        store.subscribe('currentPage', handleCurrentPageChanged);\n        return function () {\n            store.unsubscribe('currentPage', handleCurrentPageChanged);\n        };\n    }, []);\n    return currentDoc ? (React__namespace.createElement(core.LazyRender, { testId: \"thumbnail__list-container\", attrs: {\n            className: 'rpv-thumbnail__list-container',\n        } },\n        React__namespace.createElement(FetchLabels, { doc: currentDoc }, function (labels) { return (React__namespace.createElement(ThumbnailList, { currentPage: currentPage, doc: currentDoc, labels: labels, pagesRotation: pagesRotation, pageHeight: pageHeight, pageWidth: pageWidth, renderCurrentPageLabel: renderCurrentPageLabel, renderThumbnailItem: renderThumbnailItem, rotatedPage: rotatedPage, rotation: rotation, thumbnailDirection: thumbnailDirection, thumbnailWidth: thumbnailWidth, viewMode: viewMode, onJumpToPage: jump, onRotatePage: rotatePage })); }))) : (React__namespace.createElement(\"div\", { \"data-testid\": \"thumbnail-list__loader\", className: \"rpv-thumbnail__loader\" }, React__namespace.useContext(SpinnerContext).renderSpinner()));\n};\n\nvar thumbnailPlugin = function (pluginProps) {\n    var store = React__namespace.useMemo(function () {\n        return core.createStore({\n            rotatePage: function () {\n            },\n            viewMode: core.ViewMode.SinglePage,\n        });\n    }, []);\n    var _a = React__namespace.useState(''), docId = _a[0], setDocId = _a[1];\n    var CoverDecorator = function (props) { return (React__namespace.createElement(Cover, __assign({}, props, { renderSpinner: pluginProps === null || pluginProps === void 0 ? void 0 : pluginProps.renderSpinner, store: store }))); };\n    var ThumbnailsDecorator = React__namespace.useCallback(function (props) { return (React__namespace.createElement(SpinnerContext.Provider, { value: { renderSpinner: (pluginProps === null || pluginProps === void 0 ? void 0 : pluginProps.renderSpinner) || defaultSpinner } },\n        React__namespace.createElement(ThumbnailListWithStore, { renderCurrentPageLabel: pluginProps === null || pluginProps === void 0 ? void 0 : pluginProps.renderCurrentPageLabel, renderThumbnailItem: props === null || props === void 0 ? void 0 : props.renderThumbnailItem, store: store, thumbnailDirection: (props === null || props === void 0 ? void 0 : props.thumbnailDirection) || exports.ThumbnailDirection.Vertical, thumbnailWidth: (pluginProps === null || pluginProps === void 0 ? void 0 : pluginProps.thumbnailWidth) || 100 }))); }, [docId]);\n    return {\n        install: function (pluginFunctions) {\n            store.update('jumpToPage', pluginFunctions.jumpToPage);\n            store.update('rotatePage', pluginFunctions.rotatePage);\n        },\n        onDocumentLoad: function (props) {\n            setDocId(props.doc.loadingTask.docId);\n            store.update('doc', props.doc);\n        },\n        onViewerStateChange: function (viewerState) {\n            store.update('currentPage', viewerState.pageIndex);\n            store.update('pagesRotation', viewerState.pagesRotation);\n            store.update('pageHeight', viewerState.pageHeight);\n            store.update('pageWidth', viewerState.pageWidth);\n            store.update('rotation', viewerState.rotation);\n            store.update('rotatedPage', viewerState.rotatedPage);\n            store.update('viewMode', viewerState.viewMode);\n            return viewerState;\n        },\n        Cover: CoverDecorator,\n        Thumbnails: ThumbnailsDecorator,\n    };\n};\n\nexports.thumbnailPlugin = thumbnailPlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/thumbnail.min.js');\n} else {\n    module.exports = require('./cjs/thumbnail.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar HandToolIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M11.5,5.5v-2C11.5,2.672,12.172,2,13,2s1.5,0.672,1.5,1.5v2 M14.5,11.5v-6C14.5,4.672,15.172,4,16,4\\n            c0.828,0,1.5,0.672,1.5,1.5v3 M17.5,13V8.5C17.5,7.672,18.172,7,19,7s1.5,0.672,1.5,1.5v10c0,2.761-2.239,5-5,5h-3.335\\n            c-1.712-0.001-3.305-0.876-4.223-2.321C6.22,18.467,4.083,14,4.083,14c-0.378-0.545-0.242-1.292,0.303-1.67\\n            c0.446-0.309,1.044-0.281,1.458,0.07L8.5,15.5v-10C8.5,4.672,9.172,4,10,4s1.5,0.672,1.5,1.5v6\" }))); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nexports.SelectionMode = void 0;\n(function (SelectionMode) {\n    SelectionMode[\"Hand\"] = \"Hand\";\n    SelectionMode[\"Text\"] = \"Text\";\n})(exports.SelectionMode || (exports.SelectionMode = {}));\n\nvar TextSelectionIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M13.675,11.671l2.941-2.941c0.195-0.196,0.195-0.512-0.001-0.707C16.563,7.971,16.5,7.931,16.43,7.906\\n            L4.168,3.527C3.908,3.434,3.622,3.57,3.529,3.83c-0.039,0.109-0.039,0.228,0,0.336l4.379,12.262\\n            c0.093,0.26,0.379,0.396,0.639,0.303c0.07-0.025,0.133-0.065,0.185-0.117l2.943-2.943l6.146,6.146c0.195,0.195,0.512,0.195,0.707,0\\n            l1.293-1.293c0.195-0.195,0.195-0.512,0-0.707L13.675,11.671z\" }))); };\n\nvar SwitchSelectionModeDecorator = function (_a) {\n    var children = _a.children, mode = _a.mode, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = '';\n    var icon = React__namespace.createElement(TextSelectionIcon, null);\n    switch (mode) {\n        case exports.SelectionMode.Hand:\n            label =\n                l10n && l10n.selectionMode ? l10n.selectionMode.handTool : 'Hand tool';\n            icon = React__namespace.createElement(HandToolIcon, null);\n            break;\n        case exports.SelectionMode.Text:\n        default:\n            label =\n                l10n && l10n.selectionMode\n                    ? l10n.selectionMode.textSelectionTool\n                    : 'Text selection tool';\n            icon = React__namespace.createElement(TextSelectionIcon, null);\n            break;\n    }\n    return children({ icon: icon, label: label, onClick: onClick });\n};\n\nvar TOOLTIP_OFFSET = { left: 0, top: 8 };\nvar SwitchSelectionModeButton = function (_a) {\n    var isSelected = _a.isSelected, mode = _a.mode, onClick = _a.onClick;\n    var testId = '';\n    switch (mode) {\n        case exports.SelectionMode.Hand:\n            testId = 'selection-mode__hand-button';\n            break;\n        case exports.SelectionMode.Text:\n        default:\n            testId = 'selection-mode__text-button';\n    }\n    return (React__namespace.createElement(SwitchSelectionModeDecorator, { mode: mode, onClick: onClick }, function (props) { return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"selection-mode-switch\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: props.label, isSelected: isSelected, testId: testId, onClick: props.onClick }, props.icon), content: function () { return props.label; }, offset: TOOLTIP_OFFSET })); }));\n};\n\nvar SwitchSelectionMode = function (_a) {\n    var children = _a.children, mode = _a.mode, store = _a.store;\n    var onClick = function () { return store.update('selectionMode', mode); };\n    var isSelected = mode === store.get('selectionMode');\n    var defaultChildren = function (props) { return (React__namespace.createElement(SwitchSelectionModeButton, { isSelected: isSelected, mode: props.mode, onClick: props.onClick })); };\n    var render = children || defaultChildren;\n    return render({\n        isSelected: isSelected,\n        mode: mode,\n        onClick: onClick,\n    });\n};\n\nvar SwitchSelectionModeMenuItem = function (_a) {\n    var isSelected = _a.isSelected, mode = _a.mode, onClick = _a.onClick;\n    var testId = '';\n    switch (mode) {\n        case exports.SelectionMode.Hand:\n            testId = 'selection-mode__hand-menu';\n            break;\n        case exports.SelectionMode.Text:\n        default:\n            testId = 'selection-mode__text-menu';\n    }\n    return (React__namespace.createElement(SwitchSelectionModeDecorator, { mode: mode, onClick: onClick }, function (props) { return (React__namespace.createElement(core.MenuItem, { checked: isSelected, icon: props.icon, testId: testId, onClick: props.onClick }, props.label)); }));\n};\n\nvar Tracker = function (_a) {\n    var store = _a.store;\n    var pagesRef = React__namespace.useRef(null);\n    var _b = React__namespace.useState(exports.SelectionMode.Text), selectionMode = _b[0], setSelectionMode = _b[1];\n    var pos = React__namespace.useRef({ top: 0, left: 0, x: 0, y: 0 });\n    var onMouseMoveHandler = function (e) {\n        var ele = pagesRef.current;\n        if (!ele) {\n            return;\n        }\n        ele.scrollTop = pos.current.top - (e.clientY - pos.current.y);\n        ele.scrollLeft = pos.current.left - (e.clientX - pos.current.x);\n    };\n    var onMouseUpHandler = function () {\n        var ele = pagesRef.current;\n        if (!ele) {\n            return;\n        }\n        ele.classList.add('rpv-selection-mode__grab');\n        ele.classList.remove('rpv-selection-mode__grabbing');\n        document.removeEventListener('mousemove', onMouseMoveHandler);\n        document.removeEventListener('mouseup', onMouseUpHandler);\n    };\n    var onMouseDownHandler = function (e) {\n        var ele = pagesRef.current;\n        if (!ele || selectionMode === exports.SelectionMode.Text) {\n            return;\n        }\n        ele.classList.remove('rpv-selection-mode__grab');\n        ele.classList.add('rpv-selection-mode__grabbing');\n        e.preventDefault();\n        e.stopPropagation();\n        pos.current = {\n            left: ele.scrollLeft,\n            top: ele.scrollTop,\n            x: e.clientX,\n            y: e.clientY,\n        };\n        document.addEventListener('mousemove', onMouseMoveHandler);\n        document.addEventListener('mouseup', onMouseUpHandler);\n    };\n    var handlePagesContainer = function (getPagesContainer) {\n        pagesRef.current = getPagesContainer();\n    };\n    var handleSelectionModeChanged = function (mode) {\n        setSelectionMode(mode);\n    };\n    React__namespace.useEffect(function () {\n        var ele = pagesRef.current;\n        if (!ele) {\n            return;\n        }\n        selectionMode === exports.SelectionMode.Hand\n            ? ele.classList.add('rpv-selection-mode__grab')\n            : ele.classList.remove('rpv-selection-mode__grab');\n        ele.addEventListener('mousedown', onMouseDownHandler);\n        return function () {\n            ele.removeEventListener('mousedown', onMouseDownHandler);\n        };\n    }, [selectionMode]);\n    React__namespace.useEffect(function () {\n        store.subscribe('getPagesContainer', handlePagesContainer);\n        store.subscribe('selectionMode', handleSelectionModeChanged);\n        return function () {\n            store.unsubscribe('getPagesContainer', handlePagesContainer);\n            store.unsubscribe('selectionMode', handleSelectionModeChanged);\n        };\n    }, []);\n    return React__namespace.createElement(React__namespace.Fragment, null);\n};\n\nvar selectionModePlugin = function (props) {\n    var store = React__namespace.useMemo(function () { return core.createStore(); }, []);\n    var SwitchSelectionModeDecorator = function (props) { return (React__namespace.createElement(SwitchSelectionMode, __assign({}, props, { store: store }))); };\n    var SwitchSelectionModeButtonDecorator = function (props) { return (React__namespace.createElement(SwitchSelectionModeDecorator, { mode: props.mode }, function (p) { return (React__namespace.createElement(SwitchSelectionModeButton, { isSelected: p.isSelected, mode: p.mode, onClick: function () {\n            p.onClick();\n        } })); })); };\n    var SwitchSelectionModeMenuItemDecorator = function (props) { return (React__namespace.createElement(SwitchSelectionModeDecorator, { mode: props.mode }, function (p) { return (React__namespace.createElement(SwitchSelectionModeMenuItem, { isSelected: p.isSelected, mode: p.mode, onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    var renderViewer = function (props) {\n        var currentSlot = props.slot;\n        if (currentSlot.subSlot && currentSlot.subSlot.children) {\n            currentSlot.subSlot.children = (React__namespace.createElement(React__namespace.Fragment, null,\n                React__namespace.createElement(Tracker, { store: store }),\n                currentSlot.subSlot.children));\n        }\n        return currentSlot;\n    };\n    return {\n        install: function (pluginFunctions) {\n            store.update('selectionMode', props && props.selectionMode ? props.selectionMode : exports.SelectionMode.Text);\n            store.update('getPagesContainer', pluginFunctions.getPagesContainer);\n        },\n        renderViewer: renderViewer,\n        SwitchSelectionMode: SwitchSelectionModeDecorator,\n        SwitchSelectionModeButton: SwitchSelectionModeButtonDecorator,\n        SwitchSelectionModeMenuItem: SwitchSelectionModeMenuItemDecorator,\n    };\n};\n\nexports.HandToolIcon = HandToolIcon;\nexports.TextSelectionIcon = TextSelectionIcon;\nexports.selectionModePlugin = selectionModePlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/selection-mode.min.js');\n} else {\n    module.exports = require('./cjs/selection-mode.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar ExitFullScreenIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M11.5 23.499L11.5 14.499\" }),\n    React__namespace.createElement(\"path\", { d: \"M7.5 18.499L11.5 14.499 15.5 18.499\" }),\n    React__namespace.createElement(\"path\", { d: \"M11.5 1.499L11.5 10.499\" }),\n    React__namespace.createElement(\"path\", { d: \"M7.5 6.499L11.5 10.499 15.5 6.499\" }),\n    React__namespace.createElement(\"path\", { d: \"M20.5 12.499L1.5 12.499\" }))); };\n\nvar FullScreenIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M0.5 12L23.5 12\" }),\n    React__namespace.createElement(\"path\", { d: \"M11.5 1L11.5 23\" }),\n    React__namespace.createElement(\"path\", { d: \"M8.5 4L11.5 1 14.5 4\" }),\n    React__namespace.createElement(\"path\", { d: \"M20.5 9L23.5 12 20.5 15\" }),\n    React__namespace.createElement(\"path\", { d: \"M3.5 15L0.5 12 3.5 9\" }),\n    React__namespace.createElement(\"path\", { d: \"M14.5 20L11.5 23 8.5 20\" }))); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar TOOLTIP_OFFSET$1 = { left: 0, top: 8 };\nvar EnterFullScreenButton = function (_a) {\n    var enableShortcuts = _a.enableShortcuts, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.fullScreen ? l10n.fullScreen.enterFullScreen : 'Full screen';\n    var ariaKeyShortcuts = enableShortcuts ? (core.isMac() ? 'Meta+Ctrl+F' : 'F11') : '';\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"full-screen-enter\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaKeyShortcuts: ariaKeyShortcuts, ariaLabel: label, isDisabled: !core.isFullScreenEnabled(), testId: \"full-screen__enter-button\", onClick: onClick },\n            React__namespace.createElement(FullScreenIcon, null)), content: function () { return label; }, offset: TOOLTIP_OFFSET$1 }));\n};\n\nvar TOOLTIP_OFFSET = { left: 0, top: 8 };\nvar ExitFullScreenButtonWithTooltip = function (_a) {\n    var onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var exitFullScreenLabel = l10n && l10n.fullScreen ? l10n.fullScreen.exitFullScreen : 'Exit full screen';\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"full-screen-exit\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaKeyShortcuts: \"Esc\", ariaLabel: exitFullScreenLabel, testId: \"full-screen__exit-button-with-tooltip\", onClick: onClick },\n            React__namespace.createElement(ExitFullScreenIcon, null)), content: function () { return exitFullScreenLabel; }, offset: TOOLTIP_OFFSET }));\n};\n\nvar useEnterFullScreen = function (getFullScreenTarget, store) {\n    var _a = React__namespace.useState(store.get('fullScreenMode')), fullScreenMode = _a[0], setFullScreenMode = _a[1];\n    var handleFullScreenMode = React__namespace.useCallback(function (fullScreenMode) {\n        setFullScreenMode(fullScreenMode);\n    }, []);\n    var enterFullScreen = function () {\n        var pagesContainer = store.get('getPagesContainer');\n        if (!pagesContainer) {\n            return;\n        }\n        var target = getFullScreenTarget(pagesContainer());\n        store.get('enterFullScreenMode')(target);\n    };\n    var exitFullScreen = function () {\n        store.get('exitFullScreenMode')();\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('fullScreenMode', handleFullScreenMode);\n        return function () {\n            store.unsubscribe('fullScreenMode', handleFullScreenMode);\n        };\n    }, []);\n    return {\n        enterFullScreen: enterFullScreen,\n        exitFullScreen: exitFullScreen,\n        isFullScreen: fullScreenMode === core.FullScreenMode.Entering || fullScreenMode === core.FullScreenMode.EnteredCompletely,\n    };\n};\n\nvar EnterFullScreen = function (_a) {\n    var children = _a.children, enableShortcuts = _a.enableShortcuts, getFullScreenTarget = _a.getFullScreenTarget, store = _a.store;\n    var _b = useEnterFullScreen(getFullScreenTarget, store), enterFullScreen = _b.enterFullScreen, exitFullScreen = _b.exitFullScreen, isFullScreen = _b.isFullScreen;\n    var defaultChildren = function (props) {\n        return isFullScreen ? (React__namespace.createElement(ExitFullScreenButtonWithTooltip, { onClick: props.onClick })) : (React__namespace.createElement(EnterFullScreenButton, { enableShortcuts: enableShortcuts, onClick: props.onClick }));\n    };\n    var render = children || defaultChildren;\n    return render({\n        onClick: isFullScreen ? exitFullScreen : enterFullScreen,\n    });\n};\n\nvar EnterFullScreenMenuItem = function (_a) {\n    var onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.fullScreen ? l10n.fullScreen.enterFullScreen : 'Full screen';\n    return (React__namespace.createElement(core.MenuItem, { icon: React__namespace.createElement(FullScreenIcon, null), isDisabled: !core.isFullScreenEnabled(), testId: \"full-screen__enter-menu\", onClick: onClick }, label));\n};\n\nvar ExitFullScreenButton = function (_a) {\n    var onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var direction = React__namespace.useContext(core.ThemeContext).direction;\n    var isRtl = direction === core.TextDirection.RightToLeft;\n    var exitFullScreenLabel = l10n && l10n.fullScreen ? l10n.fullScreen.exitFullScreen : 'Exit full screen';\n    return (React__namespace.createElement(\"div\", { className: core.classNames({\n            'rpv-full-screen__exit-button': true,\n            'rpv-full-screen__exit-button--ltr': !isRtl,\n            'rpv-full-screen__exit-button--rtl': isRtl,\n        }) },\n        React__namespace.createElement(core.MinimalButton, { ariaLabel: exitFullScreenLabel, testId: \"full-screen__exit-button\", onClick: onClick },\n            React__namespace.createElement(ExitFullScreenIcon, null))));\n};\n\nvar ExitFullScreen = function (_a) {\n    var children = _a.children, getFullScreenTarget = _a.getFullScreenTarget, store = _a.store;\n    var _b = useEnterFullScreen(getFullScreenTarget, store), enterFullScreen = _b.enterFullScreen, exitFullScreen = _b.exitFullScreen, isFullScreen = _b.isFullScreen;\n    var defaultChildren = function (props) { return React__namespace.createElement(ExitFullScreenButton, { onClick: props.onClick }); };\n    var render = children || defaultChildren;\n    return (isFullScreen &&\n        render({\n            onClick: isFullScreen ? exitFullScreen : enterFullScreen,\n        }));\n};\n\nvar FullScreenModeTracker = function (_a) {\n    var store = _a.store, onEnterFullScreen = _a.onEnterFullScreen, onExitFullScreen = _a.onExitFullScreen;\n    var _b = React__namespace.useState(store.get('fullScreenMode')), fullScreenMode = _b[0], setFullScreenMode = _b[1];\n    var handleFullScreenMode = React__namespace.useCallback(function (fullScreenMode) {\n        setFullScreenMode(fullScreenMode);\n    }, []);\n    var handleEnteredFullScreen = function () {\n        onEnterFullScreen(store.get('zoom'));\n    };\n    var handleExitedFullScreen = function () {\n        onExitFullScreen(store.get('zoom'));\n    };\n    React__namespace.useEffect(function () {\n        switch (fullScreenMode) {\n            case core.FullScreenMode.EnteredCompletely:\n                handleEnteredFullScreen();\n                break;\n            case core.FullScreenMode.Exited:\n                handleExitedFullScreen();\n                break;\n        }\n    }, [fullScreenMode]);\n    React__namespace.useEffect(function () {\n        store.subscribe('fullScreenMode', handleFullScreenMode);\n        return function () {\n            store.unsubscribe('fullScreenMode', handleFullScreenMode);\n        };\n    }, []);\n    return ((fullScreenMode === core.FullScreenMode.Entering || fullScreenMode === core.FullScreenMode.Entered) && (React__namespace.createElement(\"div\", { className: \"rpv-full-screen__overlay\" },\n        React__namespace.createElement(core.Spinner, null))));\n};\n\nvar ShortcutHandler = function (_a) {\n    var containerRef = _a.containerRef, getFullScreenTarget = _a.getFullScreenTarget, store = _a.store;\n    var enterFullScreen = useEnterFullScreen(getFullScreenTarget, store).enterFullScreen;\n    var keydownHandler = function (e) {\n        if (e.shiftKey || e.altKey) {\n            return;\n        }\n        var areShortcutsPressed = core.isMac() ? e.metaKey && e.ctrlKey && e.key === 'f' : e.key === 'F11';\n        if (!areShortcutsPressed) {\n            return;\n        }\n        var containerEle = containerRef.current;\n        if (!containerEle || !document.activeElement || !containerEle.contains(document.activeElement)) {\n            return;\n        }\n        e.preventDefault();\n        enterFullScreen();\n    };\n    React__namespace.useEffect(function () {\n        var containerEle = containerRef.current;\n        if (!containerEle) {\n            return;\n        }\n        document.addEventListener('keydown', keydownHandler);\n        return function () {\n            document.removeEventListener('keydown', keydownHandler);\n        };\n    }, [containerRef.current]);\n    return React__namespace.createElement(React__namespace.Fragment, null);\n};\n\nvar fullScreenPlugin = function (props) {\n    var defaultFullScreenTarget = function (ele) { return ele; };\n    var getFullScreenTarget = (props === null || props === void 0 ? void 0 : props.getFullScreenTarget) || defaultFullScreenTarget;\n    var fullScreenPluginProps = React__namespace.useMemo(function () {\n        return Object.assign({}, { enableShortcuts: true, onEnterFullScreen: function () { }, onExitFullScreen: function () { } }, props);\n    }, []);\n    var store = React__namespace.useMemo(function () {\n        return core.createStore({\n            enterFullScreenMode: function () { },\n            exitFullScreenMode: function () { },\n            fullScreenMode: core.FullScreenMode.Normal,\n            zoom: function () { },\n        });\n    }, []);\n    var EnterFullScreenDecorator = function (props) { return (React__namespace.createElement(EnterFullScreen, __assign({}, props, { enableShortcuts: fullScreenPluginProps.enableShortcuts, getFullScreenTarget: getFullScreenTarget, store: store }))); };\n    var EnterFullScreenButtonDecorator = function () { return (React__namespace.createElement(EnterFullScreenDecorator, null, function (renderProps) { return (React__namespace.createElement(EnterFullScreenButton, __assign({ enableShortcuts: fullScreenPluginProps.enableShortcuts }, renderProps))); })); };\n    var EnterFullScreenMenuItemDecorator = function (props) { return (React__namespace.createElement(EnterFullScreenDecorator, null, function (p) { return (React__namespace.createElement(EnterFullScreenMenuItem, { onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    var ExitFullScreenDecorator = function () { return (React__namespace.createElement(ExitFullScreen, { getFullScreenTarget: getFullScreenTarget, store: store }, props === null || props === void 0 ? void 0 : props.renderExitFullScreenButton)); };\n    var renderViewer = function (props) {\n        var currentSlot = props.slot;\n        if (currentSlot.subSlot) {\n            currentSlot.subSlot.children = (React__namespace.createElement(React__namespace.Fragment, null,\n                fullScreenPluginProps.enableShortcuts && (React__namespace.createElement(ShortcutHandler, { containerRef: props.containerRef, getFullScreenTarget: getFullScreenTarget, store: store })),\n                React__namespace.createElement(FullScreenModeTracker, { store: store, onEnterFullScreen: fullScreenPluginProps.onEnterFullScreen, onExitFullScreen: fullScreenPluginProps.onExitFullScreen }),\n                React__namespace.createElement(ExitFullScreenDecorator, null),\n                currentSlot.subSlot.children));\n        }\n        return currentSlot;\n    };\n    return {\n        install: function (pluginFunctions) {\n            store.update('enterFullScreenMode', pluginFunctions.enterFullScreenMode);\n            store.update('exitFullScreenMode', pluginFunctions.exitFullScreenMode);\n            store.update('getPagesContainer', pluginFunctions.getPagesContainer);\n            store.update('zoom', pluginFunctions.zoom);\n        },\n        onViewerStateChange: function (viewerState) {\n            store.update('fullScreenMode', viewerState.fullScreenMode);\n            return viewerState;\n        },\n        renderViewer: renderViewer,\n        EnterFullScreen: EnterFullScreenDecorator,\n        EnterFullScreenButton: EnterFullScreenButtonDecorator,\n        EnterFullScreenMenuItem: EnterFullScreenMenuItemDecorator,\n    };\n};\n\nexports.ExitFullScreenIcon = ExitFullScreenIcon;\nexports.FullScreenIcon = FullScreenIcon;\nexports.fullScreenPlugin = fullScreenPlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/full-screen.min.js');\n} else {\n    module.exports = require('./cjs/full-screen.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar DownloadIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M5.5,11.5c-.275,0-.341.159-.146.354l6.292,6.293a.5.5,0,0,0,.709,0l6.311-6.275c.2-.193.13-.353-.145-.355L15.5,11.5V1.5a1,1,0,0,0-1-1h-5a1,1,0,0,0-1,1V11a.5.5,0,0,1-.5.5Z\" }),\n    React__namespace.createElement(\"path\", { d: \"M23.5,18.5v4a1,1,0,0,1-1,1H1.5a1,1,0,0,1-1-1v-4\" }))); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar TOOLTIP_OFFSET = { left: 0, top: 8 };\nvar DownloadButton = function (_a) {\n    var onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.download ? l10n.download.download : 'Download';\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"get-file\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: label, testId: \"get-file__download-button\", onClick: onClick },\n            React__namespace.createElement(DownloadIcon, null)), content: function () { return label; }, offset: TOOLTIP_OFFSET }));\n};\n\nvar isChromeIOS = function () { return /iphone|ipod|ipad/i.test(navigator.userAgent) && /CriOS/i.test(navigator.userAgent); };\nvar isSafariIOS = function () { return /iphone|ipod|ipad/i.test(navigator.userAgent) && !/CriOS/i.test(navigator.userAgent); };\nvar encodeUint8Array = function (data) {\n    return btoa(Array(data.length)\n        .fill('')\n        .map(function (_, i) { return String.fromCharCode(data[i]); })\n        .join(''));\n};\nvar download = function (url, saveAs) {\n    var link = document.createElement('a');\n    link.style.display = 'none';\n    link.href = url;\n    link.setAttribute('download', saveAs);\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n};\nvar downloadBlob = function (data, saveAs, mimeType) {\n    var blobUrl = URL.createObjectURL(new Blob([data], { type: mimeType }));\n    download(blobUrl, saveAs);\n    if (blobUrl) {\n        URL.revokeObjectURL(blobUrl);\n    }\n    return;\n};\nvar downloadFile = function (doc, saveAs) {\n    doc.getData().then(function (data) {\n        isSafariIOS()\n            ?\n                downloadBlob(data, saveAs, 'application/octet-stream')\n            : isChromeIOS()\n                ? download(\"data:application/pdf;base64,\".concat(encodeUint8Array(data)), saveAs)\n                : downloadBlob(data, saveAs, 'application/pdf');\n    });\n};\n\nvar Download = function (_a) {\n    var children = _a.children, fileNameGenerator = _a.fileNameGenerator, store = _a.store;\n    var _b = React__namespace.useState(store.get('file')), currentFile = _b[0], setCurrentFile = _b[1];\n    var _c = React__namespace.useState(store.get('doc')), currentDocument = _c[0], setCurrentDocument = _c[1];\n    var handleDocumentChanged = function (doc) {\n        setCurrentDocument(doc);\n    };\n    var handleFileChanged = function (file) {\n        setCurrentFile(file);\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('doc', handleDocumentChanged);\n        store.subscribe('file', handleFileChanged);\n        return function () {\n            store.subscribe('doc', handleDocumentChanged);\n            store.unsubscribe('file', handleFileChanged);\n        };\n    }, []);\n    var download = function () {\n        if (currentDocument && currentFile) {\n            downloadFile(currentDocument, fileNameGenerator(currentFile));\n        }\n    };\n    var defaultChildren = function (props) { return React__namespace.createElement(DownloadButton, { onClick: props.onClick }); };\n    var render = children || defaultChildren;\n    return render({\n        onClick: download,\n    });\n};\n\nvar DownloadMenuItem = function (_a) {\n    var onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.download ? l10n.download.download : 'Download';\n    return (React__namespace.createElement(core.MenuItem, { icon: React__namespace.createElement(DownloadIcon, null), testId: \"get-file__download-menu\", onClick: onClick }, label));\n};\n\nvar getFileName = function (url) {\n    var str = url.split('/').pop();\n    return str ? str.split('#')[0].split('?')[0] : url;\n};\n\nvar getFilePlugin = function (props) {\n    var store = React__namespace.useMemo(function () { return core.createStore({}); }, []);\n    var defaultFileNameGenerator = function (file) { return (file.name ? getFileName(file.name) : 'document.pdf'); };\n    var DownloadDecorator = function (downloadProps) { return (React__namespace.createElement(Download, __assign({}, downloadProps, { fileNameGenerator: props ? props.fileNameGenerator || defaultFileNameGenerator : defaultFileNameGenerator, store: store }))); };\n    var DownloadButtonDecorator = function () { return (React__namespace.createElement(DownloadDecorator, null, function (props) { return React__namespace.createElement(DownloadButton, __assign({}, props)); })); };\n    var DownloadMenuItemDecorator = function (props) { return (React__namespace.createElement(DownloadDecorator, null, function (p) { return (React__namespace.createElement(DownloadMenuItem, { onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    return {\n        onDocumentLoad: function (props) {\n            store.update('doc', props.doc);\n            store.update('file', props.file);\n        },\n        Download: DownloadDecorator,\n        DownloadButton: DownloadButtonDecorator,\n        DownloadMenuItem: DownloadMenuItemDecorator,\n    };\n};\n\nexports.DownloadIcon = DownloadIcon;\nexports.getFilePlugin = getFilePlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/get-file.min.js');\n} else {\n    module.exports = require('./cjs/get-file.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar OpenFileIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M18.5,7.5c.275,0,.341-.159.146-.354L12.354.854a.5.5,0,0,0-.708,0L5.354,7.147c-.2.195-.129.354.146.354h3v10a1,1,0,0,0,1,1h5a1,1,0,0,0,1-1V7.5Z\" }),\n    React__namespace.createElement(\"path\", { d: \"M23.5,18.5v4a1,1,0,0,1-1,1H1.5a1,1,0,0,1-1-1v-4\" }))); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar useTriggerOpen = function (store) {\n    var inputRef = React__namespace.useRef();\n    var openFile = function () {\n        var inputEle = inputRef.current;\n        if (inputEle) {\n            inputEle.click();\n            if (store.get('triggerOpenFile')) {\n                store.update('triggerOpenFile', false);\n            }\n        }\n    };\n    var handleOpenFileTriggered = function (trigger) {\n        if (trigger) {\n            openFile();\n        }\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('triggerOpenFile', handleOpenFileTriggered);\n        return function () {\n            store.unsubscribe('triggerOpenFile', handleOpenFileTriggered);\n        };\n    }, []);\n    return {\n        inputRef: inputRef,\n        openFile: openFile,\n    };\n};\n\nvar TOOLTIP_OFFSET = { left: 0, top: 8 };\nvar OpenButton = function (_a) {\n    var enableShortcuts = _a.enableShortcuts, store = _a.store, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.open ? l10n.open.openFile : 'Open file';\n    var _b = useTriggerOpen(store), inputRef = _b.inputRef, openFile = _b.openFile;\n    var ariaKeyShortcuts = enableShortcuts ? (core.isMac() ? 'Meta+O' : 'Ctrl+O') : '';\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"open\", position: core.Position.BottomCenter, target: React__namespace.createElement(\"div\", { className: \"rpv-open__input-wrapper\" },\n            React__namespace.createElement(\"input\", { accept: \".pdf\", ref: inputRef, className: \"rpv-open__input\", multiple: false, tabIndex: -1, title: \"\", type: \"file\", onChange: onClick }),\n            React__namespace.createElement(core.MinimalButton, { ariaKeyShortcuts: ariaKeyShortcuts, ariaLabel: label, testId: \"open__button\", onClick: openFile },\n                React__namespace.createElement(OpenFileIcon, null))), content: function () { return label; }, offset: TOOLTIP_OFFSET }));\n};\n\nvar Open = function (_a) {\n    var children = _a.children, enableShortcuts = _a.enableShortcuts, store = _a.store;\n    var handleOpenFiles = function (e) {\n        var files = e.target.files;\n        if (!files || !files.length) {\n            return;\n        }\n        var openFile = store.get('openFile');\n        if (openFile) {\n            openFile(files[0]);\n        }\n    };\n    var defaultChildren = function (props) { return (React__namespace.createElement(OpenButton, { enableShortcuts: enableShortcuts, store: store, onClick: props.onClick })); };\n    var render = children || defaultChildren;\n    return render({\n        onClick: handleOpenFiles,\n    });\n};\n\nvar OpenMenuItem = function (_a) {\n    var store = _a.store, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.open ? l10n.open.openFile : 'Open file';\n    var _b = useTriggerOpen(store), inputRef = _b.inputRef, openFile = _b.openFile;\n    return (React__namespace.createElement(core.MenuItem, { icon: React__namespace.createElement(OpenFileIcon, null), testId: \"open__menu\", onClick: openFile },\n        React__namespace.createElement(\"div\", { className: \"rpv-open__input-wrapper\" },\n            React__namespace.createElement(\"input\", { accept: \".pdf\", ref: inputRef, className: \"rpv-open__input\", multiple: false, tabIndex: -1, title: \"\", type: \"file\", onChange: onClick }),\n            label)));\n};\n\nvar ShortcutHandler = function (_a) {\n    var containerRef = _a.containerRef, store = _a.store;\n    var keydownHandler = function (e) {\n        if (e.shiftKey || e.altKey || e.key !== 'o') {\n            return;\n        }\n        var isCommandPressed = core.isMac() ? e.metaKey : e.ctrlKey;\n        if (!isCommandPressed) {\n            return;\n        }\n        var containerEle = containerRef.current;\n        if (!containerEle || !document.activeElement || !containerEle.contains(document.activeElement)) {\n            return;\n        }\n        e.preventDefault();\n        store.update('triggerOpenFile', true);\n    };\n    React__namespace.useEffect(function () {\n        var containerEle = containerRef.current;\n        if (!containerEle) {\n            return;\n        }\n        document.addEventListener('keydown', keydownHandler);\n        return function () {\n            document.removeEventListener('keydown', keydownHandler);\n        };\n    }, [containerRef.current]);\n    return React__namespace.createElement(React__namespace.Fragment, null);\n};\n\nvar openPlugin = function (props) {\n    var openPluginProps = React__namespace.useMemo(function () { return Object.assign({}, { enableShortcuts: true }, props); }, []);\n    var store = React__namespace.useMemo(function () { return core.createStore({}); }, []);\n    var OpenDecorator = function (props) { return (React__namespace.createElement(Open, __assign({ enableShortcuts: openPluginProps.enableShortcuts }, props, { store: store }))); };\n    var OpenButtonDecorator = function () { return React__namespace.createElement(OpenDecorator, null); };\n    var OpenMenuItemDecorator = function () { return (React__namespace.createElement(OpenDecorator, null, function (p) { return React__namespace.createElement(OpenMenuItem, { store: store, onClick: p.onClick }); })); };\n    var renderViewer = function (props) {\n        var slot = props.slot;\n        var updateSlot = {\n            children: (React__namespace.createElement(React__namespace.Fragment, null,\n                openPluginProps.enableShortcuts && (React__namespace.createElement(ShortcutHandler, { containerRef: props.containerRef, store: store })),\n                slot.children)),\n        };\n        return __assign(__assign({}, slot), updateSlot);\n    };\n    return {\n        install: function (pluginFunctions) {\n            store.update('openFile', pluginFunctions.openFile);\n        },\n        renderViewer: renderViewer,\n        Open: OpenDecorator,\n        OpenButton: OpenButtonDecorator,\n        OpenMenuItem: OpenMenuItemDecorator,\n    };\n};\n\nexports.OpenFileIcon = OpenFileIcon;\nexports.openPlugin = openPlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/open.min.js');\n} else {\n    module.exports = require('./cjs/open.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar DownArrowIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M2.32,2.966h19.452c0.552,0.001,1,0.449,0.999,1.001c0,0.182-0.05,0.36-0.144,0.516L12.9,20.552\\n            c-0.286,0.472-0.901,0.624-1.373,0.338c-0.138-0.084-0.254-0.2-0.338-0.338L1.465,4.483C1.179,4.01,1.331,3.396,1.804,3.11\\n            C1.96,3.016,2.138,2.966,2.32,2.966z\" }))); };\n\nvar NextIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M0.541,5.627L11.666,18.2c0.183,0.207,0.499,0.226,0.706,0.043c0.015-0.014,0.03-0.028,0.043-0.043\\n            L23.541,5.627\" }))); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar useCurrentPage = function (store) {\n    var _a = React__namespace.useState(store.get('currentPage') || 0), currentPage = _a[0], setCurrentPage = _a[1];\n    var handleCurrentPageChanged = function (currentPageIndex) {\n        setCurrentPage(currentPageIndex);\n    };\n    core.useIsomorphicLayoutEffect(function () {\n        store.subscribe('currentPage', handleCurrentPageChanged);\n        return function () {\n            store.unsubscribe('currentPage', handleCurrentPageChanged);\n        };\n    }, []);\n    return { currentPage: currentPage };\n};\n\nvar useNumberOfPages = function (store) {\n    var _a = React__namespace.useState(store.get('numberOfPages') || 0), numberOfPages = _a[0], setNumberOfPages = _a[1];\n    var handleNumberOfPages = function (total) {\n        setNumberOfPages(total);\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('numberOfPages', handleNumberOfPages);\n        return function () {\n            store.unsubscribe('numberOfPages', handleNumberOfPages);\n        };\n    }, []);\n    return { numberOfPages: numberOfPages };\n};\n\nvar CurrentPageInput = function (_a) {\n    var store = _a.store;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var _b = React__namespace.useState('1'), editingPage = _b[0], setEditingPage = _b[1];\n    var currentPage = useCurrentPage(store).currentPage;\n    var numberOfPages = useNumberOfPages(store).numberOfPages;\n    React__namespace.useEffect(function () { return setEditingPage(\"\".concat(currentPage + 1)); }, [currentPage]);\n    var gotoNextPage = function () {\n        var nextPage = currentPage + 1;\n        if (nextPage < numberOfPages) {\n            setEditingPage(\"\".concat(nextPage + 1));\n            jumpTo(nextPage);\n        }\n    };\n    var gotoPreviousPage = function () {\n        var previousPage = currentPage - 1;\n        if (previousPage >= 0) {\n            setEditingPage(\"\".concat(previousPage + 1));\n            jumpTo(previousPage);\n        }\n    };\n    var jumpTo = function (page) {\n        var jumpToPage = store.get('jumpToPage');\n        if (jumpToPage) {\n            jumpToPage(page);\n        }\n    };\n    var jump = function () {\n        var newPage = parseInt(editingPage, 10);\n        editingPage === '' || newPage < 1 || newPage > numberOfPages\n            ? setEditingPage(\"\".concat(currentPage + 1))\n            : jumpTo(newPage - 1);\n    };\n    var keydownPage = function (e) {\n        switch (e.key) {\n            case 'ArrowUp':\n                gotoPreviousPage();\n                break;\n            case 'ArrowDown':\n                gotoNextPage();\n                break;\n            case 'Enter':\n                jump();\n                break;\n        }\n    };\n    var label = l10n && l10n.pageNavigation\n        ? l10n.pageNavigation.enterPageNumber\n        : 'Enter a page number';\n    return (React__namespace.createElement(\"span\", { className: \"rpv-page-navigation__current-page-input\" },\n        React__namespace.createElement(core.TextBox, { ariaLabel: label, testId: \"page-navigation__current-page-input\", type: \"text\", value: editingPage, onChange: setEditingPage, onKeyDown: keydownPage })));\n};\n\nvar FetchLabels = function (_a) {\n    var children = _a.children, doc = _a.doc;\n    var isMounted = core.useIsMounted();\n    var _b = React__namespace.useState({\n        loading: true,\n        labels: [],\n    }), status = _b[0], setStatus = _b[1];\n    React__namespace.useEffect(function () {\n        doc.getPageLabels().then(function (result) {\n            isMounted.current && setStatus({ loading: false, labels: result || [] });\n        });\n    }, [doc.loadingTask.docId]);\n    return status.loading ? React__namespace.createElement(React__namespace.Fragment, null) : children(status.labels);\n};\n\nvar useDocument = function (store) {\n    var _a = React__namespace.useState(store.get('doc')), currentDoc = _a[0], setCurrentDoc = _a[1];\n    var handleDocumentChanged = function (doc) {\n        setCurrentDoc(doc);\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('doc', handleDocumentChanged);\n        return function () {\n            store.unsubscribe('doc', handleDocumentChanged);\n        };\n    }, []);\n    return currentDoc;\n};\n\nvar CurrentPageLabel = function (_a) {\n    var children = _a.children, store = _a.store;\n    var currentDoc = useDocument(store);\n    var currentPage = useCurrentPage(store).currentPage;\n    var numberOfPages = useNumberOfPages(store).numberOfPages;\n    var defaultChildren = function (props) { return React__namespace.createElement(React__namespace.Fragment, null, props.currentPage + 1); };\n    var render = children || defaultChildren;\n    return currentDoc ? (React__namespace.createElement(FetchLabels, { doc: currentDoc }, function (labels) {\n        var pageLabel = labels.length === numberOfPages && numberOfPages > 0 ? labels[currentPage] : '';\n        return render({\n            currentPage: currentPage,\n            numberOfPages: numberOfPages,\n            pageLabel: pageLabel,\n        });\n    })) : (React__namespace.createElement(React__namespace.Fragment, null));\n};\n\nvar UpArrowIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M21.783,21.034H2.332c-0.552,0-1-0.448-1-1c0-0.182,0.05-0.361,0.144-0.517L11.2,3.448\\n            c0.286-0.472,0.901-0.624,1.373-0.338c0.138,0.084,0.254,0.2,0.338,0.338l9.726,16.069c0.286,0.473,0.134,1.087-0.339,1.373\\n            C22.143,20.984,21.965,21.034,21.783,21.034z\" }))); };\n\nvar TOOLTIP_OFFSET$3 = { left: 0, top: 8 };\nvar GoToFirstPageButton = function (_a) {\n    var isDisabled = _a.isDisabled, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.pageNavigation ? l10n.pageNavigation.goToFirstPage : 'First page';\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"page-navigation-first\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: label, isDisabled: isDisabled, testId: \"page-navigation__first-button\", onClick: onClick },\n            React__namespace.createElement(UpArrowIcon, null)), content: function () { return label; }, offset: TOOLTIP_OFFSET$3 }));\n};\n\nvar GoToFirstPage = function (_a) {\n    var children = _a.children, store = _a.store;\n    var currentPage = useCurrentPage(store).currentPage;\n    var goToFirstPage = function () {\n        var jumpToPage = store.get('jumpToPage');\n        if (jumpToPage) {\n            jumpToPage(0);\n        }\n    };\n    var defaultChildren = function (props) { return (React__namespace.createElement(GoToFirstPageButton, { isDisabled: props.isDisabled, onClick: props.onClick })); };\n    var render = children || defaultChildren;\n    return render({\n        isDisabled: currentPage === 0,\n        onClick: goToFirstPage,\n    });\n};\n\nvar GoToFirstPageMenuItem = function (_a) {\n    var isDisabled = _a.isDisabled, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.pageNavigation ? l10n.pageNavigation.goToFirstPage : 'First page';\n    return (React__namespace.createElement(core.MenuItem, { icon: React__namespace.createElement(UpArrowIcon, null), isDisabled: isDisabled, testId: \"page-navigation__first-menu\", onClick: onClick }, label));\n};\n\nvar TOOLTIP_OFFSET$2 = { left: 0, top: 8 };\nvar GoToLastPageButton = function (_a) {\n    var isDisabled = _a.isDisabled, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.pageNavigation ? l10n.pageNavigation.goToLastPage : 'Last page';\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"page-navigation-last\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: label, isDisabled: isDisabled, testId: \"page-navigation__last-button\", onClick: onClick },\n            React__namespace.createElement(DownArrowIcon, null)), content: function () { return label; }, offset: TOOLTIP_OFFSET$2 }));\n};\n\nvar GoToLastPage = function (_a) {\n    var children = _a.children, store = _a.store;\n    var currentPage = useCurrentPage(store).currentPage;\n    var numberOfPages = useNumberOfPages(store).numberOfPages;\n    var goToLastPage = function () {\n        var jumpToPage = store.get('jumpToPage');\n        if (jumpToPage) {\n            jumpToPage(numberOfPages - 1);\n        }\n    };\n    var defaultChildren = function (props) { return (React__namespace.createElement(GoToLastPageButton, { isDisabled: props.isDisabled, onClick: props.onClick })); };\n    var render = children || defaultChildren;\n    return render({\n        isDisabled: currentPage + 1 >= numberOfPages,\n        onClick: goToLastPage,\n    });\n};\n\nvar GoToLastPageMenuItem = function (_a) {\n    var isDisabled = _a.isDisabled, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.pageNavigation ? l10n.pageNavigation.goToLastPage : 'Last page';\n    return (React__namespace.createElement(core.MenuItem, { icon: React__namespace.createElement(DownArrowIcon, null), isDisabled: isDisabled, testId: \"page-navigation__last-menu\", onClick: onClick }, label));\n};\n\nvar TOOLTIP_OFFSET$1 = { left: 0, top: 8 };\nvar GoToNextPageButton = function (_a) {\n    var isDisabled = _a.isDisabled, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.pageNavigation ? l10n.pageNavigation.goToNextPage : 'Next page';\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"page-navigation-next\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: label, isDisabled: isDisabled, testId: \"page-navigation__next-button\", onClick: onClick },\n            React__namespace.createElement(NextIcon, null)), content: function () { return label; }, offset: TOOLTIP_OFFSET$1 }));\n};\n\nvar GoToNextPage = function (_a) {\n    var children = _a.children, store = _a.store;\n    var currentPage = useCurrentPage(store).currentPage;\n    var numberOfPages = useNumberOfPages(store).numberOfPages;\n    var goToNextPage = function () {\n        var jumpToNextPage = store.get('jumpToNextPage');\n        if (jumpToNextPage) {\n            jumpToNextPage();\n        }\n    };\n    var defaultChildren = function (props) { return (React__namespace.createElement(GoToNextPageButton, { onClick: props.onClick, isDisabled: props.isDisabled })); };\n    var render = children || defaultChildren;\n    return render({\n        isDisabled: currentPage + 1 >= numberOfPages,\n        onClick: goToNextPage,\n    });\n};\n\nvar GoToNextPageMenuItem = function (_a) {\n    var isDisabled = _a.isDisabled, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.pageNavigation ? l10n.pageNavigation.goToNextPage : 'Next page';\n    return (React__namespace.createElement(core.MenuItem, { icon: React__namespace.createElement(NextIcon, null), isDisabled: isDisabled, testId: \"page-navigation__next-menu\", onClick: onClick }, label));\n};\n\nvar PreviousIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M23.535,18.373L12.409,5.8c-0.183-0.207-0.499-0.226-0.706-0.043C11.688,5.77,11.674,5.785,11.66,5.8\\n            L0.535,18.373\" }))); };\n\nvar TOOLTIP_OFFSET = { left: 0, top: 8 };\nvar GoToPreviousPageButton = function (_a) {\n    var isDisabled = _a.isDisabled, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.pageNavigation\n        ? l10n.pageNavigation.goToPreviousPage\n        : 'Previous page';\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"page-navigation-previous\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: label, isDisabled: isDisabled, testId: \"page-navigation__previous-button\", onClick: onClick },\n            React__namespace.createElement(PreviousIcon, null)), content: function () { return label; }, offset: TOOLTIP_OFFSET }));\n};\n\nvar GoToPreviousPage = function (_a) {\n    var store = _a.store, children = _a.children;\n    var currentPage = useCurrentPage(store).currentPage;\n    var goToPreviousPage = function () {\n        var jumpToPreviousPage = store.get('jumpToPreviousPage');\n        if (jumpToPreviousPage) {\n            jumpToPreviousPage();\n        }\n    };\n    var defaultChildren = function (props) { return (React__namespace.createElement(GoToPreviousPageButton, { isDisabled: props.isDisabled, onClick: props.onClick })); };\n    var render = children || defaultChildren;\n    return render({\n        isDisabled: currentPage <= 0,\n        onClick: goToPreviousPage,\n    });\n};\n\nvar GoToPreviousPageMenuItem = function (_a) {\n    var isDisabled = _a.isDisabled, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.pageNavigation\n        ? l10n.pageNavigation.goToPreviousPage\n        : 'Previous page';\n    return (React__namespace.createElement(core.MenuItem, { icon: React__namespace.createElement(PreviousIcon, null), isDisabled: isDisabled, testId: \"page-navigation__previous-menu\", onClick: onClick }, label));\n};\n\nvar NumberOfPages = function (_a) {\n    var children = _a.children, store = _a.store;\n    var numberOfPages = useNumberOfPages(store).numberOfPages;\n    return children ? children({ numberOfPages: numberOfPages }) : React__namespace.createElement(React__namespace.Fragment, null, numberOfPages);\n};\n\nvar ShortcutHandler = function (_a) {\n    var containerRef = _a.containerRef, numPages = _a.numPages, store = _a.store;\n    var currentPage = useCurrentPage(store).currentPage;\n    var currentPageRef = React__namespace.useRef(currentPage);\n    currentPageRef.current = currentPage;\n    var isMouseInsideRef = React__namespace.useRef(false);\n    var handleMouseEnter = function () {\n        isMouseInsideRef.current = true;\n    };\n    var handleMouseLeave = function () {\n        isMouseInsideRef.current = false;\n    };\n    var goToNextPage = function () {\n        var jumpToPage = store.get('jumpToPage');\n        var targetPage = currentPageRef.current + 1;\n        if (jumpToPage && targetPage < numPages) {\n            jumpToPage(targetPage);\n        }\n    };\n    var goToPreviousPage = function () {\n        var jumpToPage = store.get('jumpToPage');\n        var targetPage = currentPageRef.current - 1;\n        if (jumpToPage && targetPage >= 0) {\n            jumpToPage(targetPage);\n        }\n    };\n    var jumpToNextDestination = function () {\n        var jumpToNextDestination = store.get('jumpToNextDestination');\n        if (jumpToNextDestination) {\n            jumpToNextDestination();\n        }\n    };\n    var jumpToPreviousDestination = function () {\n        var jumpToPreviousDestination = store.get('jumpToPreviousDestination');\n        if (jumpToPreviousDestination) {\n            jumpToPreviousDestination();\n        }\n    };\n    var handleKeydown = function (e) {\n        var containerEle = containerRef.current;\n        var shouldHandleShortcuts = isMouseInsideRef.current || (document.activeElement && containerEle.contains(document.activeElement));\n        if (!containerEle || !shouldHandleShortcuts) {\n            return;\n        }\n        var shouldGoToNextPage = (e.altKey && e.key === 'ArrowDown') || (!e.shiftKey && !e.altKey && e.key === 'PageDown');\n        var shouldGoToPreviousPage = (e.altKey && e.key === 'ArrowUp') || (!e.shiftKey && !e.altKey && e.key === 'PageUp');\n        if (shouldGoToNextPage) {\n            e.preventDefault();\n            goToNextPage();\n            return;\n        }\n        if (shouldGoToPreviousPage) {\n            e.preventDefault();\n            goToPreviousPage();\n            return;\n        }\n        var isCommandPressed = core.isMac() ? e.metaKey && !e.ctrlKey : e.altKey;\n        if (isCommandPressed) {\n            switch (e.key) {\n                case 'ArrowLeft':\n                    e.preventDefault();\n                    jumpToPreviousDestination();\n                    break;\n                case 'ArrowRight':\n                    e.preventDefault();\n                    jumpToNextDestination();\n                    break;\n            }\n        }\n    };\n    React__namespace.useEffect(function () {\n        var containerEle = containerRef.current;\n        if (!containerEle) {\n            return;\n        }\n        document.addEventListener('keydown', handleKeydown);\n        containerEle.addEventListener('mouseenter', handleMouseEnter);\n        containerEle.addEventListener('mouseleave', handleMouseLeave);\n        return function () {\n            document.removeEventListener('keydown', handleKeydown);\n            containerEle.removeEventListener('mouseenter', handleMouseEnter);\n            containerEle.removeEventListener('mouseleave', handleMouseLeave);\n        };\n    }, [containerRef.current]);\n    return React__namespace.createElement(React__namespace.Fragment, null);\n};\n\nvar pageNavigationPlugin = function (props) {\n    var pageNavigationPluginProps = React__namespace.useMemo(function () { return Object.assign({}, { enableShortcuts: true }, props); }, []);\n    var store = React__namespace.useMemo(function () { return core.createStore(); }, []);\n    var CurrentPageInputDecorator = function () { return React__namespace.createElement(CurrentPageInput, { store: store }); };\n    var CurrentPageLabelDecorator = function (props) { return React__namespace.createElement(CurrentPageLabel, __assign({}, props, { store: store })); };\n    var GoToFirstPageDecorator = function (props) { return React__namespace.createElement(GoToFirstPage, __assign({}, props, { store: store })); };\n    var GoToFirstPageButtonDecorator = function () { return (React__namespace.createElement(GoToFirstPageDecorator, null, function (props) { return React__namespace.createElement(GoToFirstPageButton, __assign({}, props)); })); };\n    var GoToFirstPageMenuItemDecorator = function (props) { return (React__namespace.createElement(GoToFirstPageDecorator, null, function (p) { return (React__namespace.createElement(GoToFirstPageMenuItem, { isDisabled: p.isDisabled, onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    var GoToLastPageDecorator = function (props) { return React__namespace.createElement(GoToLastPage, __assign({}, props, { store: store })); };\n    var GoToLastPageButtonDecorator = function () { return (React__namespace.createElement(GoToLastPageDecorator, null, function (props) { return React__namespace.createElement(GoToLastPageButton, __assign({}, props)); })); };\n    var GoToLastPageMenuItemDecorator = function (props) { return (React__namespace.createElement(GoToLastPageDecorator, null, function (p) { return (React__namespace.createElement(GoToLastPageMenuItem, { isDisabled: p.isDisabled, onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    var GoToNextPageDecorator = function (props) { return React__namespace.createElement(GoToNextPage, __assign({}, props, { store: store })); };\n    var GoToNextPageButtonDecorator = function () { return (React__namespace.createElement(GoToNextPageDecorator, null, function (props) { return React__namespace.createElement(GoToNextPageButton, __assign({}, props)); })); };\n    var GoToNextPageMenuItemDecorator = function (props) { return (React__namespace.createElement(GoToNextPageDecorator, null, function (p) { return (React__namespace.createElement(GoToNextPageMenuItem, { isDisabled: p.isDisabled, onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    var GoToPreviousPageDecorator = function (props) { return React__namespace.createElement(GoToPreviousPage, __assign({}, props, { store: store })); };\n    var GoToPreviousPageButtonDecorator = function () { return (React__namespace.createElement(GoToPreviousPageDecorator, null, function (props) { return React__namespace.createElement(GoToPreviousPageButton, __assign({}, props)); })); };\n    var GoToPreviousPageMenuItemDecorator = function (props) { return (React__namespace.createElement(GoToPreviousPageDecorator, null, function (p) { return (React__namespace.createElement(GoToPreviousPageMenuItem, { isDisabled: p.isDisabled, onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    var NumberOfPagesDecorator = function (props) { return React__namespace.createElement(NumberOfPages, __assign({}, props, { store: store })); };\n    var renderViewer = function (props) {\n        var slot = props.slot;\n        if (!pageNavigationPluginProps.enableShortcuts) {\n            return slot;\n        }\n        var updateSlot = {\n            children: (React__namespace.createElement(React__namespace.Fragment, null,\n                React__namespace.createElement(ShortcutHandler, { containerRef: props.containerRef, numPages: props.doc.numPages, store: store }),\n                slot.children)),\n        };\n        return __assign(__assign({}, slot), updateSlot);\n    };\n    return {\n        install: function (pluginFunctions) {\n            store.update('jumpToDestination', pluginFunctions.jumpToDestination);\n            store.update('jumpToNextDestination', pluginFunctions.jumpToNextDestination);\n            store.update('jumpToNextPage', pluginFunctions.jumpToNextPage);\n            store.update('jumpToPage', pluginFunctions.jumpToPage);\n            store.update('jumpToPreviousDestination', pluginFunctions.jumpToPreviousDestination);\n            store.update('jumpToPreviousPage', pluginFunctions.jumpToPreviousPage);\n        },\n        renderViewer: renderViewer,\n        onDocumentLoad: function (props) {\n            store.update('doc', props.doc);\n            store.update('numberOfPages', props.doc.numPages);\n        },\n        onViewerStateChange: function (viewerState) {\n            store.update('currentPage', viewerState.pageIndex);\n            return viewerState;\n        },\n        jumpToNextPage: function () {\n            var jump = store.get('jumpToNextPage');\n            if (jump) {\n                jump();\n            }\n        },\n        jumpToPage: function (pageIndex) {\n            var jumpTo = store.get('jumpToPage');\n            if (jumpTo) {\n                jumpTo(pageIndex);\n            }\n        },\n        jumpToPreviousPage: function () {\n            var jump = store.get('jumpToPreviousPage');\n            if (jump) {\n                jump();\n            }\n        },\n        CurrentPageInput: CurrentPageInputDecorator,\n        CurrentPageLabel: CurrentPageLabelDecorator,\n        GoToFirstPage: GoToFirstPageDecorator,\n        GoToFirstPageButton: GoToFirstPageButtonDecorator,\n        GoToFirstPageMenuItem: GoToFirstPageMenuItemDecorator,\n        GoToLastPage: GoToLastPageDecorator,\n        GoToLastPageButton: GoToLastPageButtonDecorator,\n        GoToLastPageMenuItem: GoToLastPageMenuItemDecorator,\n        GoToNextPage: GoToNextPageDecorator,\n        GoToNextPageButton: GoToNextPageButtonDecorator,\n        GoToNextPageMenuItem: GoToNextPageMenuItemDecorator,\n        GoToPreviousPage: GoToPreviousPageDecorator,\n        GoToPreviousPageButton: GoToPreviousPageButtonDecorator,\n        GoToPreviousPageMenuItem: GoToPreviousPageMenuItemDecorator,\n        NumberOfPages: NumberOfPagesDecorator,\n    };\n};\n\nexports.DownArrowIcon = DownArrowIcon;\nexports.NextIcon = NextIcon;\nexports.PreviousIcon = PreviousIcon;\nexports.UpArrowIcon = UpArrowIcon;\nexports.pageNavigationPlugin = pageNavigationPlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/page-navigation.min.js');\n} else {\n    module.exports = require('./cjs/page-navigation.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\nvar reactDom = require('react-dom');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar getAllPagesNumbers = function (doc) {\n    return Array(doc.numPages)\n        .fill(0)\n        .map(function (_, i) { return i; });\n};\n\nvar generateRange = function (min, max) {\n    return Array(max - min + 1)\n        .fill(0)\n        .map(function (_, i) { return min + i; });\n};\nvar removeDuplicate = function (arr) { return arr.filter(function (i) { return arr.indexOf(i) === arr.lastIndexOf(i); }); };\nvar getCustomPagesNumbers = function (customPages) {\n    return function (doc) {\n        var results = [];\n        customPages\n            .replace(/\\s+/g, '')\n            .split(',')\n            .forEach(function (part) {\n            var range = part\n                .split('-')\n                .map(function (c) { return parseInt(c, 10); })\n                .filter(function (c) { return Number.isInteger(c); });\n            if (range.length === 1) {\n                results.push(range[0] - 1);\n            }\n            else if (range.length === 2) {\n                results.push.apply(results, generateRange(range[0] - 1, range[1] - 1));\n            }\n        });\n        return removeDuplicate(results).filter(function (i) { return i >= 0 && i < doc.numPages; });\n    };\n};\n\nvar getEvenPagesNumbers = function (doc) {\n    return Array(doc.numPages)\n        .fill(0)\n        .map(function (_, i) { return i; })\n        .filter(function (i) { return (i + 1) % 2 === 0; });\n};\n\nvar getOddPagesNumbers = function (doc) {\n    return Array(doc.numPages)\n        .fill(0)\n        .map(function (_, i) { return i; })\n        .filter(function (i) { return (i + 1) % 2 === 1; });\n};\n\nvar PrintIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M7.5,19.499h9 M7.5,16.499h9 M5.5,16.5h-3c-1.103-0.003-1.997-0.897-2-2v-6c0.003-1.103,0.897-1.997,2-2h19\\n            c1.103,0.003,1.997,0.897,2,2v6c-0.003,1.103-0.897,1.997-2,2h-3\\n            M5.5,4.5v-4h9.586c0.265,0,0.52,0.105,0.707,0.293l2.414,2.414\\n            C18.395,3.394,18.5,3.649,18.5,3.914V4.5\\n            M18.5,22.5c0,0.552-0.448,1-1,1h-11c-0.552,0-1-0.448-1-1v-9h13V22.5z\\n            M3.5,8.499\\n            c0.552,0,1,0.448,1,1s-0.448,1-1,1s-1-0.448-1-1S2.948,8.499,3.5,8.499z\\n            M14.5,0.499v4h4\" }))); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar TOOLTIP_OFFSET = { left: 0, top: 8 };\nvar PrintButton = function (_a) {\n    var enableShortcuts = _a.enableShortcuts, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.print ? l10n.print.print : 'Print';\n    var ariaKeyShortcuts = enableShortcuts ? (core.isMac() ? 'Meta+P' : 'Ctrl+P') : '';\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"print\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaKeyShortcuts: ariaKeyShortcuts, ariaLabel: label, testId: \"print__button\", onClick: onClick },\n            React__namespace.createElement(PrintIcon, null)), content: function () { return label; }, offset: TOOLTIP_OFFSET }));\n};\n\nvar PrintStatus;\n(function (PrintStatus) {\n    PrintStatus[\"CheckingPermission\"] = \"CheckingPermission\";\n    PrintStatus[\"Inactive\"] = \"Inactive\";\n    PrintStatus[\"Preparing\"] = \"Preparing\";\n    PrintStatus[\"Cancelled\"] = \"Cancelled\";\n    PrintStatus[\"Ready\"] = \"Ready\";\n})(PrintStatus || (PrintStatus = {}));\n\nvar Print = function (_a) {\n    var children = _a.children, enableShortcuts = _a.enableShortcuts, store = _a.store;\n    var print = function () {\n        store.update('printStatus', PrintStatus.CheckingPermission);\n    };\n    var render = children || PrintButton;\n    return render({\n        enableShortcuts: enableShortcuts,\n        onClick: print,\n    });\n};\n\nvar PERMISSION_PRINT = 4;\nvar PERMISSION_PRINT_HIGHT_QUALITY = 2048;\nvar CheckPrintPermission = function (_a) {\n    var doc = _a.doc, store = _a.store;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var _b = React__namespace.useState(true), isAllowed = _b[0], setIsAllowed = _b[1];\n    React__namespace.useEffect(function () {\n        doc.getPermissions().then(function (permissions) {\n            var canPrint = permissions === null ||\n                permissions.includes(PERMISSION_PRINT) ||\n                permissions.includes(PERMISSION_PRINT_HIGHT_QUALITY);\n            canPrint ? store.update('printStatus', PrintStatus.Preparing) : setIsAllowed(false);\n        });\n    }, []);\n    return isAllowed ? (React__namespace.createElement(React__namespace.Fragment, null)) : (React__namespace.createElement(core.Modal, { ariaControlsSuffix: \"print-permission\", closeOnClickOutside: false, closeOnEscape: false, content: function (toggle) {\n            var close = function () {\n                toggle();\n                store.update('printStatus', PrintStatus.Cancelled);\n            };\n            return (React__namespace.createElement(React__namespace.Fragment, null,\n                React__namespace.createElement(\"div\", { className: \"rpv-print__permission-body\" }, l10n && l10n.print\n                    ? l10n.print.disallowPrint\n                    : 'The document does not allow to print'),\n                React__namespace.createElement(\"div\", { className: \"rpv-print__permission-footer\" },\n                    React__namespace.createElement(core.Button, { onClick: close }, l10n && l10n.print ? l10n.print.close : 'Close'))));\n        }, isOpened: true }));\n};\n\nvar PrintProgress = function (_a) {\n    var numLoadedPages = _a.numLoadedPages, numPages = _a.numPages, onCancel = _a.onCancel;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var direction = React__namespace.useContext(core.ThemeContext).direction;\n    var isRtl = direction === core.TextDirection.RightToLeft;\n    var progress = Math.floor((numLoadedPages * 100) / numPages);\n    return (React__namespace.createElement(\"div\", { className: \"rpv-print__progress\" },\n        React__namespace.createElement(\"div\", { className: core.classNames({\n                'rpv-print__progress-body': true,\n                'rpv-print__progress-body--rtl': isRtl,\n            }) },\n            React__namespace.createElement(\"div\", { className: \"rpv-print__progress-message\" }, l10n && l10n.print\n                ? l10n.print.preparingDocument\n                : 'Preparing document ...'),\n            React__namespace.createElement(\"div\", { className: \"rpv-print__progress-bar\" },\n                React__namespace.createElement(core.ProgressBar, { progress: progress })),\n            React__namespace.createElement(core.Button, { onClick: onCancel }, l10n && l10n.print ? l10n.print.cancel : 'Cancel'))));\n};\n\nvar isRunningInJest = function () { return typeof process !== 'undefined' && process.env.JEST_WORKER_ID !== undefined; };\n\nvar PageThumbnail = function (_a) {\n    var canvas = _a.canvas, page = _a.page, pageHeight = _a.pageHeight, pageIndex = _a.pageIndex, pageWidth = _a.pageWidth, rotation = _a.rotation, onLoad = _a.onLoad;\n    var isMounted = core.useIsMounted();\n    var renderTask = React__namespace.useRef();\n    var _b = React__namespace.useState(''), src = _b[0], setSrc = _b[1];\n    var testWithJest = React__namespace.useMemo(function () { return isRunningInJest(); }, []);\n    var handleImageLoad = function () {\n        if (!testWithJest) {\n            onLoad();\n        }\n    };\n    React__namespace.useEffect(function () {\n        var task = renderTask.current;\n        if (task) {\n            task.cancel();\n        }\n        var printUnit = 150 / 72;\n        canvas.height = Math.floor(pageHeight * printUnit);\n        canvas.width = Math.floor(pageWidth * printUnit);\n        var canvasContext = canvas.getContext('2d');\n        canvasContext.save();\n        canvasContext.fillStyle = 'rgb(255, 255, 255)';\n        canvasContext.fillRect(0, 0, canvas.width, canvas.height);\n        canvasContext.restore();\n        var viewport = page.getViewport({ rotation: rotation, scale: 1 });\n        renderTask.current = page.render({\n            canvasContext: canvasContext,\n            intent: 'print',\n            transform: [printUnit, 0, 0, printUnit, 0, 0],\n            viewport: viewport,\n        });\n        renderTask.current.promise.then(function () {\n            if ('toBlob' in canvas && 'createObjectURL' in URL) {\n                canvas.toBlob(function (blob) {\n                    isMounted.current && setSrc(URL.createObjectURL(blob));\n                    testWithJest && onLoad();\n                });\n            }\n            else {\n                isMounted.current && setSrc(canvas.toDataURL());\n                testWithJest && onLoad();\n            }\n        }, function () {\n        });\n    }, []);\n    return (src && (React__namespace.createElement(\"div\", { className: \"rpv-print__page\" },\n        React__namespace.createElement(\"img\", { \"data-testid\": \"print__thumbnail-\".concat(pageIndex), src: src, onLoad: handleImageLoad }))));\n};\n\nvar PageThumbnailContainer = function (_a) {\n    var canvas = _a.canvas, doc = _a.doc, pageIndex = _a.pageIndex, pageRotation = _a.pageRotation, pageSize = _a.pageSize, rotation = _a.rotation, shouldRender = _a.shouldRender, onLoad = _a.onLoad;\n    var isMounted = core.useIsMounted();\n    var _b = React__namespace.useState(null), page = _b[0], setPage = _b[1];\n    var isVertical = Math.abs(rotation + pageRotation) % 180 === 0;\n    React__namespace.useEffect(function () {\n        if (shouldRender) {\n            core.getPage(doc, pageIndex).then(function (pdfPage) {\n                isMounted.current && setPage(pdfPage);\n            });\n        }\n    }, [shouldRender]);\n    var rotationNumber = (pageSize.rotation + rotation + pageRotation) % 360;\n    return (page && (React__namespace.createElement(PageThumbnail, { canvas: canvas, page: page, pageHeight: isVertical ? pageSize.pageHeight : pageSize.pageWidth, pageIndex: pageIndex, pageWidth: isVertical ? pageSize.pageWidth : pageSize.pageHeight, rotation: rotationNumber, onLoad: onLoad })));\n};\n\nvar PrintZone = function (_a) {\n    var doc = _a.doc, numLoadedPages = _a.numLoadedPages, pagesRotation = _a.pagesRotation, pageSizes = _a.pageSizes, printPages = _a.printPages, printStatus = _a.printStatus, rotation = _a.rotation, onCancel = _a.onCancel, onLoad = _a.onLoad;\n    var canvas = React__namespace.useMemo(function () { return document.createElement('canvas'); }, []);\n    var container = React__namespace.useMemo(function () {\n        var zoneEle = document.querySelector('.rpv-print__zone');\n        if (zoneEle) {\n            return zoneEle;\n        }\n        var div = document.createElement('div');\n        div.classList.add('rpv-print__zone');\n        div.setAttribute('data-testid', 'print__zone');\n        document.body.appendChild(div);\n        return div;\n    }, []);\n    React__namespace.useEffect(function () {\n        if (printStatus === PrintStatus.Ready) {\n            document.documentElement.classList.add('rpv-print__html-printing');\n            document.body.classList.add('rpv-print__body-printing');\n            window.print();\n        }\n        var handler = function () {\n            if (printStatus === PrintStatus.Ready) {\n                document.documentElement.classList.remove('rpv-print__html-printing');\n                document.body.classList.remove('rpv-print__body-printing');\n                var zones = document.querySelectorAll('.rpv-print__zone');\n                if (zones) {\n                    zones.forEach(function (zoneEle) {\n                        zoneEle.parentElement.removeChild(zoneEle);\n                    });\n                }\n                canvas.height = 0;\n                canvas.width = 0;\n                document.removeEventListener('mousemove', handler);\n                onCancel();\n            }\n        };\n        document.addEventListener('mousemove', handler);\n        return function () { return document.removeEventListener('mousemove', handler); };\n    }, [printStatus]);\n    var pageHeight = pageSizes[0].pageHeight;\n    var pageWidth = pageSizes[0].pageWidth;\n    return reactDom.createPortal(React__namespace.createElement(React__namespace.Fragment, null,\n        printPages.map(function (pageIndex, loopIndex) { return (React__namespace.createElement(PageThumbnailContainer, { key: pageIndex, canvas: canvas, doc: doc, pageIndex: pageIndex, pageRotation: pagesRotation.has(pageIndex) ? pagesRotation.get(pageIndex) : 0, pageSize: pageSizes[pageIndex], rotation: rotation, shouldRender: loopIndex === numLoadedPages, onLoad: onLoad })); }),\n        React__namespace.createElement(\"style\", { dangerouslySetInnerHTML: {\n                __html: \"@page { size: \".concat(pageWidth, \"pt \").concat(pageHeight, \"pt }\"),\n            } })), container);\n};\n\nvar PrintContainer = function (_a) {\n    var doc = _a.doc, pagesRotation = _a.pagesRotation, pageSizes = _a.pageSizes, renderProgressBar = _a.renderProgressBar, rotation = _a.rotation, setPages = _a.setPages, store = _a.store;\n    var _b = React__namespace.useState(PrintStatus.Inactive), printStatus = _b[0], setPrintStatus = _b[1];\n    var _c = React__namespace.useState(0), numLoadedPagesForPrint = _c[0], setNumLoadedPagesForPrint = _c[1];\n    var printPages = React__namespace.useMemo(function () {\n        var numPages = doc.numPages;\n        return setPages(doc).filter(function (index) { return index >= 0 && index < numPages; });\n    }, [doc, setPages]);\n    var numPrintPages = printPages.length;\n    var cancelPrinting = function () {\n        setNumLoadedPagesForPrint(0);\n        setPrintStatus(PrintStatus.Inactive);\n    };\n    var handlePrintStatus = function (status) { return setPrintStatus(status); };\n    var onLoadPage = function () {\n        var total = numLoadedPagesForPrint + 1;\n        if (total <= numPrintPages) {\n            setNumLoadedPagesForPrint(total);\n            total === numPrintPages && setPrintStatus(PrintStatus.Ready);\n        }\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('printStatus', handlePrintStatus);\n        return function () {\n            store.unsubscribe('printStatus', handlePrintStatus);\n        };\n    }, []);\n    return (React__namespace.createElement(React__namespace.Fragment, null,\n        printStatus === PrintStatus.CheckingPermission && React__namespace.createElement(CheckPrintPermission, { doc: doc, store: store }),\n        printStatus === PrintStatus.Preparing &&\n            (renderProgressBar ? (renderProgressBar(numLoadedPagesForPrint, numPrintPages, cancelPrinting)) : (React__namespace.createElement(PrintProgress, { numLoadedPages: numLoadedPagesForPrint, numPages: numPrintPages, onCancel: cancelPrinting }))),\n        (printStatus === PrintStatus.Preparing || printStatus === PrintStatus.Ready) &&\n            numLoadedPagesForPrint <= numPrintPages && (React__namespace.createElement(PrintZone, { doc: doc, numLoadedPages: numLoadedPagesForPrint, pagesRotation: pagesRotation, pageSizes: pageSizes, printPages: printPages, printStatus: printStatus, rotation: rotation, onCancel: cancelPrinting, onLoad: onLoadPage }))));\n};\n\nvar PrintMenuItem = function (_a) {\n    var onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.print ? l10n.print.print : 'Print';\n    return (React__namespace.createElement(core.MenuItem, { icon: React__namespace.createElement(PrintIcon, null), testId: \"print__menu\", onClick: onClick }, label));\n};\n\nvar ShortcutHandler = function (_a) {\n    var containerRef = _a.containerRef, store = _a.store;\n    var keydownHandler = function (e) {\n        if (e.shiftKey || e.altKey || e.key !== 'p') {\n            return;\n        }\n        var isCommandPressed = core.isMac() ? e.metaKey : e.ctrlKey;\n        if (!isCommandPressed) {\n            return;\n        }\n        var containerEle = containerRef.current;\n        if (!containerEle || !document.activeElement || !containerEle.contains(document.activeElement)) {\n            return;\n        }\n        e.preventDefault();\n        store.update('printStatus', PrintStatus.Preparing);\n    };\n    React__namespace.useEffect(function () {\n        var containerEle = containerRef.current;\n        if (!containerEle) {\n            return;\n        }\n        document.addEventListener('keydown', keydownHandler);\n        return function () {\n            document.removeEventListener('keydown', keydownHandler);\n        };\n    }, [containerRef.current]);\n    return React__namespace.createElement(React__namespace.Fragment, null);\n};\n\nvar printPlugin = function (props) {\n    var printPluginProps = React__namespace.useMemo(function () {\n        return Object.assign({}, {\n            enableShortcuts: true,\n            setPages: function (doc) {\n                return Array(doc.numPages)\n                    .fill(0)\n                    .map(function (_, i) { return i; });\n            },\n        }, props);\n    }, []);\n    var store = React__namespace.useMemo(function () {\n        return core.createStore({\n            printStatus: PrintStatus.Inactive,\n        });\n    }, []);\n    var print = function () {\n        store.update('printStatus', PrintStatus.CheckingPermission);\n    };\n    var PrintDecorator = function (props) { return (React__namespace.createElement(Print, __assign({ enableShortcuts: printPluginProps.enableShortcuts }, props, { store: store }))); };\n    var PrintButtonDecorator = function () { return React__namespace.createElement(PrintDecorator, null, function (props) { return React__namespace.createElement(PrintButton, __assign({}, props)); }); };\n    var PrintMenuItemDecorator = function (props) { return (React__namespace.createElement(PrintDecorator, null, function (p) { return (React__namespace.createElement(PrintMenuItem, { onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    var renderViewer = function (renderViewerProps) {\n        var slot = renderViewerProps.slot;\n        var updateSlot = {\n            children: (React__namespace.createElement(React__namespace.Fragment, null,\n                printPluginProps.enableShortcuts && (React__namespace.createElement(ShortcutHandler, { containerRef: renderViewerProps.containerRef, store: store })),\n                React__namespace.createElement(PrintContainer, { doc: renderViewerProps.doc, pagesRotation: renderViewerProps.pagesRotation, pageSizes: renderViewerProps.pageSizes, renderProgressBar: props === null || props === void 0 ? void 0 : props.renderProgressBar, rotation: renderViewerProps.rotation, setPages: printPluginProps.setPages, store: store }),\n                slot.children)),\n        };\n        return __assign(__assign({}, slot), updateSlot);\n    };\n    var setPages = function (printPages) {\n        printPluginProps.setPages = printPages;\n    };\n    return {\n        print: print,\n        renderViewer: renderViewer,\n        Print: PrintDecorator,\n        PrintButton: PrintButtonDecorator,\n        PrintMenuItem: PrintMenuItemDecorator,\n        setPages: setPages,\n    };\n};\n\nexports.PrintIcon = PrintIcon;\nexports.getAllPagesNumbers = getAllPagesNumbers;\nexports.getCustomPagesNumbers = getCustomPagesNumbers;\nexports.getEvenPagesNumbers = getEvenPagesNumbers;\nexports.getOddPagesNumbers = getOddPagesNumbers;\nexports.printPlugin = printPlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/print.min.js');\n} else {\n    module.exports = require('./cjs/print.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar InfoIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M12,1.001c6.075,0,11,4.925,11,11s-4.925,11-11,11s-11-4.925-11-11S5.925,1.001,12,1.001z\\n            M14.5,17.005H13\\n            c-0.552,0-1-0.448-1-1v-6.5c0-0.276-0.224-0.5-0.5-0.5H10\\n            M11.745,6.504L11.745,6.504\\n            M11.745,6.5c-0.138,0-0.25,0.112-0.25,0.25\\n            S11.607,7,11.745,7s0.25-0.112,0.25-0.25S11.883,6.5,11.745,6.5\" }))); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar PropertiesLoader = function (_a) {\n    var doc = _a.doc, render = _a.render;\n    var _b = React__namespace.useState(), data = _b[0], setData = _b[1];\n    React__namespace.useEffect(function () {\n        doc.getMetadata()\n            .then(function (meta) {\n            return Promise.resolve(meta);\n        })\n            .then(function (meta) {\n            return doc.getDownloadInfo().then(function (d) {\n                return Promise.resolve({\n                    fileName: meta.contentDispositionFilename || '',\n                    info: meta.info,\n                    length: d.length,\n                });\n            });\n        })\n            .then(function (response) {\n            setData(response);\n        });\n    }, []);\n    return data ? (render(data)) : (React__namespace.createElement(\"div\", { className: \"rpv-properties__loader\" },\n        React__namespace.createElement(core.Spinner, null)));\n};\n\nvar PropertyItem = function (_a) {\n    var label = _a.label, value = _a.value;\n    var direction = React__namespace.useContext(core.ThemeContext).direction;\n    var isRtl = direction === core.TextDirection.RightToLeft;\n    return (React__namespace.createElement(\"dl\", { className: core.classNames({\n            'rpv-properties__item': true,\n            'rpv-properties__item--rtl': isRtl,\n        }) },\n        React__namespace.createElement(\"dt\", { className: \"rpv-properties__item-label\" },\n            label,\n            \":\"),\n        React__namespace.createElement(\"dd\", { className: \"rpv-properties__item-value\" }, value || '-')));\n};\n\nvar dateRegex = new RegExp('^D:' +\n    '(\\\\d{4})' +\n    '(\\\\d{2})?' +\n    '(\\\\d{2})?' +\n    '(\\\\d{2})?' +\n    '(\\\\d{2})?' +\n    '(\\\\d{2})?' +\n    '([Z|+|-])?' +\n    '(\\\\d{2})?' +\n    \"'?\" +\n    '(\\\\d{2})?' +\n    \"'?\");\nvar parse = function (value, min, max, defaultValue) {\n    var parsed = parseInt(value, 10);\n    return parsed >= min && parsed <= max ? parsed : defaultValue;\n};\nvar convertDate = function (input) {\n    var matches = dateRegex.exec(input);\n    if (!matches) {\n        return null;\n    }\n    var year = parseInt(matches[1], 10);\n    var month = parse(matches[2], 1, 12, 1) - 1;\n    var day = parse(matches[3], 1, 31, 1);\n    var hour = parse(matches[4], 0, 23, 0);\n    var minute = parse(matches[5], 0, 59, 0);\n    var second = parse(matches[6], 0, 59, 0);\n    var universalTimeRelation = matches[7] || 'Z';\n    var offsetHour = parse(matches[8], 0, 23, 0);\n    var offsetMinute = parse(matches[9], 0, 59, 0);\n    switch (universalTimeRelation) {\n        case '-':\n            hour += offsetHour;\n            minute += offsetMinute;\n            break;\n        case '+':\n            hour -= offsetHour;\n            minute -= offsetMinute;\n            break;\n    }\n    return new Date(Date.UTC(year, month, day, hour, minute, second));\n};\n\nvar getFileName = function (url) {\n    var str = url.split('/').pop();\n    return str ? str.split('#')[0].split('?')[0] : url;\n};\n\nvar getFileSize = function (bytes) {\n    var sufixes = ['B', 'kB', 'MB', 'GB', 'TB'];\n    var i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return \"\".concat((bytes / Math.pow(1024, i)).toFixed(2), \" \").concat(sufixes[i]);\n};\n\nvar PropertiesModal = function (_a) {\n    var doc = _a.doc, fileName = _a.fileName, onToggle = _a.onToggle;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var formatDate = function (input) {\n        var date = convertDate(input);\n        return date ? \"\".concat(date.toLocaleDateString(), \", \").concat(date.toLocaleTimeString()) : '';\n    };\n    var renderData = function (data) { return (React__namespace.createElement(React__namespace.Fragment, null,\n        React__namespace.createElement(\"div\", { className: \"rpv-properties__modal-section\" },\n            React__namespace.createElement(PropertyItem, { label: l10n && l10n.properties\n                    ? l10n.properties.fileName\n                    : 'File name', value: data.fileName || getFileName(fileName) }),\n            React__namespace.createElement(PropertyItem, { label: l10n && l10n.properties\n                    ? l10n.properties.fileSize\n                    : 'File size', value: getFileSize(data.length) })),\n        React__namespace.createElement(core.Separator, null),\n        React__namespace.createElement(\"div\", { className: \"rpv-properties__modal-section\" },\n            React__namespace.createElement(PropertyItem, { label: l10n && l10n.properties ? l10n.properties.title : 'Title', value: data.info.Title }),\n            React__namespace.createElement(PropertyItem, { label: l10n && l10n.properties ? l10n.properties.author : 'Author', value: data.info.Author }),\n            React__namespace.createElement(PropertyItem, { label: l10n && l10n.properties ? l10n.properties.subject : 'Subject', value: data.info.Subject }),\n            React__namespace.createElement(PropertyItem, { label: l10n && l10n.properties ? l10n.properties.keywords : 'Keywords', value: data.info.Keywords }),\n            React__namespace.createElement(PropertyItem, { label: l10n && l10n.properties ? l10n.properties.creator : 'Creator', value: data.info.Creator }),\n            React__namespace.createElement(PropertyItem, { label: l10n && l10n.properties\n                    ? l10n.properties.creationDate\n                    : 'Creation date', value: formatDate(data.info.CreationDate) }),\n            React__namespace.createElement(PropertyItem, { label: l10n && l10n.properties\n                    ? l10n.properties.modificationDate\n                    : 'Modification date', value: formatDate(data.info.ModDate) })),\n        React__namespace.createElement(core.Separator, null),\n        React__namespace.createElement(\"div\", { className: \"rpv-properties__modal-section\" },\n            React__namespace.createElement(PropertyItem, { label: l10n && l10n.properties\n                    ? l10n.properties.pdfProducer\n                    : 'PDF producer', value: data.info.Producer }),\n            React__namespace.createElement(PropertyItem, { label: l10n && l10n.properties\n                    ? l10n.properties.pdfVersion\n                    : 'PDF version', value: data.info.PDFFormatVersion }),\n            React__namespace.createElement(PropertyItem, { label: l10n && l10n.properties\n                    ? l10n.properties.pageCount\n                    : 'Page count', value: \"\".concat(doc.numPages) })))); };\n    return (React__namespace.createElement(\"div\", { className: \"rpv-properties__modal\" },\n        React__namespace.createElement(PropertiesLoader, { doc: doc, render: renderData }),\n        React__namespace.createElement(\"div\", { className: \"rpv-properties__modal-footer\" },\n            React__namespace.createElement(core.Button, { onClick: onToggle }, l10n && l10n.properties ? l10n.properties.close : 'Close'))));\n};\n\nvar TOOLTIP_OFFSET = { left: 0, top: 8 };\nvar ShowPropertiesButton = function (_a) {\n    var onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.properties ? l10n.properties.showProperties : 'Show properties';\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"properties\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: label, testId: \"properties__button\", onClick: onClick },\n            React__namespace.createElement(InfoIcon, null)), content: function () { return label; }, offset: TOOLTIP_OFFSET }));\n};\n\nvar useDocument = function (store) {\n    var _a = React__namespace.useState(store.get('doc')), currentDoc = _a[0], setCurrentDoc = _a[1];\n    var handleDocumentChanged = function (doc) {\n        setCurrentDoc(doc);\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('doc', handleDocumentChanged);\n        return function () {\n            store.unsubscribe('doc', handleDocumentChanged);\n        };\n    }, []);\n    return { currentDoc: currentDoc };\n};\n\nvar ShowProperties = function (_a) {\n    var children = _a.children, store = _a.store;\n    var currentDoc = useDocument(store).currentDoc;\n    var fileName = store.get('fileName') || '';\n    var defaultChildren = function (props) { return React__namespace.createElement(ShowPropertiesButton, __assign({}, props)); };\n    var render = children || defaultChildren;\n    return currentDoc ? (React__namespace.createElement(core.Modal, { ariaControlsSuffix: \"properties\", target: function (toggle) {\n            return render({\n                onClick: toggle,\n            });\n        }, content: function (toggle) { return React__namespace.createElement(PropertiesModal, { doc: currentDoc, fileName: fileName, onToggle: toggle }); }, closeOnClickOutside: true, closeOnEscape: true })) : (React__namespace.createElement(React__namespace.Fragment, null));\n};\n\nvar ShowPropertiesMenuItem = function (_a) {\n    var onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.properties ? l10n.properties.showProperties : 'Show properties';\n    return (React__namespace.createElement(core.MenuItem, { icon: React__namespace.createElement(InfoIcon, null), testId: \"properties__menu\", onClick: onClick }, label));\n};\n\nvar propertiesPlugin = function () {\n    var store = React__namespace.useMemo(function () {\n        return core.createStore({\n            fileName: '',\n        });\n    }, []);\n    var ShowPropertiesDecorator = function (props) { return React__namespace.createElement(ShowProperties, __assign({}, props, { store: store })); };\n    var ShowPropertiesButtonDecorator = function () { return React__namespace.createElement(ShowProperties, { store: store }); };\n    var ShowPropertiesMenuItemDecorator = function (props) { return (React__namespace.createElement(ShowPropertiesDecorator, null, function (p) { return React__namespace.createElement(ShowPropertiesMenuItem, __assign({}, p)); })); };\n    return {\n        onDocumentLoad: function (props) {\n            store.update('doc', props.doc);\n        },\n        onViewerStateChange: function (viewerState) {\n            store.update('fileName', viewerState.file.name);\n            return viewerState;\n        },\n        ShowProperties: ShowPropertiesDecorator,\n        ShowPropertiesButton: ShowPropertiesButtonDecorator,\n        ShowPropertiesMenuItem: ShowPropertiesMenuItemDecorator,\n    };\n};\n\nexports.InfoIcon = InfoIcon;\nexports.propertiesPlugin = propertiesPlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/properties.min.js');\n} else {\n    module.exports = require('./cjs/properties.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar RotateBackwardIcon = function () { return (React__namespace.createElement(core.Icon, { ignoreDirection: true, size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M3.434,10.537c0.141-0.438,0.316-0.864,0.523-1.274\\n            M3.069,14.425C3.023,14.053,3,13.679,3,13.305 c0-0.291,0.014-0.579,0.041-0.863\\n            M4.389,18.111c-0.341-0.539-0.623-1.112-0.843-1.711\\n            M7.163,20.9 c-0.543-0.345-1.048-0.747-1.506-1.2\\n            M10.98,22.248c-0.65-0.074-1.29-0.218-1.909-0.431\\n            M10,4.25h2 c4.987,0.015,9.017,4.069,9.003,9.055c-0.013,4.581-3.456,8.426-8.008,8.945\\n            M13.5,1.75L10,4.25l3.5,2.5\" }))); };\n\nvar RotateForwardIcon = function () { return (React__namespace.createElement(core.Icon, { ignoreDirection: true, size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M20.566,10.537c-0.141-0.438-0.316-0.864-0.523-1.274\\n            M20.931,14.425C20.977,14.053,21,13.679,21,13.305 c0-0.291-0.014-0.579-0.041-0.863\\n            M19.611,18.111c0.341-0.539,0.624-1.114,0.843-1.713\\n            M16.837,20.9 c0.543-0.345,1.048-0.747,1.506-1.2\\n            M13.02,22.248c0.65-0.074,1.29-0.218,1.909-0.431\\n            M14,4.25h-2 c-4.987,0.015-9.017,4.069-9.003,9.055c0.013,4.581,3.456,8.426,8.008,8.945\\n            M10.5,1.75l3.5,2.5l-3.5,2.5\" }))); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar TOOLTIP_OFFSET = { left: 0, top: 8 };\nvar RotateButton = function (_a) {\n    var direction = _a.direction, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var backwardLabel = l10n && l10n.rotate ? l10n.rotate.rotateBackward : 'Rotate counterclockwise';\n    var forwardLabel = l10n && l10n.rotate ? l10n.rotate.rotateForward : 'Rotate clockwise';\n    var label = direction === core.RotateDirection.Backward ? backwardLabel : forwardLabel;\n    var icon = direction === core.RotateDirection.Backward ? React__namespace.createElement(RotateBackwardIcon, null) : React__namespace.createElement(RotateForwardIcon, null);\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"rotate\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: label, testId: direction === core.RotateDirection.Backward ? 'rotate__backward-button' : 'rotate__forward-button', onClick: onClick }, icon), content: function () { return label; }, offset: TOOLTIP_OFFSET }));\n};\n\nvar Rotate = function (_a) {\n    var children = _a.children, direction = _a.direction, store = _a.store;\n    var onClick = function () {\n        var rotate = store.get('rotate');\n        if (rotate) {\n            rotate(direction);\n        }\n    };\n    var defaultChildren = function (props) { return (React__namespace.createElement(RotateButton, { direction: props.direction, onClick: props.onClick })); };\n    var render = children || defaultChildren;\n    return render({\n        direction: direction,\n        onClick: onClick,\n    });\n};\n\nvar RotateMenuItem = function (_a) {\n    var direction = _a.direction, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var backwardLabel = l10n && l10n.rotate ? l10n.rotate.rotateBackward : 'Rotate counterclockwise';\n    var forwardLabel = l10n && l10n.rotate ? l10n.rotate.rotateForward : 'Rotate clockwise';\n    var label = direction === core.RotateDirection.Backward ? backwardLabel : forwardLabel;\n    var icon = direction === core.RotateDirection.Backward ? React__namespace.createElement(RotateBackwardIcon, null) : React__namespace.createElement(RotateForwardIcon, null);\n    return (React__namespace.createElement(core.MenuItem, { icon: icon, testId: direction === core.RotateDirection.Backward ? 'rotate__backward-menu' : 'rotate__forward-menu', onClick: onClick }, label));\n};\n\nvar RotatePage = function (_a) {\n    var children = _a.children, store = _a.store;\n    var onRotatePage = function (pageIndex, direction) {\n        var rotatePage = store.get('rotatePage');\n        if (rotatePage) {\n            rotatePage(pageIndex, direction);\n        }\n    };\n    return children({\n        onRotatePage: onRotatePage,\n    });\n};\n\nvar rotatePlugin = function () {\n    var store = React__namespace.useMemo(function () { return core.createStore(); }, []);\n    var RotateDecorator = function (props) { return React__namespace.createElement(Rotate, __assign({}, props, { store: store })); };\n    var RotateBackwardButtonDecorator = function () { return (React__namespace.createElement(RotateDecorator, { direction: core.RotateDirection.Backward }, function (props) { return React__namespace.createElement(RotateButton, __assign({}, props)); })); };\n    var RotateBackwardMenuItemDecorator = function (props) { return (React__namespace.createElement(RotateDecorator, { direction: core.RotateDirection.Backward }, function (p) { return (React__namespace.createElement(RotateMenuItem, { direction: p.direction, onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    var RotateForwardButtonDecorator = function () { return (React__namespace.createElement(RotateDecorator, { direction: core.RotateDirection.Forward }, function (props) { return React__namespace.createElement(RotateButton, __assign({}, props)); })); };\n    var RotateForwardMenuItemDecorator = function (props) { return (React__namespace.createElement(RotateDecorator, { direction: core.RotateDirection.Forward }, function (p) { return (React__namespace.createElement(RotateMenuItem, { direction: p.direction, onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    var RotatePageDecorator = function (props) { return React__namespace.createElement(RotatePage, __assign({}, props, { store: store })); };\n    return {\n        install: function (pluginFunctions) {\n            store.update('rotate', pluginFunctions.rotate);\n            store.update('rotatePage', pluginFunctions.rotatePage);\n        },\n        Rotate: RotateDecorator,\n        RotateBackwardButton: RotateBackwardButtonDecorator,\n        RotateBackwardMenuItem: RotateBackwardMenuItemDecorator,\n        RotateForwardButton: RotateForwardButtonDecorator,\n        RotateForwardMenuItem: RotateForwardMenuItemDecorator,\n        RotatePage: RotatePageDecorator,\n    };\n};\n\nexports.RotateBackwardIcon = RotateBackwardIcon;\nexports.RotateForwardIcon = RotateForwardIcon;\nexports.rotatePlugin = rotatePlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/rotate.min.js');\n} else {\n    module.exports = require('./cjs/rotate.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar DualPageCoverViewModeIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"rect\", { x: \"0.5\", y: \"0.497\", width: \"22\", height: \"22\", rx: \"1\", ry: \"1\" }),\n    React__namespace.createElement(\"line\", { x1: \"0.5\", y1: \"6.497\", x2: \"22.5\", y2: \"6.497\" }),\n    React__namespace.createElement(\"line\", { x1: \"11.5\", y1: \"6.497\", x2: \"11.5\", y2: \"22.497\" }))); };\n\nvar DualPageViewModeIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"rect\", { x: \"0.5\", y: \"0.497\", width: \"22\", height: \"22\", rx: \"1\", ry: \"1\" }),\n    React__namespace.createElement(\"line\", { x1: \"11.5\", y1: \"0.497\", x2: \"11.5\", y2: \"22.497\" }))); };\n\nvar HorizontalScrollingIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M6.5,21.5c0,0.552-0.448,1-1,1h-4c-0.552,0-1-0.448-1-1v-20c0-0.552,0.448-1,1-1h4c0.552,0,1,0.448,1,1V21.5z\\n            M14.5,21.5c0,0.552-0.448,1-1,1h-4c-0.552,0-1-0.448-1-1v-20c0-0.552,0.448-1,1-1h4c0.552,0,1,0.448,1,1V21.5z\\n            M22.5,21.5 c0,0.552-0.448,1-1,1h-4c-0.552,0-1-0.448-1-1v-20c0-0.552,0.448-1,1-1h4c0.552,0,1,0.448,1,1V21.5z\" }))); };\n\nvar PageScrollingIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"rect\", { x: \"0.5\", y: \"0.497\", width: \"22\", height: \"22\", rx: \"1\", ry: \"1\" }))); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar switchScrollMode = function (store, scrollMode) {\n    store.get('switchScrollMode')(scrollMode);\n    var currentViewMode = store.get('viewMode');\n    if ((scrollMode === core.ScrollMode.Horizontal || scrollMode === core.ScrollMode.Wrapped) &&\n        currentViewMode !== core.ViewMode.SinglePage) {\n        store.get('switchViewMode')(core.ViewMode.SinglePage);\n    }\n};\n\nvar VerticalScrollingIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M23.5,5.5c0,0.552-0.448,1-1,1h-21c-0.552,0-1-0.448-1-1v-3c0-0.552,0.448-1,1-1h21c0.552,0,1,0.448,1,1V5.5z\\n            M23.5,13.5c0,0.552-0.448,1-1,1h-21c-0.552,0-1-0.448-1-1v-3c0-0.552,0.448-1,1-1h21c0.552,0,1,0.448,1,1V13.5z\\n            M23.5,21.5 c0,0.552-0.448,1-1,1h-21c-0.552,0-1-0.448-1-1v-3c0-0.552,0.448-1,1-1h21c0.552,0,1,0.448,1,1V21.5z\" }))); };\n\nvar WrappedScrollingIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M10.5,9.5c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V9.5z\\n            M23.5,9.5c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V9.5z\\n            M10.5,22.5 c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V22.5z\\n            M23.5,22.5c0,0.552-0.448,1-1,1 h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V22.5z\" }))); };\n\nvar SwitchScrollModeDecorator = function (_a) {\n    var children = _a.children, mode = _a.mode, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = '';\n    var icon = React__namespace.createElement(VerticalScrollingIcon, null);\n    switch (mode) {\n        case core.ScrollMode.Horizontal:\n            label =\n                l10n && l10n.scrollMode\n                    ? l10n.scrollMode.horizontalScrolling\n                    : 'Horizontal scrolling';\n            icon = React__namespace.createElement(HorizontalScrollingIcon, null);\n            break;\n        case core.ScrollMode.Page:\n            label =\n                l10n && l10n.scrollMode\n                    ? l10n.scrollMode.pageScrolling\n                    : 'Page scrolling';\n            icon = React__namespace.createElement(PageScrollingIcon, null);\n            break;\n        case core.ScrollMode.Wrapped:\n            label =\n                l10n && l10n.scrollMode\n                    ? l10n.scrollMode.wrappedScrolling\n                    : 'Wrapped scrolling';\n            icon = React__namespace.createElement(WrappedScrollingIcon, null);\n            break;\n        case core.ScrollMode.Vertical:\n        default:\n            label =\n                l10n && l10n.scrollMode\n                    ? l10n.scrollMode.verticalScrolling\n                    : 'Vertical scrolling';\n            icon = React__namespace.createElement(VerticalScrollingIcon, null);\n            break;\n    }\n    return children({ icon: icon, label: label, onClick: onClick });\n};\n\nvar TOOLTIP_OFFSET$1 = { left: 0, top: 8 };\nvar SwitchScrollModeButton = function (_a) {\n    var isDisabled = _a.isDisabled, isSelected = _a.isSelected, mode = _a.mode, onClick = _a.onClick;\n    var testId = '';\n    switch (mode) {\n        case core.ScrollMode.Horizontal:\n            testId = 'scroll-mode__horizontal-button';\n            break;\n        case core.ScrollMode.Page:\n            testId = 'scroll-mode__page-button';\n            break;\n        case core.ScrollMode.Wrapped:\n            testId = 'scroll-mode__wrapped-button';\n            break;\n        case core.ScrollMode.Vertical:\n        default:\n            testId = 'scroll-mode__vertical-button';\n            break;\n    }\n    return (React__namespace.createElement(SwitchScrollModeDecorator, { mode: mode, onClick: onClick }, function (props) { return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"scroll-mode-switch\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: props.label, isDisabled: isDisabled, isSelected: isSelected, testId: testId, onClick: props.onClick }, props.icon), content: function () { return props.label; }, offset: TOOLTIP_OFFSET$1 })); }));\n};\n\nvar useScrollMode = function (store) {\n    var _a = React__namespace.useState(store.get('scrollMode') || core.ScrollMode.Vertical), scrollMode = _a[0], setScrollMode = _a[1];\n    var handleScrollModeChanged = function (currentScrollMode) {\n        setScrollMode(currentScrollMode);\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('scrollMode', handleScrollModeChanged);\n        return function () {\n            store.unsubscribe('scrollMode', handleScrollModeChanged);\n        };\n    }, []);\n    return { scrollMode: scrollMode };\n};\n\nvar useViewMode = function (store) {\n    var _a = React__namespace.useState(store.get('viewMode') || core.ViewMode.SinglePage), viewMode = _a[0], setViewMode = _a[1];\n    var handleViewModeChanged = function (currentViewMode) {\n        setViewMode(currentViewMode);\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('viewMode', handleViewModeChanged);\n        return function () {\n            store.unsubscribe('viewMode', handleViewModeChanged);\n        };\n    }, []);\n    return { viewMode: viewMode };\n};\n\nvar SwitchScrollMode = function (_a) {\n    var children = _a.children, mode = _a.mode, store = _a.store;\n    var viewMode = useViewMode(store).viewMode;\n    var scrollMode = useScrollMode(store).scrollMode;\n    var onClick = function () {\n        switchScrollMode(store, mode);\n    };\n    var isSelected = scrollMode === mode;\n    var isDisabled = (mode === core.ScrollMode.Horizontal || mode === core.ScrollMode.Wrapped) && viewMode !== core.ViewMode.SinglePage;\n    var defaultChildren = function (props) { return (React__namespace.createElement(SwitchScrollModeButton, { isDisabled: isDisabled, isSelected: isSelected, mode: props.mode, onClick: props.onClick })); };\n    var render = children || defaultChildren;\n    return render({\n        isDisabled: isDisabled,\n        isSelected: isSelected,\n        mode: mode,\n        onClick: onClick,\n    });\n};\n\nvar SwitchScrollModeMenuItem = function (_a) {\n    var isDisabled = _a.isDisabled, isSelected = _a.isSelected, mode = _a.mode, onClick = _a.onClick;\n    var testId = '';\n    switch (mode) {\n        case core.ScrollMode.Horizontal:\n            testId = 'scroll-mode__horizontal-menu';\n            break;\n        case core.ScrollMode.Page:\n            testId = 'scroll-mode__page-menu';\n            break;\n        case core.ScrollMode.Wrapped:\n            testId = 'scroll-mode__wrapped-menu';\n            break;\n        case core.ScrollMode.Vertical:\n        default:\n            testId = 'scroll-mode__vertical-menu';\n            break;\n    }\n    return (React__namespace.createElement(SwitchScrollModeDecorator, { mode: mode, onClick: onClick }, function (props) { return (React__namespace.createElement(core.MenuItem, { checked: isSelected, icon: props.icon, isDisabled: isDisabled, testId: testId, onClick: props.onClick }, props.label)); }));\n};\n\nvar switchViewMode = function (store, viewMode) {\n    store.get('switchViewMode')(viewMode);\n    var currentScrollMode = store.get('scrollMode');\n    if ((currentScrollMode === core.ScrollMode.Horizontal || currentScrollMode === core.ScrollMode.Wrapped) &&\n        viewMode !== core.ViewMode.SinglePage) {\n        store.get('switchScrollMode')(core.ScrollMode.Vertical);\n    }\n};\n\nvar SwitchViewModeDecorator = function (_a) {\n    var children = _a.children, mode = _a.mode, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = '';\n    var icon = React__namespace.createElement(PageScrollingIcon, null);\n    switch (mode) {\n        case core.ViewMode.DualPage:\n            label = l10n && l10n.scrollMode ? l10n.scrollMode.dualPage : 'Dual page';\n            icon = React__namespace.createElement(DualPageViewModeIcon, null);\n            break;\n        case core.ViewMode.DualPageWithCover:\n            label =\n                l10n && l10n.scrollMode\n                    ? l10n.scrollMode.dualPageCover\n                    : 'Dual page with cover';\n            icon = React__namespace.createElement(DualPageCoverViewModeIcon, null);\n            break;\n        case core.ViewMode.SinglePage:\n        default:\n            label =\n                l10n && l10n.scrollMode ? l10n.scrollMode.singlePage : 'Single page';\n            icon = React__namespace.createElement(PageScrollingIcon, null);\n            break;\n    }\n    return children({ icon: icon, label: label, onClick: onClick });\n};\n\nvar TOOLTIP_OFFSET = { left: 0, top: 8 };\nvar SwitchViewModeButton = function (_a) {\n    var isDisabled = _a.isDisabled, isSelected = _a.isSelected, mode = _a.mode, onClick = _a.onClick;\n    var testId = '';\n    switch (mode) {\n        case core.ViewMode.DualPage:\n            testId = 'view-mode__dual-button';\n            break;\n        case core.ViewMode.DualPageWithCover:\n            testId = 'view-mode__dual-cover-button';\n            break;\n        case core.ViewMode.SinglePage:\n        default:\n            testId = 'view-mode__single-button';\n            break;\n    }\n    return (React__namespace.createElement(SwitchViewModeDecorator, { mode: mode, onClick: onClick }, function (props) { return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"view-mode-switch\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: props.label, isDisabled: isDisabled, isSelected: isSelected, testId: testId, onClick: props.onClick }, props.icon), content: function () { return props.label; }, offset: TOOLTIP_OFFSET })); }));\n};\n\nvar SwitchViewMode = function (_a) {\n    var children = _a.children, mode = _a.mode, store = _a.store;\n    var viewMode = useViewMode(store).viewMode;\n    var scrollMode = useScrollMode(store).scrollMode;\n    var onClick = function () {\n        switchViewMode(store, mode);\n    };\n    var isSelected = viewMode === mode;\n    var isDisabled = (scrollMode === core.ScrollMode.Horizontal || scrollMode === core.ScrollMode.Wrapped) && mode !== core.ViewMode.SinglePage;\n    var defaultChildren = function (props) { return (React__namespace.createElement(SwitchViewModeButton, { isDisabled: isDisabled, isSelected: isSelected, mode: props.mode, onClick: props.onClick })); };\n    var render = children || defaultChildren;\n    return render({\n        isDisabled: isDisabled,\n        isSelected: isSelected,\n        mode: mode,\n        onClick: onClick,\n    });\n};\n\nvar SwitchViewModeMenuItem = function (_a) {\n    var isDisabled = _a.isDisabled, isSelected = _a.isSelected, mode = _a.mode, onClick = _a.onClick;\n    var testId = '';\n    switch (mode) {\n        case core.ViewMode.DualPage:\n            testId = 'view-mode__dual-menu';\n            break;\n        case core.ViewMode.DualPageWithCover:\n            testId = 'view-mode__dual-cover-menu';\n            break;\n        case core.ViewMode.SinglePage:\n        default:\n            testId = 'view-mode__single-menu';\n            break;\n    }\n    return (React__namespace.createElement(SwitchViewModeDecorator, { mode: mode, onClick: onClick }, function (props) { return (React__namespace.createElement(core.MenuItem, { checked: isSelected, icon: props.icon, isDisabled: isDisabled, testId: testId, onClick: props.onClick }, props.label)); }));\n};\n\nvar scrollModePlugin = function () {\n    var store = React__namespace.useMemo(function () {\n        return core.createStore({\n            scrollMode: core.ScrollMode.Vertical,\n            viewMode: core.ViewMode.SinglePage,\n            switchScrollMode: function () {\n            },\n            switchViewMode: function () {\n            },\n        });\n    }, []);\n    var SwitchScrollModeDecorator = function (props) { return React__namespace.createElement(SwitchScrollMode, __assign({}, props, { store: store })); };\n    var SwitchScrollModeButtonDecorator = function (props) { return (React__namespace.createElement(SwitchScrollModeDecorator, { mode: props.mode }, function (p) { return (React__namespace.createElement(SwitchScrollModeButton, { isDisabled: p.isDisabled, isSelected: p.isSelected, mode: p.mode, onClick: function () {\n            p.onClick();\n        } })); })); };\n    var SwitchScrollModeMenuItemDecorator = function (props) { return (React__namespace.createElement(SwitchScrollModeDecorator, { mode: props.mode }, function (p) { return (React__namespace.createElement(SwitchScrollModeMenuItem, { isDisabled: p.isDisabled, isSelected: p.isSelected, mode: p.mode, onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    var SwitchViewModeDecorator = function (props) { return React__namespace.createElement(SwitchViewMode, __assign({}, props, { store: store })); };\n    var SwitchViewModeButtonDecorator = function (props) { return (React__namespace.createElement(SwitchViewModeDecorator, { mode: props.mode }, function (p) { return (React__namespace.createElement(SwitchViewModeButton, { isDisabled: p.isDisabled, isSelected: p.isSelected, mode: p.mode, onClick: function () {\n            p.onClick();\n        } })); })); };\n    var SwitchViewModeMenuItemDecorator = function (props) { return (React__namespace.createElement(SwitchViewModeDecorator, { mode: props.mode }, function (p) { return (React__namespace.createElement(SwitchViewModeMenuItem, { isDisabled: p.isDisabled, isSelected: p.isSelected, mode: p.mode, onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    return {\n        install: function (pluginFunctions) {\n            store.update('switchScrollMode', pluginFunctions.switchScrollMode);\n            store.update('switchViewMode', pluginFunctions.switchViewMode);\n        },\n        onViewerStateChange: function (viewerState) {\n            store.update('scrollMode', viewerState.scrollMode);\n            store.update('viewMode', viewerState.viewMode);\n            return viewerState;\n        },\n        switchScrollMode: function (scrollMode) {\n            switchScrollMode(store, scrollMode);\n        },\n        switchViewMode: function (viewMode) {\n            switchViewMode(store, viewMode);\n        },\n        SwitchScrollMode: SwitchScrollModeDecorator,\n        SwitchScrollModeButton: SwitchScrollModeButtonDecorator,\n        SwitchScrollModeMenuItem: SwitchScrollModeMenuItemDecorator,\n        SwitchViewMode: SwitchViewModeDecorator,\n        SwitchViewModeButton: SwitchViewModeButtonDecorator,\n        SwitchViewModeMenuItem: SwitchViewModeMenuItemDecorator,\n    };\n};\n\nexports.DualPageCoverViewModeIcon = DualPageCoverViewModeIcon;\nexports.DualPageViewModeIcon = DualPageViewModeIcon;\nexports.HorizontalScrollingIcon = HorizontalScrollingIcon;\nexports.PageScrollingIcon = PageScrollingIcon;\nexports.VerticalScrollingIcon = VerticalScrollingIcon;\nexports.WrappedScrollingIcon = WrappedScrollingIcon;\nexports.scrollModePlugin = scrollModePlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/scroll-mode.min.js');\n} else {\n    module.exports = require('./cjs/scroll-mode.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar NextIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M0.541,5.627L11.666,18.2c0.183,0.207,0.499,0.226,0.706,0.043c0.015-0.014,0.03-0.028,0.043-0.043\\n            L23.541,5.627\" }))); };\n\nvar PreviousIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M23.535,18.373L12.409,5.8c-0.183-0.207-0.499-0.226-0.706-0.043C11.688,5.77,11.674,5.785,11.66,5.8\\n            L0.535,18.373\" }))); };\n\nvar SearchIcon = function () { return (React__namespace.createElement(core.Icon, { ignoreDirection: true, size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M10.5,0.5c5.523,0,10,4.477,10,10s-4.477,10-10,10s-10-4.477-10-10S4.977,0.5,10.5,0.5z\\n            M23.5,23.5\\n            l-5.929-5.929\" }))); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar EMPTY_KEYWORD_REGEXP = {\n    keyword: '',\n    regExp: new RegExp(' '),\n    wholeWords: false,\n};\n\nvar calculateOffset = function (children, parent) {\n    var top = children.offsetTop;\n    var left = children.offsetLeft;\n    var p = children.parentElement;\n    while (p && p !== parent) {\n        top += p.offsetTop;\n        left += p.offsetLeft;\n        p = p.parentElement;\n    }\n    return {\n        left: left,\n        top: top,\n    };\n};\n\nvar getCssProperties = function (area) {\n    return {\n        left: \"\".concat(area.left, \"%\"),\n        top: \"\".concat(area.top, \"%\"),\n        height: \"\".concat(area.height, \"%\"),\n        width: \"\".concat(area.width, \"%\"),\n    };\n};\n\nvar HightlightItem = function (_a) {\n    var index = _a.index, area = _a.area, onHighlightKeyword = _a.onHighlightKeyword;\n    var containerRef = React__namespace.useRef();\n    core.useIsomorphicLayoutEffect(function () {\n        var highlightEle = containerRef.current;\n        if (onHighlightKeyword && highlightEle) {\n            onHighlightKeyword({\n                highlightEle: highlightEle,\n                keyword: area.keyword,\n            });\n        }\n    }, []);\n    return (React__namespace.createElement(\"div\", { className: \"rpv-search__highlight\", \"data-index\": index, ref: containerRef, style: getCssProperties(area), title: area.keywordStr.trim() }));\n};\n\nvar removeNode = function (ele) {\n    var parent = ele.parentNode;\n    if (parent) {\n        parent.removeChild(ele);\n    }\n};\nvar replaceNode = function (replacementNode, node) {\n    removeNode(replacementNode);\n    var parent = node.parentNode;\n    if (parent) {\n        parent.insertBefore(replacementNode, node);\n    }\n    removeNode(node);\n};\nvar unwrap = function (ele) {\n    var parent = ele.parentNode;\n    if (!parent) {\n        return;\n    }\n    var range = document.createRange();\n    range.selectNodeContents(ele);\n    replaceNode(range.extractContents(), ele);\n    parent.normalize();\n};\n\nvar sortHighlightPosition = function (a, b) {\n    if (a.top < b.top) {\n        return -1;\n    }\n    if (a.top > b.top) {\n        return 1;\n    }\n    if (a.left < b.left) {\n        return -1;\n    }\n    if (a.left > b.left) {\n        return 1;\n    }\n    return 0;\n};\nvar Highlights = function (_a) {\n    var numPages = _a.numPages, pageIndex = _a.pageIndex, renderHighlights = _a.renderHighlights, store = _a.store, onHighlightKeyword = _a.onHighlightKeyword;\n    var containerRef = React__namespace.useRef();\n    var defaultRenderHighlights = React__namespace.useCallback(function (renderProps) { return (React__namespace.createElement(React__namespace.Fragment, null, renderProps.highlightAreas.map(function (area, index) { return (React__namespace.createElement(HightlightItem, { index: index, key: index, area: area, onHighlightKeyword: onHighlightKeyword })); }))); }, []);\n    var renderHighlightElements = renderHighlights || defaultRenderHighlights;\n    var _b = React__namespace.useState(store.get('matchPosition')), matchPosition = _b[0], setMatchPosition = _b[1];\n    var _c = React__namespace.useState(store.get('keyword') || [EMPTY_KEYWORD_REGEXP]), keywordRegexp = _c[0], setKeywordRegexp = _c[1];\n    var _d = React__namespace.useState({\n        pageIndex: pageIndex,\n        scale: 1,\n        status: core.LayerRenderStatus.PreRender,\n    }), renderStatus = _d[0], setRenderStatus = _d[1];\n    var currentMatchRef = React__namespace.useRef(null);\n    var characterIndexesRef = React__namespace.useRef([]);\n    var _e = React__namespace.useState([]), highlightAreas = _e[0], setHighlightAreas = _e[1];\n    var defaultTargetPageFilter = function () { return true; };\n    var targetPageFilter = React__namespace.useCallback(function () { return store.get('targetPageFilter') || defaultTargetPageFilter; }, [store.get('targetPageFilter')]);\n    var highlight = function (keywordStr, keyword, textLayerEle, span, charIndexSpan) {\n        var range = document.createRange();\n        var firstChild = span.firstChild;\n        if (!firstChild || firstChild.nodeType !== Node.TEXT_NODE) {\n            return null;\n        }\n        var length = firstChild.textContent.length;\n        var startOffset = charIndexSpan[0].charIndexInSpan;\n        var endOffset = charIndexSpan.length === 1 ? startOffset : charIndexSpan[charIndexSpan.length - 1].charIndexInSpan;\n        if (startOffset > length || endOffset + 1 > length) {\n            return null;\n        }\n        range.setStart(firstChild, startOffset);\n        range.setEnd(firstChild, endOffset + 1);\n        var wrapper = document.createElement('span');\n        range.surroundContents(wrapper);\n        var wrapperRect = wrapper.getBoundingClientRect();\n        var textLayerRect = textLayerEle.getBoundingClientRect();\n        var pageHeight = textLayerRect.height;\n        var pageWidth = textLayerRect.width;\n        var left = (100 * (wrapperRect.left - textLayerRect.left)) / pageWidth;\n        var top = (100 * (wrapperRect.top - textLayerRect.top)) / pageHeight;\n        var height = (100 * wrapperRect.height) / pageHeight;\n        var width = (100 * wrapperRect.width) / pageWidth;\n        unwrap(wrapper);\n        return {\n            keyword: keyword,\n            keywordStr: keywordStr,\n            numPages: numPages,\n            pageIndex: pageIndex,\n            left: left,\n            top: top,\n            height: height,\n            width: width,\n            pageHeight: pageHeight,\n            pageWidth: pageWidth,\n        };\n    };\n    var highlightAll = function (textLayerEle) {\n        var charIndexes = characterIndexesRef.current;\n        if (charIndexes.length === 0) {\n            return [];\n        }\n        var highlightPos = [];\n        var spans = [].slice.call(textLayerEle.querySelectorAll('.rpv-core__text-layer-text'));\n        var fullText = charIndexes.map(function (item) { return item.char; }).join('');\n        keywordRegexp.forEach(function (keyword) {\n            var keywordStr = keyword.keyword;\n            if (!keywordStr.trim()) {\n                return;\n            }\n            var cloneKeyword = keyword.regExp.flags.indexOf('g') === -1\n                ? new RegExp(keyword.regExp, \"\".concat(keyword.regExp.flags, \"g\"))\n                : keyword.regExp;\n            var match;\n            var matches = [];\n            while ((match = cloneKeyword.exec(fullText)) !== null) {\n                matches.push({\n                    keyword: cloneKeyword,\n                    startIndex: match.index,\n                    endIndex: cloneKeyword.lastIndex,\n                });\n            }\n            matches\n                .map(function (item) { return ({\n                keyword: item.keyword,\n                indexes: charIndexes.slice(item.startIndex, item.endIndex),\n            }); })\n                .forEach(function (item) {\n                var spanIndexes = item.indexes.reduce(function (acc, item) {\n                    acc[item.spanIndex] = (acc[item.spanIndex] || []).concat([item]);\n                    return acc;\n                }, {});\n                Object.values(spanIndexes).forEach(function (charIndexSpan) {\n                    if (charIndexSpan.length !== 1 || charIndexSpan[0].char.trim() !== '') {\n                        var normalizedCharSpan = keyword.wholeWords ? charIndexSpan.slice(1, -1) : charIndexSpan;\n                        var hightlighPosition = highlight(keywordStr, item.keyword, textLayerEle, spans[normalizedCharSpan[0].spanIndex], normalizedCharSpan);\n                        if (hightlighPosition) {\n                            highlightPos.push(hightlighPosition);\n                        }\n                    }\n                });\n            });\n        });\n        return highlightPos.sort(sortHighlightPosition);\n    };\n    var handleKeywordChanged = function (keyword) {\n        if (keyword && keyword.length > 0) {\n            setKeywordRegexp(keyword);\n        }\n    };\n    var handleMatchPositionChanged = function (currentPosition) { return setMatchPosition(currentPosition); };\n    var handleRenderStatusChanged = function (status) {\n        if (!status.has(pageIndex)) {\n            return;\n        }\n        var currentStatus = status.get(pageIndex);\n        if (currentStatus) {\n            setRenderStatus({\n                ele: currentStatus.ele,\n                pageIndex: pageIndex,\n                scale: currentStatus.scale,\n                status: currentStatus.status,\n            });\n        }\n    };\n    var isEmptyKeyword = function () {\n        return keywordRegexp.length === 0 || (keywordRegexp.length === 1 && keywordRegexp[0].keyword.trim() === '');\n    };\n    React__namespace.useEffect(function () {\n        if (isEmptyKeyword() ||\n            renderStatus.status !== core.LayerRenderStatus.DidRender ||\n            characterIndexesRef.current.length) {\n            return;\n        }\n        var textLayerEle = renderStatus.ele;\n        var spans = [].slice.call(textLayerEle.querySelectorAll('.rpv-core__text-layer-text'));\n        var charIndexes = spans\n            .map(function (span) { return span.textContent; })\n            .reduce(function (prev, curr, index) {\n            return prev.concat(curr.split('').map(function (c, i) { return ({\n                char: c,\n                charIndexInSpan: i,\n                spanIndex: index,\n            }); }));\n        }, [\n            {\n                char: '',\n                charIndexInSpan: 0,\n                spanIndex: 0,\n            },\n        ])\n            .slice(1);\n        characterIndexesRef.current = charIndexes;\n    }, [keywordRegexp, renderStatus.status]);\n    React__namespace.useEffect(function () {\n        if (isEmptyKeyword() ||\n            !renderStatus.ele ||\n            renderStatus.status !== core.LayerRenderStatus.DidRender ||\n            !targetPageFilter()({ pageIndex: pageIndex, numPages: numPages })) {\n            return;\n        }\n        var textLayerEle = renderStatus.ele;\n        var highlightPos = highlightAll(textLayerEle);\n        setHighlightAreas(highlightPos);\n    }, [keywordRegexp, matchPosition, renderStatus.status, characterIndexesRef.current]);\n    React__namespace.useEffect(function () {\n        if (isEmptyKeyword() && renderStatus.ele && renderStatus.status === core.LayerRenderStatus.DidRender) {\n            setHighlightAreas([]);\n        }\n    }, [keywordRegexp, renderStatus.status]);\n    React__namespace.useEffect(function () {\n        if (highlightAreas.length === 0) {\n            return;\n        }\n        var container = containerRef.current;\n        if (matchPosition.pageIndex !== pageIndex ||\n            !container ||\n            renderStatus.status !== core.LayerRenderStatus.DidRender) {\n            return;\n        }\n        var highlightEle = container.querySelector(\".rpv-search__highlight[data-index=\\\"\".concat(matchPosition.matchIndex, \"\\\"]\"));\n        if (!highlightEle) {\n            return;\n        }\n        var _a = calculateOffset(highlightEle, container), left = _a.left, top = _a.top;\n        var jump = store.get('jumpToDestination');\n        if (jump) {\n            jump({\n                pageIndex: pageIndex,\n                bottomOffset: (container.getBoundingClientRect().height - top) / renderStatus.scale,\n                leftOffset: left / renderStatus.scale,\n                scaleTo: renderStatus.scale,\n            });\n            if (currentMatchRef.current) {\n                currentMatchRef.current.classList.remove('rpv-search__highlight--current');\n            }\n            currentMatchRef.current = highlightEle;\n            highlightEle.classList.add('rpv-search__highlight--current');\n        }\n    }, [highlightAreas, matchPosition]);\n    React__namespace.useEffect(function () {\n        store.subscribe('keyword', handleKeywordChanged);\n        store.subscribe('matchPosition', handleMatchPositionChanged);\n        store.subscribe('renderStatus', handleRenderStatusChanged);\n        return function () {\n            store.unsubscribe('keyword', handleKeywordChanged);\n            store.unsubscribe('matchPosition', handleMatchPositionChanged);\n            store.unsubscribe('renderStatus', handleRenderStatusChanged);\n        };\n    }, []);\n    return (React__namespace.createElement(\"div\", { className: \"rpv-search__highlights\", \"data-testid\": \"search__highlights-\".concat(pageIndex), ref: containerRef }, renderHighlightElements({\n        getCssProperties: getCssProperties,\n        highlightAreas: highlightAreas,\n    })));\n};\n\nvar escapeRegExp = function (input) { return input.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'); };\nvar normalizeFlagKeyword = function (flagKeyword) {\n    var source = flagKeyword.wholeWords ? \" \".concat(flagKeyword.keyword, \" \") : flagKeyword.keyword;\n    var flags = flagKeyword.matchCase ? 'g' : 'gi';\n    return {\n        keyword: flagKeyword.keyword,\n        regExp: new RegExp(escapeRegExp(source), flags),\n        wholeWords: flagKeyword.wholeWords || false,\n    };\n};\nvar normalizeSingleKeyword = function (keyword, matchCase, wholeWords) {\n    if (keyword instanceof RegExp) {\n        return {\n            keyword: keyword.source,\n            regExp: keyword,\n            wholeWords: wholeWords || false,\n        };\n    }\n    if (typeof keyword === 'string') {\n        return keyword === ''\n            ? EMPTY_KEYWORD_REGEXP\n            : normalizeFlagKeyword({\n                keyword: keyword,\n                matchCase: matchCase || false,\n                wholeWords: wholeWords || false,\n            });\n    }\n    if (typeof matchCase !== 'undefined') {\n        keyword.matchCase = matchCase;\n    }\n    if (typeof wholeWords !== 'undefined') {\n        keyword.wholeWords = wholeWords;\n    }\n    return normalizeFlagKeyword(keyword);\n};\n\nvar useDocument = function (store) {\n    var currentDocRef = React__namespace.useRef(store.get('doc'));\n    var handleDocumentChanged = function (doc) {\n        currentDocRef.current = doc;\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('doc', handleDocumentChanged);\n        return function () {\n            store.unsubscribe('doc', handleDocumentChanged);\n        };\n    }, []);\n    return currentDocRef;\n};\n\nvar useSearch = function (store) {\n    var initialKeyword = store.get('initialKeyword');\n    var normalizedKeywordFlags = React__namespace.useMemo(function () {\n        if (initialKeyword && initialKeyword.length === 1) {\n            var normalizedKeyword = normalizeSingleKeyword(initialKeyword[0]);\n            return {\n                matchCase: normalizedKeyword.regExp.flags.indexOf('i') === -1,\n                wholeWords: normalizedKeyword.wholeWords,\n            };\n        }\n        else {\n            return {\n                matchCase: false,\n                wholeWords: false,\n            };\n        }\n    }, []);\n    var currentDocRef = useDocument(store);\n    var _a = React__namespace.useState(initialKeyword), keywords = _a[0], setKeywords = _a[1];\n    var _b = React__namespace.useState([]), found = _b[0], setFound = _b[1];\n    var _c = React__namespace.useState(0), currentMatch = _c[0], setCurrentMatch = _c[1];\n    var _d = React__namespace.useState(normalizedKeywordFlags.matchCase), matchCase = _d[0], setMatchCase = _d[1];\n    var textContents = React__namespace.useRef([]);\n    var _e = React__namespace.useState(normalizedKeywordFlags.wholeWords), wholeWords = _e[0], setWholeWords = _e[1];\n    var defaultTargetPageFilter = function () { return true; };\n    var targetPageFilter = React__namespace.useCallback(function () { return store.get('targetPageFilter') || defaultTargetPageFilter; }, [store.get('targetPageFilter')]);\n    var changeMatchCase = function (isChecked) {\n        setMatchCase(isChecked);\n        if (keywords.length > 0) {\n            searchFor(keywords, isChecked, wholeWords);\n        }\n    };\n    var changeWholeWords = function (isChecked) {\n        setWholeWords(isChecked);\n        if (keywords.length > 0) {\n            searchFor(keywords, matchCase, isChecked);\n        }\n    };\n    var jumpToMatch = function (index) {\n        var numMatches = found.length;\n        if (keywords.length === 0 || numMatches === 0) {\n            return null;\n        }\n        var normalizedIndex = index === numMatches + 1 ? 1 : Math.max(1, Math.min(numMatches, index));\n        setCurrentMatch(normalizedIndex);\n        return jumpToGivenMatch(found[normalizedIndex - 1]);\n    };\n    var jumpToPreviousMatch = function () { return jumpToMatch(currentMatch - 1); };\n    var jumpToNextMatch = function () { return jumpToMatch(currentMatch + 1); };\n    var clearKeyword = function () {\n        store.update('keyword', [EMPTY_KEYWORD_REGEXP]);\n        setKeyword('');\n        setCurrentMatch(0);\n        setFound([]);\n        setMatchCase(false);\n        setWholeWords(false);\n    };\n    var search = function () { return searchFor(keywords, matchCase, wholeWords); };\n    var setKeyword = function (keyword) { return setKeywords(keyword === '' ? [] : [keyword]); };\n    var setTargetPages = function (targetPageFilter) {\n        store.update('targetPageFilter', targetPageFilter);\n    };\n    var getTextContents = function () {\n        var currentDoc = currentDocRef.current;\n        if (!currentDoc) {\n            return Promise.resolve([]);\n        }\n        var promises = Array(currentDoc.numPages)\n            .fill(0)\n            .map(function (_, pageIndex) {\n            return core.getPage(currentDoc, pageIndex)\n                .then(function (page) {\n                return page.getTextContent();\n            })\n                .then(function (content) {\n                var pageContent = content.items.map(function (item) { return item.str || ''; }).join('');\n                return Promise.resolve({\n                    pageContent: pageContent,\n                    pageIndex: pageIndex,\n                });\n            });\n        });\n        return Promise.all(promises).then(function (data) {\n            data.sort(function (a, b) { return a.pageIndex - b.pageIndex; });\n            return Promise.resolve(data.map(function (item) { return item.pageContent; }));\n        });\n    };\n    var jumpToGivenMatch = function (match) {\n        var jumpToPage = store.get('jumpToPage');\n        if (jumpToPage) {\n            jumpToPage(match.pageIndex);\n        }\n        store.update('matchPosition', {\n            matchIndex: match.matchIndex,\n            pageIndex: match.pageIndex,\n        });\n        return match;\n    };\n    var getKeywordSource = function (keyword) {\n        if (keyword instanceof RegExp) {\n            return keyword.source;\n        }\n        if (typeof keyword === 'string') {\n            return keyword;\n        }\n        return keyword.keyword;\n    };\n    var searchFor = function (keywordParam, matchCaseParam, wholeWordsParam) {\n        var currentDoc = currentDocRef.current;\n        if (!currentDoc) {\n            return Promise.resolve([]);\n        }\n        var numPages = currentDoc.numPages;\n        var keywords = keywordParam.map(function (k) { return normalizeSingleKeyword(k, matchCaseParam, wholeWordsParam); });\n        store.update('keyword', keywords);\n        setCurrentMatch(0);\n        setFound([]);\n        return new Promise(function (resolve, _) {\n            var getTextPromise = textContents.current.length === 0\n                ? getTextContents().then(function (response) {\n                    textContents.current = response;\n                    return Promise.resolve(response);\n                })\n                : Promise.resolve(textContents.current);\n            getTextPromise.then(function (response) {\n                var arr = [];\n                response.forEach(function (pageText, pageIndex) {\n                    if (targetPageFilter()({ pageIndex: pageIndex, numPages: numPages })) {\n                        keywords.forEach(function (keyword) {\n                            var matchIndex = 0;\n                            var matches;\n                            while ((matches = keyword.regExp.exec(pageText)) !== null) {\n                                arr.push({\n                                    keyword: keyword.regExp,\n                                    matchIndex: matchIndex,\n                                    pageIndex: pageIndex,\n                                    pageText: pageText,\n                                    startIndex: matches.index,\n                                    endIndex: keyword.regExp.lastIndex,\n                                });\n                                matchIndex++;\n                            }\n                        });\n                    }\n                });\n                setFound(arr);\n                if (arr.length > 0) {\n                    setCurrentMatch(1);\n                    jumpToGivenMatch(arr[0]);\n                }\n                resolve(arr);\n            });\n        });\n    };\n    React__namespace.useEffect(function () {\n        textContents.current = [];\n    }, [currentDocRef.current]);\n    return {\n        clearKeyword: clearKeyword,\n        changeMatchCase: changeMatchCase,\n        changeWholeWords: changeWholeWords,\n        currentMatch: currentMatch,\n        jumpToMatch: jumpToMatch,\n        jumpToNextMatch: jumpToNextMatch,\n        jumpToPreviousMatch: jumpToPreviousMatch,\n        keywords: keywords,\n        matchCase: matchCase,\n        numberOfMatches: found.length,\n        wholeWords: wholeWords,\n        search: search,\n        searchFor: searchFor,\n        setKeywords: setKeywords,\n        keyword: keywords.length === 0 ? '' : getKeywordSource(keywords[0]),\n        setKeyword: setKeyword,\n        setTargetPages: setTargetPages,\n    };\n};\n\nvar Search = function (_a) {\n    var children = _a.children, store = _a.store;\n    var result = useSearch(store);\n    var _b = React__namespace.useState(false), isDocumentLoaded = _b[0], setDocumentLoaded = _b[1];\n    var handleDocumentChanged = function (_) { return setDocumentLoaded(true); };\n    React__namespace.useEffect(function () {\n        store.subscribe('doc', handleDocumentChanged);\n        return function () {\n            store.unsubscribe('doc', handleDocumentChanged);\n        };\n    }, []);\n    return children(__assign(__assign({}, result), { isDocumentLoaded: isDocumentLoaded }));\n};\n\nvar ShortcutHandler = function (_a) {\n    var containerRef = _a.containerRef, store = _a.store;\n    var isMouseInsideRef = React__namespace.useRef(false);\n    var handleMouseEnter = function () {\n        isMouseInsideRef.current = true;\n    };\n    var handleMouseLeave = function () {\n        isMouseInsideRef.current = false;\n    };\n    var handleKeydown = function (e) {\n        var containerEle = containerRef.current;\n        if (!containerEle) {\n            return;\n        }\n        if (e.shiftKey || e.altKey || e.key !== 'f') {\n            return;\n        }\n        var isCommandPressed = core.isMac() ? e.metaKey && !e.ctrlKey : e.ctrlKey;\n        if (!isCommandPressed) {\n            return;\n        }\n        if (isMouseInsideRef.current || (document.activeElement && containerEle.contains(document.activeElement))) {\n            e.preventDefault();\n            store.update('areShortcutsPressed', true);\n        }\n    };\n    React__namespace.useEffect(function () {\n        var containerEle = containerRef.current;\n        if (!containerEle) {\n            return;\n        }\n        document.addEventListener('keydown', handleKeydown);\n        containerEle.addEventListener('mouseenter', handleMouseEnter);\n        containerEle.addEventListener('mouseleave', handleMouseLeave);\n        return function () {\n            document.removeEventListener('keydown', handleKeydown);\n            containerEle.removeEventListener('mouseenter', handleMouseEnter);\n            containerEle.removeEventListener('mouseleave', handleMouseLeave);\n        };\n    }, [containerRef.current]);\n    return React__namespace.createElement(React__namespace.Fragment, null);\n};\n\nvar PORTAL_OFFSET$1 = { left: 0, top: 8 };\nvar SearchPopover = function (_a) {\n    var store = _a.store, onToggle = _a.onToggle;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var direction = React__namespace.useContext(core.ThemeContext).direction;\n    var _b = React__namespace.useState(false), isQuerying = _b[0], setIsQuerying = _b[1];\n    var _c = React__namespace.useState(false), searchDone = _c[0], setSearchDone = _c[1];\n    var isRtl = direction === core.TextDirection.RightToLeft;\n    var _d = useSearch(store), clearKeyword = _d.clearKeyword, changeMatchCase = _d.changeMatchCase, changeWholeWords = _d.changeWholeWords, currentMatch = _d.currentMatch, jumpToNextMatch = _d.jumpToNextMatch, jumpToPreviousMatch = _d.jumpToPreviousMatch, keyword = _d.keyword, matchCase = _d.matchCase, numberOfMatches = _d.numberOfMatches, wholeWords = _d.wholeWords, search = _d.search, setKeyword = _d.setKeyword;\n    var performSearch = function (cb) {\n        setIsQuerying(true);\n        search().then(function (_) {\n            setIsQuerying(false);\n            setSearchDone(true);\n            cb && cb();\n        });\n    };\n    var onKeydownSearch = function (e) {\n        if (e.key === 'Enter' && keyword) {\n            searchDone ? jumpToNextMatch() : performSearch();\n        }\n    };\n    var onChangeMatchCase = function (e) {\n        setSearchDone(false);\n        changeMatchCase(e.target.checked);\n    };\n    var onChangeWholeWords = function (e) {\n        setSearchDone(false);\n        changeWholeWords(e.target.checked);\n    };\n    var onClose = function () {\n        onToggle();\n        clearKeyword();\n    };\n    var onChangeKeyword = function (value) {\n        setSearchDone(false);\n        setKeyword(value);\n    };\n    React__namespace.useEffect(function () {\n        var initialKeyword = store.get('initialKeyword');\n        if (initialKeyword && initialKeyword.length === 1 && keyword) {\n            performSearch(function () {\n                store.update('initialKeyword', []);\n            });\n        }\n    }, []);\n    var searchLabel = l10n && l10n.search ? l10n.search.enterToSearch : 'Enter to search';\n    var previousMatchLabel = l10n && l10n.search ? l10n.search.previousMatch : 'Previous match';\n    var nextMatchLabel = l10n && l10n.search ? l10n.search.nextMatch : 'Next match';\n    var closeButtonLabel = l10n && l10n.search ? l10n.search.close : 'Close';\n    return (React__namespace.createElement(\"div\", { className: \"rpv-search__popover\" },\n        React__namespace.createElement(\"div\", { className: \"rpv-search__popover-input-counter\" },\n            React__namespace.createElement(core.TextBox, { ariaLabel: searchLabel, autoFocus: true, placeholder: searchLabel, type: \"text\", value: keyword, onChange: onChangeKeyword, onKeyDown: onKeydownSearch }),\n            React__namespace.createElement(\"div\", { className: core.classNames({\n                    'rpv-search__popover-counter': true,\n                    'rpv-search__popover-counter--ltr': !isRtl,\n                    'rpv-search__popover-counter--rtl': isRtl,\n                }) },\n                isQuerying && React__namespace.createElement(core.Spinner, { testId: \"search__popover-searching\", size: \"1rem\" }),\n                !isQuerying && (React__namespace.createElement(\"span\", { \"data-testid\": \"search__popover-num-matches\" },\n                    currentMatch,\n                    \"/\",\n                    numberOfMatches)))),\n        React__namespace.createElement(\"label\", { className: \"rpv-search__popover-label\" },\n            React__namespace.createElement(\"input\", { className: \"rpv-search__popover-label-checkbox\", \"data-testid\": \"search__popover-match-case\", checked: matchCase, type: \"checkbox\", onChange: onChangeMatchCase }),\n            ' ',\n            l10n && l10n.search ? l10n.search.matchCase : 'Match case'),\n        React__namespace.createElement(\"label\", { className: \"rpv-search__popover-label\" },\n            React__namespace.createElement(\"input\", { className: \"rpv-search__popover-label-checkbox\", checked: wholeWords, \"data-testid\": \"search__popover-whole-words\", type: \"checkbox\", onChange: onChangeWholeWords }),\n            ' ',\n            l10n && l10n.search ? l10n.search.wholeWords : 'Whole words'),\n        React__namespace.createElement(\"div\", { className: \"rpv-search__popover-footer\" },\n            React__namespace.createElement(\"div\", { className: \"rpv-search__popover-footer-item\" },\n                React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"search-previous-match\", position: isRtl ? core.Position.BottomRight : core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: previousMatchLabel, isDisabled: currentMatch <= 1, onClick: jumpToPreviousMatch },\n                        React__namespace.createElement(PreviousIcon, null)), content: function () { return previousMatchLabel; }, offset: PORTAL_OFFSET$1 })),\n            React__namespace.createElement(\"div\", { className: \"rpv-search__popover-footer-item\" },\n                React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"search-next-match\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: nextMatchLabel, isDisabled: currentMatch > numberOfMatches - 1, onClick: jumpToNextMatch },\n                        React__namespace.createElement(NextIcon, null)), content: function () { return nextMatchLabel; }, offset: PORTAL_OFFSET$1 })),\n            React__namespace.createElement(\"div\", { className: core.classNames({\n                    'rpv-search__popover-footer-button': true,\n                    'rpv-search__popover-footer-button--ltr': !isRtl,\n                    'rpv-search__popover-footer-button--rtl': isRtl,\n                }) },\n                React__namespace.createElement(core.Button, { onClick: onClose }, closeButtonLabel)))));\n};\n\nvar ShowSearchPopoverDecorator = function (_a) {\n    var children = _a.children, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.search ? l10n.search.search : 'Search';\n    var icon = React__namespace.createElement(SearchIcon, null);\n    return children({ icon: icon, label: label, onClick: onClick });\n};\n\nvar TOOLTIP_OFFSET = { left: 0, top: 8 };\nvar ShowSearchPopoverButton = function (_a) {\n    var enableShortcuts = _a.enableShortcuts, store = _a.store, onClick = _a.onClick;\n    var ariaKeyShortcuts = enableShortcuts ? (core.isMac() ? 'Meta+F' : 'Ctrl+F') : '';\n    var handleShortcutsPressed = function (areShortcutsPressed) {\n        if (areShortcutsPressed) {\n            onClick();\n        }\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('areShortcutsPressed', handleShortcutsPressed);\n        return function () {\n            store.unsubscribe('areShortcutsPressed', handleShortcutsPressed);\n        };\n    }, []);\n    return (React__namespace.createElement(ShowSearchPopoverDecorator, { onClick: onClick }, function (p) { return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"search-popover\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaKeyShortcuts: ariaKeyShortcuts, ariaLabel: p.label, testId: \"search__popover-button\", onClick: onClick }, p.icon), content: function () { return p.label; }, offset: TOOLTIP_OFFSET })); }));\n};\n\nvar PORTAL_OFFSET = { left: 0, top: 8 };\nvar ShowSearchPopover = function (_a) {\n    var children = _a.children, enableShortcuts = _a.enableShortcuts, store = _a.store;\n    var direction = React__namespace.useContext(core.ThemeContext).direction;\n    var portalPosition = direction === core.TextDirection.RightToLeft ? core.Position.BottomRight : core.Position.BottomLeft;\n    var defaultChildren = function (props) { return (React__namespace.createElement(ShowSearchPopoverButton, __assign({ enableShortcuts: enableShortcuts, store: store }, props))); };\n    var render = children || defaultChildren;\n    return (React__namespace.createElement(core.Popover, { ariaControlsSuffix: \"search\", lockScroll: false, position: portalPosition, target: function (toggle) {\n            return render({\n                onClick: toggle,\n            });\n        }, content: function (toggle) { return React__namespace.createElement(SearchPopover, { store: store, onToggle: toggle }); }, offset: PORTAL_OFFSET, closeOnClickOutside: false, closeOnEscape: true }));\n};\n\nvar normalizeKeywords = function (keyword) {\n    return Array.isArray(keyword) ? keyword.map(function (k) { return normalizeSingleKeyword(k); }) : [normalizeSingleKeyword(keyword)];\n};\nvar searchPlugin = function (props) {\n    var searchPluginProps = React__namespace.useMemo(function () { return Object.assign({}, { enableShortcuts: true, onHighlightKeyword: function () { } }, props); }, []);\n    var store = React__namespace.useMemo(function () {\n        return core.createStore({\n            initialKeyword: props && props.keyword ? (Array.isArray(props.keyword) ? props.keyword : [props.keyword]) : [],\n            keyword: props && props.keyword ? normalizeKeywords(props.keyword) : [EMPTY_KEYWORD_REGEXP],\n            matchPosition: {\n                matchIndex: -1,\n                pageIndex: -1,\n            },\n            renderStatus: new Map(),\n        });\n    }, []);\n    var _a = useSearch(store), clearKeyword = _a.clearKeyword, jumpToMatch = _a.jumpToMatch, jumpToNextMatch = _a.jumpToNextMatch, jumpToPreviousMatch = _a.jumpToPreviousMatch, searchFor = _a.searchFor, setKeywords = _a.setKeywords, setTargetPages = _a.setTargetPages;\n    var SearchDecorator = function (props) { return React__namespace.createElement(Search, __assign({}, props, { store: store })); };\n    var ShowSearchPopoverDecorator = function (props) { return (React__namespace.createElement(ShowSearchPopover, __assign({ enableShortcuts: searchPluginProps.enableShortcuts }, props, { store: store }))); };\n    var ShowSearchPopoverButtonDecorator = function () { return (React__namespace.createElement(ShowSearchPopoverDecorator, null, function (props) { return (React__namespace.createElement(ShowSearchPopoverButton, __assign({ enableShortcuts: searchPluginProps.enableShortcuts, store: store }, props))); })); };\n    var renderViewer = function (renderViewerProps) {\n        var currentSlot = renderViewerProps.slot;\n        if (currentSlot.subSlot) {\n            currentSlot.subSlot.children = (React__namespace.createElement(React__namespace.Fragment, null,\n                searchPluginProps.enableShortcuts && (React__namespace.createElement(ShortcutHandler, { containerRef: renderViewerProps.containerRef, store: store })),\n                currentSlot.subSlot.children));\n        }\n        return currentSlot;\n    };\n    var renderPageLayer = function (renderProps) { return (React__namespace.createElement(Highlights, { key: renderProps.pageIndex, numPages: renderProps.doc.numPages, pageIndex: renderProps.pageIndex, renderHighlights: props === null || props === void 0 ? void 0 : props.renderHighlights, store: store, onHighlightKeyword: searchPluginProps.onHighlightKeyword })); };\n    return {\n        install: function (pluginFunctions) {\n            var initialKeyword = props && props.keyword ? (Array.isArray(props.keyword) ? props.keyword : [props.keyword]) : [];\n            var keyword = props && props.keyword ? normalizeKeywords(props.keyword) : [EMPTY_KEYWORD_REGEXP];\n            store.update('initialKeyword', initialKeyword);\n            store.update('jumpToDestination', pluginFunctions.jumpToDestination);\n            store.update('jumpToPage', pluginFunctions.jumpToPage);\n            store.update('keyword', keyword);\n        },\n        renderPageLayer: renderPageLayer,\n        renderViewer: renderViewer,\n        uninstall: function (props) {\n            var renderStatus = store.get('renderStatus');\n            if (renderStatus) {\n                renderStatus.clear();\n            }\n        },\n        onDocumentLoad: function (props) {\n            store.update('doc', props.doc);\n        },\n        onTextLayerRender: function (props) {\n            var renderStatus = store.get('renderStatus');\n            if (renderStatus) {\n                renderStatus = renderStatus.set(props.pageIndex, props);\n                store.update('renderStatus', renderStatus);\n            }\n        },\n        Search: SearchDecorator,\n        ShowSearchPopover: ShowSearchPopoverDecorator,\n        ShowSearchPopoverButton: ShowSearchPopoverButtonDecorator,\n        clearHighlights: function () {\n            clearKeyword();\n        },\n        highlight: function (keyword) {\n            var keywords = Array.isArray(keyword) ? keyword : [keyword];\n            setKeywords(keywords);\n            return searchFor(keywords);\n        },\n        jumpToMatch: jumpToMatch,\n        jumpToNextMatch: jumpToNextMatch,\n        jumpToPreviousMatch: jumpToPreviousMatch,\n        setTargetPages: setTargetPages,\n    };\n};\n\nexports.NextIcon = NextIcon;\nexports.PreviousIcon = PreviousIcon;\nexports.SearchIcon = SearchIcon;\nexports.searchPlugin = searchPlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/search.min.js');\n} else {\n    module.exports = require('./cjs/search.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar DarkIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M19.5,15.106l2.4-2.4a1,1,0,0,0,0-1.414l-2.4-2.4V5.5a1,1,0,0,0-1-1H15.106l-2.4-2.4a1,1,0,0,0-1.414,0l-2.4,2.4H5.5a1,1,0,0,0-1,1V8.894l-2.4,2.4a1,1,0,0,0,0,1.414l2.4,2.4V18.5a1,1,0,0,0,1,1H8.894l2.4,2.4a1,1,0,0,0,1.414,0l2.4-2.4H18.5a1,1,0,0,0,1-1Z\" }),\n    React__namespace.createElement(\"path\", { d: \"M10,6.349a6,6,0,0,1,0,11.3,6,6,0,1,0,0-11.3Z\" }))); };\n\nvar LightIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M19.491,15.106l2.4-2.4a1,1,0,0,0,0-1.414l-2.4-2.4V5.5a1,1,0,0,0-1-1H15.1L12.7,2.1a1,1,0,0,0-1.414,0l-2.4,2.4H5.491a1,1,0,0,0-1,1V8.894l-2.4,2.4a1,1,0,0,0,0,1.414l2.4,2.4V18.5a1,1,0,0,0,1,1H8.885l2.4,2.4a1,1,0,0,0,1.414,0l2.4-2.4h3.394a1,1,0,0,0,1-1Z\" }),\n    React__namespace.createElement(\"path\", { d: \"M11.491,6c4,0,6,2.686,6,6s-2,6-6,6Z\" }))); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar TOOLTIP_OFFSET = { left: 0, top: 8 };\nvar SwitchThemeButton = function (_a) {\n    var onClick = _a.onClick;\n    var theme = React__namespace.useContext(core.ThemeContext);\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var isDarkTheme = theme.currentTheme === 'dark';\n    var label = l10n && l10n.theme\n        ? isDarkTheme\n            ? l10n.theme.switchLightTheme\n            : l10n.theme.switchDarkTheme\n        : isDarkTheme\n            ? 'Switch to the light theme'\n            : 'Switch to the dark theme';\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"theme-switch\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: label, testId: \"theme__switch-button\", onClick: onClick }, isDarkTheme ? React__namespace.createElement(LightIcon, null) : React__namespace.createElement(DarkIcon, null)), content: function () { return label; }, offset: TOOLTIP_OFFSET }));\n};\n\nvar SwitchTheme = function (_a) {\n    var children = _a.children;\n    var theme = React__namespace.useContext(core.ThemeContext);\n    var defaultChildern = function (props) { return React__namespace.createElement(SwitchThemeButton, { onClick: props.onClick }); };\n    var render = children || defaultChildern;\n    return render({\n        onClick: function () { return theme.setCurrentTheme(theme.currentTheme === 'dark' ? 'light' : 'dark'); },\n    });\n};\n\nvar SwitchThemeMenuItem = function (_a) {\n    var onClick = _a.onClick;\n    var theme = React__namespace.useContext(core.ThemeContext);\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var isDarkTheme = theme.currentTheme === 'dark';\n    var label = l10n && l10n.theme\n        ? isDarkTheme\n            ? l10n.theme.switchLightTheme\n            : l10n.theme.switchDarkTheme\n        : isDarkTheme\n            ? 'Switch to the light theme'\n            : 'Switch to the dark theme';\n    return (React__namespace.createElement(core.MenuItem, { icon: isDarkTheme ? React__namespace.createElement(LightIcon, null) : React__namespace.createElement(DarkIcon, null), testId: \"theme__switch-menu\", onClick: onClick }, label));\n};\n\nvar themePlugin = function () {\n    var SwitchThemeDecorator = function (props) { return React__namespace.createElement(SwitchTheme, __assign({}, props)); };\n    var SwitchThemeButtonDecorator = function () { return (React__namespace.createElement(SwitchThemeDecorator, null, function (props) { return React__namespace.createElement(SwitchThemeButton, __assign({}, props)); })); };\n    var SwitchThemeMenuItemDecorator = function (props) { return (React__namespace.createElement(SwitchThemeDecorator, null, function (p) { return (React__namespace.createElement(SwitchThemeMenuItem, { onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    return {\n        SwitchTheme: SwitchThemeDecorator,\n        SwitchThemeButton: SwitchThemeButtonDecorator,\n        SwitchThemeMenuItem: SwitchThemeMenuItemDecorator,\n    };\n};\n\nexports.DarkIcon = DarkIcon;\nexports.LightIcon = LightIcon;\nexports.themePlugin = themePlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/theme.min.js');\n} else {\n    module.exports = require('./cjs/theme.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar ZoomInIcon = function () { return (React__namespace.createElement(core.Icon, { ignoreDirection: true, size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M10.5,0.499c5.523,0,10,4.477,10,10s-4.477,10-10,10s-10-4.477-10-10S4.977,0.499,10.5,0.499z\\n            M23.5,23.499\\n            l-5.929-5.929\\n            M5.5,10.499h10\\n            M10.5,5.499v10\" }))); };\n\nvar ZoomOutIcon = function () { return (React__namespace.createElement(core.Icon, { ignoreDirection: true, size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M10.5,0.499c5.523,0,10,4.477,10,10s-4.477,10-10,10s-10-4.477-10-10S4.977,0.499,10.5,0.499z\\n            M23.5,23.499\\n            l-5.929-5.929\\n            M5.5,10.499h10\" }))); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar useZoom = function (store) {\n    var _a = React__namespace.useState(store.get('scale') || 0), scale = _a[0], setScale = _a[1];\n    var handleScaleChanged = function (currentScale) {\n        setScale(currentScale);\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('scale', handleScaleChanged);\n        return function () {\n            store.unsubscribe('scale', handleScaleChanged);\n        };\n    }, []);\n    return { scale: scale };\n};\n\nvar CurrentScale = function (_a) {\n    var children = _a.children, store = _a.store;\n    var scale = useZoom(store).scale;\n    var defaultChildren = function (props) { return React__namespace.createElement(React__namespace.Fragment, null, \"\".concat(Math.round(props.scale * 100), \"%\")); };\n    var render = children || defaultChildren;\n    return render({ scale: scale });\n};\n\nvar WHEEL_EVENT_OPTIONS = {\n    passive: false,\n};\nvar svgElement = null;\nvar createSvgElement = function () {\n    return svgElement || (svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'svg'));\n};\nvar PinchZoom = function (_a) {\n    var pagesContainerRef = _a.pagesContainerRef, store = _a.store;\n    var zoomTo = core.useDebounceCallback(function (scale) {\n        var zoom = store.get('zoom');\n        if (zoom) {\n            zoom(scale);\n        }\n    }, 40);\n    var handleWheelEvent = function (e) {\n        if (!e.ctrlKey) {\n            return;\n        }\n        e.preventDefault();\n        var target = e.target;\n        var rect = target.getBoundingClientRect();\n        var scaleDiff = 1 - e.deltaY / 100;\n        var originX = e.clientX - rect.left;\n        var originY = e.clientY - rect.top;\n        var currentScale = store.get('scale');\n        var matrix = createSvgElement()\n            .createSVGMatrix()\n            .translate(originX, originY)\n            .scale(scaleDiff)\n            .translate(-originX, -originY)\n            .scale(currentScale);\n        zoomTo(matrix.a);\n    };\n    core.useIsomorphicLayoutEffect(function () {\n        var pagesContainer = pagesContainerRef.current;\n        if (!pagesContainer) {\n            return;\n        }\n        pagesContainer.addEventListener('wheel', handleWheelEvent, WHEEL_EVENT_OPTIONS);\n        return function () {\n            pagesContainer.removeEventListener('wheel', handleWheelEvent);\n        };\n    }, []);\n    return React__namespace.createElement(React__namespace.Fragment, null);\n};\n\nvar LEVELS = [\n    0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1, 1.1, 1.3, 1.5, 1.7, 1.9, 2.1, 2.4, 2.7, 3.0, 3.3, 3.7, 4.1, 4.6,\n    5.1, 5.7, 6.3, 7.0, 7.7, 8.5, 9.4, 10,\n];\nvar increase = function (currentLevel) {\n    var found = LEVELS.find(function (item) { return item > currentLevel; });\n    return found || currentLevel;\n};\nvar decrease = function (currentLevel) {\n    var found = LEVELS.findIndex(function (item) { return item >= currentLevel; });\n    return found === -1 || found === 0 ? currentLevel : LEVELS[found - 1];\n};\n\nvar ShortcutHandler = function (_a) {\n    var containerRef = _a.containerRef, store = _a.store;\n    var keydownHandler = function (e) {\n        if (e.shiftKey || e.altKey) {\n            return;\n        }\n        var isCommandPressed = core.isMac() ? e.metaKey : e.ctrlKey;\n        if (!isCommandPressed) {\n            return;\n        }\n        var containerEle = containerRef.current;\n        if (!containerEle || !document.activeElement || !containerEle.contains(document.activeElement)) {\n            return;\n        }\n        var zoom = store.get('zoom');\n        if (!zoom) {\n            return;\n        }\n        var scale = store.get('scale') || 1;\n        var newScale = 1;\n        switch (e.key) {\n            case '-':\n                newScale = decrease(scale);\n                break;\n            case '=':\n                newScale = increase(scale);\n                break;\n            case '0':\n                newScale = 1;\n                break;\n            default:\n                newScale = scale;\n                break;\n        }\n        if (newScale !== scale) {\n            e.preventDefault();\n            zoom(newScale);\n        }\n    };\n    React__namespace.useEffect(function () {\n        var containerEle = containerRef.current;\n        if (!containerEle) {\n            return;\n        }\n        document.addEventListener('keydown', keydownHandler);\n        return function () {\n            document.removeEventListener('keydown', keydownHandler);\n        };\n    }, [containerRef.current]);\n    return React__namespace.createElement(React__namespace.Fragment, null);\n};\n\nvar DEFAULT_LEVELS = [0.5, 0.75, 1, 1.25, 1.5, 2, 3, 4];\nvar PORTAL_OFFSET = { left: 0, top: 8 };\nvar ZoomPopover = function (_a) {\n    var _b = _a.levels, levels = _b === void 0 ? DEFAULT_LEVELS : _b, scale = _a.scale, onZoom = _a.onZoom;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var direction = React__namespace.useContext(core.ThemeContext).direction;\n    var isRtl = direction === core.TextDirection.RightToLeft;\n    var getSpcialLevelLabel = function (level) {\n        switch (level) {\n            case core.SpecialZoomLevel.ActualSize:\n                return l10n && l10n.zoom ? l10n.zoom.actualSize : 'Actual size';\n            case core.SpecialZoomLevel.PageFit:\n                return l10n && l10n.zoom ? l10n.zoom.pageFit : 'Page fit';\n            case core.SpecialZoomLevel.PageWidth:\n                return l10n && l10n.zoom ? l10n.zoom.pageWidth : 'Page width';\n        }\n    };\n    var zoomDocumentLabel = l10n && l10n.zoom ? l10n.zoom.zoomDocument : 'Zoom document';\n    var renderTarget = function (toggle) {\n        var click = function () {\n            toggle();\n        };\n        return (React__namespace.createElement(core.MinimalButton, { ariaLabel: zoomDocumentLabel, testId: \"zoom__popover-target\", onClick: click },\n            React__namespace.createElement(\"span\", { className: \"rpv-zoom__popover-target\" },\n                React__namespace.createElement(\"span\", { \"data-testid\": \"zoom__popover-target-scale\", className: core.classNames({\n                        'rpv-zoom__popover-target-scale': true,\n                        'rpv-zoom__popover-target-scale--ltr': !isRtl,\n                        'rpv-zoom__popover-target-scale--rtl': isRtl,\n                    }) },\n                    Math.round(scale * 100),\n                    \"%\"),\n                React__namespace.createElement(\"span\", { className: \"rpv-zoom__popover-target-arrow\" }))));\n    };\n    var renderContent = function (toggle) { return (React__namespace.createElement(core.Menu, null,\n        Object.keys(core.SpecialZoomLevel).map(function (k) {\n            var level = k;\n            var clickMenuItem = function () {\n                toggle();\n                onZoom(level);\n            };\n            return (React__namespace.createElement(core.MenuItem, { key: level, onClick: clickMenuItem }, getSpcialLevelLabel(level)));\n        }),\n        React__namespace.createElement(core.MenuDivider, null),\n        levels.map(function (level) {\n            var clickMenuItem = function () {\n                toggle();\n                onZoom(level);\n            };\n            return (React__namespace.createElement(core.MenuItem, { key: level, onClick: clickMenuItem }, \"\".concat(Math.round(level * 100), \"%\")));\n        }))); };\n    return (React__namespace.createElement(core.Popover, { ariaControlsSuffix: \"zoom\", ariaHasPopup: \"menu\", position: core.Position.BottomCenter, target: renderTarget, content: renderContent, offset: PORTAL_OFFSET, closeOnClickOutside: true, closeOnEscape: true }));\n};\n\nvar Zoom = function (_a) {\n    var children = _a.children, levels = _a.levels, store = _a.store;\n    var scale = useZoom(store).scale;\n    var zoomTo = function (newLevel) {\n        var zoom = store.get('zoom');\n        if (zoom) {\n            zoom(newLevel);\n        }\n    };\n    var defaultChildren = function (props) { return (React__namespace.createElement(ZoomPopover, { levels: levels, scale: props.scale, onZoom: props.onZoom })); };\n    var render = children || defaultChildren;\n    return render({\n        scale: scale,\n        onZoom: zoomTo,\n    });\n};\n\nvar TOOLTIP_OFFSET$1 = { left: 0, top: 8 };\nvar ZoomInButton = function (_a) {\n    var enableShortcuts = _a.enableShortcuts, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.zoom ? l10n.zoom.zoomIn : 'Zoom in';\n    var ariaKeyShortcuts = enableShortcuts ? (core.isMac() ? 'Meta+=' : 'Ctrl+=') : '';\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"zoom-in\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaKeyShortcuts: ariaKeyShortcuts, ariaLabel: label, testId: \"zoom__in-button\", onClick: onClick },\n            React__namespace.createElement(ZoomInIcon, null)), content: function () { return label; }, offset: TOOLTIP_OFFSET$1 }));\n};\n\nvar ZoomIn = function (_a) {\n    var children = _a.children, enableShortcuts = _a.enableShortcuts, store = _a.store;\n    var scale = useZoom(store).scale;\n    var zoomIn = function () {\n        var zoom = store.get('zoom');\n        if (zoom) {\n            var newLevel = increase(scale);\n            zoom(newLevel);\n        }\n    };\n    var render = children || ZoomInButton;\n    return render({\n        enableShortcuts: enableShortcuts,\n        onClick: zoomIn,\n    });\n};\n\nvar ZoomInMenuItem = function (_a) {\n    var onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.zoom ? l10n.zoom.zoomIn : 'Zoom in';\n    return (React__namespace.createElement(core.MenuItem, { icon: React__namespace.createElement(ZoomInIcon, null), testId: \"zoom__in-menu\", onClick: onClick }, label));\n};\n\nvar TOOLTIP_OFFSET = { left: 0, top: 8 };\nvar ZoomOutButton = function (_a) {\n    var enableShortcuts = _a.enableShortcuts, onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.zoom ? l10n.zoom.zoomOut : 'Zoom out';\n    var ariaKeyShortcuts = enableShortcuts ? (core.isMac() ? 'Meta+-' : 'Ctrl+-') : '';\n    return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"zoom-out\", position: core.Position.BottomCenter, target: React__namespace.createElement(core.MinimalButton, { ariaKeyShortcuts: ariaKeyShortcuts, ariaLabel: label, testId: \"zoom__out-button\", onClick: onClick },\n            React__namespace.createElement(ZoomOutIcon, null)), content: function () { return label; }, offset: TOOLTIP_OFFSET }));\n};\n\nvar ZoomOut = function (_a) {\n    var children = _a.children, enableShortcuts = _a.enableShortcuts, store = _a.store;\n    var scale = useZoom(store).scale;\n    var zoomIn = function () {\n        var zoom = store.get('zoom');\n        if (zoom) {\n            var newLevel = decrease(scale);\n            zoom(newLevel);\n        }\n    };\n    var render = children || ZoomOutButton;\n    return render({\n        enableShortcuts: enableShortcuts,\n        onClick: zoomIn,\n    });\n};\n\nvar ZoomOutMenuItem = function (_a) {\n    var onClick = _a.onClick;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var label = l10n && l10n.zoom ? l10n.zoom.zoomOut : 'Zoom out';\n    return (React__namespace.createElement(core.MenuItem, { icon: React__namespace.createElement(ZoomOutIcon, null), testId: \"zoom__out-menu\", onClick: onClick }, label));\n};\n\nvar zoomPlugin = function (props) {\n    var zoomPluginProps = React__namespace.useMemo(function () { return Object.assign({}, { enableShortcuts: true }, props); }, []);\n    var store = React__namespace.useMemo(function () { return core.createStore({}); }, []);\n    var CurrentScaleDecorator = function (props) { return React__namespace.createElement(CurrentScale, __assign({}, props, { store: store })); };\n    var ZoomInDecorator = function (props) { return (React__namespace.createElement(ZoomIn, __assign({ enableShortcuts: zoomPluginProps.enableShortcuts }, props, { store: store }))); };\n    var ZoomInButtonDecorator = function () { return React__namespace.createElement(ZoomInDecorator, null, function (props) { return React__namespace.createElement(ZoomInButton, __assign({}, props)); }); };\n    var ZoomInMenuItemDecorator = function (props) { return (React__namespace.createElement(ZoomInDecorator, null, function (p) { return (React__namespace.createElement(ZoomInMenuItem, { onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    var ZoomOutDecorator = function (props) { return (React__namespace.createElement(ZoomOut, __assign({ enableShortcuts: zoomPluginProps.enableShortcuts }, props, { store: store }))); };\n    var ZoomOutButtonDecorator = function () { return React__namespace.createElement(ZoomOutDecorator, null, function (props) { return React__namespace.createElement(ZoomOutButton, __assign({}, props)); }); };\n    var ZoomOutMenuItemDecorator = function (props) { return (React__namespace.createElement(ZoomOutDecorator, null, function (p) { return (React__namespace.createElement(ZoomOutMenuItem, { onClick: function () {\n            p.onClick();\n            props.onClick();\n        } })); })); };\n    var ZoomDecorator = function (props) { return React__namespace.createElement(Zoom, __assign({}, props, { store: store })); };\n    var ZoomPopoverDecorator = function (zoomPopverProps) { return (React__namespace.createElement(ZoomDecorator, null, function (props) { return React__namespace.createElement(ZoomPopover, __assign({ levels: zoomPopverProps === null || zoomPopverProps === void 0 ? void 0 : zoomPopverProps.levels }, props)); })); };\n    var renderViewer = function (props) {\n        var slot = props.slot;\n        if (!zoomPluginProps.enableShortcuts) {\n            return slot;\n        }\n        var updateSlot = {\n            children: (React__namespace.createElement(React__namespace.Fragment, null,\n                React__namespace.createElement(ShortcutHandler, { containerRef: props.containerRef, store: store }),\n                React__namespace.createElement(PinchZoom, { pagesContainerRef: props.pagesContainerRef, store: store }),\n                slot.children)),\n        };\n        return __assign(__assign({}, slot), updateSlot);\n    };\n    return {\n        renderViewer: renderViewer,\n        install: function (pluginFunctions) {\n            store.update('zoom', pluginFunctions.zoom);\n        },\n        onViewerStateChange: function (viewerState) {\n            store.update('scale', viewerState.scale);\n            return viewerState;\n        },\n        zoomTo: function (scale) {\n            var zoom = store.get('zoom');\n            if (zoom) {\n                zoom(scale);\n            }\n        },\n        CurrentScale: CurrentScaleDecorator,\n        ZoomIn: ZoomInDecorator,\n        ZoomInButton: ZoomInButtonDecorator,\n        ZoomInMenuItem: ZoomInMenuItemDecorator,\n        ZoomOut: ZoomOutDecorator,\n        ZoomOutButton: ZoomOutButtonDecorator,\n        ZoomOutMenuItem: ZoomOutMenuItemDecorator,\n        Zoom: ZoomDecorator,\n        ZoomPopover: ZoomPopoverDecorator,\n    };\n};\n\nexports.ZoomInIcon = ZoomInIcon;\nexports.ZoomOutIcon = ZoomOutIcon;\nexports.zoomPlugin = zoomPlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/zoom.min.js');\n} else {\n    module.exports = require('./cjs/zoom.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar selectionMode = require('@react-pdf-viewer/selection-mode');\nvar React = require('react');\nvar fullScreen = require('@react-pdf-viewer/full-screen');\nvar getFile = require('@react-pdf-viewer/get-file');\nvar open = require('@react-pdf-viewer/open');\nvar pageNavigation = require('@react-pdf-viewer/page-navigation');\nvar print = require('@react-pdf-viewer/print');\nvar properties = require('@react-pdf-viewer/properties');\nvar rotate = require('@react-pdf-viewer/rotate');\nvar scrollMode = require('@react-pdf-viewer/scroll-mode');\nvar search = require('@react-pdf-viewer/search');\nvar theme = require('@react-pdf-viewer/theme');\nvar zoom = require('@react-pdf-viewer/zoom');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar MoreIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M12,0.5c1.381,0,2.5,1.119,2.5,2.5S13.381,5.5,12,5.5S9.5,4.381,9.5,3S10.619,0.5,12,0.5z\\n            M12,9.5\\n            c1.381,0,2.5,1.119,2.5,2.5s-1.119,2.5-2.5,2.5S9.5,13.381,9.5,12S10.619,9.5,12,9.5z\\n            M12,18.5c1.381,0,2.5,1.119,2.5,2.5\\n            s-1.119,2.5-2.5,2.5S9.5,22.381,9.5,21S10.619,18.5,12,18.5z\" }))); };\n\nvar PORTAL_OFFSET = { left: 0, top: 8 };\nvar MoreActionsPopover = function (_a) {\n    var toolbarSlot = _a.toolbarSlot;\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var direction = React__namespace.useContext(core.ThemeContext).direction;\n    var portalPosition = direction === core.TextDirection.RightToLeft ? core.Position.BottomLeft : core.Position.BottomRight;\n    var DownloadMenuItem = toolbarSlot.DownloadMenuItem, EnterFullScreenMenuItem = toolbarSlot.EnterFullScreenMenuItem, GoToFirstPageMenuItem = toolbarSlot.GoToFirstPageMenuItem, GoToLastPageMenuItem = toolbarSlot.GoToLastPageMenuItem, GoToNextPageMenuItem = toolbarSlot.GoToNextPageMenuItem, GoToPreviousPageMenuItem = toolbarSlot.GoToPreviousPageMenuItem, OpenMenuItem = toolbarSlot.OpenMenuItem, PrintMenuItem = toolbarSlot.PrintMenuItem, RotateBackwardMenuItem = toolbarSlot.RotateBackwardMenuItem, RotateForwardMenuItem = toolbarSlot.RotateForwardMenuItem, ShowPropertiesMenuItem = toolbarSlot.ShowPropertiesMenuItem, SwitchScrollModeMenuItem = toolbarSlot.SwitchScrollModeMenuItem, SwitchSelectionModeMenuItem = toolbarSlot.SwitchSelectionModeMenuItem, SwitchViewModeMenuItem = toolbarSlot.SwitchViewModeMenuItem, SwitchThemeMenuItem = toolbarSlot.SwitchThemeMenuItem;\n    var renderTarget = function (toggle, opened) {\n        var label = l10n && l10n.toolbar ? l10n.toolbar.moreActions : 'More actions';\n        return (React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"toolbar-more-actions\", position: portalPosition, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: label, isSelected: opened, testId: \"toolbar__more-actions-popover-target\", onClick: toggle },\n                React__namespace.createElement(MoreIcon, null)), content: function () { return label; }, offset: PORTAL_OFFSET }));\n    };\n    var renderContent = function (toggle) {\n        return (React__namespace.createElement(core.Menu, null,\n            React__namespace.createElement(\"div\", { className: \"rpv-core__display--block rpv-core__display--hidden-medium\" },\n                React__namespace.createElement(SwitchThemeMenuItem, { onClick: toggle })),\n            React__namespace.createElement(\"div\", { className: \"rpv-core__display--block rpv-core__display--hidden-medium\" },\n                React__namespace.createElement(EnterFullScreenMenuItem, { onClick: toggle })),\n            React__namespace.createElement(\"div\", { className: \"rpv-core__display--block rpv-core__display--hidden-medium\" },\n                React__namespace.createElement(OpenMenuItem, null)),\n            React__namespace.createElement(\"div\", { className: \"rpv-core__display--block rpv-core__display--hidden-medium\" },\n                React__namespace.createElement(PrintMenuItem, { onClick: toggle })),\n            React__namespace.createElement(\"div\", { className: \"rpv-core__display--block rpv-core__display--hidden-medium\" },\n                React__namespace.createElement(DownloadMenuItem, { onClick: toggle })),\n            React__namespace.createElement(\"div\", { className: \"rpv-core__display--block rpv-core__display--hidden-medium\" },\n                React__namespace.createElement(core.MenuDivider, null)),\n            React__namespace.createElement(GoToFirstPageMenuItem, { onClick: toggle }),\n            React__namespace.createElement(\"div\", { className: \"rpv-core__display--block rpv-core__display--hidden-medium\" },\n                React__namespace.createElement(GoToPreviousPageMenuItem, { onClick: toggle })),\n            React__namespace.createElement(\"div\", { className: \"rpv-core__display--block rpv-core__display--hidden-medium\" },\n                React__namespace.createElement(GoToNextPageMenuItem, { onClick: toggle })),\n            React__namespace.createElement(GoToLastPageMenuItem, { onClick: toggle }),\n            React__namespace.createElement(core.MenuDivider, null),\n            React__namespace.createElement(RotateForwardMenuItem, { onClick: toggle }),\n            React__namespace.createElement(RotateBackwardMenuItem, { onClick: toggle }),\n            React__namespace.createElement(core.MenuDivider, null),\n            React__namespace.createElement(SwitchSelectionModeMenuItem, { mode: selectionMode.SelectionMode.Text, onClick: toggle }),\n            React__namespace.createElement(SwitchSelectionModeMenuItem, { mode: selectionMode.SelectionMode.Hand, onClick: toggle }),\n            React__namespace.createElement(core.MenuDivider, null),\n            React__namespace.createElement(SwitchScrollModeMenuItem, { mode: core.ScrollMode.Page, onClick: toggle }),\n            React__namespace.createElement(SwitchScrollModeMenuItem, { mode: core.ScrollMode.Vertical, onClick: toggle }),\n            React__namespace.createElement(SwitchScrollModeMenuItem, { mode: core.ScrollMode.Horizontal, onClick: toggle }),\n            React__namespace.createElement(SwitchScrollModeMenuItem, { mode: core.ScrollMode.Wrapped, onClick: toggle }),\n            React__namespace.createElement(core.MenuDivider, null),\n            React__namespace.createElement(\"div\", { className: \"rpv-core__display--hidden rpv-core__display--block-small\" },\n                React__namespace.createElement(SwitchViewModeMenuItem, { mode: core.ViewMode.SinglePage, onClick: toggle })),\n            React__namespace.createElement(\"div\", { className: \"rpv-core__display--hidden rpv-core__display--block-small\" },\n                React__namespace.createElement(SwitchViewModeMenuItem, { mode: core.ViewMode.DualPage, onClick: toggle })),\n            React__namespace.createElement(\"div\", { className: \"rpv-core__display--hidden rpv-core__display--block-small\" },\n                React__namespace.createElement(SwitchViewModeMenuItem, { mode: core.ViewMode.DualPageWithCover, onClick: toggle })),\n            React__namespace.createElement(\"div\", { className: \"rpv-core__display--hidden rpv-core__display--block-small\" },\n                React__namespace.createElement(core.MenuDivider, null)),\n            React__namespace.createElement(ShowPropertiesMenuItem, { onClick: toggle })));\n    };\n    return (React__namespace.createElement(core.Popover, { ariaControlsSuffix: \"toolbar-more-actions\", ariaHasPopup: \"menu\", position: portalPosition, target: renderTarget, content: renderContent, offset: PORTAL_OFFSET, closeOnClickOutside: true, closeOnEscape: true }));\n};\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar renderDefaultToolbar = function (transformToolbarSlot) {\n    return function (defaultToolbarSlot) {\n        var toolbarSlot = React__namespace.useMemo(function () { return transformToolbarSlot(defaultToolbarSlot); }, []);\n        var direction = React__namespace.useContext(core.ThemeContext).direction;\n        var isRtl = direction === core.TextDirection.RightToLeft;\n        var CurrentPageInput = toolbarSlot.CurrentPageInput, Download = toolbarSlot.Download, EnterFullScreen = toolbarSlot.EnterFullScreen, GoToNextPage = toolbarSlot.GoToNextPage, GoToPreviousPage = toolbarSlot.GoToPreviousPage, NumberOfPages = toolbarSlot.NumberOfPages, Open = toolbarSlot.Open, Print = toolbarSlot.Print, ShowSearchPopover = toolbarSlot.ShowSearchPopover, SwitchTheme = toolbarSlot.SwitchTheme, Zoom = toolbarSlot.Zoom, ZoomIn = toolbarSlot.ZoomIn, ZoomOut = toolbarSlot.ZoomOut;\n        return (React__namespace.createElement(\"div\", { \"data-testid\": \"toolbar\", className: core.classNames({\n                'rpv-toolbar': true,\n                'rpv-toolbar--rtl': isRtl,\n            }), role: \"toolbar\", \"aria-orientation\": \"horizontal\" },\n            React__namespace.createElement(\"div\", { className: \"rpv-toolbar__left\" },\n                React__namespace.createElement(\"div\", { className: \"rpv-toolbar__item\" },\n                    React__namespace.createElement(ShowSearchPopover, null)),\n                React__namespace.createElement(\"div\", { className: \"rpv-core__display--hidden rpv-core__display--block-small\" },\n                    React__namespace.createElement(\"div\", { className: \"rpv-toolbar__item\" },\n                        React__namespace.createElement(GoToPreviousPage, null))),\n                React__namespace.createElement(\"div\", { className: \"rpv-toolbar__item\" },\n                    React__namespace.createElement(CurrentPageInput, null),\n                    React__namespace.createElement(\"span\", { className: \"rpv-toolbar__label\" },\n                        React__namespace.createElement(NumberOfPages, null))),\n                React__namespace.createElement(\"div\", { className: \"rpv-core__display--hidden rpv-core__display--block-small\" },\n                    React__namespace.createElement(\"div\", { className: \"rpv-toolbar__item\" },\n                        React__namespace.createElement(GoToNextPage, null)))),\n            React__namespace.createElement(\"div\", { className: \"rpv-toolbar__center\" },\n                React__namespace.createElement(\"div\", { className: \"rpv-toolbar__item\" },\n                    React__namespace.createElement(ZoomOut, null)),\n                React__namespace.createElement(\"div\", { className: \"rpv-core__display--hidden rpv-core__display--block-small\" },\n                    React__namespace.createElement(\"div\", { className: \"rpv-toolbar__item\" },\n                        React__namespace.createElement(Zoom, null))),\n                React__namespace.createElement(\"div\", { className: \"rpv-toolbar__item\" },\n                    React__namespace.createElement(ZoomIn, null))),\n            React__namespace.createElement(\"div\", { className: \"rpv-toolbar__right\" },\n                React__namespace.createElement(\"div\", { className: \"rpv-core__display--hidden rpv-core__display--block-medium\" },\n                    React__namespace.createElement(\"div\", { className: \"rpv-toolbar__item\" },\n                        React__namespace.createElement(SwitchTheme, null))),\n                React__namespace.createElement(\"div\", { className: \"rpv-core__display--hidden rpv-core__display--block-medium\" },\n                    React__namespace.createElement(\"div\", { className: \"rpv-toolbar__item\" },\n                        React__namespace.createElement(EnterFullScreen, null))),\n                React__namespace.createElement(\"div\", { className: \"rpv-core__display--hidden rpv-core__display--block-medium\" },\n                    React__namespace.createElement(\"div\", { className: \"rpv-toolbar__item\" },\n                        React__namespace.createElement(Open, null))),\n                React__namespace.createElement(\"div\", { className: \"rpv-core__display--hidden rpv-core__display--block-medium\" },\n                    React__namespace.createElement(\"div\", { className: \"rpv-toolbar__item\" },\n                        React__namespace.createElement(Download, null))),\n                React__namespace.createElement(\"div\", { className: \"rpv-core__display--hidden rpv-core__display--block-medium\" },\n                    React__namespace.createElement(\"div\", { className: \"rpv-toolbar__item\" },\n                        React__namespace.createElement(Print, null))),\n                React__namespace.createElement(\"div\", { className: \"rpv-toolbar__item\" },\n                    React__namespace.createElement(MoreActionsPopover, { toolbarSlot: toolbarSlot })))));\n    };\n};\n\nvar defaultTransform = function (slot) {\n    var NumberOfPages = slot.NumberOfPages;\n    return Object.assign({}, slot, {\n        NumberOfPages: function () { return (React__namespace.createElement(React__namespace.Fragment, null,\n            \"/ \",\n            React__namespace.createElement(NumberOfPages, null))); },\n    });\n};\nvar DefaultToobar = function (toolbarSlot) {\n    return renderDefaultToolbar(defaultTransform)(toolbarSlot);\n};\n\nvar Toolbar = function (_a) {\n    var children = _a.children, slot = _a.slot;\n    var render = children || DefaultToobar;\n    return render(slot);\n};\n\nvar toolbarPlugin = function (props) {\n    var fullScreenPluginInstance = fullScreen.fullScreenPlugin(props ? props.fullScreenPlugin : {});\n    var getFilePluginInstance = getFile.getFilePlugin(props ? props.getFilePlugin : {});\n    var openPluginInstance = open.openPlugin(props ? props.openPlugin : {});\n    var pageNavigationPluginInstance = pageNavigation.pageNavigationPlugin(props ? props.pageNavigationPlugin : {});\n    var printPluginInstance = print.printPlugin(props ? props.printPlugin : {});\n    var propertiesPluginInstance = properties.propertiesPlugin();\n    var rotatePluginInstance = rotate.rotatePlugin();\n    var scrollModePluginInstance = scrollMode.scrollModePlugin();\n    var searchPluginInstance = search.searchPlugin(props ? props.searchPlugin : {});\n    var selectionModePluginInstance = selectionMode.selectionModePlugin(props ? props.selectionModePlugin : {});\n    var themePluginInstance = theme.themePlugin();\n    var zoomPluginInstance = zoom.zoomPlugin(props ? props.zoomPlugin : {});\n    var plugins = [\n        fullScreenPluginInstance,\n        getFilePluginInstance,\n        openPluginInstance,\n        pageNavigationPluginInstance,\n        printPluginInstance,\n        propertiesPluginInstance,\n        rotatePluginInstance,\n        scrollModePluginInstance,\n        searchPluginInstance,\n        selectionModePluginInstance,\n        themePluginInstance,\n        zoomPluginInstance,\n    ];\n    var ToolbarDecorator = React__namespace.useCallback(function (props) {\n        var EnterFullScreen = fullScreenPluginInstance.EnterFullScreen, EnterFullScreenMenuItem = fullScreenPluginInstance.EnterFullScreenMenuItem;\n        var Download = getFilePluginInstance.Download, DownloadMenuItem = getFilePluginInstance.DownloadMenuItem;\n        var Open = openPluginInstance.Open, OpenMenuItem = openPluginInstance.OpenMenuItem;\n        var CurrentPageInput = pageNavigationPluginInstance.CurrentPageInput, CurrentPageLabel = pageNavigationPluginInstance.CurrentPageLabel, GoToFirstPage = pageNavigationPluginInstance.GoToFirstPage, GoToFirstPageMenuItem = pageNavigationPluginInstance.GoToFirstPageMenuItem, GoToLastPage = pageNavigationPluginInstance.GoToLastPage, GoToLastPageMenuItem = pageNavigationPluginInstance.GoToLastPageMenuItem, GoToNextPage = pageNavigationPluginInstance.GoToNextPage, GoToNextPageMenuItem = pageNavigationPluginInstance.GoToNextPageMenuItem, GoToPreviousPage = pageNavigationPluginInstance.GoToPreviousPage, GoToPreviousPageMenuItem = pageNavigationPluginInstance.GoToPreviousPageMenuItem, NumberOfPages = pageNavigationPluginInstance.NumberOfPages;\n        var Print = printPluginInstance.Print, PrintMenuItem = printPluginInstance.PrintMenuItem;\n        var ShowProperties = propertiesPluginInstance.ShowProperties, ShowPropertiesMenuItem = propertiesPluginInstance.ShowPropertiesMenuItem;\n        var Rotate = rotatePluginInstance.Rotate, RotateBackwardMenuItem = rotatePluginInstance.RotateBackwardMenuItem, RotateForwardMenuItem = rotatePluginInstance.RotateForwardMenuItem;\n        var SwitchScrollMode = scrollModePluginInstance.SwitchScrollMode, SwitchScrollModeMenuItem = scrollModePluginInstance.SwitchScrollModeMenuItem, SwitchViewMode = scrollModePluginInstance.SwitchViewMode, SwitchViewModeMenuItem = scrollModePluginInstance.SwitchViewModeMenuItem;\n        var Search = searchPluginInstance.Search, ShowSearchPopover = searchPluginInstance.ShowSearchPopover;\n        var SwitchSelectionMode = selectionModePluginInstance.SwitchSelectionMode, SwitchSelectionModeMenuItem = selectionModePluginInstance.SwitchSelectionModeMenuItem;\n        var SwitchTheme = themePluginInstance.SwitchTheme, SwitchThemeMenuItem = themePluginInstance.SwitchThemeMenuItem;\n        var CurrentScale = zoomPluginInstance.CurrentScale, Zoom = zoomPluginInstance.Zoom, ZoomIn = zoomPluginInstance.ZoomIn, ZoomInMenuItem = zoomPluginInstance.ZoomInMenuItem, ZoomOut = zoomPluginInstance.ZoomOut, ZoomOutMenuItem = zoomPluginInstance.ZoomOutMenuItem;\n        return (React__namespace.createElement(Toolbar, __assign({}, props, { slot: {\n                CurrentPageInput: CurrentPageInput,\n                CurrentPageLabel: CurrentPageLabel,\n                CurrentScale: CurrentScale,\n                Download: Download,\n                DownloadMenuItem: DownloadMenuItem,\n                EnterFullScreen: EnterFullScreen,\n                EnterFullScreenMenuItem: EnterFullScreenMenuItem,\n                GoToFirstPage: GoToFirstPage,\n                GoToFirstPageMenuItem: GoToFirstPageMenuItem,\n                GoToLastPage: GoToLastPage,\n                GoToLastPageMenuItem: GoToLastPageMenuItem,\n                GoToNextPage: GoToNextPage,\n                GoToNextPageMenuItem: GoToNextPageMenuItem,\n                GoToPreviousPage: GoToPreviousPage,\n                GoToPreviousPageMenuItem: GoToPreviousPageMenuItem,\n                NumberOfPages: NumberOfPages,\n                Open: Open,\n                OpenMenuItem: OpenMenuItem,\n                Print: Print,\n                PrintMenuItem: PrintMenuItem,\n                Rotate: Rotate,\n                RotateBackwardMenuItem: RotateBackwardMenuItem,\n                RotateForwardMenuItem: RotateForwardMenuItem,\n                Search: Search,\n                ShowProperties: ShowProperties,\n                ShowPropertiesMenuItem: ShowPropertiesMenuItem,\n                ShowSearchPopover: ShowSearchPopover,\n                SwitchScrollMode: SwitchScrollMode,\n                SwitchScrollModeMenuItem: SwitchScrollModeMenuItem,\n                SwitchSelectionMode: SwitchSelectionMode,\n                SwitchSelectionModeMenuItem: SwitchSelectionModeMenuItem,\n                SwitchViewMode: SwitchViewMode,\n                SwitchViewModeMenuItem: SwitchViewModeMenuItem,\n                SwitchTheme: SwitchTheme,\n                SwitchThemeMenuItem: SwitchThemeMenuItem,\n                Zoom: Zoom,\n                ZoomIn: ZoomIn,\n                ZoomInMenuItem: ZoomInMenuItem,\n                ZoomOut: ZoomOut,\n                ZoomOutMenuItem: ZoomOutMenuItem,\n            } })));\n    }, []);\n    return {\n        fullScreenPluginInstance: fullScreenPluginInstance,\n        getFilePluginInstance: getFilePluginInstance,\n        openPluginInstance: openPluginInstance,\n        pageNavigationPluginInstance: pageNavigationPluginInstance,\n        printPluginInstance: printPluginInstance,\n        propertiesPluginInstance: propertiesPluginInstance,\n        rotatePluginInstance: rotatePluginInstance,\n        scrollModePluginInstance: scrollModePluginInstance,\n        searchPluginInstance: searchPluginInstance,\n        selectionModePluginInstance: selectionModePluginInstance,\n        themePluginInstance: themePluginInstance,\n        zoomPluginInstance: zoomPluginInstance,\n        install: function (pluginFunctions) {\n            plugins.forEach(function (plugin) {\n                if (plugin.install) {\n                    plugin.install(pluginFunctions);\n                }\n            });\n        },\n        renderPageLayer: function (renderProps) { return (React__namespace.createElement(React__namespace.Fragment, null, plugins.map(function (plugin, idx) {\n            return plugin.renderPageLayer ? (React__namespace.createElement(React__namespace.Fragment, { key: idx }, plugin.renderPageLayer(renderProps))) : (React__namespace.createElement(React__namespace.Fragment, { key: idx }));\n        }))); },\n        renderViewer: function (props) {\n            var slot = props.slot;\n            plugins.forEach(function (plugin) {\n                if (plugin.renderViewer) {\n                    slot = plugin.renderViewer(__assign(__assign({}, props), { slot: slot }));\n                }\n            });\n            return slot;\n        },\n        uninstall: function (pluginFunctions) {\n            plugins.forEach(function (plugin) {\n                if (plugin.uninstall) {\n                    plugin.uninstall(pluginFunctions);\n                }\n            });\n        },\n        onDocumentLoad: function (props) {\n            plugins.forEach(function (plugin) {\n                if (plugin.onDocumentLoad) {\n                    plugin.onDocumentLoad(props);\n                }\n            });\n        },\n        onAnnotationLayerRender: function (props) {\n            plugins.forEach(function (plugin) {\n                if (plugin.onAnnotationLayerRender) {\n                    plugin.onAnnotationLayerRender(props);\n                }\n            });\n        },\n        onTextLayerRender: function (props) {\n            plugins.forEach(function (plugin) {\n                if (plugin.onTextLayerRender) {\n                    plugin.onTextLayerRender(props);\n                }\n            });\n        },\n        onViewerStateChange: function (viewerState) {\n            var newState = viewerState;\n            plugins.forEach(function (plugin) {\n                if (plugin.onViewerStateChange) {\n                    newState = plugin.onViewerStateChange(newState);\n                }\n            });\n            return newState;\n        },\n        renderDefaultToolbar: renderDefaultToolbar,\n        Toolbar: ToolbarDecorator,\n    };\n};\n\nexports.MoreActionsPopover = MoreActionsPopover;\nexports.MoreIcon = MoreIcon;\nexports.toolbarPlugin = toolbarPlugin;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/toolbar.min.js');\n} else {\n    module.exports = require('./cjs/toolbar.js');\n}\n", "'use strict';\n\nvar core = require('@react-pdf-viewer/core');\nvar React = require('react');\nvar attachment = require('@react-pdf-viewer/attachment');\nvar bookmark = require('@react-pdf-viewer/bookmark');\nvar thumbnail = require('@react-pdf-viewer/thumbnail');\nvar toolbar = require('@react-pdf-viewer/toolbar');\n\nfunction _interopNamespaceDefault(e) {\n    var n = Object.create(null);\n    if (e) {\n        Object.keys(e).forEach(function (k) {\n            if (k !== 'default') {\n                var d = Object.getOwnPropertyDescriptor(e, k);\n                Object.defineProperty(n, k, d.get ? d : {\n                    enumerable: true,\n                    get: function () { return e[k]; }\n                });\n            }\n        });\n    }\n    n.default = e;\n    return Object.freeze(n);\n}\n\nvar React__namespace = /*#__PURE__*/_interopNamespaceDefault(React);\n\nvar BookmarkIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M11.5,1.5h11c0.552,0,1,0.448,1,1v20c0,0.552-0.448,1-1,1h-21c-0.552,0-1-0.448-1-1v-20c0-0.552,0.448-1,1-1h3\\n            M11.5,10.5c0,0.55-0.3,0.661-0.659,0.248L8,7.5l-2.844,3.246c-0.363,0.414-0.659,0.3-0.659-0.247v-9c0-0.552,0.448-1,1-1h5\\n            c0.552,0,1,0.448,1,1L11.5,10.5z\\n            M14.5,6.499h6\\n            M14.5,10.499h6\\n            M3.5,14.499h17\\n            M3.5,18.499h16.497\" }))); };\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar FileIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M7.618,15.345l8.666-8.666c0.78-0.812,2.071-0.838,2.883-0.058s0.838,2.071,0.058,2.883\\n            c-0.019,0.02-0.038,0.039-0.058,0.058L7.461,21.305c-1.593,1.593-4.175,1.592-5.767,0s-1.592-4.175,0-5.767c0,0,0,0,0,0\\n            L13.928,3.305c2.189-2.19,5.739-2.19,7.929-0.001s2.19,5.739,0,7.929l0,0L13.192,19.9\" }))); };\n\nvar ThumbnailIcon = function () { return (React__namespace.createElement(core.Icon, { size: 16 },\n    React__namespace.createElement(\"path\", { d: \"M10.5,9.5c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V9.5z\\n            M23.5,9.5c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V9.5z\\n            M10.5,22.5 c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V22.5z\\n            M23.5,22.5c0,0.552-0.448,1-1,1 h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V22.5z\" }))); };\n\nvar TOOLTIP_OFFSET_LTR = { left: 8, top: 0 };\nvar TOOLTIP_OFFSET_RTL = { left: -8, top: 0 };\nvar Sidebar = function (_a) {\n    var attachmentTabContent = _a.attachmentTabContent, bookmarkTabContent = _a.bookmarkTabContent, store = _a.store, thumbnailTabContent = _a.thumbnailTabContent, tabs = _a.tabs;\n    var containerRef = React__namespace.useRef();\n    var l10n = React__namespace.useContext(core.LocalizationContext).l10n;\n    var _b = React__namespace.useState(store.get('isCurrentTabOpened') || false), opened = _b[0], setOpened = _b[1];\n    var _c = React__namespace.useState(Math.max(store.get('currentTab') || 0, 0)), currentTab = _c[0], setCurrentTab = _c[1];\n    var direction = React__namespace.useContext(core.ThemeContext).direction;\n    var isRtl = direction === core.TextDirection.RightToLeft;\n    var resizeConstrain = function (size) { return size.firstHalfPercentage >= 20 && size.firstHalfPercentage <= 80; };\n    var defaultTabs = [\n        {\n            content: thumbnailTabContent,\n            icon: React__namespace.createElement(ThumbnailIcon, null),\n            title: l10n && l10n.defaultLayout\n                ? l10n.defaultLayout.thumbnail\n                : 'Thumbnail',\n        },\n        {\n            content: bookmarkTabContent,\n            icon: React__namespace.createElement(BookmarkIcon, null),\n            title: l10n && l10n.defaultLayout ? l10n.defaultLayout.bookmark : 'Bookmark',\n        },\n        {\n            content: attachmentTabContent,\n            icon: React__namespace.createElement(FileIcon, null),\n            title: l10n && l10n.defaultLayout\n                ? l10n.defaultLayout.attachment\n                : 'Attachment',\n        },\n    ];\n    var listTabs = tabs ? tabs(defaultTabs) : defaultTabs;\n    var toggleTab = function (index) {\n        if (currentTab === index) {\n            store.update('isCurrentTabOpened', !store.get('isCurrentTabOpened'));\n            var container = containerRef.current;\n            if (container) {\n                var width = container.style.width;\n                if (width) {\n                    container.style.removeProperty('width');\n                }\n            }\n        }\n        else {\n            store.update('currentTab', index);\n        }\n    };\n    var switchToTab = function (index) {\n        if (index >= 0 && index <= listTabs.length - 1) {\n            store.update('isCurrentTabOpened', true);\n            setCurrentTab(index);\n        }\n    };\n    var handleCurrentTabOpened = function (opened) {\n        setOpened(opened);\n    };\n    React__namespace.useEffect(function () {\n        store.subscribe('currentTab', switchToTab);\n        store.subscribe('isCurrentTabOpened', handleCurrentTabOpened);\n        return function () {\n            store.unsubscribe('currentTab', switchToTab);\n            store.unsubscribe('isCurrentTabOpened', handleCurrentTabOpened);\n        };\n    }, []);\n    if (listTabs.length === 0) {\n        return React__namespace.createElement(React__namespace.Fragment, null);\n    }\n    return (React__namespace.createElement(React__namespace.Fragment, null,\n        React__namespace.createElement(\"div\", { \"data-testid\": \"default-layout__sidebar\", className: core.classNames({\n                'rpv-default-layout__sidebar': true,\n                'rpv-default-layout__sidebar--opened': opened,\n                'rpv-default-layout__sidebar--ltr': !isRtl,\n                'rpv-default-layout__sidebar--rtl': isRtl,\n            }), ref: containerRef },\n            React__namespace.createElement(\"div\", { className: \"rpv-default-layout__sidebar-tabs\" },\n                React__namespace.createElement(\"div\", { className: \"rpv-default-layout__sidebar-headers\", role: \"tablist\", \"aria-orientation\": \"vertical\" }, listTabs.map(function (tab, index) { return (React__namespace.createElement(\"div\", { \"aria-controls\": \"rpv-default-layout__sidebar-content\", \"aria-selected\": currentTab === index, key: index, className: \"rpv-default-layout__sidebar-header\", id: \"rpv-default-layout__sidebar-tab-\".concat(index), role: \"tab\" },\n                    React__namespace.createElement(core.Tooltip, { ariaControlsSuffix: \"default-layout-sidebar-tab-\".concat(index), position: isRtl ? core.Position.LeftCenter : core.Position.RightCenter, target: React__namespace.createElement(core.MinimalButton, { ariaLabel: tab.title, isSelected: currentTab === index, onClick: function () { return toggleTab(index); } }, tab.icon), content: function () { return tab.title; }, offset: isRtl ? TOOLTIP_OFFSET_RTL : TOOLTIP_OFFSET_LTR }))); })),\n                React__namespace.createElement(\"div\", { \"aria-labelledby\": \"rpv-default-layout__sidebar-tab-\".concat(currentTab), id: \"rpv-default-layout__sidebar-content\", className: core.classNames({\n                        'rpv-default-layout__sidebar-content': true,\n                        'rpv-default-layout__sidebar-content--opened': opened,\n                        'rpv-default-layout__sidebar-content--ltr': !isRtl,\n                        'rpv-default-layout__sidebar-content--rtl': isRtl,\n                    }), role: \"tabpanel\", tabIndex: -1 }, listTabs[currentTab].content))),\n        opened && React__namespace.createElement(core.Splitter, { constrain: resizeConstrain })));\n};\n\nvar defaultLayoutPlugin = function (props) {\n    var store = React__namespace.useMemo(function () {\n        return core.createStore({\n            isCurrentTabOpened: false,\n            currentTab: 0,\n        });\n    }, []);\n    var attachmentPluginInstance = attachment.attachmentPlugin();\n    var bookmarkPluginInstance = bookmark.bookmarkPlugin();\n    var thumbnailPluginInstance = thumbnail.thumbnailPlugin(props ? props.thumbnailPlugin : {});\n    var toolbarPluginInstance = toolbar.toolbarPlugin(props ? props.toolbarPlugin : {});\n    var Attachments = attachmentPluginInstance.Attachments;\n    var Bookmarks = bookmarkPluginInstance.Bookmarks;\n    var Thumbnails = thumbnailPluginInstance.Thumbnails;\n    var Toolbar = toolbarPluginInstance.Toolbar;\n    var sidebarTabs = props ? props.sidebarTabs : function (defaultTabs) { return defaultTabs; };\n    var plugins = [attachmentPluginInstance, bookmarkPluginInstance, thumbnailPluginInstance, toolbarPluginInstance];\n    return {\n        attachmentPluginInstance: attachmentPluginInstance,\n        bookmarkPluginInstance: bookmarkPluginInstance,\n        thumbnailPluginInstance: thumbnailPluginInstance,\n        toolbarPluginInstance: toolbarPluginInstance,\n        activateTab: function (index) {\n            store.update('currentTab', index);\n        },\n        toggleTab: function (index) {\n            var currentTab = store.get('currentTab');\n            store.update('isCurrentTabOpened', !store.get('isCurrentTabOpened'));\n            if (currentTab !== index) {\n                store.update('currentTab', index);\n            }\n        },\n        install: function (pluginFunctions) {\n            plugins.forEach(function (plugin) {\n                if (plugin.install) {\n                    plugin.install(pluginFunctions);\n                }\n            });\n        },\n        renderPageLayer: function (renderProps) { return (React__namespace.createElement(React__namespace.Fragment, null, plugins.map(function (plugin, idx) {\n            return plugin.renderPageLayer ? (React__namespace.createElement(React__namespace.Fragment, { key: idx }, plugin.renderPageLayer(renderProps))) : (React__namespace.createElement(React__namespace.Fragment, { key: idx },\n                React__namespace.createElement(React__namespace.Fragment, null)));\n        }))); },\n        renderViewer: function (renderProps) {\n            var slot = renderProps.slot;\n            plugins.forEach(function (plugin) {\n                if (plugin.renderViewer) {\n                    slot = plugin.renderViewer(__assign(__assign({}, renderProps), { slot: slot }));\n                }\n            });\n            var mergeSubSlot = slot.subSlot && slot.subSlot.attrs\n                ? {\n                    className: slot.subSlot.attrs.className,\n                    'data-testid': slot.subSlot.attrs['data-testid'],\n                    ref: slot.subSlot.attrs.ref,\n                    style: slot.subSlot.attrs.style,\n                }\n                : {};\n            slot.children = (React__namespace.createElement(\"div\", { className: \"rpv-default-layout__container\" },\n                React__namespace.createElement(\"div\", { \"data-testid\": \"default-layout__main\", className: core.classNames({\n                        'rpv-default-layout__main': true,\n                        'rpv-default-layout__main--rtl': renderProps.themeContext.direction === core.TextDirection.RightToLeft,\n                    }) },\n                    React__namespace.createElement(Sidebar, { attachmentTabContent: React__namespace.createElement(Attachments, null), bookmarkTabContent: React__namespace.createElement(Bookmarks, null), store: store, thumbnailTabContent: React__namespace.createElement(Thumbnails, null), tabs: sidebarTabs }),\n                    React__namespace.createElement(\"div\", { className: \"rpv-default-layout__body\", \"data-testid\": \"default-layout__body\" },\n                        React__namespace.createElement(\"div\", { className: \"rpv-default-layout__toolbar\" }, props && props.renderToolbar ? props.renderToolbar(Toolbar) : React__namespace.createElement(Toolbar, null)),\n                        React__namespace.createElement(\"div\", __assign({}, mergeSubSlot), slot.subSlot.children))),\n                slot.children));\n            slot.subSlot.attrs = {};\n            slot.subSlot.children = React__namespace.createElement(React__namespace.Fragment, null);\n            return slot;\n        },\n        uninstall: function (pluginFunctions) {\n            plugins.forEach(function (plugin) {\n                if (plugin.uninstall) {\n                    plugin.uninstall(pluginFunctions);\n                }\n            });\n        },\n        onDocumentLoad: function (documentLoadProps) {\n            plugins.forEach(function (plugin) {\n                if (plugin.onDocumentLoad) {\n                    plugin.onDocumentLoad(documentLoadProps);\n                }\n            });\n            if (props && props.setInitialTab) {\n                props.setInitialTab(documentLoadProps.doc).then(function (initialTab) {\n                    store.update('currentTab', initialTab);\n                    store.update('isCurrentTabOpened', true);\n                });\n            }\n        },\n        onAnnotationLayerRender: function (props) {\n            plugins.forEach(function (plugin) {\n                if (plugin.onAnnotationLayerRender) {\n                    plugin.onAnnotationLayerRender(props);\n                }\n            });\n        },\n        onTextLayerRender: function (props) {\n            plugins.forEach(function (plugin) {\n                if (plugin.onTextLayerRender) {\n                    plugin.onTextLayerRender(props);\n                }\n            });\n        },\n        onViewerStateChange: function (viewerState) {\n            var newState = viewerState;\n            plugins.forEach(function (plugin) {\n                if (plugin.onViewerStateChange) {\n                    newState = plugin.onViewerStateChange(newState);\n                }\n            });\n            return newState;\n        },\n    };\n};\n\nvar setInitialTabFromPageMode = function (doc) {\n    return new Promise(function (resolve, _) {\n        doc.getPageMode().then(function (pageMode) {\n            if (!pageMode) {\n                resolve(-1);\n            }\n            else {\n                switch (pageMode) {\n                    case core.PageMode.Attachments:\n                        resolve(2);\n                        break;\n                    case core.PageMode.Bookmarks:\n                        resolve(1);\n                        break;\n                    case core.PageMode.Thumbnails:\n                        resolve(0);\n                        break;\n                    default:\n                        resolve(-1);\n                        break;\n                }\n            }\n        });\n    });\n};\n\nexports.BookmarkIcon = BookmarkIcon;\nexports.FileIcon = FileIcon;\nexports.ThumbnailIcon = ThumbnailIcon;\nexports.defaultLayoutPlugin = defaultLayoutPlugin;\nexports.setInitialTabFromPageMode = setInitialTabFromPageMode;\n", "/**\n * A React component to view a PDF document\n *\n * @see https://react-pdf-viewer.dev\n * @license https://react-pdf-viewer.dev/license\n * @copyright 2019-2023 <PERSON><PERSON><PERSON> <<EMAIL>>\n */\n\n'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n    module.exports = require('./cjs/default-layout.min.js');\n} else {\n    module.exports = require('./cjs/default-layout.js');\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,cAAc,SAAU,KAAK;AAC7B,UAAI,MAAM,IAAI,MAAM,GAAG,EAAE,IAAI;AAC7B,aAAO,MAAM,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,IACnD;AAEA,QAAI,eAAe,SAAU,KAAK,MAAM;AACpC,UAAI,UAAU,OAAO,SAAS,WAAW,KAAK,IAAI,gBAAgB,IAAI,KAAK,CAAC,IAAI,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC;AAChG,UAAI,OAAO,SAAS,cAAc,GAAG;AACrC,WAAK,MAAM,UAAU;AACrB,WAAK,OAAO,WAAW;AACvB,WAAK,aAAa,YAAY,YAAY,GAAG,CAAC;AAC9C,eAAS,KAAK,YAAY,IAAI;AAC9B,WAAK,MAAM;AACX,eAAS,KAAK,YAAY,IAAI;AAC9B,UAAI,SAAS;AACT,YAAI,gBAAgB,OAAO;AAAA,MAC/B;AAAA,IACJ;AAEA,QAAI,iBAAiB,SAAU,IAAI;AAC/B,UAAI,QAAQ,GAAG;AACf,UAAI,eAAe,iBAAiB,OAAO;AAC3C,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,YAAY,iBAAiB,WAAW,KAAK,YAAY,EAAE;AAC/D,UAAI,QAAQ,cAAc,KAAK,cAAc;AAC7C,UAAI,qBAAqB,iBAAiB,OAAO,CAAC,CAAC;AACnD,UAAI,qBAAqB,QAAQ,KAAK,aAChC,KAAK,WAAW,kBAChB;AACN,UAAI,gBAAgB,SAAU,GAAG;AAC7B,gBAAQ,EAAE,KAAK;AAAA,UACX,KAAK;AACD,cAAE,eAAe;AACjB,uBAAW,SAAU,OAAO,WAAW;AAAE,qBAAO,MAAM,QAAQ,SAAS,IAAI;AAAA,YAAG,CAAC;AAC/E;AAAA,UACJ,KAAK;AACD,cAAE,eAAe;AACjB,uBAAW,SAAU,OAAO,WAAW;AAAE,qBAAO,MAAM,QAAQ,SAAS,IAAI;AAAA,YAAG,CAAC;AAC/E;AAAA,UACJ,KAAK;AACD,cAAE,eAAe;AACjB,uBAAW,SAAU,OAAO,GAAG;AAAE,qBAAO,MAAM,SAAS;AAAA,YAAG,CAAC;AAC3D;AAAA,UACJ,KAAK;AACD,cAAE,eAAe;AACjB,uBAAW,SAAU,GAAG,IAAI;AAAE,qBAAO;AAAA,YAAG,CAAC;AACzC;AAAA,QACR;AAAA,MACJ;AACA,UAAI,aAAa,SAAU,cAAc;AACrC,YAAI,YAAY,aAAa;AAC7B,YAAI,kBAAkB,CAAC,EAAE,MAAM,KAAK,UAAU,uBAAuB,sBAAsB,CAAC;AAC5F,YAAI,gBAAgB,WAAW,GAAG;AAC9B;AAAA,QACJ;AACA,wBAAgB,QAAQ,SAAU,MAAM;AAAE,iBAAO,KAAK,aAAa,YAAY,IAAI;AAAA,QAAG,CAAC;AACvF,YAAI,YAAY,SAAS;AACzB,YAAI,cAAc,KAAK,IAAI,gBAAgB,SAAS,GAAG,KAAK,IAAI,GAAG,aAAa,iBAAiB,SAAS,CAAC,CAAC;AAC5G,YAAI,YAAY,gBAAgB,WAAW;AAC3C,kBAAU,aAAa,YAAY,GAAG;AACtC,kBAAU,MAAM;AAAA,MACpB;AACA,WAAK,0BAA0B,WAAY;AACvC,YAAI,YAAY,aAAa;AAC7B,YAAI,CAAC,WAAW;AACZ;AAAA,QACJ;AACA,YAAI,kBAAkB,CAAC,EAAE,MAAM,KAAK,UAAU,uBAAuB,sBAAsB,CAAC;AAC5F,2BAAmB,UAAU;AAC7B,YAAI,gBAAgB,SAAS,GAAG;AAC5B,cAAI,YAAY,gBAAgB,CAAC;AACjC,oBAAU,MAAM;AAChB,oBAAU,aAAa,YAAY,GAAG;AAAA,QAC1C;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAQ,iBAAiB,cAAc,OAAO,EAAE,eAAe,oBAAoB,WAAW,KAAK,WAAW;AAAA,QACtG,wBAAwB;AAAA,QACxB,6BAA6B;AAAA,MACjC,CAAC,GAAG,KAAK,cAAc,UAAU,IAAI,WAAW,cAAc,GAAG,MAAM,IAAI,SAAU,MAAM;AAAE,eAAQ,iBAAiB,cAAc,UAAU,EAAE,WAAW,wBAAwB,KAAK,KAAK,UAAU,UAAU,IAAI,OAAO,oBAAoB,MAAM,UAAU,SAAS,WAAY;AAAE,iBAAO,aAAa,KAAK,UAAU,KAAK,IAAI;AAAA,QAAG,EAAE,GAAG,KAAK,QAAQ;AAAA,MAAI,CAAC,CAAC;AAAA,IACtW;AAEA,QAAI,mBAAmB,SAAU,IAAI;AACjC,UAAI,MAAM,GAAG;AACb,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,YAAY,iBAAiB,WAAW,KAAK,YAAY,EAAE;AAC/D,UAAI,QAAQ,cAAc,KAAK,cAAc;AAC7C,UAAI,oBAAoB,QAAQ,KAAK,aAC/B,KAAK,WAAW,eAChB;AACN,UAAI,KAAK,iBAAiB,SAAS;AAAA,QAC/B,OAAO,CAAC;AAAA,QACR,UAAU;AAAA,MACd,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AAC9C,uBAAiB,UAAU,WAAY;AACnC,YAAI,eAAe,EAAE,KAAK,SAAU,UAAU;AAC1C,cAAI,QAAQ,WACN,OAAO,KAAK,QAAQ,EAAE,IAAI,SAAU,MAAM;AACxC,mBAAO;AAAA,cACH,MAAM,SAAS,IAAI,EAAE;AAAA,cACrB,UAAU,SAAS,IAAI,EAAE;AAAA,YAC7B;AAAA,UACJ,CAAC,IACC,CAAC;AACP,yBAAe;AAAA,YACX;AAAA,YACA,UAAU;AAAA,UACd,CAAC;AAAA,QACL,CAAC;AAAA,MACL,GAAG,CAAC,GAAG,CAAC;AACR,aAAO,CAAC,YAAY,WAAY,iBAAiB,cAAc,KAAK,SAAS,IAAI,IAAK,YAAY,MAAM,WAAW,IAAK,iBAAiB,cAAc,OAAO,EAAE,eAAe,qBAAqB,WAAW,KAAK,WAAW;AAAA,QACvN,yBAAyB;AAAA,QACzB,8BAA8B;AAAA,MAClC,CAAC,EAAE,GAAG,iBAAiB,IAAM,iBAAiB,cAAc,gBAAgB,EAAE,OAAO,YAAY,MAAM,CAAC;AAAA,IAChH;AAEA,QAAI,0BAA0B,SAAU,IAAI;AACxC,UAAI,QAAQ,GAAG;AACf,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,KAAK,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AAC9F,UAAI,wBAAwB,SAAU,KAAK;AACvC,sBAAc,GAAG;AAAA,MACrB;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,OAAO,qBAAqB;AAC5C,eAAO,WAAY;AACf,gBAAM,YAAY,OAAO,qBAAqB;AAAA,QAClD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO,aAAc,iBAAiB,cAAc,kBAAkB,EAAE,KAAK,WAAW,CAAC,IAAM,iBAAiB;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,yBAAyB;AAAA,QACvK,iBAAiB,cAAc,KAAK,SAAS,IAAI;AAAA,MAAC;AAAA,IAC1D;AAEA,QAAI,mBAAmB,WAAY;AAC/B,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAAE,eAAO,KAAK,YAAY,CAAC,CAAC;AAAA,MAAG,GAAG,CAAC,CAAC;AACrF,UAAI,uBAAuB,WAAY;AAAE,eAAO,iBAAiB,cAAc,yBAAyB,EAAE,MAAa,CAAC;AAAA,MAAG;AAC3H,aAAO;AAAA,QACH,gBAAgB,SAAU,OAAO;AAC7B,gBAAM,OAAO,OAAO,MAAM,GAAG;AAAA,QACjC;AAAA,QACA,aAAa;AAAA,MACjB;AAAA,IACJ;AAEA,YAAQ,mBAAmB;AAAA;AAAA;;;ACtK3B,IAAAA,eAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAiBlE,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,gBAAgB,WAAY;AAC5B,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QACzD,iBAAiB,cAAc,QAAQ,EAAE,GAAG,2FAA2F,CAAC;AAAA,MAAC;AAAA,IACjJ;AAEA,QAAI,iBAAiB,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAC5F,iBAAiB,cAAc,QAAQ,EAAE,GAAG,+FAA+F,CAAC;AAAA,MAAC;AAAA,IAAI;AAErJ,QAAI,oBAAoB,SAAU,UAAU;AACxC,UAAI,QAAQ,SAAS,OAAO,QAAQ,SAAS;AAC7C,UAAI,SAAS,GAAG;AACZ,eAAO;AAAA,MACX;AACA,UAAI,cAAc,MAAM;AACxB,UAAI,gBAAgB,GAAG;AACnB,eAAO;AAAA,MACX;AACA,UAAI,WAAW,MAAM,OAAO,CAAC,CAAC;AAC9B,aAAO,SAAS,SAAS,GAAG;AACxB,YAAI,aAAa,SAAS,MAAM;AAChC,YAAI,WAAW,WAAW;AAC1B,YAAI,WAAW,SAAS,YAAY,WAAW,QAAQ,KAAK,SAAS,SAAS,GAAG;AAC7E,yBAAe,SAAS;AACxB,qBAAW,SAAS,OAAO,QAAQ;AAAA,QACvC;AAAA,MACJ;AACA,aAAO,KAAK,IAAI,KAAK,MAAM;AAAA,IAC/B;AAEA,QAAI,eAAe,SAAU,IAAI;AAC7B,UAAI,WAAW,GAAG,UAAU,QAAQ,GAAG,OAAO,MAAM,GAAG,KAAK,QAAQ,GAAG,OAAO,qBAAqB,GAAG,oBAAoB,mBAAmB,GAAG,kBAAkB,eAAe,GAAG,cAAc,qBAAqB,GAAG,oBAAoB,QAAQ,GAAG;AACzP,UAAI,OAAO,eAAe,GAAG,OAAO,cAAc,GAAG,EAAE,OAAO,KAAK,IAAI,GAAG,OAAO,KAAK;AACtF,UAAI,qBAAqB,iBAAiB,QAAQ,WAAY;AAAE,eAAO,kBAAkB,QAAQ;AAAA,MAAG,GAAG,CAAC,QAAQ,CAAC;AACjH,UAAI,sBAAsB,MAAM,IAAI,qBAAqB;AACzD,UAAI,kBAAkB,qBAChB,mBAAmB,EAAE,UAAoB,KAAU,OAAc,MAAa,CAAC,IAC/E,oBAAoB,IAAI,IAAI,IACxB,oBAAoB,IAAI,IAAI,IAC5B,CAAC;AACX,UAAI,KAAK,iBAAiB,SAAS,eAAe,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;AACzF,UAAI,cAAc,SAAS,SAAS,SAAS,MAAM,SAAS;AAC5D,UAAI,iBAAiB,WAAY;AAC7B,YAAI,WAAW,CAAC;AAChB,cAAM,mBAAmB,uBAAuB,SAAU,cAAc;AAAE,iBAAO,aAAa,IAAI,MAAM,QAAQ;AAAA,QAAG,CAAC;AACpH,oBAAY,QAAQ;AAAA,MACxB;AACA,UAAI,aAAa,WAAY;AACzB,YAAI,OAAO,SAAS;AACpB,YAAI,oBAAoB,MAAM,IAAI,mBAAmB;AACrD,aAAK,eAAe,KAAK,IAAI,EAAE,KAAK,SAAU,QAAQ;AAClD,cAAI,mBAAmB;AACnB,8BAAkB,SAAS,EAAE,OAAO,SAAS,MAAM,GAAG,MAAM,CAAC;AAAA,UACjE;AAAA,QACJ,CAAC;AAAA,MACL;AACA,UAAI,gBAAgB,WAAY;AAC5B,YAAI,eAAe,SAAS,MAAM;AAC9B,qBAAW;AAAA,QACf;AAAA,MACJ;AACA,UAAI,YAAY,WAAY;AACxB,YAAI,CAAC,eAAe,SAAS,MAAM;AAC/B,qBAAW;AAAA,QACf;AAAA,MACJ;AACA,UAAI,oBAAoB,SAAU,aAAa,UAAU;AAAE,eAAQ,iBAAiB,cAAc,OAAO,EAAE,WAAW,sBAAsB,OAAO;AAAA,UAC3I,aAAa,GAAG,OAAO,QAAQ,MAAM,KAAK;AAAA,QAC9C,GAAG,SAAS,YAAY,GAAG,QAAQ;AAAA,MAAI;AAC3C,UAAI,sBAAsB,SAAU,YAAY,cAAc;AAC1D,eAAO,cAAe,iBAAiB,cAAc,QAAQ,EAAE,WAAW,wBAAwB,eAAe,oBAAoB,OAAO,OAAO,GAAG,EAAE,OAAO,KAAK,GAAG,SAAS,eAAe,GAAG,WAAW,aAAa,YAAY,IAAM,iBAAiB,cAAc,QAAQ,EAAE,WAAW,uBAAuB,CAAC;AAAA,MAC5T;AACA,UAAI,qBAAqB,SAAU,iBAAiB;AAChD,eAAO,SAAS,MAAO,iBAAiB,cAAc,KAAK,EAAE,WAAW,uBAAuB,MAAM,SAAS,KAAK,KAAK,gCAAgC,QAAQ,SAAS,YAAY,WAAW,GAAG,GAAG,SAAS,KAAK,IAAM,iBAAiB,cAAc,OAAO,EAAE,WAAW,uBAAuB,cAAc,SAAS,OAAO,SAAS,gBAAgB,GAAG,SAAS,KAAK;AAAA,MAChX;AACA,aAAQ,iBAAiB;AAAA,QAAc;AAAA,QAAM,EAAE,iBAAiB,WAAW,SAAS,SAAS,cAAc,SAAS,OAAO,cAAc,QAAQ,GAAG,iBAAiB,QAAQ,GAAG,gBAAgB,kBAAkB,MAAM,YAAY,UAAU,GAAG;AAAA,QAC7O,qBACM,mBAAmB;AAAA,UACjB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,YAAY;AAAA,UACZ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,aAAa;AAAA,UACb,cAAc;AAAA,UACd,kBAAkB;AAAA,QACtB,CAAC,IACC,kBAAkB,WAAW,iBAAiB;AAAA,UAAc,iBAAiB;AAAA,UAAU;AAAA,UACrF,oBAAoB,iBAAiB,cAAc,eAAe,IAAI,GAAG,iBAAiB,cAAc,gBAAgB,IAAI,CAAC;AAAA,UAC7H,mBAAmB,aAAa;AAAA,QAAC,CAAC;AAAA,QAC1C,eAAe,YAAa,iBAAiB,cAAc,cAAc,EAAE,WAAW,SAAS,OAAO,OAAO,QAAQ,GAAG,KAAU,oBAAwC,QAAQ,OAAO,cAAc,MAAM,oBAAwC,MAAa,CAAC;AAAA,MAAE;AAAA,IAC7Q;AAEA,QAAI,eAAe,SAAU,IAAI;AAC7B,UAAI,YAAY,GAAG,WAAW,KAAK,GAAG,OAAO,QAAQ,OAAO,SAAS,IAAI,IAAI,MAAM,GAAG,KAAK,qBAAqB,GAAG,oBAAoB,SAAS,GAAG,QAAQ,eAAe,GAAG,cAAc,qBAAqB,GAAG,oBAAoB,QAAQ,GAAG;AAClP,aAAQ,iBAAiB,cAAc,MAAM,EAAE,WAAW,sBAAsB,MAAM,SAAS,SAAS,SAAS,UAAU,GAAG,GAAG,UAAU,IAAI,SAAU,UAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,cAAc,EAAE,UAAoB,OAAc,KAAU,OAAc,oBAAwC,KAAK,OAAO,kBAAkB,UAAU,QAAQ,cAA4B,oBAAwC,MAAa,CAAC;AAAA,MAAI,CAAC,CAAC;AAAA,IACjd;AAEA,QAAI;AACJ,KAAC,SAAUC,SAAQ;AACf,MAAAA,QAAOA,QAAO,UAAU,IAAI,CAAC,IAAI;AACjC,MAAAA,QAAOA,QAAO,QAAQ,IAAI,CAAC,IAAI;AAAA,IACnC,GAAG,WAAW,SAAS,CAAC,EAAE;AAC1B,QAAI,mBAAmB,SAAU,IAAI;AACjC,UAAI,YAAY,GAAG,WAAW,MAAM,GAAG,KAAK,qBAAqB,GAAG,oBAAoB,qBAAqB,GAAG,oBAAoB,QAAQ,GAAG;AAC/I,UAAI,eAAe,iBAAiB,OAAO;AAC3C,UAAI,gBAAgB,SAAU,GAAG;AAC7B,YAAI,YAAY,aAAa;AAC7B,YAAI,CAAC,aAAa,EAAE,EAAE,kBAAkB,gBAAgB,CAAC,UAAU,SAAS,EAAE,MAAM,GAAG;AACnF;AAAA,QACJ;AACA,gBAAQ,EAAE,KAAK;AAAA,UACX,KAAK;AACD,cAAE,eAAe;AACjB,uBAAW,SAAU,kBAAkB,WAAW;AAAE,qBAAO,iBAAiB,QAAQ,SAAS,IAAI;AAAA,YAAG,CAAC;AACrG;AAAA,UACJ,KAAK;AACD,cAAE,eAAe;AACjB,mBAAO,OAAO,QAAQ;AACtB;AAAA,UACJ,KAAK;AACD,cAAE,eAAe;AACjB,mBAAO,OAAO,MAAM;AACpB;AAAA,UACJ,KAAK;AACD,cAAE;AACF,uBAAW,SAAU,kBAAkB,WAAW;AAAE,qBAAO,iBAAiB,QAAQ,SAAS,IAAI;AAAA,YAAG,CAAC;AACrG;AAAA,UACJ,KAAK;AACD,cAAE,eAAe;AACjB,uBAAW,SAAU,kBAAkB,GAAG;AAAE,qBAAO,iBAAiB,SAAS;AAAA,YAAG,CAAC;AACjF;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACD,cAAE,eAAe;AACjB,0BAAc;AACd;AAAA,UACJ,KAAK;AACD,cAAE,eAAe;AACjB,uBAAW,SAAU,GAAG,IAAI;AAAE,qBAAO;AAAA,YAAG,CAAC;AACzC;AAAA,QACR;AAAA,MACJ;AACA,UAAI,gBAAgB,WAAY;AAC5B,YAAI,cAAc,SAAS,cAAc,QAAQ,qBAAqB;AACtE,YAAI,WAAW,YAAY,cAAc,sBAAsB;AAC/D,YAAI,UAAU;AACV,mBAAS,MAAM;AAAA,QACnB;AAAA,MACJ;AACA,UAAI,aAAa,SAAU,cAAc;AACrC,YAAI,YAAY,aAAa;AAC7B,YAAI,mBAAmB,CAAC,EAAE,MAAM,KAAK,UAAU,uBAAuB,oBAAoB,CAAC;AAC3F,YAAI,iBAAiB,WAAW,GAAG;AAC/B;AAAA,QACJ;AACA,YAAI,YAAY,SAAS;AACzB,YAAI,cAAc,KAAK,IAAI,iBAAiB,SAAS,GAAG,KAAK,IAAI,GAAG,aAAa,kBAAkB,SAAS,CAAC,CAAC;AAC9G,YAAI,YAAY,iBAAiB,WAAW;AAC5C,kBAAU,aAAa,YAAY,IAAI;AACvC,kBAAU,aAAa,YAAY,GAAG;AACtC,kBAAU,MAAM;AAAA,MACpB;AACA,UAAI,SAAS,SAAUC,SAAQ;AAC3B,YAAI,YAAY,aAAa;AAC7B,YAAI,mBAAmB,CAAC,EAAE,MAAM,KAAK,UAAU,uBAAuB,oBAAoB,CAAC;AAC3F,YAAI,iBAAiB,WAAW,GAAG;AAC/B;AAAA,QACJ;AACA,YAAI,cAAc,SAAS,cAAc,QAAQ,qBAAqB;AACtE,YAAI,mBAAmBA,YAAW,OAAO,WAAW,SAAS;AAC7D,YAAI,eAAe,YAAY,cAAc,aAAa,eAAe,MAAM,kBAAkB;AAC7F,cAAI,YAAY,YAAY,cAAc,uBAAuB;AACjE,cAAI,WAAW;AACX,sBAAU,MAAM;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AACA,uBAAiB,UAAU,WAAY;AACnC,iBAAS,iBAAiB,WAAW,aAAa;AAClD,eAAO,WAAY;AACf,mBAAS,oBAAoB,WAAW,aAAa;AAAA,QACzD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,uBAAiB,UAAU,WAAY;AACnC,YAAI,YAAY,aAAa;AAC7B,YAAI,CAAC,WAAW;AACZ;AAAA,QACJ;AACA,YAAI,mBAAmB,CAAC,EAAE,MAAM,KAAK,UAAU,uBAAuB,oBAAoB,CAAC;AAC3F,YAAI,iBAAiB,SAAS,GAAG;AAC7B,2BAAiB,CAAC,EAAE,MAAM;AAC1B,2BAAiB,CAAC,EAAE,aAAa,YAAY,GAAG;AAAA,QACpD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAQ,iBAAiB;AAAA,QAAc;AAAA,QAAO,EAAE,KAAK,aAAa;AAAA,QAC9D,iBAAiB,cAAc,cAAc,EAAE,WAAsB,OAAO,GAAG,KAAU,oBAAwC,QAAQ,MAAM,cAAc,IAAI,oBAAwC,MAAa,CAAC;AAAA,MAAC;AAAA,IAChO;AAEA,QAAI,iBAAiB,SAAU,IAAI;AAC/B,UAAI,MAAM,GAAG,KAAK,qBAAqB,GAAG,oBAAoB,qBAAqB,GAAG,oBAAoB,QAAQ,GAAG;AACrH,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,YAAY,iBAAiB,WAAW,KAAK,YAAY,EAAE;AAC/D,UAAI,QAAQ,cAAc,KAAK,cAAc;AAC7C,UAAI,KAAK,iBAAiB,SAAS;AAAA,QAC/B,UAAU;AAAA,QACV,OAAO,CAAC;AAAA,MACZ,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAC1C,uBAAiB,UAAU,WAAY;AACnC,qBAAa;AAAA,UACT,UAAU;AAAA,UACV,OAAO,CAAC;AAAA,QACZ,CAAC;AACD,YAAI,WAAW,EAAE,KAAK,SAAU,SAAS;AACrC,uBAAa;AAAA,YACT,UAAU;AAAA,YACV,OAAO,WAAW,CAAC;AAAA,UACvB,CAAC;AAAA,QACL,CAAC;AAAA,MACL,GAAG,CAAC,GAAG,CAAC;AACR,aAAO,CAAC,UAAU,WAAY,iBAAiB;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,uBAAuB;AAAA,QACpG,iBAAiB,cAAc,KAAK,SAAS,IAAI;AAAA,MAAC,IAAK,UAAU,MAAM,WAAW,IAAK,iBAAiB,cAAc,OAAO,EAAE,eAAe,mBAAmB,WAAW,KAAK,WAAW;AAAA,QACxL,uBAAuB;AAAA,QACvB,4BAA4B;AAAA,MAChC,CAAC,EAAE,GAAG,QAAQ,KAAK,WAAW,KAAK,SAAS,aAAa,sBAAsB,IAAM,iBAAiB;AAAA,QAAc;AAAA,QAAO,EAAE,eAAe,uBAAuB,WAAW,KAAK,WAAW;AAAA,UAC1L,2BAA2B;AAAA,UAC3B,gCAAgC;AAAA,QACpC,CAAC,EAAE;AAAA,QACH,iBAAiB,cAAc,kBAAkB,EAAE,WAAW,UAAU,OAAO,KAAU,oBAAwC,oBAAwC,MAAa,CAAC;AAAA,MAAC;AAAA,IAChM;AAEA,QAAI,wBAAwB,SAAU,IAAI;AACtC,UAAI,qBAAqB,GAAG,oBAAoB,qBAAqB,GAAG,oBAAoB,QAAQ,GAAG;AACvG,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,KAAK,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AAC9F,UAAI,wBAAwB,SAAU,KAAK;AACvC,sBAAc,GAAG;AAAA,MACrB;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,OAAO,qBAAqB;AAC5C,eAAO,WAAY;AACf,gBAAM,YAAY,OAAO,qBAAqB;AAAA,QAClD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO,aAAc,iBAAiB,cAAc,gBAAgB,EAAE,KAAK,YAAY,oBAAwC,oBAAwC,MAAa,CAAC,IAAM,iBAAiB;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,uBAAuB;AAAA,QACjQ,iBAAiB,cAAc,KAAK,SAAS,IAAI;AAAA,MAAC;AAAA,IAC1D;AAEA,QAAI,iBAAiB,WAAY;AAC7B,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAC7C,eAAO,KAAK,YAAY;AAAA,UACpB,qBAAqB,oBAAI,IAAI;AAAA,QACjC,CAAC;AAAA,MACL,GAAG,CAAC,CAAC;AACL,UAAI,qBAAqB,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,uBAAuB,EAAE,oBAAoB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,oBAAoB,oBAAoB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,oBAAoB,MAAa,CAAC;AAAA,MAAI;AACtT,aAAO;AAAA,QACH,SAAS,SAAU,iBAAiB;AAChC,gBAAM,OAAO,qBAAqB,gBAAgB,iBAAiB;AAAA,QACvE;AAAA,QACA,gBAAgB,SAAU,OAAO;AAC7B,gBAAM,OAAO,OAAO,MAAM,GAAG;AAAA,QACjC;AAAA,QACA,WAAW;AAAA,MACf;AAAA,IACJ;AAEA,YAAQ,gBAAgB;AACxB,YAAQ,iBAAiB;AACzB,YAAQ,iBAAiB;AAAA;AAAA;;;ACjUzB,IAAAC,eAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,YAAQ,qBAAqB;AAC7B,KAAC,SAAU,oBAAoB;AAC3B,yBAAmB,YAAY,IAAI;AACnC,yBAAmB,UAAU,IAAI;AAAA,IACrC,GAAG,QAAQ,uBAAuB,QAAQ,qBAAqB,CAAC,EAAE;AAiBlE,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,aAAa,SAAU,IAAI;AAC3B,UAAI,MAAM,GAAG,KAAK,eAAe,GAAG,cAAc,gBAAgB,GAAG,eAAe,QAAQ,GAAG,OAAO,QAAQ,GAAG;AACjH,UAAI,WAAW,IAAI;AACnB,UAAI,aAAa,eAAe,aAAa,EAAE,SAAmB,CAAC,IAAI;AACvE,UAAI,gBAAgB,KAAK,IAAI,GAAG,KAAK,IAAI,YAAY,WAAW,CAAC,CAAC;AAClE,UAAI,uBAAuB,MAAM,IAAI,eAAe,KAAK,oBAAI,IAAI;AACjE,UAAI,4BAA4B,qBAAqB,IAAI,aAAa,IAChE,qBAAqB,IAAI,aAAa,IACtC;AACN,UAAI,KAAK,iBAAiB,SAAS,EAAE,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC;AAClE,UAAI,YAAY,KAAK,aAAa;AAClC,UAAI,aAAa,iBAAiB,OAAO;AACzC,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,UAAU,KAAK,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;AACpG,UAAI,KAAK,iBAAiB,SAAS,yBAAyB,GAAG,eAAe,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC;AAC3G,UAAI,KAAK,iBAAiB,SAAS,KAAK,GAAG,YAAY,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AAC/E,UAAI,6BAA6B,SAAU,WAAW;AAClD,YAAIC,gBAAe,UAAU,IAAI,aAAa,IAAI,UAAU,IAAI,aAAa,IAAI;AACjF,wBAAgBA,aAAY;AAAA,MAChC;AACA,UAAI,wBAAwB,SAAU,iBAAiB;AACnD,oBAAY,eAAe;AAAA,MAC/B;AACA,UAAI,0BAA0B,SAAU,QAAQ;AAC5C,mBAAW,OAAO,SAAS;AAAA,MAC/B;AACA,UAAI,eAAe,KAAK,wBAAwB;AAAA,QAC5C,qBAAqB;AAAA,MACzB,CAAC;AACD,uBAAiB,UAAU,WAAY;AACnC,YAAI,CAAC,WAAW;AACZ;AAAA,QACJ;AACA,YAAI,eAAe,aAAa;AAChC,YAAI,CAAC,cAAc;AACf;AAAA,QACJ;AACA,eAAO,EAAE;AACT,aAAK,QAAQ,KAAK,aAAa,EAAE,KAAK,SAAU,MAAM;AAClD,cAAI,WAAW,KAAK,YAAY,EAAE,OAAO,EAAE,CAAC;AAC5C,cAAI,mBAAmB,SAAS;AAChC,cAAI,iBAAiB,mBAAmB,WAAW,gBAAgB;AACnE,cAAI,aAAa,KAAK,IAAI,WAAW,YAAY,IAAI,QAAQ;AAC7D,cAAI,IAAI,aAAa,SAAS,QAAQ,SAAS;AAC/C,cAAI,IAAI,aAAa,SAAS,SAAS,SAAS;AAChD,cAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,cAAI,gBAAgB,OAAO,WAAW,MAAM,EAAE,OAAO,MAAM,CAAC;AAC5D,cAAI,iBAAiB,aAAa;AAClC,cAAI,kBAAkB,aAAa;AACnC,cAAI,SAAS,QAAQ,QAAQ,IAAI,KAAK,IAAI,iBAAiB,GAAG,kBAAkB,CAAC;AACjF,cAAI,cAAc,SAAS;AAC3B,cAAI,eAAe,SAAS;AAC5B,iBAAO,SAAS;AAChB,iBAAO,QAAQ;AACf,iBAAO,MAAM,UAAU;AACvB,cAAI,iBAAiB,KAAK,YAAY;AAAA,YAClC,UAAU;AAAA,YACV,OAAO;AAAA,UACX,CAAC;AACD,qBAAW,UAAU,KAAK,OAAO,EAAE,eAA8B,UAAU,eAAe,CAAC;AAC3F,qBAAW,QAAQ,QAAQ,KAAK,WAAY;AACxC,sBAAU,WAAW,OAAO,OAAO,UAAU,CAAC;AAC9C,mBAAO,QAAQ;AACf,mBAAO,SAAS;AAAA,UACpB,GAAG,WAAY;AAAA,UACf,CAAC;AAAA,QACL,CAAC;AAAA,MACL,GAAG,CAAC,cAAc,SAAS,CAAC;AAC5B,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,iBAAiB,0BAA0B;AAC3D,cAAM,UAAU,YAAY,qBAAqB;AACjD,eAAO,WAAY;AACf,gBAAM,YAAY,iBAAiB,0BAA0B;AAC7D,gBAAM,YAAY,YAAY,qBAAqB;AAAA,QACvD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,uBAAiB,UAAU,WAAY;AACnC,eAAO,WAAY;AACf,cAAIC;AACJ,WAACA,MAAK,WAAW,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,OAAO;AAAA,QAC7E;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAQ,iBAAiB,cAAc,OAAO,EAAE,KAAK,cAAc,WAAW,8BAA8B,eAAe,yBAAyB,GAAG,MAAO,iBAAiB,cAAc,OAAO,EAAE,WAAW,8BAA8B,eAAe,0BAA0B,IAAS,CAAC,IAAM,iBAAiB,cAAc,OAAO,EAAE,WAAW,+BAA+B,eAAe,0BAA0B,GAAG,gBAAgB,cAAc,IAAI,iBAAiB,cAAc,KAAK,SAAS,IAAI,CAAC,CAAE;AAAA,IAChgB;AAEA,QAAI,QAAQ,SAAU,IAAI;AACtB,UAAI,eAAe,GAAG,cAAc,gBAAgB,GAAG,eAAe,QAAQ,GAAG,OAAO,QAAQ,GAAG;AACnG,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,KAAK,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AAC9F,UAAI,wBAAwB,SAAU,KAAK;AACvC,sBAAc,GAAG;AAAA,MACrB;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,OAAO,qBAAqB;AAC5C,eAAO,WAAY;AACf,gBAAM,YAAY,OAAO,qBAAqB;AAAA,QAClD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAQ,iBAAiB,cAAc,OAAO,EAAE,WAAW,uBAAuB,GAAG,aAAc,iBAAiB,cAAc,YAAY,EAAE,KAAK,YAAY,cAA4B,eAA8B,OAAc,MAAa,CAAC,IAAM,iBAAiB,cAAc,OAAO,EAAE,WAAW,8BAA8B,GAAG,gBAAgB,cAAc,IAAI,iBAAiB,cAAc,KAAK,SAAS,IAAI,CAAC,CAAE;AAAA,IAC3a;AAEA,QAAI,iBAAiB,WAAY;AAAE,aAAO,iBAAiB,cAAc,KAAK,SAAS,IAAI;AAAA,IAAG;AAC9F,QAAI,iBAAiB,iBAAiB,cAAc;AAAA,MAChD,eAAe;AAAA,IACnB,CAAC;AAED,QAAI,cAAc,SAAU,IAAI;AAC5B,UAAI,WAAW,GAAG,UAAU,MAAM,GAAG;AACrC,UAAI,YAAY,KAAK,aAAa;AAClC,UAAI,KAAK,iBAAiB,SAAS;AAAA,QAC/B,SAAS;AAAA,QACT,QAAQ,CAAC;AAAA,MACb,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC;AACpC,uBAAiB,UAAU,WAAY;AACnC,YAAI,cAAc,EAAE,KAAK,SAAU,QAAQ;AACvC,oBAAU,WAAW,UAAU,EAAE,SAAS,OAAO,QAAQ,UAAU,CAAC,EAAE,CAAC;AAAA,QAC3E,CAAC;AAAA,MACL,GAAG,CAAC,IAAI,YAAY,KAAK,CAAC;AAC1B,aAAO,OAAO,UAAU,iBAAiB,cAAc,iBAAiB,UAAU,IAAI,IAAI,SAAS,OAAO,MAAM;AAAA,IACpH;AAEA,QAAI,8BAA8B,SAAU,KAAK,WAAW;AACxD,UAAI,MAAM,IAAI,sBAAsB,EAAE,MAAM,UAAU,sBAAsB,EAAE;AAC9E,UAAI,YAAY,IAAI;AACpB,UAAI,kBAAkB,UAAU;AAChC,UAAI,MAAM,GAAG;AACT,kBAAU,aAAa;AACvB;AAAA,MACJ;AACA,UAAI,MAAM,aAAa,iBAAiB;AACpC;AAAA,MACJ;AACA,gBAAU,aAAa,MAAM,YAAY;AAAA,IAC7C;AACA,QAAI,gCAAgC,SAAU,KAAK,WAAW;AAC1D,UAAI,OAAO,IAAI,sBAAsB,EAAE,OAAO,UAAU,sBAAsB,EAAE;AAChF,UAAI,WAAW,IAAI;AACnB,UAAI,iBAAiB,UAAU;AAC/B,UAAI,OAAO,GAAG;AACV,kBAAU,cAAc;AACxB;AAAA,MACJ;AACA,UAAI,OAAO,YAAY,gBAAgB;AACnC;AAAA,MACJ;AACA,gBAAU,cAAc,OAAO,WAAW;AAAA,IAC9C;AAEA,QAAI,gBAAgB,SAAU,IAAI;AAC9B,UAAI,OAAO,GAAG,MAAM,aAAa,GAAG,YAAY,YAAY,GAAG,WAAW,YAAY,GAAG,WAAW,WAAW,GAAG,UAAU,kBAAkB,GAAG,iBAAiB,iBAAiB,GAAG,gBAAgB,oBAAoB,GAAG;AAC7N,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,aAAa,iBAAiB,OAAO;AACzC,UAAI,KAAK,iBAAiB,SAAS,EAAE,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC;AAClE,UAAI,iBAAiB,QAAQ,KAAK,YAC5B,KAAK,UAAU,iBACf;AACN,uBAAiB,UAAU,WAAY;AACnC,YAAI,OAAO,WAAW;AACtB,YAAI,MAAM;AACN,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,YAAI,gBAAgB,OAAO,WAAW,MAAM,EAAE,OAAO,MAAM,CAAC;AAC5D,YAAI,IAAI;AACR,YAAI,IAAI,KAAK,YAAY;AACzB,YAAI,QAAQ,IAAI;AAChB,eAAO,SAAS;AAChB,eAAO,QAAQ;AACf,eAAO,MAAM,SAAS,GAAG,OAAO,GAAG,IAAI;AACvC,eAAO,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI;AACtC,YAAI,WAAW,KAAK,YAAY,EAAE,UAAoB,MAAa,CAAC;AACpE,mBAAW,UAAU,KAAK,OAAO,EAAE,eAA8B,SAAmB,CAAC;AACrF,mBAAW,QAAQ,QAAQ,KAAK,WAAY;AACxC,iBAAO,OAAO,UAAU,CAAC;AACzB,4BAAkB,SAAS;AAAA,QAC/B,GAAG,WAAY;AACX,4BAAkB,SAAS;AAAA,QAC/B,CAAC;AACD,eAAO,WAAY;AACf,cAAIA;AACJ,WAACA,MAAK,WAAW,aAAa,QAAQA,QAAO,SAAS,SAASA,IAAG,OAAO;AAAA,QAC7E;AAAA,MACJ,GAAG,CAAC,QAAQ,CAAC;AACb,aAAO,CAAC,MAAO,iBAAiB,WAAW,cAAc,EAAE,cAAc,IAAM,iBAAiB,cAAc,OAAO,EAAE,cAAc,eAAe,QAAQ,iBAAiB,GAAG,OAAO,YAAY,CAAC,CAAC,GAAG,KAAU,QAAQ,GAAG,OAAO,iBAAiB,IAAI,GAAG,OAAO,GAAG,OAAO,gBAAgB,IAAI,EAAE,CAAC;AAAA,IACxS;AAEA,QAAI,qBAAqB,SAAU,IAAI;AACnC,UAAI,MAAM,GAAG,KAAK,aAAa,GAAG,YAAY,YAAY,GAAG,WAAW,eAAe,GAAG,cAAc,YAAY,GAAG,WAAW,WAAW,GAAG,UAAU,eAAe,GAAG,cAAc,iBAAiB,GAAG,gBAAgB,oBAAoB,GAAG,mBAAmB,sBAAsB,GAAG;AACjS,UAAI,YAAY,KAAK,aAAa;AAClC,UAAI,KAAK,iBAAiB,SAAS;AAAA,QAC/B,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,OAAO;AAAA,MACX,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;AACxC,UAAI,OAAO,SAAS,MAAM,SAAS,SAAS,QAAQ,QAAQ,SAAS;AACrE,UAAI,QAAQ,QAAQ;AACpB,UAAI,aAAa,KAAK,IAAI,WAAW,YAAY,IAAI,QAAQ;AAC7D,UAAI,IAAI,aAAa,iBAAiB,iBAAiB;AACvD,UAAI,IAAI,aAAa,iBAAiB,QAAQ;AAC9C,uBAAiB,UAAU,WAAY;AACnC,YAAI,cAAc;AACd,eAAK,QAAQ,KAAK,SAAS,EAAE,KAAK,SAAU,SAAS;AACjD,gBAAI,WAAW,QAAQ,YAAY,EAAE,OAAO,EAAE,CAAC;AAC/C,sBAAU,WACN,YAAY;AAAA,cACR,QAAQ,SAAS;AAAA,cACjB,MAAM;AAAA,cACN,kBAAkB,SAAS;AAAA,cAC3B,OAAO,SAAS;AAAA,YACpB,CAAC;AAAA,UACT,CAAC;AAAA,QACL;AAAA,MACJ,GAAG,CAAC,YAAY,CAAC;AACjB,UAAI,kBAAkB,SAAS,mBAAmB,WAAW,gBAAgB;AAC7E,UAAI,eAAe,KAAK,wBAAwB;AAAA,QAC5C,qBAAqB,SAAU,YAAY;AACvC,8BAAoB,WAAW,UAAU;AAAA,QAC7C;AAAA,MACJ,CAAC;AACD,aAAQ,iBAAiB,cAAc,OAAO,EAAE,WAAW,4BAA4B,eAAe,wBAAwB,OAAO,SAAS,GAAG,KAAK,cAAc,OAAO;AAAA,QACnK,QAAQ,GAAG,OAAO,GAAG,IAAI;AAAA,QACzB,OAAO,GAAG,OAAO,GAAG,IAAI;AAAA,MAC5B,EAAE,GAAG,CAAC,OAAQ,iBAAiB,WAAW,cAAc,EAAE,cAAc,IAAM,iBAAiB,cAAc,eAAe,EAAE,MAAY,YAAY,aAAa,SAAS,OAAO,WAAsB,WAAW,aAAa,QAAQ,QAAQ,UAAU,gBAAgB,iBAAiB,GAAG,gBAAgB,GAAG,kBAAqC,CAAC,CAAE;AAAA,IAClW;AAEA,QAAI,gBAAgB,SAAU,IAAI;AAC9B,UAAI,cAAc,GAAG,aAAa,MAAM,GAAG,KAAK,SAAS,GAAG,QAAQ,gBAAgB,GAAG,eAAe,aAAa,GAAG,YAAY,YAAY,GAAG,WAAW,yBAAyB,GAAG,wBAAwB,sBAAsB,GAAG,qBAAqB,cAAc,GAAG,aAAa,WAAW,GAAG,UAAU,qBAAqB,GAAG,oBAAoB,iBAAiB,GAAG,gBAAgB,WAAW,GAAG,UAAU,eAAe,GAAG,cAAc,eAAe,GAAG;AAC9c,UAAI,WAAW,IAAI;AACnB,UAAI,QAAQ,IAAI,YAAY;AAC5B,UAAI,eAAe,iBAAiB,OAAO,IAAI;AAC/C,UAAI,gBAAgB,iBAAiB,OAAO,CAAC,CAAC;AAC9C,UAAI,KAAK,iBAAiB,SAAS,WAAW,GAAG,iBAAiB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC;AACjG,UAAI,YAAY,iBAAiB,WAAW,KAAK,YAAY,EAAE;AAC/D,UAAI,QAAQ,cAAc,KAAK,cAAc;AAC7C,UAAI,KAAK,iBAAiB,SAAS,EAAE,GAAG,kBAAkB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC;AAC1F,UAAI,YAAY,KAAK,aAAa;AAClC,UAAI,mBAAmB,KAAK,YAAY,QAAQ;AAChD,UAAI,2BAA2B,iBAAiB,OAAO,KAAK;AAC5D,UAAI,cAAc,KAAK,eAAe,EAAE,IAAS,CAAC;AAClD,UAAI,cAAc,iBAAiB,QAAQ,WAAY;AACnD,eAAO,MAAM,QAAQ,EAChB,KAAK,CAAC,EACN,IAAI,SAAU,GAAG,WAAW;AAAE,iBAAO;AAAA,QAAW,CAAC;AAAA,MAC1D,GAAG,CAAC,KAAK,CAAC;AACV,UAAI,SAAS,iBAAiB,QAAQ,WAAY;AAC9C,gBAAQ,UAAU;AAAA,UACd,KAAK,KAAK,SAAS;AACf,mBAAO,KAAK,MAAM,aAAa,CAAC;AAAA,UACpC,KAAK,KAAK,SAAS;AACf,mBAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,MAAM,YAAY,MAAM,CAAC,GAAG,CAAC,CAAC;AAAA,UACxE,KAAK,KAAK,SAAS;AAAA,UACnB;AACI,mBAAO,KAAK,MAAM,aAAa,CAAC;AAAA,QACxC;AAAA,MACJ,GAAG,CAAC,OAAO,QAAQ,CAAC;AACpB,UAAI,gBAAgB,SAAU,GAAG;AAC7B,gBAAQ,EAAE,KAAK;AAAA,UACX,KAAK;AACD,6BAAiB;AACjB;AAAA,UACJ,KAAK;AACD,iCAAqB;AACrB;AAAA,UACJ,KAAK;AACD,8BAAkB;AAClB;AAAA,QACR;AAAA,MACJ;AACA,UAAI,mBAAmB,WAAY;AAC/B,YAAI,YAAY,aAAa;AAC7B,YAAI,CAAC,WAAW;AACZ;AAAA,QACJ;AACA,YAAI,QAAQ,cAAc;AAC1B,YAAI,WAAW,iBAAiB;AAChC,YAAI,WAAW,MAAM,QAAQ;AACzB,cAAI,kBAAkB,GAAG;AACrB,kBAAM,cAAc,EAAE,aAAa,YAAY,IAAI;AAAA,UACvD;AACA,4BAAkB,QAAQ;AAAA,QAC9B;AAAA,MACJ;AACA,UAAI,uBAAuB,WAAY;AACnC,YAAI,YAAY,aAAa;AAC7B,YAAI,CAAC,WAAW;AACZ;AAAA,QACJ;AACA,YAAI,QAAQ,cAAc;AAC1B,YAAI,WAAW,iBAAiB;AAChC,YAAI,YAAY,GAAG;AACf,cAAI,kBAAkB,GAAG;AACrB,kBAAM,cAAc,EAAE,aAAa,YAAY,IAAI;AAAA,UACvD;AACA,4BAAkB,QAAQ;AAAA,QAC9B;AAAA,MACJ;AACA,UAAI,oBAAoB,WAAY;AAChC,YAAI,kBAAkB,KAAK,iBAAiB,UAAU;AAClD,uBAAa,cAAc;AAAA,QAC/B;AAAA,MACJ;AACA,WAAK,0BAA0B,WAAY;AACvC,YAAI,YAAY,aAAa;AAC7B,YAAI,CAAC,WAAW;AACZ;AAAA,QACJ;AACA,sBAAc,UAAU,MAAM,KAAK,UAAU,iBAAiB,sBAAsB,CAAC;AAAA,MACzF,GAAG,CAAC,QAAQ,CAAC;AACb,uBAAiB,UAAU,WAAY;AACnC,YAAI,aAAa,cAAc;AAC/B,YAAI,WAAW,WAAW,KAAK,iBAAiB,KAAK,iBAAiB,WAAW,QAAQ;AACrF;AAAA,QACJ;AACA,YAAI,eAAe,WAAW,cAAc;AAC5C,qBAAa,aAAa,YAAY,GAAG;AACzC,qBAAa,MAAM;AAAA,MACvB,GAAG,CAAC,cAAc,CAAC;AACnB,WAAK,0BAA0B,WAAY;AACvC,YAAI,YAAY,aAAa;AAC7B,YAAI,aAAa,cAAc;AAC/B,YAAI,CAAC,aAAa,WAAW,WAAW,KAAK,cAAc,KAAK,cAAc,WAAW,QAAQ;AAC7F;AAAA,QACJ;AACA,YAAI,qBAAqB,WAAW,WAAW,EAAE,QAAQ,uBAAuB;AAChF,YAAI,oBAAoB;AACpB,iCAAuB,QAAQ,mBAAmB,WAC5C,4BAA4B,oBAAoB,SAAS,IACzD,8BAA8B,oBAAoB,SAAS;AAAA,QACrE;AAAA,MACJ,GAAG,CAAC,aAAa,kBAAkB,CAAC;AACpC,UAAI,wBAAwB,iBAAiB,YAAY,SAAU,WAAW;AAC1E,YAAI,UAAU,SAAS;AACnB,sBAAY,aAAa,SAAS;AAClC,mCAAyB,UAAU;AACnC,8BAAoB;AAAA,QACxB;AAAA,MACJ,GAAG,CAAC,KAAK,CAAC;AACV,UAAI,0BAA0B,iBAAiB,YAAY,SAAU,WAAW,YAAY;AACxF,mBAAW,YACL,YAAY,cAAc,WAAW,WAAW,KAAK,IAEnD,YAAY,cAAc,SAAS;AAC3C,4BAAoB;AAAA,MACxB,GAAG,CAAC,KAAK,CAAC;AACV,UAAI,sBAAsB,iBAAiB,YAAY,WAAY;AAC/D,YAAI,yBAAyB,SAAS;AAClC;AAAA,QACJ;AACA,YAAI,WAAW,YAAY,uBAAuB;AAClD,YAAI,WAAW,IAAI;AACf,sBAAY,cAAc,QAAQ;AAClC,mCAAyB,UAAU;AACnC,6BAAmB,QAAQ;AAAA,QAC/B;AAAA,MACJ,GAAG,CAAC,KAAK,CAAC;AACV,uBAAiB,UAAU,WAAY;AACnC,YAAI,eAAe,GAAG;AAClB,sBAAY,cAAc,WAAW;AACrC,mCAAyB,UAAU;AACnC,6BAAmB,WAAW;AAAA,QAClC;AAAA,MACJ,GAAG,CAAC,OAAO,WAAW,CAAC;AACvB,WAAK,0BAA0B,WAAY;AACvC,YAAI,qBAAqB,UAAU;AAC/B,sBAAY,gBAAgB;AAC5B,8BAAoB;AAAA,QACxB;AAAA,MACJ,GAAG,CAAC,QAAQ,CAAC;AACb,UAAI,sBAAsB,SAAU,WAAW;AAC3C,YAAI,UAAU,aAAa,KAAK,SAAS,sBACpC,cAAc,KAAM,WAAW,MAAM,KAAK,cAAc,WAAW;AACxE,YAAI,MAAM,GAAG,OAAO,IAAI,YAAY,OAAO,KAAK,EAAE,OAAO,SAAS;AAClE,YAAI,YAAY,OAAO,WAAW,WAAW,OAAO,SAAS,IAAI,GAAG,OAAO,YAAY,CAAC;AACxF,YAAI,QAAQ,yBACN,uBAAuB,EAAE,aAA0B,WAAsB,UAAoB,UAAqB,CAAC,IACnH;AACN,YAAI,eAAe,cAAc,IAAI,SAAS,IAAI,cAAc,IAAI,SAAS,IAAI;AACjF,YAAI,YAAa,iBAAiB,cAAc,oBAAoB,EAAE,KAAU,YAAwB,WAAsB,cAA4B,WAAsB,UAAoB,cAAc,oBAAoB,WAAW,gBAAgC,mBAAmB,uBAAuB,qBAAqB,wBAAwB,CAAC;AACzW,eAAO,sBAAuB,oBAAoB;AAAA,UAC9C;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,iBAAiB,iBAAiB,cAAc,iBAAiB,UAAU,MAAM,KAAK;AAAA,UACtF,qBAAqB;AAAA,UACrB,cAAc,WAAY;AAAE,mBAAO,aAAa,SAAS;AAAA,UAAG;AAAA,UAC5D,cAAc,SAAUC,YAAW;AAAE,mBAAO,aAAa,WAAWA,UAAS;AAAA,UAAG;AAAA,QACpF,CAAC,IAAM,iBAAiB;AAAA,UAAc;AAAA,UAAO,EAAE,IAAS;AAAA,UACpD,iBAAiB,cAAc,OAAO,EAAE,WAAW,KAAK,WAAW;AAAA,YAC3D,uBAAuB;AAAA,YACvB,kCAAkC,aAAa,KAAK,SAAS,YAAY,YAAY,MAAM;AAAA,YAC3F,iCAAiC,aAAa,KAAK,SAAS,YAAY,YAAY,MAAM;AAAA,YAC1F,mCAAmC;AAAA,YACnC,wCAAwC,aAAa,KAAK,SAAS,qBAAqB,CAAC,WAAW,YAAY,MAAM;AAAA,YACtH,uCAAuC,aAAa,KAAK,SAAS,qBAAqB,CAAC,WAAW,YAAY,MAAM;AAAA,YACrH,+BAA+B,aAAa,KAAK,SAAS;AAAA,YAC1D,iCAAiC,gBAAgB;AAAA,UACrD,CAAC,GAAG,MAAM,UAAU,UAAU,gBAAgB,YAAY,IAAI,IAAI,SAAS,WAAY;AAAE,mBAAO,aAAa,SAAS;AAAA,UAAG,EAAE,GAAG,SAAS;AAAA,UAC3I,iBAAiB,cAAc,OAAO,EAAE,eAAe,oBAAoB,OAAO,SAAS,GAAG,WAAW,uBAAuB,GAAG,KAAK;AAAA,QAAC;AAAA,MACjJ;AACA,aAAQ,iBAAiB,cAAc,OAAO,EAAE,KAAK,cAAc,eAAe,mBAAmB,WAAW,KAAK,WAAW;AAAA,QACxH,uBAAuB;AAAA,QACvB,mCAAmC,uBAAuB,QAAQ,mBAAmB;AAAA,QACrF,4BAA4B;AAAA,QAC5B,iCAAiC,uBAAuB,QAAQ,mBAAmB;AAAA,MACvF,CAAC,GAAG,WAAW,cAAc,GAAG,OAAO,IAAI,SAAU,WAAW,OAAO;AACvE,YAAI,kBAAkB;AACtB,gBAAQ,UAAU;AAAA,UACd,KAAK,KAAK,SAAS;AACf,8BAAkB,gBAAgB,IAAI,SAAS,gBAAgB,IAAI,QAAQ;AAC3E;AAAA,UACJ,KAAK,KAAK,SAAS;AACf,8BACK,gBAAgB,KAAK,UAAU,KAC3B,QAAQ,KAAK,gBAAgB,IAAI,QAAQ,KACzC,QAAQ,KAAK,gBAAgB,IAAI;AAC1C;AAAA,UACJ,KAAK,KAAK,SAAS;AAAA,UACnB;AACI,8BAAkB,gBAAgB;AAClC;AAAA,QACR;AACA,eAAQ,iBAAiB,cAAc,OAAO,EAAE,WAAW,KAAK,WAAW;AAAA,UACnE,wBAAwB;AAAA,UACxB,8BAA8B,aAAa,KAAK,SAAS;AAAA,UACzD,oCAAoC,aAAa,KAAK,SAAS;AAAA,UAC/D,gCAAgC,aAAa,KAAK,SAAS;AAAA,UAC3D,kCAAkC;AAAA,QACtC,CAAC,GAAG,KAAK,GAAG,OAAO,OAAO,KAAK,EAAE,OAAO,QAAQ,EAAE,GAAG,UAAU,IAAI,SAAU,WAAW;AAAE,iBAAO,oBAAoB,SAAS;AAAA,QAAG,CAAC,CAAC;AAAA,MAC3I,CAAC,CAAC;AAAA,IACN;AAEA,QAAI,yBAAyB,SAAU,IAAI;AACvC,UAAI,yBAAyB,GAAG,wBAAwB,sBAAsB,GAAG,qBAAqB,QAAQ,GAAG,OAAO,qBAAqB,GAAG,oBAAoB,iBAAiB,GAAG;AACxL,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,KAAK,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AAC9F,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,aAAa,KAAK,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AAC7G,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,YAAY,KAAK,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AAC1G,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,WAAW,KAAK,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AACvG,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,UAAU,KAAK,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;AACpG,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,eAAe,KAAK,oBAAI,IAAI,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC;AAC3H,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,aAAa,KAAK,EAAE,GAAG,cAAc,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AAC9G,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,UAAU,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;AAC/F,UAAI,2BAA2B,SAAU,kBAAkB;AACvD,uBAAe,gBAAgB;AAAA,MACnC;AACA,UAAI,wBAAwB,SAAU,KAAK;AACvC,sBAAc,GAAG;AAAA,MACrB;AACA,UAAI,0BAA0B,SAAU,QAAQ;AAC5C,sBAAc,MAAM;AAAA,MACxB;AACA,UAAI,yBAAyB,SAAU,OAAO;AAC1C,qBAAa,KAAK;AAAA,MACtB;AACA,UAAI,wBAAwB,SAAU,iBAAiB;AACnD,oBAAY,eAAe;AAAA,MAC/B;AACA,UAAI,6BAA6B,SAAU,WAAW;AAClD,yBAAiB,SAAS;AAAA,MAC9B;AACA,UAAI,oBAAoB,SAAUC,cAAa;AAC3C,uBAAeA,YAAW;AAAA,MAC9B;AACA,UAAI,wBAAwB,SAAUC,WAAU;AAC5C,oBAAYA,SAAQ;AAAA,MACxB;AACA,UAAI,OAAO,SAAU,WAAW;AAC5B,YAAI,aAAa,MAAM,IAAI,YAAY;AACvC,YAAI,YAAY;AACZ,qBAAW,SAAS;AAAA,QACxB;AAAA,MACJ;AACA,UAAI,aAAa,SAAU,WAAW,WAAW;AAC7C,cAAM,IAAI,YAAY,EAAE,WAAW,SAAS;AAAA,MAChD;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,OAAO,qBAAqB;AAC5C,cAAM,UAAU,cAAc,uBAAuB;AACrD,cAAM,UAAU,aAAa,sBAAsB;AACnD,cAAM,UAAU,eAAe,iBAAiB;AAChD,cAAM,UAAU,YAAY,qBAAqB;AACjD,cAAM,UAAU,iBAAiB,0BAA0B;AAC3D,cAAM,UAAU,YAAY,qBAAqB;AACjD,eAAO,WAAY;AACf,gBAAM,YAAY,OAAO,qBAAqB;AAC9C,gBAAM,YAAY,cAAc,uBAAuB;AACvD,gBAAM,YAAY,aAAa,sBAAsB;AACrD,gBAAM,YAAY,eAAe,iBAAiB;AAClD,gBAAM,YAAY,YAAY,qBAAqB;AACnD,gBAAM,YAAY,iBAAiB,0BAA0B;AAC7D,gBAAM,YAAY,YAAY,qBAAqB;AAAA,QACvD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,WAAK,0BAA0B,WAAY;AACvC,cAAM,UAAU,eAAe,wBAAwB;AACvD,eAAO,WAAY;AACf,gBAAM,YAAY,eAAe,wBAAwB;AAAA,QAC7D;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO,aAAc,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAY,EAAE,QAAQ,6BAA6B,OAAO;AAAA,UAC3G,WAAW;AAAA,QACf,EAAE;AAAA,QACF,iBAAiB,cAAc,aAAa,EAAE,KAAK,WAAW,GAAG,SAAU,QAAQ;AAAE,iBAAQ,iBAAiB,cAAc,eAAe,EAAE,aAA0B,KAAK,YAAY,QAAgB,eAA8B,YAAwB,WAAsB,wBAAgD,qBAA0C,aAA0B,UAAoB,oBAAwC,gBAAgC,UAAoB,cAAc,MAAM,cAAc,WAAW,CAAC;AAAA,QAAI,CAAC;AAAA,MAAC,IAAM,iBAAiB,cAAc,OAAO,EAAE,eAAe,0BAA0B,WAAW,wBAAwB,GAAG,iBAAiB,WAAW,cAAc,EAAE,cAAc,CAAC;AAAA,IAC5uB;AAEA,QAAI,kBAAkB,SAAU,aAAa;AACzC,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAC7C,eAAO,KAAK,YAAY;AAAA,UACpB,YAAY,WAAY;AAAA,UACxB;AAAA,UACA,UAAU,KAAK,SAAS;AAAA,QAC5B,CAAC;AAAA,MACL,GAAG,CAAC,CAAC;AACL,UAAI,KAAK,iBAAiB,SAAS,EAAE,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACtE,UAAI,iBAAiB,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,OAAO,SAAS,CAAC,GAAG,OAAO,EAAE,eAAe,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,eAAe,MAAa,CAAC,CAAC;AAAA,MAAI;AACnO,UAAI,sBAAsB,iBAAiB,YAAY,SAAU,OAAO;AAAE,eAAQ,iBAAiB;AAAA,UAAc,eAAe;AAAA,UAAU,EAAE,OAAO,EAAE,gBAAgB,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,kBAAkB,eAAe,EAAE;AAAA,UAC1Q,iBAAiB,cAAc,wBAAwB,EAAE,wBAAwB,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,wBAAwB,qBAAqB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,qBAAqB,OAAc,qBAAqB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,uBAAuB,QAAQ,mBAAmB,UAAU,iBAAiB,gBAAgB,QAAQ,gBAAgB,SAAS,SAAS,YAAY,mBAAmB,IAAI,CAAC;AAAA,QAAC;AAAA,MAAI,GAAG,CAAC,KAAK,CAAC;AACliB,aAAO;AAAA,QACH,SAAS,SAAU,iBAAiB;AAChC,gBAAM,OAAO,cAAc,gBAAgB,UAAU;AACrD,gBAAM,OAAO,cAAc,gBAAgB,UAAU;AAAA,QACzD;AAAA,QACA,gBAAgB,SAAU,OAAO;AAC7B,mBAAS,MAAM,IAAI,YAAY,KAAK;AACpC,gBAAM,OAAO,OAAO,MAAM,GAAG;AAAA,QACjC;AAAA,QACA,qBAAqB,SAAU,aAAa;AACxC,gBAAM,OAAO,eAAe,YAAY,SAAS;AACjD,gBAAM,OAAO,iBAAiB,YAAY,aAAa;AACvD,gBAAM,OAAO,cAAc,YAAY,UAAU;AACjD,gBAAM,OAAO,aAAa,YAAY,SAAS;AAC/C,gBAAM,OAAO,YAAY,YAAY,QAAQ;AAC7C,gBAAM,OAAO,eAAe,YAAY,WAAW;AACnD,gBAAM,OAAO,YAAY,YAAY,QAAQ;AAC7C,iBAAO;AAAA,QACX;AAAA,QACA,OAAO;AAAA,QACP,YAAY;AAAA,MAChB;AAAA,IACJ;AAEA,YAAQ,kBAAkB;AAAA;AAAA;;;ACplB1B,IAAAC,eAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,eAAe,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAC1F,iBAAiB,cAAc,QAAQ,EAAE,GAAG,icAAic,CAAC;AAAA,MAAC;AAAA,IAAI;AAiBvf,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,YAAQ,gBAAgB;AACxB,KAAC,SAAU,eAAe;AACtB,oBAAc,MAAM,IAAI;AACxB,oBAAc,MAAM,IAAI;AAAA,IAC5B,GAAG,QAAQ,kBAAkB,QAAQ,gBAAgB,CAAC,EAAE;AAExD,QAAI,oBAAoB,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAC/F,iBAAiB,cAAc,QAAQ,EAAE,GAAG,oaAAoa,CAAC;AAAA,MAAC;AAAA,IAAI;AAE1d,QAAI,+BAA+B,SAAU,IAAI;AAC7C,UAAI,WAAW,GAAG,UAAU,OAAO,GAAG,MAAM,UAAU,GAAG;AACzD,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ;AACZ,UAAI,OAAO,iBAAiB,cAAc,mBAAmB,IAAI;AACjE,cAAQ,MAAM;AAAA,QACV,KAAK,QAAQ,cAAc;AACvB,kBACI,QAAQ,KAAK,gBAAgB,KAAK,cAAc,WAAW;AAC/D,iBAAO,iBAAiB,cAAc,cAAc,IAAI;AACxD;AAAA,QACJ,KAAK,QAAQ,cAAc;AAAA,QAC3B;AACI,kBACI,QAAQ,KAAK,gBACP,KAAK,cAAc,oBACnB;AACV,iBAAO,iBAAiB,cAAc,mBAAmB,IAAI;AAC7D;AAAA,MACR;AACA,aAAO,SAAS,EAAE,MAAY,OAAc,QAAiB,CAAC;AAAA,IAClE;AAEA,QAAI,iBAAiB,EAAE,MAAM,GAAG,KAAK,EAAE;AACvC,QAAI,4BAA4B,SAAU,IAAI;AAC1C,UAAI,aAAa,GAAG,YAAY,OAAO,GAAG,MAAM,UAAU,GAAG;AAC7D,UAAI,SAAS;AACb,cAAQ,MAAM;AAAA,QACV,KAAK,QAAQ,cAAc;AACvB,mBAAS;AACT;AAAA,QACJ,KAAK,QAAQ,cAAc;AAAA,QAC3B;AACI,mBAAS;AAAA,MACjB;AACA,aAAQ,iBAAiB,cAAc,8BAA8B,EAAE,MAAY,QAAiB,GAAG,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,yBAAyB,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB,cAAc,KAAK,eAAe,EAAE,WAAW,MAAM,OAAO,YAAwB,QAAgB,SAAS,MAAM,QAAQ,GAAG,MAAM,IAAI,GAAG,SAAS,WAAY;AAAE,iBAAO,MAAM;AAAA,QAAO,GAAG,QAAQ,eAAe,CAAC;AAAA,MAAI,CAAC;AAAA,IACvf;AAEA,QAAI,sBAAsB,SAAU,IAAI;AACpC,UAAI,WAAW,GAAG,UAAU,OAAO,GAAG,MAAM,QAAQ,GAAG;AACvD,UAAI,UAAU,WAAY;AAAE,eAAO,MAAM,OAAO,iBAAiB,IAAI;AAAA,MAAG;AACxE,UAAI,aAAa,SAAS,MAAM,IAAI,eAAe;AACnD,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,2BAA2B,EAAE,YAAwB,MAAM,MAAM,MAAM,SAAS,MAAM,QAAQ,CAAC;AAAA,MAAI;AACnL,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AAEA,QAAI,8BAA8B,SAAU,IAAI;AAC5C,UAAI,aAAa,GAAG,YAAY,OAAO,GAAG,MAAM,UAAU,GAAG;AAC7D,UAAI,SAAS;AACb,cAAQ,MAAM;AAAA,QACV,KAAK,QAAQ,cAAc;AACvB,mBAAS;AACT;AAAA,QACJ,KAAK,QAAQ,cAAc;AAAA,QAC3B;AACI,mBAAS;AAAA,MACjB;AACA,aAAQ,iBAAiB,cAAc,8BAA8B,EAAE,MAAY,QAAiB,GAAG,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,SAAS,YAAY,MAAM,MAAM,MAAM,QAAgB,SAAS,MAAM,QAAQ,GAAG,MAAM,KAAK;AAAA,MAAI,CAAC;AAAA,IACvR;AAEA,QAAI,UAAU,SAAU,IAAI;AACxB,UAAI,QAAQ,GAAG;AACf,UAAI,WAAW,iBAAiB,OAAO,IAAI;AAC3C,UAAI,KAAK,iBAAiB,SAAS,QAAQ,cAAc,IAAI,GAAG,gBAAgB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC;AAC9G,UAAI,MAAM,iBAAiB,OAAO,EAAE,KAAK,GAAG,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AACjE,UAAI,qBAAqB,SAAU,GAAG;AAClC,YAAI,MAAM,SAAS;AACnB,YAAI,CAAC,KAAK;AACN;AAAA,QACJ;AACA,YAAI,YAAY,IAAI,QAAQ,OAAO,EAAE,UAAU,IAAI,QAAQ;AAC3D,YAAI,aAAa,IAAI,QAAQ,QAAQ,EAAE,UAAU,IAAI,QAAQ;AAAA,MACjE;AACA,UAAI,mBAAmB,WAAY;AAC/B,YAAI,MAAM,SAAS;AACnB,YAAI,CAAC,KAAK;AACN;AAAA,QACJ;AACA,YAAI,UAAU,IAAI,0BAA0B;AAC5C,YAAI,UAAU,OAAO,8BAA8B;AACnD,iBAAS,oBAAoB,aAAa,kBAAkB;AAC5D,iBAAS,oBAAoB,WAAW,gBAAgB;AAAA,MAC5D;AACA,UAAI,qBAAqB,SAAU,GAAG;AAClC,YAAI,MAAM,SAAS;AACnB,YAAI,CAAC,OAAO,kBAAkB,QAAQ,cAAc,MAAM;AACtD;AAAA,QACJ;AACA,YAAI,UAAU,OAAO,0BAA0B;AAC/C,YAAI,UAAU,IAAI,8BAA8B;AAChD,UAAE,eAAe;AACjB,UAAE,gBAAgB;AAClB,YAAI,UAAU;AAAA,UACV,MAAM,IAAI;AAAA,UACV,KAAK,IAAI;AAAA,UACT,GAAG,EAAE;AAAA,UACL,GAAG,EAAE;AAAA,QACT;AACA,iBAAS,iBAAiB,aAAa,kBAAkB;AACzD,iBAAS,iBAAiB,WAAW,gBAAgB;AAAA,MACzD;AACA,UAAI,uBAAuB,SAAU,mBAAmB;AACpD,iBAAS,UAAU,kBAAkB;AAAA,MACzC;AACA,UAAI,6BAA6B,SAAU,MAAM;AAC7C,yBAAiB,IAAI;AAAA,MACzB;AACA,uBAAiB,UAAU,WAAY;AACnC,YAAI,MAAM,SAAS;AACnB,YAAI,CAAC,KAAK;AACN;AAAA,QACJ;AACA,0BAAkB,QAAQ,cAAc,OAClC,IAAI,UAAU,IAAI,0BAA0B,IAC5C,IAAI,UAAU,OAAO,0BAA0B;AACrD,YAAI,iBAAiB,aAAa,kBAAkB;AACpD,eAAO,WAAY;AACf,cAAI,oBAAoB,aAAa,kBAAkB;AAAA,QAC3D;AAAA,MACJ,GAAG,CAAC,aAAa,CAAC;AAClB,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,qBAAqB,oBAAoB;AACzD,cAAM,UAAU,iBAAiB,0BAA0B;AAC3D,eAAO,WAAY;AACf,gBAAM,YAAY,qBAAqB,oBAAoB;AAC3D,gBAAM,YAAY,iBAAiB,0BAA0B;AAAA,QACjE;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO,iBAAiB,cAAc,iBAAiB,UAAU,IAAI;AAAA,IACzE;AAEA,QAAI,sBAAsB,SAAU,OAAO;AACvC,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAAE,eAAO,KAAK,YAAY;AAAA,MAAG,GAAG,CAAC,CAAC;AACnF,UAAIC,gCAA+B,SAAUC,QAAO;AAAE,eAAQ,iBAAiB,cAAc,qBAAqB,SAAS,CAAC,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAI;AAC3J,UAAI,qCAAqC,SAAUA,QAAO;AAAE,eAAQ,iBAAiB,cAAcD,+BAA8B,EAAE,MAAMC,OAAM,KAAK,GAAG,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,2BAA2B,EAAE,YAAY,EAAE,YAAY,MAAM,EAAE,MAAM,SAAS,WAAY;AAC/R,cAAE,QAAQ;AAAA,UACd,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,uCAAuC,SAAUA,QAAO;AAAE,eAAQ,iBAAiB,cAAcD,+BAA8B,EAAE,MAAMC,OAAM,KAAK,GAAG,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,6BAA6B,EAAE,YAAY,EAAE,YAAY,MAAM,EAAE,MAAM,SAAS,WAAY;AACnS,cAAE,QAAQ;AACV,YAAAA,OAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,eAAe,SAAUA,QAAO;AAChC,YAAI,cAAcA,OAAM;AACxB,YAAI,YAAY,WAAW,YAAY,QAAQ,UAAU;AACrD,sBAAY,QAAQ,WAAY,iBAAiB;AAAA,YAAc,iBAAiB;AAAA,YAAU;AAAA,YACtF,iBAAiB,cAAc,SAAS,EAAE,MAAa,CAAC;AAAA,YACxD,YAAY,QAAQ;AAAA,UAAQ;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,QACH,SAAS,SAAU,iBAAiB;AAChC,gBAAM,OAAO,iBAAiB,SAAS,MAAM,gBAAgB,MAAM,gBAAgB,QAAQ,cAAc,IAAI;AAC7G,gBAAM,OAAO,qBAAqB,gBAAgB,iBAAiB;AAAA,QACvE;AAAA,QACA;AAAA,QACA,qBAAqBD;AAAA,QACrB,2BAA2B;AAAA,QAC3B,6BAA6B;AAAA,MACjC;AAAA,IACJ;AAEA,YAAQ,eAAe;AACvB,YAAQ,oBAAoB;AAC5B,YAAQ,sBAAsB;AAAA;AAAA;;;ACvO9B,IAAAE,eAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,qBAAqB,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAChG,iBAAiB,cAAc,QAAQ,EAAE,GAAG,2BAA2B,CAAC;AAAA,QACxE,iBAAiB,cAAc,QAAQ,EAAE,GAAG,sCAAsC,CAAC;AAAA,QACnF,iBAAiB,cAAc,QAAQ,EAAE,GAAG,0BAA0B,CAAC;AAAA,QACvE,iBAAiB,cAAc,QAAQ,EAAE,GAAG,oCAAoC,CAAC;AAAA,QACjF,iBAAiB,cAAc,QAAQ,EAAE,GAAG,0BAA0B,CAAC;AAAA,MAAC;AAAA,IAAI;AAEhF,QAAI,iBAAiB,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAC5F,iBAAiB,cAAc,QAAQ,EAAE,GAAG,kBAAkB,CAAC;AAAA,QAC/D,iBAAiB,cAAc,QAAQ,EAAE,GAAG,kBAAkB,CAAC;AAAA,QAC/D,iBAAiB,cAAc,QAAQ,EAAE,GAAG,uBAAuB,CAAC;AAAA,QACpE,iBAAiB,cAAc,QAAQ,EAAE,GAAG,0BAA0B,CAAC;AAAA,QACvE,iBAAiB,cAAc,QAAQ,EAAE,GAAG,uBAAuB,CAAC;AAAA,QACpE,iBAAiB,cAAc,QAAQ,EAAE,GAAG,0BAA0B,CAAC;AAAA,MAAC;AAAA,IAAI;AAiBhF,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,mBAAmB,EAAE,MAAM,GAAG,KAAK,EAAE;AACzC,QAAI,wBAAwB,SAAU,IAAI;AACtC,UAAI,kBAAkB,GAAG,iBAAiB,UAAU,GAAG;AACvD,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,aAAa,KAAK,WAAW,kBAAkB;AACxE,UAAI,mBAAmB,kBAAmB,KAAK,MAAM,IAAI,gBAAgB,QAAS;AAClF,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,qBAAqB,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAe,EAAE,kBAAoC,WAAW,OAAO,YAAY,CAAC,KAAK,oBAAoB,GAAG,QAAQ,6BAA6B,QAAiB;AAAA,QAChV,iBAAiB,cAAc,gBAAgB,IAAI;AAAA,MAAC,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAO,GAAG,QAAQ,iBAAiB,CAAC;AAAA,IACrI;AAEA,QAAI,iBAAiB,EAAE,MAAM,GAAG,KAAK,EAAE;AACvC,QAAI,kCAAkC,SAAU,IAAI;AAChD,UAAI,UAAU,GAAG;AACjB,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,sBAAsB,QAAQ,KAAK,aAAa,KAAK,WAAW,iBAAiB;AACrF,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,oBAAoB,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAe,EAAE,kBAAkB,OAAO,WAAW,qBAAqB,QAAQ,yCAAyC,QAAiB;AAAA,QACrT,iBAAiB,cAAc,oBAAoB,IAAI;AAAA,MAAC,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAqB,GAAG,QAAQ,eAAe,CAAC;AAAA,IACrJ;AAEA,QAAI,qBAAqB,SAAU,qBAAqB,OAAO;AAC3D,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,gBAAgB,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC;AACjH,UAAI,uBAAuB,iBAAiB,YAAY,SAAUC,iBAAgB;AAC9E,0BAAkBA,eAAc;AAAA,MACpC,GAAG,CAAC,CAAC;AACL,UAAI,kBAAkB,WAAY;AAC9B,YAAI,iBAAiB,MAAM,IAAI,mBAAmB;AAClD,YAAI,CAAC,gBAAgB;AACjB;AAAA,QACJ;AACA,YAAI,SAAS,oBAAoB,eAAe,CAAC;AACjD,cAAM,IAAI,qBAAqB,EAAE,MAAM;AAAA,MAC3C;AACA,UAAI,iBAAiB,WAAY;AAC7B,cAAM,IAAI,oBAAoB,EAAE;AAAA,MACpC;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,kBAAkB,oBAAoB;AACtD,eAAO,WAAY;AACf,gBAAM,YAAY,kBAAkB,oBAAoB;AAAA,QAC5D;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA,cAAc,mBAAmB,KAAK,eAAe,YAAY,mBAAmB,KAAK,eAAe;AAAA,MAC5G;AAAA,IACJ;AAEA,QAAI,kBAAkB,SAAU,IAAI;AAChC,UAAI,WAAW,GAAG,UAAU,kBAAkB,GAAG,iBAAiB,sBAAsB,GAAG,qBAAqB,QAAQ,GAAG;AAC3H,UAAI,KAAK,mBAAmB,qBAAqB,KAAK,GAAG,kBAAkB,GAAG,iBAAiB,iBAAiB,GAAG,gBAAgB,eAAe,GAAG;AACrJ,UAAI,kBAAkB,SAAU,OAAO;AACnC,eAAO,eAAgB,iBAAiB,cAAc,iCAAiC,EAAE,SAAS,MAAM,QAAQ,CAAC,IAAM,iBAAiB,cAAc,uBAAuB,EAAE,iBAAkC,SAAS,MAAM,QAAQ,CAAC;AAAA,MAC7O;AACA,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV,SAAS,eAAe,iBAAiB;AAAA,MAC7C,CAAC;AAAA,IACL;AAEA,QAAI,0BAA0B,SAAU,IAAI;AACxC,UAAI,UAAU,GAAG;AACjB,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,aAAa,KAAK,WAAW,kBAAkB;AACxE,aAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,MAAM,iBAAiB,cAAc,gBAAgB,IAAI,GAAG,YAAY,CAAC,KAAK,oBAAoB,GAAG,QAAQ,2BAA2B,QAAiB,GAAG,KAAK;AAAA,IAC7N;AAEA,QAAI,uBAAuB,SAAU,IAAI;AACrC,UAAI,UAAU,GAAG;AACjB,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,YAAY,iBAAiB,WAAW,KAAK,YAAY,EAAE;AAC/D,UAAI,QAAQ,cAAc,KAAK,cAAc;AAC7C,UAAI,sBAAsB,QAAQ,KAAK,aAAa,KAAK,WAAW,iBAAiB;AACrF,aAAQ,iBAAiB;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,KAAK,WAAW;AAAA,UACnE,gCAAgC;AAAA,UAChC,qCAAqC,CAAC;AAAA,UACtC,qCAAqC;AAAA,QACzC,CAAC,EAAE;AAAA,QACH,iBAAiB;AAAA,UAAc,KAAK;AAAA,UAAe,EAAE,WAAW,qBAAqB,QAAQ,4BAA4B,QAAiB;AAAA,UACtI,iBAAiB,cAAc,oBAAoB,IAAI;AAAA,QAAC;AAAA,MAAC;AAAA,IACrE;AAEA,QAAI,iBAAiB,SAAU,IAAI;AAC/B,UAAI,WAAW,GAAG,UAAU,sBAAsB,GAAG,qBAAqB,QAAQ,GAAG;AACrF,UAAI,KAAK,mBAAmB,qBAAqB,KAAK,GAAG,kBAAkB,GAAG,iBAAiB,iBAAiB,GAAG,gBAAgB,eAAe,GAAG;AACrJ,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAO,iBAAiB,cAAc,sBAAsB,EAAE,SAAS,MAAM,QAAQ,CAAC;AAAA,MAAG;AAClI,UAAI,SAAS,YAAY;AACzB,aAAQ,gBACJ,OAAO;AAAA,QACH,SAAS,eAAe,iBAAiB;AAAA,MAC7C,CAAC;AAAA,IACT;AAEA,QAAI,wBAAwB,SAAU,IAAI;AACtC,UAAI,QAAQ,GAAG,OAAO,oBAAoB,GAAG,mBAAmB,mBAAmB,GAAG;AACtF,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,gBAAgB,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC;AACjH,UAAI,uBAAuB,iBAAiB,YAAY,SAAUA,iBAAgB;AAC9E,0BAAkBA,eAAc;AAAA,MACpC,GAAG,CAAC,CAAC;AACL,UAAI,0BAA0B,WAAY;AACtC,0BAAkB,MAAM,IAAI,MAAM,CAAC;AAAA,MACvC;AACA,UAAI,yBAAyB,WAAY;AACrC,yBAAiB,MAAM,IAAI,MAAM,CAAC;AAAA,MACtC;AACA,uBAAiB,UAAU,WAAY;AACnC,gBAAQ,gBAAgB;AAAA,UACpB,KAAK,KAAK,eAAe;AACrB,oCAAwB;AACxB;AAAA,UACJ,KAAK,KAAK,eAAe;AACrB,mCAAuB;AACvB;AAAA,QACR;AAAA,MACJ,GAAG,CAAC,cAAc,CAAC;AACnB,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,kBAAkB,oBAAoB;AACtD,eAAO,WAAY;AACf,gBAAM,YAAY,kBAAkB,oBAAoB;AAAA,QAC5D;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,cAAS,mBAAmB,KAAK,eAAe,YAAY,mBAAmB,KAAK,eAAe,YAAa,iBAAiB;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,2BAA2B;AAAA,QAC1L,iBAAiB,cAAc,KAAK,SAAS,IAAI;AAAA,MAAC;AAAA,IAC1D;AAEA,QAAI,kBAAkB,SAAU,IAAI;AAChC,UAAI,eAAe,GAAG,cAAc,sBAAsB,GAAG,qBAAqB,QAAQ,GAAG;AAC7F,UAAI,kBAAkB,mBAAmB,qBAAqB,KAAK,EAAE;AACrE,UAAI,iBAAiB,SAAU,GAAG;AAC9B,YAAI,EAAE,YAAY,EAAE,QAAQ;AACxB;AAAA,QACJ;AACA,YAAI,sBAAsB,KAAK,MAAM,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,MAAM,EAAE,QAAQ;AAC7F,YAAI,CAAC,qBAAqB;AACtB;AAAA,QACJ;AACA,YAAI,eAAe,aAAa;AAChC,YAAI,CAAC,gBAAgB,CAAC,SAAS,iBAAiB,CAAC,aAAa,SAAS,SAAS,aAAa,GAAG;AAC5F;AAAA,QACJ;AACA,UAAE,eAAe;AACjB,wBAAgB;AAAA,MACpB;AACA,uBAAiB,UAAU,WAAY;AACnC,YAAI,eAAe,aAAa;AAChC,YAAI,CAAC,cAAc;AACf;AAAA,QACJ;AACA,iBAAS,iBAAiB,WAAW,cAAc;AACnD,eAAO,WAAY;AACf,mBAAS,oBAAoB,WAAW,cAAc;AAAA,QAC1D;AAAA,MACJ,GAAG,CAAC,aAAa,OAAO,CAAC;AACzB,aAAO,iBAAiB,cAAc,iBAAiB,UAAU,IAAI;AAAA,IACzE;AAEA,QAAI,mBAAmB,SAAU,OAAO;AACpC,UAAI,0BAA0B,SAAU,KAAK;AAAE,eAAO;AAAA,MAAK;AAC3D,UAAI,uBAAuB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,wBAAwB;AACvG,UAAI,wBAAwB,iBAAiB,QAAQ,WAAY;AAC7D,eAAO,OAAO,OAAO,CAAC,GAAG,EAAE,iBAAiB,MAAM,mBAAmB,WAAY;AAAA,QAAE,GAAG,kBAAkB,WAAY;AAAA,QAAE,EAAE,GAAG,KAAK;AAAA,MACpI,GAAG,CAAC,CAAC;AACL,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAC7C,eAAO,KAAK,YAAY;AAAA,UACpB,qBAAqB,WAAY;AAAA,UAAE;AAAA,UACnC,oBAAoB,WAAY;AAAA,UAAE;AAAA,UAClC,gBAAgB,KAAK,eAAe;AAAA,UACpC,MAAM,WAAY;AAAA,UAAE;AAAA,QACxB,CAAC;AAAA,MACL,GAAG,CAAC,CAAC;AACL,UAAI,2BAA2B,SAAUC,QAAO;AAAE,eAAQ,iBAAiB,cAAc,iBAAiB,SAAS,CAAC,GAAGA,QAAO,EAAE,iBAAiB,sBAAsB,iBAAiB,qBAA0C,MAAa,CAAC,CAAC;AAAA,MAAI;AACrP,UAAI,iCAAiC,WAAY;AAAE,eAAQ,iBAAiB,cAAc,0BAA0B,MAAM,SAAU,aAAa;AAAE,iBAAQ,iBAAiB,cAAc,uBAAuB,SAAS,EAAE,iBAAiB,sBAAsB,gBAAgB,GAAG,WAAW,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAC3S,UAAI,mCAAmC,SAAUA,QAAO;AAAE,eAAQ,iBAAiB,cAAc,0BAA0B,MAAM,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,yBAAyB,EAAE,SAAS,WAAY;AAC/N,cAAE,QAAQ;AACV,YAAAA,OAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,0BAA0B,WAAY;AAAE,eAAQ,iBAAiB,cAAc,gBAAgB,EAAE,qBAA0C,MAAa,GAAG,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,0BAA0B;AAAA,MAAI;AACjP,UAAI,eAAe,SAAUA,QAAO;AAChC,YAAI,cAAcA,OAAM;AACxB,YAAI,YAAY,SAAS;AACrB,sBAAY,QAAQ,WAAY,iBAAiB;AAAA,YAAc,iBAAiB;AAAA,YAAU;AAAA,YACtF,sBAAsB,mBAAoB,iBAAiB,cAAc,iBAAiB,EAAE,cAAcA,OAAM,cAAc,qBAA0C,MAAa,CAAC;AAAA,YACtL,iBAAiB,cAAc,uBAAuB,EAAE,OAAc,mBAAmB,sBAAsB,mBAAmB,kBAAkB,sBAAsB,iBAAiB,CAAC;AAAA,YAC5L,iBAAiB,cAAc,yBAAyB,IAAI;AAAA,YAC5D,YAAY,QAAQ;AAAA,UAAQ;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,QACH,SAAS,SAAU,iBAAiB;AAChC,gBAAM,OAAO,uBAAuB,gBAAgB,mBAAmB;AACvE,gBAAM,OAAO,sBAAsB,gBAAgB,kBAAkB;AACrE,gBAAM,OAAO,qBAAqB,gBAAgB,iBAAiB;AACnE,gBAAM,OAAO,QAAQ,gBAAgB,IAAI;AAAA,QAC7C;AAAA,QACA,qBAAqB,SAAU,aAAa;AACxC,gBAAM,OAAO,kBAAkB,YAAY,cAAc;AACzD,iBAAO;AAAA,QACX;AAAA,QACA;AAAA,QACA,iBAAiB;AAAA,QACjB,uBAAuB;AAAA,QACvB,yBAAyB;AAAA,MAC7B;AAAA,IACJ;AAEA,YAAQ,qBAAqB;AAC7B,YAAQ,iBAAiB;AACzB,YAAQ,mBAAmB;AAAA;AAAA;;;ACjR3B,IAAAC,eAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,eAAe,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAC1F,iBAAiB,cAAc,QAAQ,EAAE,GAAG,2KAA2K,CAAC;AAAA,QACxN,iBAAiB,cAAc,QAAQ,EAAE,GAAG,kDAAkD,CAAC;AAAA,MAAC;AAAA,IAAI;AAiBxG,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,iBAAiB,EAAE,MAAM,GAAG,KAAK,EAAE;AACvC,QAAI,iBAAiB,SAAU,IAAI;AAC/B,UAAI,UAAU,GAAG;AACjB,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,WAAW,KAAK,SAAS,WAAW;AAC7D,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,YAAY,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAe,EAAE,WAAW,OAAO,QAAQ,6BAA6B,QAAiB;AAAA,QAC1P,iBAAiB,cAAc,cAAc,IAAI;AAAA,MAAC,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAO,GAAG,QAAQ,eAAe,CAAC;AAAA,IACjI;AAEA,QAAI,cAAc,WAAY;AAAE,aAAO,oBAAoB,KAAK,UAAU,SAAS,KAAK,SAAS,KAAK,UAAU,SAAS;AAAA,IAAG;AAC5H,QAAI,cAAc,WAAY;AAAE,aAAO,oBAAoB,KAAK,UAAU,SAAS,KAAK,CAAC,SAAS,KAAK,UAAU,SAAS;AAAA,IAAG;AAC7H,QAAI,mBAAmB,SAAU,MAAM;AACnC,aAAO,KAAK,MAAM,KAAK,MAAM,EACxB,KAAK,EAAE,EACP,IAAI,SAAU,GAAG,GAAG;AAAE,eAAO,OAAO,aAAa,KAAK,CAAC,CAAC;AAAA,MAAG,CAAC,EAC5D,KAAK,EAAE,CAAC;AAAA,IACjB;AACA,QAAI,WAAW,SAAU,KAAK,QAAQ;AAClC,UAAI,OAAO,SAAS,cAAc,GAAG;AACrC,WAAK,MAAM,UAAU;AACrB,WAAK,OAAO;AACZ,WAAK,aAAa,YAAY,MAAM;AACpC,eAAS,KAAK,YAAY,IAAI;AAC9B,WAAK,MAAM;AACX,eAAS,KAAK,YAAY,IAAI;AAAA,IAClC;AACA,QAAI,eAAe,SAAU,MAAM,QAAQ,UAAU;AACjD,UAAI,UAAU,IAAI,gBAAgB,IAAI,KAAK,CAAC,IAAI,GAAG,EAAE,MAAM,SAAS,CAAC,CAAC;AACtE,eAAS,SAAS,MAAM;AACxB,UAAI,SAAS;AACT,YAAI,gBAAgB,OAAO;AAAA,MAC/B;AACA;AAAA,IACJ;AACA,QAAI,eAAe,SAAU,KAAK,QAAQ;AACtC,UAAI,QAAQ,EAAE,KAAK,SAAU,MAAM;AAC/B,oBAAY,IAEJ,aAAa,MAAM,QAAQ,0BAA0B,IACvD,YAAY,IACR,SAAS,+BAA+B,OAAO,iBAAiB,IAAI,CAAC,GAAG,MAAM,IAC9E,aAAa,MAAM,QAAQ,iBAAiB;AAAA,MAC1D,CAAC;AAAA,IACL;AAEA,QAAI,WAAW,SAAU,IAAI;AACzB,UAAI,WAAW,GAAG,UAAU,oBAAoB,GAAG,mBAAmB,QAAQ,GAAG;AACjF,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,MAAM,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AACjG,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,KAAK,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC;AACxG,UAAI,wBAAwB,SAAU,KAAK;AACvC,2BAAmB,GAAG;AAAA,MAC1B;AACA,UAAI,oBAAoB,SAAU,MAAM;AACpC,uBAAe,IAAI;AAAA,MACvB;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,OAAO,qBAAqB;AAC5C,cAAM,UAAU,QAAQ,iBAAiB;AACzC,eAAO,WAAY;AACf,gBAAM,UAAU,OAAO,qBAAqB;AAC5C,gBAAM,YAAY,QAAQ,iBAAiB;AAAA,QAC/C;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,UAAIC,YAAW,WAAY;AACvB,YAAI,mBAAmB,aAAa;AAChC,uBAAa,iBAAiB,kBAAkB,WAAW,CAAC;AAAA,QAChE;AAAA,MACJ;AACA,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAO,iBAAiB,cAAc,gBAAgB,EAAE,SAAS,MAAM,QAAQ,CAAC;AAAA,MAAG;AAC5H,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV,SAASA;AAAA,MACb,CAAC;AAAA,IACL;AAEA,QAAI,mBAAmB,SAAU,IAAI;AACjC,UAAI,UAAU,GAAG;AACjB,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,WAAW,KAAK,SAAS,WAAW;AAC7D,aAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,MAAM,iBAAiB,cAAc,cAAc,IAAI,GAAG,QAAQ,2BAA2B,QAAiB,GAAG,KAAK;AAAA,IAClL;AAEA,QAAI,cAAc,SAAU,KAAK;AAC7B,UAAI,MAAM,IAAI,MAAM,GAAG,EAAE,IAAI;AAC7B,aAAO,MAAM,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,IACnD;AAEA,QAAI,gBAAgB,SAAU,OAAO;AACjC,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAAE,eAAO,KAAK,YAAY,CAAC,CAAC;AAAA,MAAG,GAAG,CAAC,CAAC;AACrF,UAAI,2BAA2B,SAAU,MAAM;AAAE,eAAQ,KAAK,OAAO,YAAY,KAAK,IAAI,IAAI;AAAA,MAAiB;AAC/G,UAAI,oBAAoB,SAAU,eAAe;AAAE,eAAQ,iBAAiB,cAAc,UAAU,SAAS,CAAC,GAAG,eAAe,EAAE,mBAAmB,QAAQ,MAAM,qBAAqB,2BAA2B,0BAA0B,MAAa,CAAC,CAAC;AAAA,MAAI;AAChQ,UAAI,0BAA0B,WAAY;AAAE,eAAQ,iBAAiB,cAAc,mBAAmB,MAAM,SAAUC,QAAO;AAAE,iBAAO,iBAAiB,cAAc,gBAAgB,SAAS,CAAC,GAAGA,MAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MAAI;AAChN,UAAI,4BAA4B,SAAUA,QAAO;AAAE,eAAQ,iBAAiB,cAAc,mBAAmB,MAAM,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,kBAAkB,EAAE,SAAS,WAAY;AAC1M,cAAE,QAAQ;AACV,YAAAA,OAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,aAAO;AAAA,QACH,gBAAgB,SAAUA,QAAO;AAC7B,gBAAM,OAAO,OAAOA,OAAM,GAAG;AAC7B,gBAAM,OAAO,QAAQA,OAAM,IAAI;AAAA,QACnC;AAAA,QACA,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,MACtB;AAAA,IACJ;AAEA,YAAQ,eAAe;AACvB,YAAQ,gBAAgB;AAAA;AAAA;;;AClKxB,IAAAC,eAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,eAAe,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAC1F,iBAAiB,cAAc,QAAQ,EAAE,GAAG,gJAAgJ,CAAC;AAAA,QAC7L,iBAAiB,cAAc,QAAQ,EAAE,GAAG,kDAAkD,CAAC;AAAA,MAAC;AAAA,IAAI;AAiBxG,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,iBAAiB,SAAU,OAAO;AAClC,UAAI,WAAW,iBAAiB,OAAO;AACvC,UAAI,WAAW,WAAY;AACvB,YAAI,WAAW,SAAS;AACxB,YAAI,UAAU;AACV,mBAAS,MAAM;AACf,cAAI,MAAM,IAAI,iBAAiB,GAAG;AAC9B,kBAAM,OAAO,mBAAmB,KAAK;AAAA,UACzC;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,0BAA0B,SAAU,SAAS;AAC7C,YAAI,SAAS;AACT,mBAAS;AAAA,QACb;AAAA,MACJ;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,mBAAmB,uBAAuB;AAC1D,eAAO,WAAY;AACf,gBAAM,YAAY,mBAAmB,uBAAuB;AAAA,QAChE;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,iBAAiB,EAAE,MAAM,GAAG,KAAK,EAAE;AACvC,QAAI,aAAa,SAAU,IAAI;AAC3B,UAAI,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG,OAAO,UAAU,GAAG;AACzE,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,OAAO,KAAK,KAAK,WAAW;AACrD,UAAI,KAAK,eAAe,KAAK,GAAG,WAAW,GAAG,UAAU,WAAW,GAAG;AACtE,UAAI,mBAAmB,kBAAmB,KAAK,MAAM,IAAI,WAAW,WAAY;AAChF,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,QAAQ,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,0BAA0B;AAAA,QACtM,iBAAiB,cAAc,SAAS,EAAE,QAAQ,QAAQ,KAAK,UAAU,WAAW,mBAAmB,UAAU,OAAO,UAAU,IAAI,OAAO,IAAI,MAAM,QAAQ,UAAU,QAAQ,CAAC;AAAA,QAClL,iBAAiB;AAAA,UAAc,KAAK;AAAA,UAAe,EAAE,kBAAoC,WAAW,OAAO,QAAQ,gBAAgB,SAAS,SAAS;AAAA,UACjJ,iBAAiB,cAAc,cAAc,IAAI;AAAA,QAAC;AAAA,MAAC,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAO,GAAG,QAAQ,eAAe,CAAC;AAAA,IACtI;AAEA,QAAI,OAAO,SAAU,IAAI;AACrB,UAAI,WAAW,GAAG,UAAU,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG;AAC7E,UAAI,kBAAkB,SAAU,GAAG;AAC/B,YAAI,QAAQ,EAAE,OAAO;AACrB,YAAI,CAAC,SAAS,CAAC,MAAM,QAAQ;AACzB;AAAA,QACJ;AACA,YAAI,WAAW,MAAM,IAAI,UAAU;AACnC,YAAI,UAAU;AACV,mBAAS,MAAM,CAAC,CAAC;AAAA,QACrB;AAAA,MACJ;AACA,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,YAAY,EAAE,iBAAkC,OAAc,SAAS,MAAM,QAAQ,CAAC;AAAA,MAAI;AAC1K,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AAEA,QAAI,eAAe,SAAU,IAAI;AAC7B,UAAI,QAAQ,GAAG,OAAO,UAAU,GAAG;AACnC,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,OAAO,KAAK,KAAK,WAAW;AACrD,UAAI,KAAK,eAAe,KAAK,GAAG,WAAW,GAAG,UAAU,WAAW,GAAG;AACtE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAU,EAAE,MAAM,iBAAiB,cAAc,cAAc,IAAI,GAAG,QAAQ,cAAc,SAAS,SAAS;AAAA,QACtJ,iBAAiB;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,0BAA0B;AAAA,UACzE,iBAAiB,cAAc,SAAS,EAAE,QAAQ,QAAQ,KAAK,UAAU,WAAW,mBAAmB,UAAU,OAAO,UAAU,IAAI,OAAO,IAAI,MAAM,QAAQ,UAAU,QAAQ,CAAC;AAAA,UAClL;AAAA,QAAK;AAAA,MAAC;AAAA,IAClB;AAEA,QAAI,kBAAkB,SAAU,IAAI;AAChC,UAAI,eAAe,GAAG,cAAc,QAAQ,GAAG;AAC/C,UAAI,iBAAiB,SAAU,GAAG;AAC9B,YAAI,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,KAAK;AACzC;AAAA,QACJ;AACA,YAAI,mBAAmB,KAAK,MAAM,IAAI,EAAE,UAAU,EAAE;AACpD,YAAI,CAAC,kBAAkB;AACnB;AAAA,QACJ;AACA,YAAI,eAAe,aAAa;AAChC,YAAI,CAAC,gBAAgB,CAAC,SAAS,iBAAiB,CAAC,aAAa,SAAS,SAAS,aAAa,GAAG;AAC5F;AAAA,QACJ;AACA,UAAE,eAAe;AACjB,cAAM,OAAO,mBAAmB,IAAI;AAAA,MACxC;AACA,uBAAiB,UAAU,WAAY;AACnC,YAAI,eAAe,aAAa;AAChC,YAAI,CAAC,cAAc;AACf;AAAA,QACJ;AACA,iBAAS,iBAAiB,WAAW,cAAc;AACnD,eAAO,WAAY;AACf,mBAAS,oBAAoB,WAAW,cAAc;AAAA,QAC1D;AAAA,MACJ,GAAG,CAAC,aAAa,OAAO,CAAC;AACzB,aAAO,iBAAiB,cAAc,iBAAiB,UAAU,IAAI;AAAA,IACzE;AAEA,QAAI,aAAa,SAAU,OAAO;AAC9B,UAAI,kBAAkB,iBAAiB,QAAQ,WAAY;AAAE,eAAO,OAAO,OAAO,CAAC,GAAG,EAAE,iBAAiB,KAAK,GAAG,KAAK;AAAA,MAAG,GAAG,CAAC,CAAC;AAC9H,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAAE,eAAO,KAAK,YAAY,CAAC,CAAC;AAAA,MAAG,GAAG,CAAC,CAAC;AACrF,UAAI,gBAAgB,SAAUC,QAAO;AAAE,eAAQ,iBAAiB,cAAc,MAAM,SAAS,EAAE,iBAAiB,gBAAgB,gBAAgB,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAI;AAC/K,UAAI,sBAAsB,WAAY;AAAE,eAAO,iBAAiB,cAAc,eAAe,IAAI;AAAA,MAAG;AACpG,UAAI,wBAAwB,WAAY;AAAE,eAAQ,iBAAiB,cAAc,eAAe,MAAM,SAAU,GAAG;AAAE,iBAAO,iBAAiB,cAAc,cAAc,EAAE,OAAc,SAAS,EAAE,QAAQ,CAAC;AAAA,QAAG,CAAC;AAAA,MAAI;AACrN,UAAI,eAAe,SAAUA,QAAO;AAChC,YAAI,OAAOA,OAAM;AACjB,YAAI,aAAa;AAAA,UACb,UAAW,iBAAiB;AAAA,YAAc,iBAAiB;AAAA,YAAU;AAAA,YACjE,gBAAgB,mBAAoB,iBAAiB,cAAc,iBAAiB,EAAE,cAAcA,OAAM,cAAc,MAAa,CAAC;AAAA,YACtI,KAAK;AAAA,UAAQ;AAAA,QACrB;AACA,eAAO,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,UAAU;AAAA,MAClD;AACA,aAAO;AAAA,QACH,SAAS,SAAU,iBAAiB;AAChC,gBAAM,OAAO,YAAY,gBAAgB,QAAQ;AAAA,QACrD;AAAA,QACA;AAAA,QACA,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,cAAc;AAAA,MAClB;AAAA,IACJ;AAEA,YAAQ,eAAe;AACvB,YAAQ,aAAa;AAAA;AAAA;;;ACtLrB,IAAAC,eAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,gBAAgB,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAC3F,iBAAiB,cAAc,QAAQ,EAAE,GAAG,oRAAoR,CAAC;AAAA,MAAC;AAAA,IAAI;AAE1U,QAAI,WAAW,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QACtF,iBAAiB,cAAc,QAAQ,EAAE,GAAG,6HAA6H,CAAC;AAAA,MAAC;AAAA,IAAI;AAiBnL,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,iBAAiB,SAAU,OAAO;AAClC,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,aAAa,KAAK,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AAC7G,UAAI,2BAA2B,SAAU,kBAAkB;AACvD,uBAAe,gBAAgB;AAAA,MACnC;AACA,WAAK,0BAA0B,WAAY;AACvC,cAAM,UAAU,eAAe,wBAAwB;AACvD,eAAO,WAAY;AACf,gBAAM,YAAY,eAAe,wBAAwB;AAAA,QAC7D;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO,EAAE,YAAyB;AAAA,IACtC;AAEA,QAAI,mBAAmB,SAAU,OAAO;AACpC,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,eAAe,KAAK,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC;AACnH,UAAI,sBAAsB,SAAU,OAAO;AACvC,yBAAiB,KAAK;AAAA,MAC1B;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,iBAAiB,mBAAmB;AACpD,eAAO,WAAY;AACf,gBAAM,YAAY,iBAAiB,mBAAmB;AAAA,QAC1D;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO,EAAE,cAA6B;AAAA,IAC1C;AAEA,QAAI,mBAAmB,SAAU,IAAI;AACjC,UAAI,QAAQ,GAAG;AACf,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,KAAK,iBAAiB,SAAS,GAAG,GAAG,cAAc,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AACnF,UAAI,cAAc,eAAe,KAAK,EAAE;AACxC,UAAI,gBAAgB,iBAAiB,KAAK,EAAE;AAC5C,uBAAiB,UAAU,WAAY;AAAE,eAAO,eAAe,GAAG,OAAO,cAAc,CAAC,CAAC;AAAA,MAAG,GAAG,CAAC,WAAW,CAAC;AAC5G,UAAI,eAAe,WAAY;AAC3B,YAAI,WAAW,cAAc;AAC7B,YAAI,WAAW,eAAe;AAC1B,yBAAe,GAAG,OAAO,WAAW,CAAC,CAAC;AACtC,iBAAO,QAAQ;AAAA,QACnB;AAAA,MACJ;AACA,UAAI,mBAAmB,WAAY;AAC/B,YAAI,eAAe,cAAc;AACjC,YAAI,gBAAgB,GAAG;AACnB,yBAAe,GAAG,OAAO,eAAe,CAAC,CAAC;AAC1C,iBAAO,YAAY;AAAA,QACvB;AAAA,MACJ;AACA,UAAI,SAAS,SAAU,MAAM;AACzB,YAAI,aAAa,MAAM,IAAI,YAAY;AACvC,YAAI,YAAY;AACZ,qBAAW,IAAI;AAAA,QACnB;AAAA,MACJ;AACA,UAAI,OAAO,WAAY;AACnB,YAAI,UAAU,SAAS,aAAa,EAAE;AACtC,wBAAgB,MAAM,UAAU,KAAK,UAAU,gBACzC,eAAe,GAAG,OAAO,cAAc,CAAC,CAAC,IACzC,OAAO,UAAU,CAAC;AAAA,MAC5B;AACA,UAAI,cAAc,SAAU,GAAG;AAC3B,gBAAQ,EAAE,KAAK;AAAA,UACX,KAAK;AACD,6BAAiB;AACjB;AAAA,UACJ,KAAK;AACD,yBAAa;AACb;AAAA,UACJ,KAAK;AACD,iBAAK;AACL;AAAA,QACR;AAAA,MACJ;AACA,UAAI,QAAQ,QAAQ,KAAK,iBACnB,KAAK,eAAe,kBACpB;AACN,aAAQ,iBAAiB;AAAA,QAAc;AAAA,QAAQ,EAAE,WAAW,0CAA0C;AAAA,QAClG,iBAAiB,cAAc,KAAK,SAAS,EAAE,WAAW,OAAO,QAAQ,uCAAuC,MAAM,QAAQ,OAAO,aAAa,UAAU,gBAAgB,WAAW,YAAY,CAAC;AAAA,MAAC;AAAA,IAC7M;AAEA,QAAI,cAAc,SAAU,IAAI;AAC5B,UAAI,WAAW,GAAG,UAAU,MAAM,GAAG;AACrC,UAAI,YAAY,KAAK,aAAa;AAClC,UAAI,KAAK,iBAAiB,SAAS;AAAA,QAC/B,SAAS;AAAA,QACT,QAAQ,CAAC;AAAA,MACb,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC;AACpC,uBAAiB,UAAU,WAAY;AACnC,YAAI,cAAc,EAAE,KAAK,SAAU,QAAQ;AACvC,oBAAU,WAAW,UAAU,EAAE,SAAS,OAAO,QAAQ,UAAU,CAAC,EAAE,CAAC;AAAA,QAC3E,CAAC;AAAA,MACL,GAAG,CAAC,IAAI,YAAY,KAAK,CAAC;AAC1B,aAAO,OAAO,UAAU,iBAAiB,cAAc,iBAAiB,UAAU,IAAI,IAAI,SAAS,OAAO,MAAM;AAAA,IACpH;AAEA,QAAI,cAAc,SAAU,OAAO;AAC/B,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,KAAK,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AAC9F,UAAI,wBAAwB,SAAU,KAAK;AACvC,sBAAc,GAAG;AAAA,MACrB;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,OAAO,qBAAqB;AAC5C,eAAO,WAAY;AACf,gBAAM,YAAY,OAAO,qBAAqB;AAAA,QAClD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO;AAAA,IACX;AAEA,QAAI,mBAAmB,SAAU,IAAI;AACjC,UAAI,WAAW,GAAG,UAAU,QAAQ,GAAG;AACvC,UAAI,aAAa,YAAY,KAAK;AAClC,UAAI,cAAc,eAAe,KAAK,EAAE;AACxC,UAAI,gBAAgB,iBAAiB,KAAK,EAAE;AAC5C,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAO,iBAAiB,cAAc,iBAAiB,UAAU,MAAM,MAAM,cAAc,CAAC;AAAA,MAAG;AACxI,UAAI,SAAS,YAAY;AACzB,aAAO,aAAc,iBAAiB,cAAc,aAAa,EAAE,KAAK,WAAW,GAAG,SAAU,QAAQ;AACpG,YAAI,YAAY,OAAO,WAAW,iBAAiB,gBAAgB,IAAI,OAAO,WAAW,IAAI;AAC7F,eAAO,OAAO;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,QACJ,CAAC;AAAA,MACL,CAAC,IAAM,iBAAiB,cAAc,iBAAiB,UAAU,IAAI;AAAA,IACzE;AAEA,QAAI,cAAc,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QACzF,iBAAiB,cAAc,QAAQ,EAAE,GAAG,oRAAoR,CAAC;AAAA,MAAC;AAAA,IAAI;AAE1U,QAAI,mBAAmB,EAAE,MAAM,GAAG,KAAK,EAAE;AACzC,QAAI,sBAAsB,SAAU,IAAI;AACpC,UAAI,aAAa,GAAG,YAAY,UAAU,GAAG;AAC7C,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,iBAAiB,KAAK,eAAe,gBAAgB;AAC9E,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,yBAAyB,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAe,EAAE,WAAW,OAAO,YAAwB,QAAQ,iCAAiC,QAAiB;AAAA,QACnS,iBAAiB,cAAc,aAAa,IAAI;AAAA,MAAC,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAO,GAAG,QAAQ,iBAAiB,CAAC;AAAA,IAClI;AAEA,QAAI,gBAAgB,SAAU,IAAI;AAC9B,UAAI,WAAW,GAAG,UAAU,QAAQ,GAAG;AACvC,UAAI,cAAc,eAAe,KAAK,EAAE;AACxC,UAAI,gBAAgB,WAAY;AAC5B,YAAI,aAAa,MAAM,IAAI,YAAY;AACvC,YAAI,YAAY;AACZ,qBAAW,CAAC;AAAA,QAChB;AAAA,MACJ;AACA,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,qBAAqB,EAAE,YAAY,MAAM,YAAY,SAAS,MAAM,QAAQ,CAAC;AAAA,MAAI;AACjK,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV,YAAY,gBAAgB;AAAA,QAC5B,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AAEA,QAAI,wBAAwB,SAAU,IAAI;AACtC,UAAI,aAAa,GAAG,YAAY,UAAU,GAAG;AAC7C,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,iBAAiB,KAAK,eAAe,gBAAgB;AAC9E,aAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,MAAM,iBAAiB,cAAc,aAAa,IAAI,GAAG,YAAwB,QAAQ,+BAA+B,QAAiB,GAAG,KAAK;AAAA,IAC7M;AAEA,QAAI,mBAAmB,EAAE,MAAM,GAAG,KAAK,EAAE;AACzC,QAAI,qBAAqB,SAAU,IAAI;AACnC,UAAI,aAAa,GAAG,YAAY,UAAU,GAAG;AAC7C,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,iBAAiB,KAAK,eAAe,eAAe;AAC7E,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,wBAAwB,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAe,EAAE,WAAW,OAAO,YAAwB,QAAQ,gCAAgC,QAAiB;AAAA,QACjS,iBAAiB,cAAc,eAAe,IAAI;AAAA,MAAC,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAO,GAAG,QAAQ,iBAAiB,CAAC;AAAA,IACpI;AAEA,QAAI,eAAe,SAAU,IAAI;AAC7B,UAAI,WAAW,GAAG,UAAU,QAAQ,GAAG;AACvC,UAAI,cAAc,eAAe,KAAK,EAAE;AACxC,UAAI,gBAAgB,iBAAiB,KAAK,EAAE;AAC5C,UAAI,eAAe,WAAY;AAC3B,YAAI,aAAa,MAAM,IAAI,YAAY;AACvC,YAAI,YAAY;AACZ,qBAAW,gBAAgB,CAAC;AAAA,QAChC;AAAA,MACJ;AACA,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,oBAAoB,EAAE,YAAY,MAAM,YAAY,SAAS,MAAM,QAAQ,CAAC;AAAA,MAAI;AAChK,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV,YAAY,cAAc,KAAK;AAAA,QAC/B,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AAEA,QAAI,uBAAuB,SAAU,IAAI;AACrC,UAAI,aAAa,GAAG,YAAY,UAAU,GAAG;AAC7C,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,iBAAiB,KAAK,eAAe,eAAe;AAC7E,aAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,MAAM,iBAAiB,cAAc,eAAe,IAAI,GAAG,YAAwB,QAAQ,8BAA8B,QAAiB,GAAG,KAAK;AAAA,IAC9M;AAEA,QAAI,mBAAmB,EAAE,MAAM,GAAG,KAAK,EAAE;AACzC,QAAI,qBAAqB,SAAU,IAAI;AACnC,UAAI,aAAa,GAAG,YAAY,UAAU,GAAG;AAC7C,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,iBAAiB,KAAK,eAAe,eAAe;AAC7E,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,wBAAwB,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAe,EAAE,WAAW,OAAO,YAAwB,QAAQ,gCAAgC,QAAiB;AAAA,QACjS,iBAAiB,cAAc,UAAU,IAAI;AAAA,MAAC,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAO,GAAG,QAAQ,iBAAiB,CAAC;AAAA,IAC/H;AAEA,QAAI,eAAe,SAAU,IAAI;AAC7B,UAAI,WAAW,GAAG,UAAU,QAAQ,GAAG;AACvC,UAAI,cAAc,eAAe,KAAK,EAAE;AACxC,UAAI,gBAAgB,iBAAiB,KAAK,EAAE;AAC5C,UAAI,eAAe,WAAY;AAC3B,YAAI,iBAAiB,MAAM,IAAI,gBAAgB;AAC/C,YAAI,gBAAgB;AAChB,yBAAe;AAAA,QACnB;AAAA,MACJ;AACA,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,oBAAoB,EAAE,SAAS,MAAM,SAAS,YAAY,MAAM,WAAW,CAAC;AAAA,MAAI;AAChK,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV,YAAY,cAAc,KAAK;AAAA,QAC/B,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AAEA,QAAI,uBAAuB,SAAU,IAAI;AACrC,UAAI,aAAa,GAAG,YAAY,UAAU,GAAG;AAC7C,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,iBAAiB,KAAK,eAAe,eAAe;AAC7E,aAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,MAAM,iBAAiB,cAAc,UAAU,IAAI,GAAG,YAAwB,QAAQ,8BAA8B,QAAiB,GAAG,KAAK;AAAA,IACzM;AAEA,QAAI,eAAe,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAC1F,iBAAiB,cAAc,QAAQ,EAAE,GAAG,+HAA+H,CAAC;AAAA,MAAC;AAAA,IAAI;AAErL,QAAI,iBAAiB,EAAE,MAAM,GAAG,KAAK,EAAE;AACvC,QAAI,yBAAyB,SAAU,IAAI;AACvC,UAAI,aAAa,GAAG,YAAY,UAAU,GAAG;AAC7C,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,iBACnB,KAAK,eAAe,mBACpB;AACN,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,4BAA4B,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAe,EAAE,WAAW,OAAO,YAAwB,QAAQ,oCAAoC,QAAiB;AAAA,QACzS,iBAAiB,cAAc,cAAc,IAAI;AAAA,MAAC,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAO,GAAG,QAAQ,eAAe,CAAC;AAAA,IACjI;AAEA,QAAI,mBAAmB,SAAU,IAAI;AACjC,UAAI,QAAQ,GAAG,OAAO,WAAW,GAAG;AACpC,UAAI,cAAc,eAAe,KAAK,EAAE;AACxC,UAAI,mBAAmB,WAAY;AAC/B,YAAI,qBAAqB,MAAM,IAAI,oBAAoB;AACvD,YAAI,oBAAoB;AACpB,6BAAmB;AAAA,QACvB;AAAA,MACJ;AACA,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,wBAAwB,EAAE,YAAY,MAAM,YAAY,SAAS,MAAM,QAAQ,CAAC;AAAA,MAAI;AACpK,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV,YAAY,eAAe;AAAA,QAC3B,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AAEA,QAAI,2BAA2B,SAAU,IAAI;AACzC,UAAI,aAAa,GAAG,YAAY,UAAU,GAAG;AAC7C,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,iBACnB,KAAK,eAAe,mBACpB;AACN,aAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,MAAM,iBAAiB,cAAc,cAAc,IAAI,GAAG,YAAwB,QAAQ,kCAAkC,QAAiB,GAAG,KAAK;AAAA,IACjN;AAEA,QAAI,gBAAgB,SAAU,IAAI;AAC9B,UAAI,WAAW,GAAG,UAAU,QAAQ,GAAG;AACvC,UAAI,gBAAgB,iBAAiB,KAAK,EAAE;AAC5C,aAAO,WAAW,SAAS,EAAE,cAA6B,CAAC,IAAI,iBAAiB,cAAc,iBAAiB,UAAU,MAAM,aAAa;AAAA,IAChJ;AAEA,QAAI,kBAAkB,SAAU,IAAI;AAChC,UAAI,eAAe,GAAG,cAAc,WAAW,GAAG,UAAU,QAAQ,GAAG;AACvE,UAAI,cAAc,eAAe,KAAK,EAAE;AACxC,UAAI,iBAAiB,iBAAiB,OAAO,WAAW;AACxD,qBAAe,UAAU;AACzB,UAAI,mBAAmB,iBAAiB,OAAO,KAAK;AACpD,UAAI,mBAAmB,WAAY;AAC/B,yBAAiB,UAAU;AAAA,MAC/B;AACA,UAAI,mBAAmB,WAAY;AAC/B,yBAAiB,UAAU;AAAA,MAC/B;AACA,UAAI,eAAe,WAAY;AAC3B,YAAI,aAAa,MAAM,IAAI,YAAY;AACvC,YAAI,aAAa,eAAe,UAAU;AAC1C,YAAI,cAAc,aAAa,UAAU;AACrC,qBAAW,UAAU;AAAA,QACzB;AAAA,MACJ;AACA,UAAI,mBAAmB,WAAY;AAC/B,YAAI,aAAa,MAAM,IAAI,YAAY;AACvC,YAAI,aAAa,eAAe,UAAU;AAC1C,YAAI,cAAc,cAAc,GAAG;AAC/B,qBAAW,UAAU;AAAA,QACzB;AAAA,MACJ;AACA,UAAI,wBAAwB,WAAY;AACpC,YAAIC,yBAAwB,MAAM,IAAI,uBAAuB;AAC7D,YAAIA,wBAAuB;AACvB,UAAAA,uBAAsB;AAAA,QAC1B;AAAA,MACJ;AACA,UAAI,4BAA4B,WAAY;AACxC,YAAIC,6BAA4B,MAAM,IAAI,2BAA2B;AACrE,YAAIA,4BAA2B;AAC3B,UAAAA,2BAA0B;AAAA,QAC9B;AAAA,MACJ;AACA,UAAI,gBAAgB,SAAU,GAAG;AAC7B,YAAI,eAAe,aAAa;AAChC,YAAI,wBAAwB,iBAAiB,WAAY,SAAS,iBAAiB,aAAa,SAAS,SAAS,aAAa;AAC/H,YAAI,CAAC,gBAAgB,CAAC,uBAAuB;AACzC;AAAA,QACJ;AACA,YAAI,qBAAsB,EAAE,UAAU,EAAE,QAAQ,eAAiB,CAAC,EAAE,YAAY,CAAC,EAAE,UAAU,EAAE,QAAQ;AACvG,YAAI,yBAA0B,EAAE,UAAU,EAAE,QAAQ,aAAe,CAAC,EAAE,YAAY,CAAC,EAAE,UAAU,EAAE,QAAQ;AACzG,YAAI,oBAAoB;AACpB,YAAE,eAAe;AACjB,uBAAa;AACb;AAAA,QACJ;AACA,YAAI,wBAAwB;AACxB,YAAE,eAAe;AACjB,2BAAiB;AACjB;AAAA,QACJ;AACA,YAAI,mBAAmB,KAAK,MAAM,IAAI,EAAE,WAAW,CAAC,EAAE,UAAU,EAAE;AAClE,YAAI,kBAAkB;AAClB,kBAAQ,EAAE,KAAK;AAAA,YACX,KAAK;AACD,gBAAE,eAAe;AACjB,wCAA0B;AAC1B;AAAA,YACJ,KAAK;AACD,gBAAE,eAAe;AACjB,oCAAsB;AACtB;AAAA,UACR;AAAA,QACJ;AAAA,MACJ;AACA,uBAAiB,UAAU,WAAY;AACnC,YAAI,eAAe,aAAa;AAChC,YAAI,CAAC,cAAc;AACf;AAAA,QACJ;AACA,iBAAS,iBAAiB,WAAW,aAAa;AAClD,qBAAa,iBAAiB,cAAc,gBAAgB;AAC5D,qBAAa,iBAAiB,cAAc,gBAAgB;AAC5D,eAAO,WAAY;AACf,mBAAS,oBAAoB,WAAW,aAAa;AACrD,uBAAa,oBAAoB,cAAc,gBAAgB;AAC/D,uBAAa,oBAAoB,cAAc,gBAAgB;AAAA,QACnE;AAAA,MACJ,GAAG,CAAC,aAAa,OAAO,CAAC;AACzB,aAAO,iBAAiB,cAAc,iBAAiB,UAAU,IAAI;AAAA,IACzE;AAEA,QAAI,uBAAuB,SAAU,OAAO;AACxC,UAAI,4BAA4B,iBAAiB,QAAQ,WAAY;AAAE,eAAO,OAAO,OAAO,CAAC,GAAG,EAAE,iBAAiB,KAAK,GAAG,KAAK;AAAA,MAAG,GAAG,CAAC,CAAC;AACxI,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAAE,eAAO,KAAK,YAAY;AAAA,MAAG,GAAG,CAAC,CAAC;AACnF,UAAI,4BAA4B,WAAY;AAAE,eAAO,iBAAiB,cAAc,kBAAkB,EAAE,MAAa,CAAC;AAAA,MAAG;AACzH,UAAI,4BAA4B,SAAUC,QAAO;AAAE,eAAO,iBAAiB,cAAc,kBAAkB,SAAS,CAAC,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AACnJ,UAAI,yBAAyB,SAAUA,QAAO;AAAE,eAAO,iBAAiB,cAAc,eAAe,SAAS,CAAC,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AAC7I,UAAI,+BAA+B,WAAY;AAAE,eAAQ,iBAAiB,cAAc,wBAAwB,MAAM,SAAUA,QAAO;AAAE,iBAAO,iBAAiB,cAAc,qBAAqB,SAAS,CAAC,GAAGA,MAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MAAI;AAC/N,UAAI,iCAAiC,SAAUA,QAAO;AAAE,eAAQ,iBAAiB,cAAc,wBAAwB,MAAM,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,uBAAuB,EAAE,YAAY,EAAE,YAAY,SAAS,WAAY;AACnP,cAAE,QAAQ;AACV,YAAAA,OAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,wBAAwB,SAAUA,QAAO;AAAE,eAAO,iBAAiB,cAAc,cAAc,SAAS,CAAC,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AAC3I,UAAI,8BAA8B,WAAY;AAAE,eAAQ,iBAAiB,cAAc,uBAAuB,MAAM,SAAUA,QAAO;AAAE,iBAAO,iBAAiB,cAAc,oBAAoB,SAAS,CAAC,GAAGA,MAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MAAI;AAC5N,UAAI,gCAAgC,SAAUA,QAAO;AAAE,eAAQ,iBAAiB,cAAc,uBAAuB,MAAM,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,sBAAsB,EAAE,YAAY,EAAE,YAAY,SAAS,WAAY;AAChP,cAAE,QAAQ;AACV,YAAAA,OAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,wBAAwB,SAAUA,QAAO;AAAE,eAAO,iBAAiB,cAAc,cAAc,SAAS,CAAC,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AAC3I,UAAI,8BAA8B,WAAY;AAAE,eAAQ,iBAAiB,cAAc,uBAAuB,MAAM,SAAUA,QAAO;AAAE,iBAAO,iBAAiB,cAAc,oBAAoB,SAAS,CAAC,GAAGA,MAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MAAI;AAC5N,UAAI,gCAAgC,SAAUA,QAAO;AAAE,eAAQ,iBAAiB,cAAc,uBAAuB,MAAM,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,sBAAsB,EAAE,YAAY,EAAE,YAAY,SAAS,WAAY;AAChP,cAAE,QAAQ;AACV,YAAAA,OAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,4BAA4B,SAAUA,QAAO;AAAE,eAAO,iBAAiB,cAAc,kBAAkB,SAAS,CAAC,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AACnJ,UAAI,kCAAkC,WAAY;AAAE,eAAQ,iBAAiB,cAAc,2BAA2B,MAAM,SAAUA,QAAO;AAAE,iBAAO,iBAAiB,cAAc,wBAAwB,SAAS,CAAC,GAAGA,MAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MAAI;AACxO,UAAI,oCAAoC,SAAUA,QAAO;AAAE,eAAQ,iBAAiB,cAAc,2BAA2B,MAAM,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,0BAA0B,EAAE,YAAY,EAAE,YAAY,SAAS,WAAY;AAC5P,cAAE,QAAQ;AACV,YAAAA,OAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,yBAAyB,SAAUA,QAAO;AAAE,eAAO,iBAAiB,cAAc,eAAe,SAAS,CAAC,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AAC7I,UAAI,eAAe,SAAUA,QAAO;AAChC,YAAI,OAAOA,OAAM;AACjB,YAAI,CAAC,0BAA0B,iBAAiB;AAC5C,iBAAO;AAAA,QACX;AACA,YAAI,aAAa;AAAA,UACb,UAAW,iBAAiB;AAAA,YAAc,iBAAiB;AAAA,YAAU;AAAA,YACjE,iBAAiB,cAAc,iBAAiB,EAAE,cAAcA,OAAM,cAAc,UAAUA,OAAM,IAAI,UAAU,MAAa,CAAC;AAAA,YAChI,KAAK;AAAA,UAAQ;AAAA,QACrB;AACA,eAAO,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,UAAU;AAAA,MAClD;AACA,aAAO;AAAA,QACH,SAAS,SAAU,iBAAiB;AAChC,gBAAM,OAAO,qBAAqB,gBAAgB,iBAAiB;AACnE,gBAAM,OAAO,yBAAyB,gBAAgB,qBAAqB;AAC3E,gBAAM,OAAO,kBAAkB,gBAAgB,cAAc;AAC7D,gBAAM,OAAO,cAAc,gBAAgB,UAAU;AACrD,gBAAM,OAAO,6BAA6B,gBAAgB,yBAAyB;AACnF,gBAAM,OAAO,sBAAsB,gBAAgB,kBAAkB;AAAA,QACzE;AAAA,QACA;AAAA,QACA,gBAAgB,SAAUA,QAAO;AAC7B,gBAAM,OAAO,OAAOA,OAAM,GAAG;AAC7B,gBAAM,OAAO,iBAAiBA,OAAM,IAAI,QAAQ;AAAA,QACpD;AAAA,QACA,qBAAqB,SAAU,aAAa;AACxC,gBAAM,OAAO,eAAe,YAAY,SAAS;AACjD,iBAAO;AAAA,QACX;AAAA,QACA,gBAAgB,WAAY;AACxB,cAAI,OAAO,MAAM,IAAI,gBAAgB;AACrC,cAAI,MAAM;AACN,iBAAK;AAAA,UACT;AAAA,QACJ;AAAA,QACA,YAAY,SAAU,WAAW;AAC7B,cAAI,SAAS,MAAM,IAAI,YAAY;AACnC,cAAI,QAAQ;AACR,mBAAO,SAAS;AAAA,UACpB;AAAA,QACJ;AAAA,QACA,oBAAoB,WAAY;AAC5B,cAAI,OAAO,MAAM,IAAI,oBAAoB;AACzC,cAAI,MAAM;AACN,iBAAK;AAAA,UACT;AAAA,QACJ;AAAA,QACA,kBAAkB;AAAA,QAClB,kBAAkB;AAAA,QAClB,eAAe;AAAA,QACf,qBAAqB;AAAA,QACrB,uBAAuB;AAAA,QACvB,cAAc;AAAA,QACd,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,cAAc;AAAA,QACd,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,kBAAkB;AAAA,QAClB,wBAAwB;AAAA,QACxB,0BAA0B;AAAA,QAC1B,eAAe;AAAA,MACnB;AAAA,IACJ;AAEA,YAAQ,gBAAgB;AACxB,YAAQ,WAAW;AACnB,YAAQ,eAAe;AACvB,YAAQ,cAAc;AACtB,YAAQ,uBAAuB;AAAA;AAAA;;;ACxgB/B,IAAAC,eAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AACZ,QAAI,WAAW;AAEf,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,qBAAqB,SAAU,KAAK;AACpC,aAAO,MAAM,IAAI,QAAQ,EACpB,KAAK,CAAC,EACN,IAAI,SAAU,GAAG,GAAG;AAAE,eAAO;AAAA,MAAG,CAAC;AAAA,IAC1C;AAEA,QAAI,gBAAgB,SAAU,KAAK,KAAK;AACpC,aAAO,MAAM,MAAM,MAAM,CAAC,EACrB,KAAK,CAAC,EACN,IAAI,SAAU,GAAG,GAAG;AAAE,eAAO,MAAM;AAAA,MAAG,CAAC;AAAA,IAChD;AACA,QAAI,kBAAkB,SAAU,KAAK;AAAE,aAAO,IAAI,OAAO,SAAU,GAAG;AAAE,eAAO,IAAI,QAAQ,CAAC,MAAM,IAAI,YAAY,CAAC;AAAA,MAAG,CAAC;AAAA,IAAG;AAC1H,QAAI,wBAAwB,SAAU,aAAa;AAC/C,aAAO,SAAU,KAAK;AAClB,YAAI,UAAU,CAAC;AACf,oBACK,QAAQ,QAAQ,EAAE,EAClB,MAAM,GAAG,EACT,QAAQ,SAAU,MAAM;AACzB,cAAI,QAAQ,KACP,MAAM,GAAG,EACT,IAAI,SAAU,GAAG;AAAE,mBAAO,SAAS,GAAG,EAAE;AAAA,UAAG,CAAC,EAC5C,OAAO,SAAU,GAAG;AAAE,mBAAO,OAAO,UAAU,CAAC;AAAA,UAAG,CAAC;AACxD,cAAI,MAAM,WAAW,GAAG;AACpB,oBAAQ,KAAK,MAAM,CAAC,IAAI,CAAC;AAAA,UAC7B,WACS,MAAM,WAAW,GAAG;AACzB,oBAAQ,KAAK,MAAM,SAAS,cAAc,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAAA,UACzE;AAAA,QACJ,CAAC;AACD,eAAO,gBAAgB,OAAO,EAAE,OAAO,SAAU,GAAG;AAAE,iBAAO,KAAK,KAAK,IAAI,IAAI;AAAA,QAAU,CAAC;AAAA,MAC9F;AAAA,IACJ;AAEA,QAAI,sBAAsB,SAAU,KAAK;AACrC,aAAO,MAAM,IAAI,QAAQ,EACpB,KAAK,CAAC,EACN,IAAI,SAAU,GAAG,GAAG;AAAE,eAAO;AAAA,MAAG,CAAC,EACjC,OAAO,SAAU,GAAG;AAAE,gBAAQ,IAAI,KAAK,MAAM;AAAA,MAAG,CAAC;AAAA,IAC1D;AAEA,QAAI,qBAAqB,SAAU,KAAK;AACpC,aAAO,MAAM,IAAI,QAAQ,EACpB,KAAK,CAAC,EACN,IAAI,SAAU,GAAG,GAAG;AAAE,eAAO;AAAA,MAAG,CAAC,EACjC,OAAO,SAAU,GAAG;AAAE,gBAAQ,IAAI,KAAK,MAAM;AAAA,MAAG,CAAC;AAAA,IAC1D;AAEA,QAAI,YAAY,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QACvF,iBAAiB,cAAc,QAAQ,EAAE,GAAG,8gBAA8gB,CAAC;AAAA,MAAC;AAAA,IAAI;AAiBpkB,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,iBAAiB,EAAE,MAAM,GAAG,KAAK,EAAE;AACvC,QAAI,cAAc,SAAU,IAAI;AAC5B,UAAI,kBAAkB,GAAG,iBAAiB,UAAU,GAAG;AACvD,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,QAAQ,KAAK,MAAM,QAAQ;AACpD,UAAI,mBAAmB,kBAAmB,KAAK,MAAM,IAAI,WAAW,WAAY;AAChF,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,SAAS,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAe,EAAE,kBAAoC,WAAW,OAAO,QAAQ,iBAAiB,QAAiB;AAAA,QAC/Q,iBAAiB,cAAc,WAAW,IAAI;AAAA,MAAC,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAO,GAAG,QAAQ,eAAe,CAAC;AAAA,IAC9H;AAEA,QAAI;AACJ,KAAC,SAAUC,cAAa;AACpB,MAAAA,aAAY,oBAAoB,IAAI;AACpC,MAAAA,aAAY,UAAU,IAAI;AAC1B,MAAAA,aAAY,WAAW,IAAI;AAC3B,MAAAA,aAAY,WAAW,IAAI;AAC3B,MAAAA,aAAY,OAAO,IAAI;AAAA,IAC3B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAEpC,QAAI,QAAQ,SAAU,IAAI;AACtB,UAAI,WAAW,GAAG,UAAU,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG;AAC7E,UAAI,QAAQ,WAAY;AACpB,cAAM,OAAO,eAAe,YAAY,kBAAkB;AAAA,MAC9D;AACA,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV;AAAA,QACA,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AAEA,QAAI,mBAAmB;AACvB,QAAI,iCAAiC;AACrC,QAAI,uBAAuB,SAAU,IAAI;AACrC,UAAI,MAAM,GAAG,KAAK,QAAQ,GAAG;AAC7B,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,KAAK,iBAAiB,SAAS,IAAI,GAAG,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAChF,uBAAiB,UAAU,WAAY;AACnC,YAAI,eAAe,EAAE,KAAK,SAAU,aAAa;AAC7C,cAAI,WAAW,gBAAgB,QAC3B,YAAY,SAAS,gBAAgB,KACrC,YAAY,SAAS,8BAA8B;AACvD,qBAAW,MAAM,OAAO,eAAe,YAAY,SAAS,IAAI,aAAa,KAAK;AAAA,QACtF,CAAC;AAAA,MACL,GAAG,CAAC,CAAC;AACL,aAAO,YAAa,iBAAiB,cAAc,iBAAiB,UAAU,IAAI,IAAM,iBAAiB,cAAc,KAAK,OAAO,EAAE,oBAAoB,oBAAoB,qBAAqB,OAAO,eAAe,OAAO,SAAS,SAAU,QAAQ;AAClP,YAAI,QAAQ,WAAY;AACpB,iBAAO;AACP,gBAAM,OAAO,eAAe,YAAY,SAAS;AAAA,QACrD;AACA,eAAQ,iBAAiB;AAAA,UAAc,iBAAiB;AAAA,UAAU;AAAA,UAC9D,iBAAiB,cAAc,OAAO,EAAE,WAAW,6BAA6B,GAAG,QAAQ,KAAK,QAC1F,KAAK,MAAM,gBACX,sCAAsC;AAAA,UAC5C,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,+BAA+B;AAAA,YAC9E,iBAAiB,cAAc,KAAK,QAAQ,EAAE,SAAS,MAAM,GAAG,QAAQ,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO;AAAA,UAAC;AAAA,QAAC;AAAA,MAC7H,GAAG,UAAU,KAAK,CAAC;AAAA,IAC3B;AAEA,QAAI,gBAAgB,SAAU,IAAI;AAC9B,UAAI,iBAAiB,GAAG,gBAAgB,WAAW,GAAG,UAAU,WAAW,GAAG;AAC9E,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,YAAY,iBAAiB,WAAW,KAAK,YAAY,EAAE;AAC/D,UAAI,QAAQ,cAAc,KAAK,cAAc;AAC7C,UAAI,WAAW,KAAK,MAAO,iBAAiB,MAAO,QAAQ;AAC3D,aAAQ,iBAAiB;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,sBAAsB;AAAA,QAC7E,iBAAiB;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,KAAK,WAAW;AAAA,YAC3D,4BAA4B;AAAA,YAC5B,iCAAiC;AAAA,UACrC,CAAC,EAAE;AAAA,UACH,iBAAiB,cAAc,OAAO,EAAE,WAAW,8BAA8B,GAAG,QAAQ,KAAK,QAC3F,KAAK,MAAM,oBACX,wBAAwB;AAAA,UAC9B,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,0BAA0B;AAAA,YACzE,iBAAiB,cAAc,KAAK,aAAa,EAAE,SAAmB,CAAC;AAAA,UAAC;AAAA,UAC5E,iBAAiB,cAAc,KAAK,QAAQ,EAAE,SAAS,SAAS,GAAG,QAAQ,KAAK,QAAQ,KAAK,MAAM,SAAS,QAAQ;AAAA,QAAC;AAAA,MAAC;AAAA,IAClI;AAEA,QAAI,kBAAkB,WAAY;AAAE,aAAO,OAAO,YAAY,eAAe,QAAQ,IAAI,mBAAmB;AAAA,IAAW;AAEvH,QAAI,gBAAgB,SAAU,IAAI;AAC9B,UAAI,SAAS,GAAG,QAAQ,OAAO,GAAG,MAAM,aAAa,GAAG,YAAY,YAAY,GAAG,WAAW,YAAY,GAAG,WAAW,WAAW,GAAG,UAAU,SAAS,GAAG;AAC5J,UAAI,YAAY,KAAK,aAAa;AAClC,UAAI,aAAa,iBAAiB,OAAO;AACzC,UAAI,KAAK,iBAAiB,SAAS,EAAE,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC;AAClE,UAAI,eAAe,iBAAiB,QAAQ,WAAY;AAAE,eAAO,gBAAgB;AAAA,MAAG,GAAG,CAAC,CAAC;AACzF,UAAI,kBAAkB,WAAY;AAC9B,YAAI,CAAC,cAAc;AACf,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,uBAAiB,UAAU,WAAY;AACnC,YAAI,OAAO,WAAW;AACtB,YAAI,MAAM;AACN,eAAK,OAAO;AAAA,QAChB;AACA,YAAI,YAAY,MAAM;AACtB,eAAO,SAAS,KAAK,MAAM,aAAa,SAAS;AACjD,eAAO,QAAQ,KAAK,MAAM,YAAY,SAAS;AAC/C,YAAI,gBAAgB,OAAO,WAAW,IAAI;AAC1C,sBAAc,KAAK;AACnB,sBAAc,YAAY;AAC1B,sBAAc,SAAS,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AACxD,sBAAc,QAAQ;AACtB,YAAI,WAAW,KAAK,YAAY,EAAE,UAAoB,OAAO,EAAE,CAAC;AAChE,mBAAW,UAAU,KAAK,OAAO;AAAA,UAC7B;AAAA,UACA,QAAQ;AAAA,UACR,WAAW,CAAC,WAAW,GAAG,GAAG,WAAW,GAAG,CAAC;AAAA,UAC5C;AAAA,QACJ,CAAC;AACD,mBAAW,QAAQ,QAAQ,KAAK,WAAY;AACxC,cAAI,YAAY,UAAU,qBAAqB,KAAK;AAChD,mBAAO,OAAO,SAAU,MAAM;AAC1B,wBAAU,WAAW,OAAO,IAAI,gBAAgB,IAAI,CAAC;AACrD,8BAAgB,OAAO;AAAA,YAC3B,CAAC;AAAA,UACL,OACK;AACD,sBAAU,WAAW,OAAO,OAAO,UAAU,CAAC;AAC9C,4BAAgB,OAAO;AAAA,UAC3B;AAAA,QACJ,GAAG,WAAY;AAAA,QACf,CAAC;AAAA,MACL,GAAG,CAAC,CAAC;AACL,aAAQ,OAAQ,iBAAiB;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,kBAAkB;AAAA,QACjF,iBAAiB,cAAc,OAAO,EAAE,eAAe,oBAAoB,OAAO,SAAS,GAAG,KAAU,QAAQ,gBAAgB,CAAC;AAAA,MAAC;AAAA,IAC1I;AAEA,QAAI,yBAAyB,SAAU,IAAI;AACvC,UAAI,SAAS,GAAG,QAAQ,MAAM,GAAG,KAAK,YAAY,GAAG,WAAW,eAAe,GAAG,cAAc,WAAW,GAAG,UAAU,WAAW,GAAG,UAAU,eAAe,GAAG,cAAc,SAAS,GAAG;AAC5L,UAAI,YAAY,KAAK,aAAa;AAClC,UAAI,KAAK,iBAAiB,SAAS,IAAI,GAAG,OAAO,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC;AACtE,UAAI,aAAa,KAAK,IAAI,WAAW,YAAY,IAAI,QAAQ;AAC7D,uBAAiB,UAAU,WAAY;AACnC,YAAI,cAAc;AACd,eAAK,QAAQ,KAAK,SAAS,EAAE,KAAK,SAAU,SAAS;AACjD,sBAAU,WAAW,QAAQ,OAAO;AAAA,UACxC,CAAC;AAAA,QACL;AAAA,MACJ,GAAG,CAAC,YAAY,CAAC;AACjB,UAAI,kBAAkB,SAAS,WAAW,WAAW,gBAAgB;AACrE,aAAQ,QAAS,iBAAiB,cAAc,eAAe,EAAE,QAAgB,MAAY,YAAY,aAAa,SAAS,aAAa,SAAS,WAAW,WAAsB,WAAW,aAAa,SAAS,YAAY,SAAS,YAAY,UAAU,gBAAgB,OAAe,CAAC;AAAA,IACtS;AAEA,QAAI,YAAY,SAAU,IAAI;AAC1B,UAAI,MAAM,GAAG,KAAK,iBAAiB,GAAG,gBAAgB,gBAAgB,GAAG,eAAe,YAAY,GAAG,WAAW,aAAa,GAAG,YAAY,cAAc,GAAG,aAAa,WAAW,GAAG,UAAU,WAAW,GAAG,UAAU,SAAS,GAAG;AACxO,UAAI,SAAS,iBAAiB,QAAQ,WAAY;AAAE,eAAO,SAAS,cAAc,QAAQ;AAAA,MAAG,GAAG,CAAC,CAAC;AAClG,UAAI,YAAY,iBAAiB,QAAQ,WAAY;AACjD,YAAI,UAAU,SAAS,cAAc,kBAAkB;AACvD,YAAI,SAAS;AACT,iBAAO;AAAA,QACX;AACA,YAAI,MAAM,SAAS,cAAc,KAAK;AACtC,YAAI,UAAU,IAAI,iBAAiB;AACnC,YAAI,aAAa,eAAe,aAAa;AAC7C,iBAAS,KAAK,YAAY,GAAG;AAC7B,eAAO;AAAA,MACX,GAAG,CAAC,CAAC;AACL,uBAAiB,UAAU,WAAY;AACnC,YAAI,gBAAgB,YAAY,OAAO;AACnC,mBAAS,gBAAgB,UAAU,IAAI,0BAA0B;AACjE,mBAAS,KAAK,UAAU,IAAI,0BAA0B;AACtD,iBAAO,MAAM;AAAA,QACjB;AACA,YAAI,UAAU,WAAY;AACtB,cAAI,gBAAgB,YAAY,OAAO;AACnC,qBAAS,gBAAgB,UAAU,OAAO,0BAA0B;AACpE,qBAAS,KAAK,UAAU,OAAO,0BAA0B;AACzD,gBAAI,QAAQ,SAAS,iBAAiB,kBAAkB;AACxD,gBAAI,OAAO;AACP,oBAAM,QAAQ,SAAU,SAAS;AAC7B,wBAAQ,cAAc,YAAY,OAAO;AAAA,cAC7C,CAAC;AAAA,YACL;AACA,mBAAO,SAAS;AAChB,mBAAO,QAAQ;AACf,qBAAS,oBAAoB,aAAa,OAAO;AACjD,qBAAS;AAAA,UACb;AAAA,QACJ;AACA,iBAAS,iBAAiB,aAAa,OAAO;AAC9C,eAAO,WAAY;AAAE,iBAAO,SAAS,oBAAoB,aAAa,OAAO;AAAA,QAAG;AAAA,MACpF,GAAG,CAAC,WAAW,CAAC;AAChB,UAAI,aAAa,UAAU,CAAC,EAAE;AAC9B,UAAI,YAAY,UAAU,CAAC,EAAE;AAC7B,aAAO,SAAS,aAAa,iBAAiB;AAAA,QAAc,iBAAiB;AAAA,QAAU;AAAA,QACnF,WAAW,IAAI,SAAU,WAAW,WAAW;AAAE,iBAAQ,iBAAiB,cAAc,wBAAwB,EAAE,KAAK,WAAW,QAAgB,KAAU,WAAsB,cAAc,cAAc,IAAI,SAAS,IAAI,cAAc,IAAI,SAAS,IAAI,GAAG,UAAU,UAAU,SAAS,GAAG,UAAoB,cAAc,cAAc,gBAAgB,OAAe,CAAC;AAAA,QAAI,CAAC;AAAA,QACtX,iBAAiB,cAAc,SAAS,EAAE,yBAAyB;AAAA,UAC3D,QAAQ,iBAAiB,OAAO,WAAW,KAAK,EAAE,OAAO,YAAY,MAAM;AAAA,QAC/E,EAAE,CAAC;AAAA,MAAC,GAAG,SAAS;AAAA,IAC5B;AAEA,QAAI,iBAAiB,SAAU,IAAI;AAC/B,UAAI,MAAM,GAAG,KAAK,gBAAgB,GAAG,eAAe,YAAY,GAAG,WAAW,oBAAoB,GAAG,mBAAmB,WAAW,GAAG,UAAU,WAAW,GAAG,UAAU,QAAQ,GAAG;AACnL,UAAI,KAAK,iBAAiB,SAAS,YAAY,QAAQ,GAAG,cAAc,GAAG,CAAC,GAAG,iBAAiB,GAAG,CAAC;AACpG,UAAI,KAAK,iBAAiB,SAAS,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,4BAA4B,GAAG,CAAC;AACvG,UAAI,aAAa,iBAAiB,QAAQ,WAAY;AAClD,YAAI,WAAW,IAAI;AACnB,eAAO,SAAS,GAAG,EAAE,OAAO,SAAU,OAAO;AAAE,iBAAO,SAAS,KAAK,QAAQ;AAAA,QAAU,CAAC;AAAA,MAC3F,GAAG,CAAC,KAAK,QAAQ,CAAC;AAClB,UAAI,gBAAgB,WAAW;AAC/B,UAAI,iBAAiB,WAAY;AAC7B,kCAA0B,CAAC;AAC3B,uBAAe,YAAY,QAAQ;AAAA,MACvC;AACA,UAAI,oBAAoB,SAAU,QAAQ;AAAE,eAAO,eAAe,MAAM;AAAA,MAAG;AAC3E,UAAI,aAAa,WAAY;AACzB,YAAI,QAAQ,yBAAyB;AACrC,YAAI,SAAS,eAAe;AACxB,oCAA0B,KAAK;AAC/B,oBAAU,iBAAiB,eAAe,YAAY,KAAK;AAAA,QAC/D;AAAA,MACJ;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,eAAe,iBAAiB;AAChD,eAAO,WAAY;AACf,gBAAM,YAAY,eAAe,iBAAiB;AAAA,QACtD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAQ,iBAAiB;AAAA,QAAc,iBAAiB;AAAA,QAAU;AAAA,QAC9D,gBAAgB,YAAY,sBAAsB,iBAAiB,cAAc,sBAAsB,EAAE,KAAU,MAAa,CAAC;AAAA,QACjI,gBAAgB,YAAY,cACvB,oBAAqB,kBAAkB,wBAAwB,eAAe,cAAc,IAAM,iBAAiB,cAAc,eAAe,EAAE,gBAAgB,wBAAwB,UAAU,eAAe,UAAU,eAAe,CAAC;AAAA,SACjP,gBAAgB,YAAY,aAAa,gBAAgB,YAAY,UAClE,0BAA0B,iBAAkB,iBAAiB,cAAc,WAAW,EAAE,KAAU,gBAAgB,wBAAwB,eAA8B,WAAsB,YAAwB,aAA0B,UAAoB,UAAU,gBAAgB,QAAQ,WAAW,CAAC;AAAA,MAAE;AAAA,IAChU;AAEA,QAAI,gBAAgB,SAAU,IAAI;AAC9B,UAAI,UAAU,GAAG;AACjB,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,QAAQ,KAAK,MAAM,QAAQ;AACpD,aAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,MAAM,iBAAiB,cAAc,WAAW,IAAI,GAAG,QAAQ,eAAe,QAAiB,GAAG,KAAK;AAAA,IACnK;AAEA,QAAI,kBAAkB,SAAU,IAAI;AAChC,UAAI,eAAe,GAAG,cAAc,QAAQ,GAAG;AAC/C,UAAI,iBAAiB,SAAU,GAAG;AAC9B,YAAI,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,KAAK;AACzC;AAAA,QACJ;AACA,YAAI,mBAAmB,KAAK,MAAM,IAAI,EAAE,UAAU,EAAE;AACpD,YAAI,CAAC,kBAAkB;AACnB;AAAA,QACJ;AACA,YAAI,eAAe,aAAa;AAChC,YAAI,CAAC,gBAAgB,CAAC,SAAS,iBAAiB,CAAC,aAAa,SAAS,SAAS,aAAa,GAAG;AAC5F;AAAA,QACJ;AACA,UAAE,eAAe;AACjB,cAAM,OAAO,eAAe,YAAY,SAAS;AAAA,MACrD;AACA,uBAAiB,UAAU,WAAY;AACnC,YAAI,eAAe,aAAa;AAChC,YAAI,CAAC,cAAc;AACf;AAAA,QACJ;AACA,iBAAS,iBAAiB,WAAW,cAAc;AACnD,eAAO,WAAY;AACf,mBAAS,oBAAoB,WAAW,cAAc;AAAA,QAC1D;AAAA,MACJ,GAAG,CAAC,aAAa,OAAO,CAAC;AACzB,aAAO,iBAAiB,cAAc,iBAAiB,UAAU,IAAI;AAAA,IACzE;AAEA,QAAI,cAAc,SAAU,OAAO;AAC/B,UAAI,mBAAmB,iBAAiB,QAAQ,WAAY;AACxD,eAAO,OAAO,OAAO,CAAC,GAAG;AAAA,UACrB,iBAAiB;AAAA,UACjB,UAAU,SAAU,KAAK;AACrB,mBAAO,MAAM,IAAI,QAAQ,EACpB,KAAK,CAAC,EACN,IAAI,SAAU,GAAG,GAAG;AAAE,qBAAO;AAAA,YAAG,CAAC;AAAA,UAC1C;AAAA,QACJ,GAAG,KAAK;AAAA,MACZ,GAAG,CAAC,CAAC;AACL,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAC7C,eAAO,KAAK,YAAY;AAAA,UACpB,aAAa,YAAY;AAAA,QAC7B,CAAC;AAAA,MACL,GAAG,CAAC,CAAC;AACL,UAAI,QAAQ,WAAY;AACpB,cAAM,OAAO,eAAe,YAAY,kBAAkB;AAAA,MAC9D;AACA,UAAI,iBAAiB,SAAUC,QAAO;AAAE,eAAQ,iBAAiB,cAAc,OAAO,SAAS,EAAE,iBAAiB,iBAAiB,gBAAgB,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAI;AAClL,UAAI,uBAAuB,WAAY;AAAE,eAAO,iBAAiB,cAAc,gBAAgB,MAAM,SAAUA,QAAO;AAAE,iBAAO,iBAAiB,cAAc,aAAa,SAAS,CAAC,GAAGA,MAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AACrM,UAAI,yBAAyB,SAAUA,QAAO;AAAE,eAAQ,iBAAiB,cAAc,gBAAgB,MAAM,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,eAAe,EAAE,SAAS,WAAY;AACjM,cAAE,QAAQ;AACV,YAAAA,OAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,eAAe,SAAU,mBAAmB;AAC5C,YAAI,OAAO,kBAAkB;AAC7B,YAAI,aAAa;AAAA,UACb,UAAW,iBAAiB;AAAA,YAAc,iBAAiB;AAAA,YAAU;AAAA,YACjE,iBAAiB,mBAAoB,iBAAiB,cAAc,iBAAiB,EAAE,cAAc,kBAAkB,cAAc,MAAa,CAAC;AAAA,YACnJ,iBAAiB,cAAc,gBAAgB,EAAE,KAAK,kBAAkB,KAAK,eAAe,kBAAkB,eAAe,WAAW,kBAAkB,WAAW,mBAAmB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,mBAAmB,UAAU,kBAAkB,UAAU,UAAU,iBAAiB,UAAU,MAAa,CAAC;AAAA,YACxV,KAAK;AAAA,UAAQ;AAAA,QACrB;AACA,eAAO,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,UAAU;AAAA,MAClD;AACA,UAAI,WAAW,SAAU,YAAY;AACjC,yBAAiB,WAAW;AAAA,MAChC;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA,OAAO;AAAA,QACP,aAAa;AAAA,QACb,eAAe;AAAA,QACf;AAAA,MACJ;AAAA,IACJ;AAEA,YAAQ,YAAY;AACpB,YAAQ,qBAAqB;AAC7B,YAAQ,wBAAwB;AAChC,YAAQ,sBAAsB;AAC9B,YAAQ,qBAAqB;AAC7B,YAAQ,cAAc;AAAA;AAAA;;;ACpatB,IAAAC,gBAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,WAAW,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QACtF,iBAAiB,cAAc,QAAQ,EAAE,GAAG,qWAAqW,CAAC;AAAA,MAAC;AAAA,IAAI;AAiB3Z,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,mBAAmB,SAAU,IAAI;AACjC,UAAI,MAAM,GAAG,KAAK,SAAS,GAAG;AAC9B,UAAI,KAAK,iBAAiB,SAAS,GAAG,OAAO,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC;AAClE,uBAAiB,UAAU,WAAY;AACnC,YAAI,YAAY,EACX,KAAK,SAAU,MAAM;AACtB,iBAAO,QAAQ,QAAQ,IAAI;AAAA,QAC/B,CAAC,EACI,KAAK,SAAU,MAAM;AACtB,iBAAO,IAAI,gBAAgB,EAAE,KAAK,SAAU,GAAG;AAC3C,mBAAO,QAAQ,QAAQ;AAAA,cACnB,UAAU,KAAK,8BAA8B;AAAA,cAC7C,MAAM,KAAK;AAAA,cACX,QAAQ,EAAE;AAAA,YACd,CAAC;AAAA,UACL,CAAC;AAAA,QACL,CAAC,EACI,KAAK,SAAU,UAAU;AAC1B,kBAAQ,QAAQ;AAAA,QACpB,CAAC;AAAA,MACL,GAAG,CAAC,CAAC;AACL,aAAO,OAAQ,OAAO,IAAI,IAAM,iBAAiB;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,yBAAyB;AAAA,QACxG,iBAAiB,cAAc,KAAK,SAAS,IAAI;AAAA,MAAC;AAAA,IAC1D;AAEA,QAAI,eAAe,SAAU,IAAI;AAC7B,UAAI,QAAQ,GAAG,OAAO,QAAQ,GAAG;AACjC,UAAI,YAAY,iBAAiB,WAAW,KAAK,YAAY,EAAE;AAC/D,UAAI,QAAQ,cAAc,KAAK,cAAc;AAC7C,aAAQ,iBAAiB;AAAA,QAAc;AAAA,QAAM,EAAE,WAAW,KAAK,WAAW;AAAA,UAClE,wBAAwB;AAAA,UACxB,6BAA6B;AAAA,QACjC,CAAC,EAAE;AAAA,QACH,iBAAiB;AAAA,UAAc;AAAA,UAAM,EAAE,WAAW,6BAA6B;AAAA,UAC3E;AAAA,UACA;AAAA,QAAG;AAAA,QACP,iBAAiB,cAAc,MAAM,EAAE,WAAW,6BAA6B,GAAG,SAAS,GAAG;AAAA,MAAC;AAAA,IACvG;AAEA,QAAI,YAAY,IAAI,OAAO,0FAWnB;AACR,QAAI,QAAQ,SAAU,OAAO,KAAK,KAAK,cAAc;AACjD,UAAI,SAAS,SAAS,OAAO,EAAE;AAC/B,aAAO,UAAU,OAAO,UAAU,MAAM,SAAS;AAAA,IACrD;AACA,QAAI,cAAc,SAAU,OAAO;AAC/B,UAAI,UAAU,UAAU,KAAK,KAAK;AAClC,UAAI,CAAC,SAAS;AACV,eAAO;AAAA,MACX;AACA,UAAI,OAAO,SAAS,QAAQ,CAAC,GAAG,EAAE;AAClC,UAAI,QAAQ,MAAM,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI;AAC1C,UAAI,MAAM,MAAM,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC;AACpC,UAAI,OAAO,MAAM,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC;AACrC,UAAI,SAAS,MAAM,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC;AACvC,UAAI,SAAS,MAAM,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC;AACvC,UAAI,wBAAwB,QAAQ,CAAC,KAAK;AAC1C,UAAI,aAAa,MAAM,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC;AAC3C,UAAI,eAAe,MAAM,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC;AAC7C,cAAQ,uBAAuB;AAAA,QAC3B,KAAK;AACD,kBAAQ;AACR,oBAAU;AACV;AAAA,QACJ,KAAK;AACD,kBAAQ;AACR,oBAAU;AACV;AAAA,MACR;AACA,aAAO,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM,QAAQ,MAAM,CAAC;AAAA,IACpE;AAEA,QAAI,cAAc,SAAU,KAAK;AAC7B,UAAI,MAAM,IAAI,MAAM,GAAG,EAAE,IAAI;AAC7B,aAAO,MAAM,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI;AAAA,IACnD;AAEA,QAAI,cAAc,SAAU,OAAO;AAC/B,UAAI,UAAU,CAAC,KAAK,MAAM,MAAM,MAAM,IAAI;AAC1C,UAAI,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC;AACnD,aAAO,GAAG,QAAQ,QAAQ,KAAK,IAAI,MAAM,CAAC,GAAG,QAAQ,CAAC,GAAG,GAAG,EAAE,OAAO,QAAQ,CAAC,CAAC;AAAA,IACnF;AAEA,QAAI,kBAAkB,SAAU,IAAI;AAChC,UAAI,MAAM,GAAG,KAAK,WAAW,GAAG,UAAU,WAAW,GAAG;AACxD,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,aAAa,SAAU,OAAO;AAC9B,YAAI,OAAO,YAAY,KAAK;AAC5B,eAAO,OAAO,GAAG,OAAO,KAAK,mBAAmB,GAAG,IAAI,EAAE,OAAO,KAAK,mBAAmB,CAAC,IAAI;AAAA,MACjG;AACA,UAAI,aAAa,SAAU,MAAM;AAAE,eAAQ,iBAAiB;AAAA,UAAc,iBAAiB;AAAA,UAAU;AAAA,UACjG,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,gCAAgC;AAAA,YAC/E,iBAAiB,cAAc,cAAc,EAAE,OAAO,QAAQ,KAAK,aACzD,KAAK,WAAW,WAChB,aAAa,OAAO,KAAK,YAAY,YAAY,QAAQ,EAAE,CAAC;AAAA,YACtE,iBAAiB,cAAc,cAAc,EAAE,OAAO,QAAQ,KAAK,aACzD,KAAK,WAAW,WAChB,aAAa,OAAO,YAAY,KAAK,MAAM,EAAE,CAAC;AAAA,UAAC;AAAA,UAC7D,iBAAiB,cAAc,KAAK,WAAW,IAAI;AAAA,UACnD,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,gCAAgC;AAAA,YAC/E,iBAAiB,cAAc,cAAc,EAAE,OAAO,QAAQ,KAAK,aAAa,KAAK,WAAW,QAAQ,SAAS,OAAO,KAAK,KAAK,MAAM,CAAC;AAAA,YACzI,iBAAiB,cAAc,cAAc,EAAE,OAAO,QAAQ,KAAK,aAAa,KAAK,WAAW,SAAS,UAAU,OAAO,KAAK,KAAK,OAAO,CAAC;AAAA,YAC5I,iBAAiB,cAAc,cAAc,EAAE,OAAO,QAAQ,KAAK,aAAa,KAAK,WAAW,UAAU,WAAW,OAAO,KAAK,KAAK,QAAQ,CAAC;AAAA,YAC/I,iBAAiB,cAAc,cAAc,EAAE,OAAO,QAAQ,KAAK,aAAa,KAAK,WAAW,WAAW,YAAY,OAAO,KAAK,KAAK,SAAS,CAAC;AAAA,YAClJ,iBAAiB,cAAc,cAAc,EAAE,OAAO,QAAQ,KAAK,aAAa,KAAK,WAAW,UAAU,WAAW,OAAO,KAAK,KAAK,QAAQ,CAAC;AAAA,YAC/I,iBAAiB,cAAc,cAAc,EAAE,OAAO,QAAQ,KAAK,aACzD,KAAK,WAAW,eAChB,iBAAiB,OAAO,WAAW,KAAK,KAAK,YAAY,EAAE,CAAC;AAAA,YACtE,iBAAiB,cAAc,cAAc,EAAE,OAAO,QAAQ,KAAK,aACzD,KAAK,WAAW,mBAChB,qBAAqB,OAAO,WAAW,KAAK,KAAK,OAAO,EAAE,CAAC;AAAA,UAAC;AAAA,UAC1E,iBAAiB,cAAc,KAAK,WAAW,IAAI;AAAA,UACnD,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,gCAAgC;AAAA,YAC/E,iBAAiB,cAAc,cAAc,EAAE,OAAO,QAAQ,KAAK,aACzD,KAAK,WAAW,cAChB,gBAAgB,OAAO,KAAK,KAAK,SAAS,CAAC;AAAA,YACrD,iBAAiB,cAAc,cAAc,EAAE,OAAO,QAAQ,KAAK,aACzD,KAAK,WAAW,aAChB,eAAe,OAAO,KAAK,KAAK,iBAAiB,CAAC;AAAA,YAC5D,iBAAiB,cAAc,cAAc,EAAE,OAAO,QAAQ,KAAK,aACzD,KAAK,WAAW,YAChB,cAAc,OAAO,GAAG,OAAO,IAAI,QAAQ,EAAE,CAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAI;AACtE,aAAQ,iBAAiB;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,wBAAwB;AAAA,QAC/E,iBAAiB,cAAc,kBAAkB,EAAE,KAAU,QAAQ,WAAW,CAAC;AAAA,QACjF,iBAAiB;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,+BAA+B;AAAA,UAC9E,iBAAiB,cAAc,KAAK,QAAQ,EAAE,SAAS,SAAS,GAAG,QAAQ,KAAK,aAAa,KAAK,WAAW,QAAQ,OAAO;AAAA,QAAC;AAAA,MAAC;AAAA,IAC1I;AAEA,QAAI,iBAAiB,EAAE,MAAM,GAAG,KAAK,EAAE;AACvC,QAAI,uBAAuB,SAAU,IAAI;AACrC,UAAI,UAAU,GAAG;AACjB,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,aAAa,KAAK,WAAW,iBAAiB;AACvE,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,cAAc,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAe,EAAE,WAAW,OAAO,QAAQ,sBAAsB,QAAiB;AAAA,QACrP,iBAAiB,cAAc,UAAU,IAAI;AAAA,MAAC,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAO,GAAG,QAAQ,eAAe,CAAC;AAAA,IAC7H;AAEA,QAAI,cAAc,SAAU,OAAO;AAC/B,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,KAAK,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AAC9F,UAAI,wBAAwB,SAAU,KAAK;AACvC,sBAAc,GAAG;AAAA,MACrB;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,OAAO,qBAAqB;AAC5C,eAAO,WAAY;AACf,gBAAM,YAAY,OAAO,qBAAqB;AAAA,QAClD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO,EAAE,WAAuB;AAAA,IACpC;AAEA,QAAI,iBAAiB,SAAU,IAAI;AAC/B,UAAI,WAAW,GAAG,UAAU,QAAQ,GAAG;AACvC,UAAI,aAAa,YAAY,KAAK,EAAE;AACpC,UAAI,WAAW,MAAM,IAAI,UAAU,KAAK;AACxC,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAO,iBAAiB,cAAc,sBAAsB,SAAS,CAAC,GAAG,KAAK,CAAC;AAAA,MAAG;AAC3H,UAAI,SAAS,YAAY;AACzB,aAAO,aAAc,iBAAiB,cAAc,KAAK,OAAO,EAAE,oBAAoB,cAAc,QAAQ,SAAU,QAAQ;AACtH,eAAO,OAAO;AAAA,UACV,SAAS;AAAA,QACb,CAAC;AAAA,MACL,GAAG,SAAS,SAAU,QAAQ;AAAE,eAAO,iBAAiB,cAAc,iBAAiB,EAAE,KAAK,YAAY,UAAoB,UAAU,OAAO,CAAC;AAAA,MAAG,GAAG,qBAAqB,MAAM,eAAe,KAAK,CAAC,IAAM,iBAAiB,cAAc,iBAAiB,UAAU,IAAI;AAAA,IAClR;AAEA,QAAI,yBAAyB,SAAU,IAAI;AACvC,UAAI,UAAU,GAAG;AACjB,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,aAAa,KAAK,WAAW,iBAAiB;AACvE,aAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,MAAM,iBAAiB,cAAc,UAAU,IAAI,GAAG,QAAQ,oBAAoB,QAAiB,GAAG,KAAK;AAAA,IACvK;AAEA,QAAI,mBAAmB,WAAY;AAC/B,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAC7C,eAAO,KAAK,YAAY;AAAA,UACpB,UAAU;AAAA,QACd,CAAC;AAAA,MACL,GAAG,CAAC,CAAC;AACL,UAAI,0BAA0B,SAAU,OAAO;AAAE,eAAO,iBAAiB,cAAc,gBAAgB,SAAS,CAAC,GAAG,OAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AAC/I,UAAI,gCAAgC,WAAY;AAAE,eAAO,iBAAiB,cAAc,gBAAgB,EAAE,MAAa,CAAC;AAAA,MAAG;AAC3H,UAAI,kCAAkC,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,yBAAyB,MAAM,SAAU,GAAG;AAAE,iBAAO,iBAAiB,cAAc,wBAAwB,SAAS,CAAC,GAAG,CAAC,CAAC;AAAA,QAAG,CAAC;AAAA,MAAI;AACnO,aAAO;AAAA,QACH,gBAAgB,SAAU,OAAO;AAC7B,gBAAM,OAAO,OAAO,MAAM,GAAG;AAAA,QACjC;AAAA,QACA,qBAAqB,SAAU,aAAa;AACxC,gBAAM,OAAO,YAAY,YAAY,KAAK,IAAI;AAC9C,iBAAO;AAAA,QACX;AAAA,QACA,gBAAgB;AAAA,QAChB,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,MAC5B;AAAA,IACJ;AAEA,YAAQ,WAAW;AACnB,YAAQ,mBAAmB;AAAA;AAAA;;;AClQ3B,IAAAC,gBAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,qBAAqB,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,iBAAiB,MAAM,MAAM,GAAG;AAAA,QACvH,iBAAiB,cAAc,QAAQ,EAAE,GAAG,odAAod,CAAC;AAAA,MAAC;AAAA,IAAI;AAE1gB,QAAI,oBAAoB,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,iBAAiB,MAAM,MAAM,GAAG;AAAA,QACtH,iBAAiB,cAAc,QAAQ,EAAE,GAAG,2dAA2d,CAAC;AAAA,MAAC;AAAA,IAAI;AAiBjhB,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,iBAAiB,EAAE,MAAM,GAAG,KAAK,EAAE;AACvC,QAAI,eAAe,SAAU,IAAI;AAC7B,UAAI,YAAY,GAAG,WAAW,UAAU,GAAG;AAC3C,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,gBAAgB,QAAQ,KAAK,SAAS,KAAK,OAAO,iBAAiB;AACvE,UAAI,eAAe,QAAQ,KAAK,SAAS,KAAK,OAAO,gBAAgB;AACrE,UAAI,QAAQ,cAAc,KAAK,gBAAgB,WAAW,gBAAgB;AAC1E,UAAI,OAAO,cAAc,KAAK,gBAAgB,WAAW,iBAAiB,cAAc,oBAAoB,IAAI,IAAI,iBAAiB,cAAc,mBAAmB,IAAI;AAC1K,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,UAAU,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB,cAAc,KAAK,eAAe,EAAE,WAAW,OAAO,QAAQ,cAAc,KAAK,gBAAgB,WAAW,4BAA4B,0BAA0B,QAAiB,GAAG,IAAI,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAO,GAAG,QAAQ,eAAe,CAAC;AAAA,IACrZ;AAEA,QAAI,SAAS,SAAU,IAAI;AACvB,UAAI,WAAW,GAAG,UAAU,YAAY,GAAG,WAAW,QAAQ,GAAG;AACjE,UAAI,UAAU,WAAY;AACtB,YAAI,SAAS,MAAM,IAAI,QAAQ;AAC/B,YAAI,QAAQ;AACR,iBAAO,SAAS;AAAA,QACpB;AAAA,MACJ;AACA,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,cAAc,EAAE,WAAW,MAAM,WAAW,SAAS,MAAM,QAAQ,CAAC;AAAA,MAAI;AACxJ,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AAEA,QAAI,iBAAiB,SAAU,IAAI;AAC/B,UAAI,YAAY,GAAG,WAAW,UAAU,GAAG;AAC3C,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,gBAAgB,QAAQ,KAAK,SAAS,KAAK,OAAO,iBAAiB;AACvE,UAAI,eAAe,QAAQ,KAAK,SAAS,KAAK,OAAO,gBAAgB;AACrE,UAAI,QAAQ,cAAc,KAAK,gBAAgB,WAAW,gBAAgB;AAC1E,UAAI,OAAO,cAAc,KAAK,gBAAgB,WAAW,iBAAiB,cAAc,oBAAoB,IAAI,IAAI,iBAAiB,cAAc,mBAAmB,IAAI;AAC1K,aAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,MAAY,QAAQ,cAAc,KAAK,gBAAgB,WAAW,0BAA0B,wBAAwB,QAAiB,GAAG,KAAK;AAAA,IACzM;AAEA,QAAI,aAAa,SAAU,IAAI;AAC3B,UAAI,WAAW,GAAG,UAAU,QAAQ,GAAG;AACvC,UAAI,eAAe,SAAU,WAAW,WAAW;AAC/C,YAAI,aAAa,MAAM,IAAI,YAAY;AACvC,YAAI,YAAY;AACZ,qBAAW,WAAW,SAAS;AAAA,QACnC;AAAA,MACJ;AACA,aAAO,SAAS;AAAA,QACZ;AAAA,MACJ,CAAC;AAAA,IACL;AAEA,QAAI,eAAe,WAAY;AAC3B,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAAE,eAAO,KAAK,YAAY;AAAA,MAAG,GAAG,CAAC,CAAC;AACnF,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAO,iBAAiB,cAAc,QAAQ,SAAS,CAAC,GAAG,OAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AAC/H,UAAI,gCAAgC,WAAY;AAAE,eAAQ,iBAAiB,cAAc,iBAAiB,EAAE,WAAW,KAAK,gBAAgB,SAAS,GAAG,SAAU,OAAO;AAAE,iBAAO,iBAAiB,cAAc,cAAc,SAAS,CAAC,GAAG,KAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MAAI;AAC1P,UAAI,kCAAkC,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,iBAAiB,EAAE,WAAW,KAAK,gBAAgB,SAAS,GAAG,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,gBAAgB,EAAE,WAAW,EAAE,WAAW,SAAS,WAAY;AAC5Q,cAAE,QAAQ;AACV,kBAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,+BAA+B,WAAY;AAAE,eAAQ,iBAAiB,cAAc,iBAAiB,EAAE,WAAW,KAAK,gBAAgB,QAAQ,GAAG,SAAU,OAAO;AAAE,iBAAO,iBAAiB,cAAc,cAAc,SAAS,CAAC,GAAG,KAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MAAI;AACxP,UAAI,iCAAiC,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,iBAAiB,EAAE,WAAW,KAAK,gBAAgB,QAAQ,GAAG,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,gBAAgB,EAAE,WAAW,EAAE,WAAW,SAAS,WAAY;AAC1Q,cAAE,QAAQ;AACV,kBAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,sBAAsB,SAAU,OAAO;AAAE,eAAO,iBAAiB,cAAc,YAAY,SAAS,CAAC,GAAG,OAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AACvI,aAAO;AAAA,QACH,SAAS,SAAU,iBAAiB;AAChC,gBAAM,OAAO,UAAU,gBAAgB,MAAM;AAC7C,gBAAM,OAAO,cAAc,gBAAgB,UAAU;AAAA,QACzD;AAAA,QACA,QAAQ;AAAA,QACR,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,qBAAqB;AAAA,QACrB,uBAAuB;AAAA,QACvB,YAAY;AAAA,MAChB;AAAA,IACJ;AAEA,YAAQ,qBAAqB;AAC7B,YAAQ,oBAAoB;AAC5B,YAAQ,eAAe;AAAA;AAAA;;;ACxIvB,IAAAC,gBAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,4BAA4B,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QACvG,iBAAiB,cAAc,QAAQ,EAAE,GAAG,OAAO,GAAG,SAAS,OAAO,MAAM,QAAQ,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,QAC5G,iBAAiB,cAAc,QAAQ,EAAE,IAAI,OAAO,IAAI,SAAS,IAAI,QAAQ,IAAI,QAAQ,CAAC;AAAA,QAC1F,iBAAiB,cAAc,QAAQ,EAAE,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,IAAI,SAAS,CAAC;AAAA,MAAC;AAAA,IAAI;AAErG,QAAI,uBAAuB,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAClG,iBAAiB,cAAc,QAAQ,EAAE,GAAG,OAAO,GAAG,SAAS,OAAO,MAAM,QAAQ,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,QAC5G,iBAAiB,cAAc,QAAQ,EAAE,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,IAAI,SAAS,CAAC;AAAA,MAAC;AAAA,IAAI;AAErG,QAAI,0BAA0B,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QACrG,iBAAiB,cAAc,QAAQ,EAAE,GAAG,6VAA6V,CAAC;AAAA,MAAC;AAAA,IAAI;AAEnZ,QAAI,oBAAoB,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAC/F,iBAAiB,cAAc,QAAQ,EAAE,GAAG,OAAO,GAAG,SAAS,OAAO,MAAM,QAAQ,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,MAAC;AAAA,IAAI;AAiBrH,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,mBAAmB,SAAU,OAAO,YAAY;AAChD,YAAM,IAAI,kBAAkB,EAAE,UAAU;AACxC,UAAI,kBAAkB,MAAM,IAAI,UAAU;AAC1C,WAAK,eAAe,KAAK,WAAW,cAAc,eAAe,KAAK,WAAW,YAC7E,oBAAoB,KAAK,SAAS,YAAY;AAC9C,cAAM,IAAI,gBAAgB,EAAE,KAAK,SAAS,UAAU;AAAA,MACxD;AAAA,IACJ;AAEA,QAAI,wBAAwB,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QACnG,iBAAiB,cAAc,QAAQ,EAAE,GAAG,+VAA+V,CAAC;AAAA,MAAC;AAAA,IAAI;AAErZ,QAAI,uBAAuB,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAClG,iBAAiB,cAAc,QAAQ,EAAE,GAAG,+cAA+c,CAAC;AAAA,MAAC;AAAA,IAAI;AAErgB,QAAI,4BAA4B,SAAU,IAAI;AAC1C,UAAI,WAAW,GAAG,UAAU,OAAO,GAAG,MAAM,UAAU,GAAG;AACzD,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ;AACZ,UAAI,OAAO,iBAAiB,cAAc,uBAAuB,IAAI;AACrE,cAAQ,MAAM;AAAA,QACV,KAAK,KAAK,WAAW;AACjB,kBACI,QAAQ,KAAK,aACP,KAAK,WAAW,sBAChB;AACV,iBAAO,iBAAiB,cAAc,yBAAyB,IAAI;AACnE;AAAA,QACJ,KAAK,KAAK,WAAW;AACjB,kBACI,QAAQ,KAAK,aACP,KAAK,WAAW,gBAChB;AACV,iBAAO,iBAAiB,cAAc,mBAAmB,IAAI;AAC7D;AAAA,QACJ,KAAK,KAAK,WAAW;AACjB,kBACI,QAAQ,KAAK,aACP,KAAK,WAAW,mBAChB;AACV,iBAAO,iBAAiB,cAAc,sBAAsB,IAAI;AAChE;AAAA,QACJ,KAAK,KAAK,WAAW;AAAA,QACrB;AACI,kBACI,QAAQ,KAAK,aACP,KAAK,WAAW,oBAChB;AACV,iBAAO,iBAAiB,cAAc,uBAAuB,IAAI;AACjE;AAAA,MACR;AACA,aAAO,SAAS,EAAE,MAAY,OAAc,QAAiB,CAAC;AAAA,IAClE;AAEA,QAAI,mBAAmB,EAAE,MAAM,GAAG,KAAK,EAAE;AACzC,QAAI,yBAAyB,SAAU,IAAI;AACvC,UAAI,aAAa,GAAG,YAAY,aAAa,GAAG,YAAY,OAAO,GAAG,MAAM,UAAU,GAAG;AACzF,UAAI,SAAS;AACb,cAAQ,MAAM;AAAA,QACV,KAAK,KAAK,WAAW;AACjB,mBAAS;AACT;AAAA,QACJ,KAAK,KAAK,WAAW;AACjB,mBAAS;AACT;AAAA,QACJ,KAAK,KAAK,WAAW;AACjB,mBAAS;AACT;AAAA,QACJ,KAAK,KAAK,WAAW;AAAA,QACrB;AACI,mBAAS;AACT;AAAA,MACR;AACA,aAAQ,iBAAiB,cAAc,2BAA2B,EAAE,MAAY,QAAiB,GAAG,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,sBAAsB,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB,cAAc,KAAK,eAAe,EAAE,WAAW,MAAM,OAAO,YAAwB,YAAwB,QAAgB,SAAS,MAAM,QAAQ,GAAG,MAAM,IAAI,GAAG,SAAS,WAAY;AAAE,iBAAO,MAAM;AAAA,QAAO,GAAG,QAAQ,iBAAiB,CAAC;AAAA,MAAI,CAAC;AAAA,IAC3gB;AAEA,QAAI,gBAAgB,SAAU,OAAO;AACjC,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,YAAY,KAAK,KAAK,WAAW,QAAQ,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AACjI,UAAI,0BAA0B,SAAU,mBAAmB;AACvD,sBAAc,iBAAiB;AAAA,MACnC;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,cAAc,uBAAuB;AACrD,eAAO,WAAY;AACf,gBAAM,YAAY,cAAc,uBAAuB;AAAA,QAC3D;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO,EAAE,WAAuB;AAAA,IACpC;AAEA,QAAI,cAAc,SAAU,OAAO;AAC/B,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,UAAU,KAAK,KAAK,SAAS,UAAU,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;AAC3H,UAAI,wBAAwB,SAAU,iBAAiB;AACnD,oBAAY,eAAe;AAAA,MAC/B;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,YAAY,qBAAqB;AACjD,eAAO,WAAY;AACf,gBAAM,YAAY,YAAY,qBAAqB;AAAA,QACvD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO,EAAE,SAAmB;AAAA,IAChC;AAEA,QAAI,mBAAmB,SAAU,IAAI;AACjC,UAAI,WAAW,GAAG,UAAU,OAAO,GAAG,MAAM,QAAQ,GAAG;AACvD,UAAI,WAAW,YAAY,KAAK,EAAE;AAClC,UAAI,aAAa,cAAc,KAAK,EAAE;AACtC,UAAI,UAAU,WAAY;AACtB,yBAAiB,OAAO,IAAI;AAAA,MAChC;AACA,UAAI,aAAa,eAAe;AAChC,UAAI,cAAc,SAAS,KAAK,WAAW,cAAc,SAAS,KAAK,WAAW,YAAY,aAAa,KAAK,SAAS;AACzH,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,wBAAwB,EAAE,YAAwB,YAAwB,MAAM,MAAM,MAAM,SAAS,MAAM,QAAQ,CAAC;AAAA,MAAI;AACxM,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AAEA,QAAI,2BAA2B,SAAU,IAAI;AACzC,UAAI,aAAa,GAAG,YAAY,aAAa,GAAG,YAAY,OAAO,GAAG,MAAM,UAAU,GAAG;AACzF,UAAI,SAAS;AACb,cAAQ,MAAM;AAAA,QACV,KAAK,KAAK,WAAW;AACjB,mBAAS;AACT;AAAA,QACJ,KAAK,KAAK,WAAW;AACjB,mBAAS;AACT;AAAA,QACJ,KAAK,KAAK,WAAW;AACjB,mBAAS;AACT;AAAA,QACJ,KAAK,KAAK,WAAW;AAAA,QACrB;AACI,mBAAS;AACT;AAAA,MACR;AACA,aAAQ,iBAAiB,cAAc,2BAA2B,EAAE,MAAY,QAAiB,GAAG,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,SAAS,YAAY,MAAM,MAAM,MAAM,YAAwB,QAAgB,SAAS,MAAM,QAAQ,GAAG,MAAM,KAAK;AAAA,MAAI,CAAC;AAAA,IAC5S;AAEA,QAAI,iBAAiB,SAAU,OAAO,UAAU;AAC5C,YAAM,IAAI,gBAAgB,EAAE,QAAQ;AACpC,UAAI,oBAAoB,MAAM,IAAI,YAAY;AAC9C,WAAK,sBAAsB,KAAK,WAAW,cAAc,sBAAsB,KAAK,WAAW,YAC3F,aAAa,KAAK,SAAS,YAAY;AACvC,cAAM,IAAI,kBAAkB,EAAE,KAAK,WAAW,QAAQ;AAAA,MAC1D;AAAA,IACJ;AAEA,QAAI,0BAA0B,SAAU,IAAI;AACxC,UAAI,WAAW,GAAG,UAAU,OAAO,GAAG,MAAM,UAAU,GAAG;AACzD,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ;AACZ,UAAI,OAAO,iBAAiB,cAAc,mBAAmB,IAAI;AACjE,cAAQ,MAAM;AAAA,QACV,KAAK,KAAK,SAAS;AACf,kBAAQ,QAAQ,KAAK,aAAa,KAAK,WAAW,WAAW;AAC7D,iBAAO,iBAAiB,cAAc,sBAAsB,IAAI;AAChE;AAAA,QACJ,KAAK,KAAK,SAAS;AACf,kBACI,QAAQ,KAAK,aACP,KAAK,WAAW,gBAChB;AACV,iBAAO,iBAAiB,cAAc,2BAA2B,IAAI;AACrE;AAAA,QACJ,KAAK,KAAK,SAAS;AAAA,QACnB;AACI,kBACI,QAAQ,KAAK,aAAa,KAAK,WAAW,aAAa;AAC3D,iBAAO,iBAAiB,cAAc,mBAAmB,IAAI;AAC7D;AAAA,MACR;AACA,aAAO,SAAS,EAAE,MAAY,OAAc,QAAiB,CAAC;AAAA,IAClE;AAEA,QAAI,iBAAiB,EAAE,MAAM,GAAG,KAAK,EAAE;AACvC,QAAI,uBAAuB,SAAU,IAAI;AACrC,UAAI,aAAa,GAAG,YAAY,aAAa,GAAG,YAAY,OAAO,GAAG,MAAM,UAAU,GAAG;AACzF,UAAI,SAAS;AACb,cAAQ,MAAM;AAAA,QACV,KAAK,KAAK,SAAS;AACf,mBAAS;AACT;AAAA,QACJ,KAAK,KAAK,SAAS;AACf,mBAAS;AACT;AAAA,QACJ,KAAK,KAAK,SAAS;AAAA,QACnB;AACI,mBAAS;AACT;AAAA,MACR;AACA,aAAQ,iBAAiB,cAAc,yBAAyB,EAAE,MAAY,QAAiB,GAAG,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,oBAAoB,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB,cAAc,KAAK,eAAe,EAAE,WAAW,MAAM,OAAO,YAAwB,YAAwB,QAAgB,SAAS,MAAM,QAAQ,GAAG,MAAM,IAAI,GAAG,SAAS,WAAY;AAAE,iBAAO,MAAM;AAAA,QAAO,GAAG,QAAQ,eAAe,CAAC;AAAA,MAAI,CAAC;AAAA,IACrgB;AAEA,QAAI,iBAAiB,SAAU,IAAI;AAC/B,UAAI,WAAW,GAAG,UAAU,OAAO,GAAG,MAAM,QAAQ,GAAG;AACvD,UAAI,WAAW,YAAY,KAAK,EAAE;AAClC,UAAI,aAAa,cAAc,KAAK,EAAE;AACtC,UAAI,UAAU,WAAY;AACtB,uBAAe,OAAO,IAAI;AAAA,MAC9B;AACA,UAAI,aAAa,aAAa;AAC9B,UAAI,cAAc,eAAe,KAAK,WAAW,cAAc,eAAe,KAAK,WAAW,YAAY,SAAS,KAAK,SAAS;AACjI,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,sBAAsB,EAAE,YAAwB,YAAwB,MAAM,MAAM,MAAM,SAAS,MAAM,QAAQ,CAAC;AAAA,MAAI;AACtM,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AAEA,QAAI,yBAAyB,SAAU,IAAI;AACvC,UAAI,aAAa,GAAG,YAAY,aAAa,GAAG,YAAY,OAAO,GAAG,MAAM,UAAU,GAAG;AACzF,UAAI,SAAS;AACb,cAAQ,MAAM;AAAA,QACV,KAAK,KAAK,SAAS;AACf,mBAAS;AACT;AAAA,QACJ,KAAK,KAAK,SAAS;AACf,mBAAS;AACT;AAAA,QACJ,KAAK,KAAK,SAAS;AAAA,QACnB;AACI,mBAAS;AACT;AAAA,MACR;AACA,aAAQ,iBAAiB,cAAc,yBAAyB,EAAE,MAAY,QAAiB,GAAG,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,SAAS,YAAY,MAAM,MAAM,MAAM,YAAwB,QAAgB,SAAS,MAAM,QAAQ,GAAG,MAAM,KAAK;AAAA,MAAI,CAAC;AAAA,IAC1S;AAEA,QAAI,mBAAmB,WAAY;AAC/B,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAC7C,eAAO,KAAK,YAAY;AAAA,UACpB,YAAY,KAAK,WAAW;AAAA,UAC5B,UAAU,KAAK,SAAS;AAAA,UACxB,kBAAkB,WAAY;AAAA,UAC9B;AAAA,UACA,gBAAgB,WAAY;AAAA,UAC5B;AAAA,QACJ,CAAC;AAAA,MACL,GAAG,CAAC,CAAC;AACL,UAAIC,6BAA4B,SAAU,OAAO;AAAE,eAAO,iBAAiB,cAAc,kBAAkB,SAAS,CAAC,GAAG,OAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AACnJ,UAAI,kCAAkC,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAcA,4BAA2B,EAAE,MAAM,MAAM,KAAK,GAAG,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,wBAAwB,EAAE,YAAY,EAAE,YAAY,YAAY,EAAE,YAAY,MAAM,EAAE,MAAM,SAAS,WAAY;AAChT,cAAE,QAAQ;AAAA,UACd,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,oCAAoC,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAcA,4BAA2B,EAAE,MAAM,MAAM,KAAK,GAAG,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,0BAA0B,EAAE,YAAY,EAAE,YAAY,YAAY,EAAE,YAAY,MAAM,EAAE,MAAM,SAAS,WAAY;AACpT,cAAE,QAAQ;AACV,kBAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAIC,2BAA0B,SAAU,OAAO;AAAE,eAAO,iBAAiB,cAAc,gBAAgB,SAAS,CAAC,GAAG,OAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AAC/I,UAAI,gCAAgC,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAcA,0BAAyB,EAAE,MAAM,MAAM,KAAK,GAAG,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,sBAAsB,EAAE,YAAY,EAAE,YAAY,YAAY,EAAE,YAAY,MAAM,EAAE,MAAM,SAAS,WAAY;AAC1S,cAAE,QAAQ;AAAA,UACd,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,kCAAkC,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAcA,0BAAyB,EAAE,MAAM,MAAM,KAAK,GAAG,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,wBAAwB,EAAE,YAAY,EAAE,YAAY,YAAY,EAAE,YAAY,MAAM,EAAE,MAAM,SAAS,WAAY;AAC9S,cAAE,QAAQ;AACV,kBAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,aAAO;AAAA,QACH,SAAS,SAAU,iBAAiB;AAChC,gBAAM,OAAO,oBAAoB,gBAAgB,gBAAgB;AACjE,gBAAM,OAAO,kBAAkB,gBAAgB,cAAc;AAAA,QACjE;AAAA,QACA,qBAAqB,SAAU,aAAa;AACxC,gBAAM,OAAO,cAAc,YAAY,UAAU;AACjD,gBAAM,OAAO,YAAY,YAAY,QAAQ;AAC7C,iBAAO;AAAA,QACX;AAAA,QACA,kBAAkB,SAAU,YAAY;AACpC,2BAAiB,OAAO,UAAU;AAAA,QACtC;AAAA,QACA,gBAAgB,SAAU,UAAU;AAChC,yBAAe,OAAO,QAAQ;AAAA,QAClC;AAAA,QACA,kBAAkBD;AAAA,QAClB,wBAAwB;AAAA,QACxB,0BAA0B;AAAA,QAC1B,gBAAgBC;AAAA,QAChB,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,MAC5B;AAAA,IACJ;AAEA,YAAQ,4BAA4B;AACpC,YAAQ,uBAAuB;AAC/B,YAAQ,0BAA0B;AAClC,YAAQ,oBAAoB;AAC5B,YAAQ,wBAAwB;AAChC,YAAQ,uBAAuB;AAC/B,YAAQ,mBAAmB;AAAA;AAAA;;;ACvW3B,IAAAC,gBAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,WAAW,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QACtF,iBAAiB,cAAc,QAAQ,EAAE,GAAG,6HAA6H,CAAC;AAAA,MAAC;AAAA,IAAI;AAEnL,QAAI,eAAe,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAC1F,iBAAiB,cAAc,QAAQ,EAAE,GAAG,+HAA+H,CAAC;AAAA,MAAC;AAAA,IAAI;AAErL,QAAI,aAAa,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,iBAAiB,MAAM,MAAM,GAAG;AAAA,QAC/G,iBAAiB,cAAc,QAAQ,EAAE,GAAG,0IAA0I,CAAC;AAAA,MAAC;AAAA,IAAI;AAiBhM,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,uBAAuB;AAAA,MACvB,SAAS;AAAA,MACT,QAAQ,IAAI,OAAO,GAAG;AAAA,MACtB,YAAY;AAAA,IAChB;AAEA,QAAI,kBAAkB,SAAU,UAAU,QAAQ;AAC9C,UAAI,MAAM,SAAS;AACnB,UAAI,OAAO,SAAS;AACpB,UAAI,IAAI,SAAS;AACjB,aAAO,KAAK,MAAM,QAAQ;AACtB,eAAO,EAAE;AACT,gBAAQ,EAAE;AACV,YAAI,EAAE;AAAA,MACV;AACA,aAAO;AAAA,QACH;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,mBAAmB,SAAU,MAAM;AACnC,aAAO;AAAA,QACH,MAAM,GAAG,OAAO,KAAK,MAAM,GAAG;AAAA,QAC9B,KAAK,GAAG,OAAO,KAAK,KAAK,GAAG;AAAA,QAC5B,QAAQ,GAAG,OAAO,KAAK,QAAQ,GAAG;AAAA,QAClC,OAAO,GAAG,OAAO,KAAK,OAAO,GAAG;AAAA,MACpC;AAAA,IACJ;AAEA,QAAI,iBAAiB,SAAU,IAAI;AAC/B,UAAI,QAAQ,GAAG,OAAO,OAAO,GAAG,MAAM,qBAAqB,GAAG;AAC9D,UAAI,eAAe,iBAAiB,OAAO;AAC3C,WAAK,0BAA0B,WAAY;AACvC,YAAI,eAAe,aAAa;AAChC,YAAI,sBAAsB,cAAc;AACpC,6BAAmB;AAAA,YACf;AAAA,YACA,SAAS,KAAK;AAAA,UAClB,CAAC;AAAA,QACL;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAQ,iBAAiB,cAAc,OAAO,EAAE,WAAW,yBAAyB,cAAc,OAAO,KAAK,cAAc,OAAO,iBAAiB,IAAI,GAAG,OAAO,KAAK,WAAW,KAAK,EAAE,CAAC;AAAA,IAC9L;AAEA,QAAI,aAAa,SAAU,KAAK;AAC5B,UAAI,SAAS,IAAI;AACjB,UAAI,QAAQ;AACR,eAAO,YAAY,GAAG;AAAA,MAC1B;AAAA,IACJ;AACA,QAAI,cAAc,SAAU,iBAAiB,MAAM;AAC/C,iBAAW,eAAe;AAC1B,UAAI,SAAS,KAAK;AAClB,UAAI,QAAQ;AACR,eAAO,aAAa,iBAAiB,IAAI;AAAA,MAC7C;AACA,iBAAW,IAAI;AAAA,IACnB;AACA,QAAI,SAAS,SAAU,KAAK;AACxB,UAAI,SAAS,IAAI;AACjB,UAAI,CAAC,QAAQ;AACT;AAAA,MACJ;AACA,UAAI,QAAQ,SAAS,YAAY;AACjC,YAAM,mBAAmB,GAAG;AAC5B,kBAAY,MAAM,gBAAgB,GAAG,GAAG;AACxC,aAAO,UAAU;AAAA,IACrB;AAEA,QAAI,wBAAwB,SAAU,GAAG,GAAG;AACxC,UAAI,EAAE,MAAM,EAAE,KAAK;AACf,eAAO;AAAA,MACX;AACA,UAAI,EAAE,MAAM,EAAE,KAAK;AACf,eAAO;AAAA,MACX;AACA,UAAI,EAAE,OAAO,EAAE,MAAM;AACjB,eAAO;AAAA,MACX;AACA,UAAI,EAAE,OAAO,EAAE,MAAM;AACjB,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AACA,QAAI,aAAa,SAAU,IAAI;AAC3B,UAAI,WAAW,GAAG,UAAU,YAAY,GAAG,WAAW,mBAAmB,GAAG,kBAAkB,QAAQ,GAAG,OAAO,qBAAqB,GAAG;AACxI,UAAI,eAAe,iBAAiB,OAAO;AAC3C,UAAI,0BAA0B,iBAAiB,YAAY,SAAU,aAAa;AAAE,eAAQ,iBAAiB,cAAc,iBAAiB,UAAU,MAAM,YAAY,eAAe,IAAI,SAAU,MAAM,OAAO;AAAE,iBAAQ,iBAAiB,cAAc,gBAAgB,EAAE,OAAc,KAAK,OAAO,MAAY,mBAAuC,CAAC;AAAA,QAAI,CAAC,CAAC;AAAA,MAAI,GAAG,CAAC,CAAC;AAC1W,UAAI,0BAA0B,oBAAoB;AAClD,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,eAAe,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC;AAC9G,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,SAAS,KAAK,CAAC,oBAAoB,CAAC,GAAG,gBAAgB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC;AAClI,UAAI,KAAK,iBAAiB,SAAS;AAAA,QAC/B;AAAA,QACA,OAAO;AAAA,QACP,QAAQ,KAAK,kBAAkB;AAAA,MACnC,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC;AAChD,UAAI,kBAAkB,iBAAiB,OAAO,IAAI;AAClD,UAAI,sBAAsB,iBAAiB,OAAO,CAAC,CAAC;AACpD,UAAI,KAAK,iBAAiB,SAAS,CAAC,CAAC,GAAG,iBAAiB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC;AACxF,UAAI,0BAA0B,WAAY;AAAE,eAAO;AAAA,MAAM;AACzD,UAAI,mBAAmB,iBAAiB,YAAY,WAAY;AAAE,eAAO,MAAM,IAAI,kBAAkB,KAAK;AAAA,MAAyB,GAAG,CAAC,MAAM,IAAI,kBAAkB,CAAC,CAAC;AACrK,UAAI,YAAY,SAAU,YAAY,SAAS,cAAc,MAAM,eAAe;AAC9E,YAAI,QAAQ,SAAS,YAAY;AACjC,YAAI,aAAa,KAAK;AACtB,YAAI,CAAC,cAAc,WAAW,aAAa,KAAK,WAAW;AACvD,iBAAO;AAAA,QACX;AACA,YAAI,SAAS,WAAW,YAAY;AACpC,YAAI,cAAc,cAAc,CAAC,EAAE;AACnC,YAAI,YAAY,cAAc,WAAW,IAAI,cAAc,cAAc,cAAc,SAAS,CAAC,EAAE;AACnG,YAAI,cAAc,UAAU,YAAY,IAAI,QAAQ;AAChD,iBAAO;AAAA,QACX;AACA,cAAM,SAAS,YAAY,WAAW;AACtC,cAAM,OAAO,YAAY,YAAY,CAAC;AACtC,YAAI,UAAU,SAAS,cAAc,MAAM;AAC3C,cAAM,iBAAiB,OAAO;AAC9B,YAAI,cAAc,QAAQ,sBAAsB;AAChD,YAAI,gBAAgB,aAAa,sBAAsB;AACvD,YAAI,aAAa,cAAc;AAC/B,YAAI,YAAY,cAAc;AAC9B,YAAI,OAAQ,OAAO,YAAY,OAAO,cAAc,QAAS;AAC7D,YAAI,MAAO,OAAO,YAAY,MAAM,cAAc,OAAQ;AAC1D,YAAI,SAAU,MAAM,YAAY,SAAU;AAC1C,YAAI,QAAS,MAAM,YAAY,QAAS;AACxC,eAAO,OAAO;AACd,eAAO;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,eAAe,SAAU,cAAc;AACvC,YAAI,cAAc,oBAAoB;AACtC,YAAI,YAAY,WAAW,GAAG;AAC1B,iBAAO,CAAC;AAAA,QACZ;AACA,YAAI,eAAe,CAAC;AACpB,YAAI,QAAQ,CAAC,EAAE,MAAM,KAAK,aAAa,iBAAiB,4BAA4B,CAAC;AACrF,YAAI,WAAW,YAAY,IAAI,SAAU,MAAM;AAAE,iBAAO,KAAK;AAAA,QAAM,CAAC,EAAE,KAAK,EAAE;AAC7E,sBAAc,QAAQ,SAAU,SAAS;AACrC,cAAI,aAAa,QAAQ;AACzB,cAAI,CAAC,WAAW,KAAK,GAAG;AACpB;AAAA,UACJ;AACA,cAAI,eAAe,QAAQ,OAAO,MAAM,QAAQ,GAAG,MAAM,KACnD,IAAI,OAAO,QAAQ,QAAQ,GAAG,OAAO,QAAQ,OAAO,OAAO,GAAG,CAAC,IAC/D,QAAQ;AACd,cAAI;AACJ,cAAI,UAAU,CAAC;AACf,kBAAQ,QAAQ,aAAa,KAAK,QAAQ,OAAO,MAAM;AACnD,oBAAQ,KAAK;AAAA,cACT,SAAS;AAAA,cACT,YAAY,MAAM;AAAA,cAClB,UAAU,aAAa;AAAA,YAC3B,CAAC;AAAA,UACL;AACA,kBACK,IAAI,SAAU,MAAM;AAAE,mBAAQ;AAAA,cAC/B,SAAS,KAAK;AAAA,cACd,SAAS,YAAY,MAAM,KAAK,YAAY,KAAK,QAAQ;AAAA,YAC7D;AAAA,UAAI,CAAC,EACA,QAAQ,SAAU,MAAM;AACzB,gBAAI,cAAc,KAAK,QAAQ,OAAO,SAAU,KAAKC,OAAM;AACvD,kBAAIA,MAAK,SAAS,KAAK,IAAIA,MAAK,SAAS,KAAK,CAAC,GAAG,OAAO,CAACA,KAAI,CAAC;AAC/D,qBAAO;AAAA,YACX,GAAG,CAAC,CAAC;AACL,mBAAO,OAAO,WAAW,EAAE,QAAQ,SAAU,eAAe;AACxD,kBAAI,cAAc,WAAW,KAAK,cAAc,CAAC,EAAE,KAAK,KAAK,MAAM,IAAI;AACnE,oBAAI,qBAAqB,QAAQ,aAAa,cAAc,MAAM,GAAG,EAAE,IAAI;AAC3E,oBAAI,oBAAoB,UAAU,YAAY,KAAK,SAAS,cAAc,MAAM,mBAAmB,CAAC,EAAE,SAAS,GAAG,kBAAkB;AACpI,oBAAI,mBAAmB;AACnB,+BAAa,KAAK,iBAAiB;AAAA,gBACvC;AAAA,cACJ;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL,CAAC;AACD,eAAO,aAAa,KAAK,qBAAqB;AAAA,MAClD;AACA,UAAI,uBAAuB,SAAU,SAAS;AAC1C,YAAI,WAAW,QAAQ,SAAS,GAAG;AAC/B,2BAAiB,OAAO;AAAA,QAC5B;AAAA,MACJ;AACA,UAAI,6BAA6B,SAAU,iBAAiB;AAAE,eAAO,iBAAiB,eAAe;AAAA,MAAG;AACxG,UAAI,4BAA4B,SAAU,QAAQ;AAC9C,YAAI,CAAC,OAAO,IAAI,SAAS,GAAG;AACxB;AAAA,QACJ;AACA,YAAI,gBAAgB,OAAO,IAAI,SAAS;AACxC,YAAI,eAAe;AACf,0BAAgB;AAAA,YACZ,KAAK,cAAc;AAAA,YACnB;AAAA,YACA,OAAO,cAAc;AAAA,YACrB,QAAQ,cAAc;AAAA,UAC1B,CAAC;AAAA,QACL;AAAA,MACJ;AACA,UAAI,iBAAiB,WAAY;AAC7B,eAAO,cAAc,WAAW,KAAM,cAAc,WAAW,KAAK,cAAc,CAAC,EAAE,QAAQ,KAAK,MAAM;AAAA,MAC5G;AACA,uBAAiB,UAAU,WAAY;AACnC,YAAI,eAAe,KACf,aAAa,WAAW,KAAK,kBAAkB,aAC/C,oBAAoB,QAAQ,QAAQ;AACpC;AAAA,QACJ;AACA,YAAI,eAAe,aAAa;AAChC,YAAI,QAAQ,CAAC,EAAE,MAAM,KAAK,aAAa,iBAAiB,4BAA4B,CAAC;AACrF,YAAI,cAAc,MACb,IAAI,SAAU,MAAM;AAAE,iBAAO,KAAK;AAAA,QAAa,CAAC,EAChD,OAAO,SAAU,MAAM,MAAM,OAAO;AACrC,iBAAO,KAAK,OAAO,KAAK,MAAM,EAAE,EAAE,IAAI,SAAU,GAAG,GAAG;AAAE,mBAAQ;AAAA,cAC5D,MAAM;AAAA,cACN,iBAAiB;AAAA,cACjB,WAAW;AAAA,YACf;AAAA,UAAI,CAAC,CAAC;AAAA,QACV,GAAG;AAAA,UACC;AAAA,YACI,MAAM;AAAA,YACN,iBAAiB;AAAA,YACjB,WAAW;AAAA,UACf;AAAA,QACJ,CAAC,EACI,MAAM,CAAC;AACZ,4BAAoB,UAAU;AAAA,MAClC,GAAG,CAAC,eAAe,aAAa,MAAM,CAAC;AACvC,uBAAiB,UAAU,WAAY;AACnC,YAAI,eAAe,KACf,CAAC,aAAa,OACd,aAAa,WAAW,KAAK,kBAAkB,aAC/C,CAAC,iBAAiB,EAAE,EAAE,WAAsB,SAAmB,CAAC,GAAG;AACnE;AAAA,QACJ;AACA,YAAI,eAAe,aAAa;AAChC,YAAI,eAAe,aAAa,YAAY;AAC5C,0BAAkB,YAAY;AAAA,MAClC,GAAG,CAAC,eAAe,eAAe,aAAa,QAAQ,oBAAoB,OAAO,CAAC;AACnF,uBAAiB,UAAU,WAAY;AACnC,YAAI,eAAe,KAAK,aAAa,OAAO,aAAa,WAAW,KAAK,kBAAkB,WAAW;AAClG,4BAAkB,CAAC,CAAC;AAAA,QACxB;AAAA,MACJ,GAAG,CAAC,eAAe,aAAa,MAAM,CAAC;AACvC,uBAAiB,UAAU,WAAY;AACnC,YAAI,eAAe,WAAW,GAAG;AAC7B;AAAA,QACJ;AACA,YAAI,YAAY,aAAa;AAC7B,YAAI,cAAc,cAAc,aAC5B,CAAC,aACD,aAAa,WAAW,KAAK,kBAAkB,WAAW;AAC1D;AAAA,QACJ;AACA,YAAI,eAAe,UAAU,cAAc,sCAAuC,OAAO,cAAc,YAAY,IAAK,CAAC;AACzH,YAAI,CAAC,cAAc;AACf;AAAA,QACJ;AACA,YAAIC,MAAK,gBAAgB,cAAc,SAAS,GAAG,OAAOA,IAAG,MAAM,MAAMA,IAAG;AAC5E,YAAI,OAAO,MAAM,IAAI,mBAAmB;AACxC,YAAI,MAAM;AACN,eAAK;AAAA,YACD;AAAA,YACA,eAAe,UAAU,sBAAsB,EAAE,SAAS,OAAO,aAAa;AAAA,YAC9E,YAAY,OAAO,aAAa;AAAA,YAChC,SAAS,aAAa;AAAA,UAC1B,CAAC;AACD,cAAI,gBAAgB,SAAS;AACzB,4BAAgB,QAAQ,UAAU,OAAO,gCAAgC;AAAA,UAC7E;AACA,0BAAgB,UAAU;AAC1B,uBAAa,UAAU,IAAI,gCAAgC;AAAA,QAC/D;AAAA,MACJ,GAAG,CAAC,gBAAgB,aAAa,CAAC;AAClC,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,WAAW,oBAAoB;AAC/C,cAAM,UAAU,iBAAiB,0BAA0B;AAC3D,cAAM,UAAU,gBAAgB,yBAAyB;AACzD,eAAO,WAAY;AACf,gBAAM,YAAY,WAAW,oBAAoB;AACjD,gBAAM,YAAY,iBAAiB,0BAA0B;AAC7D,gBAAM,YAAY,gBAAgB,yBAAyB;AAAA,QAC/D;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAQ,iBAAiB,cAAc,OAAO,EAAE,WAAW,0BAA0B,eAAe,sBAAsB,OAAO,SAAS,GAAG,KAAK,aAAa,GAAG,wBAAwB;AAAA,QACtL;AAAA,QACA;AAAA,MACJ,CAAC,CAAC;AAAA,IACN;AAEA,QAAI,eAAe,SAAU,OAAO;AAAE,aAAO,MAAM,QAAQ,uBAAuB,MAAM;AAAA,IAAG;AAC3F,QAAI,uBAAuB,SAAU,aAAa;AAC9C,UAAI,SAAS,YAAY,aAAa,IAAI,OAAO,YAAY,SAAS,GAAG,IAAI,YAAY;AACzF,UAAI,QAAQ,YAAY,YAAY,MAAM;AAC1C,aAAO;AAAA,QACH,SAAS,YAAY;AAAA,QACrB,QAAQ,IAAI,OAAO,aAAa,MAAM,GAAG,KAAK;AAAA,QAC9C,YAAY,YAAY,cAAc;AAAA,MAC1C;AAAA,IACJ;AACA,QAAI,yBAAyB,SAAU,SAAS,WAAW,YAAY;AACnE,UAAI,mBAAmB,QAAQ;AAC3B,eAAO;AAAA,UACH,SAAS,QAAQ;AAAA,UACjB,QAAQ;AAAA,UACR,YAAY,cAAc;AAAA,QAC9B;AAAA,MACJ;AACA,UAAI,OAAO,YAAY,UAAU;AAC7B,eAAO,YAAY,KACb,uBACA,qBAAqB;AAAA,UACnB;AAAA,UACA,WAAW,aAAa;AAAA,UACxB,YAAY,cAAc;AAAA,QAC9B,CAAC;AAAA,MACT;AACA,UAAI,OAAO,cAAc,aAAa;AAClC,gBAAQ,YAAY;AAAA,MACxB;AACA,UAAI,OAAO,eAAe,aAAa;AACnC,gBAAQ,aAAa;AAAA,MACzB;AACA,aAAO,qBAAqB,OAAO;AAAA,IACvC;AAEA,QAAI,cAAc,SAAU,OAAO;AAC/B,UAAI,gBAAgB,iBAAiB,OAAO,MAAM,IAAI,KAAK,CAAC;AAC5D,UAAI,wBAAwB,SAAU,KAAK;AACvC,sBAAc,UAAU;AAAA,MAC5B;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,OAAO,qBAAqB;AAC5C,eAAO,WAAY;AACf,gBAAM,YAAY,OAAO,qBAAqB;AAAA,QAClD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO;AAAA,IACX;AAEA,QAAI,YAAY,SAAU,OAAO;AAC7B,UAAI,iBAAiB,MAAM,IAAI,gBAAgB;AAC/C,UAAI,yBAAyB,iBAAiB,QAAQ,WAAY;AAC9D,YAAI,kBAAkB,eAAe,WAAW,GAAG;AAC/C,cAAI,oBAAoB,uBAAuB,eAAe,CAAC,CAAC;AAChE,iBAAO;AAAA,YACH,WAAW,kBAAkB,OAAO,MAAM,QAAQ,GAAG,MAAM;AAAA,YAC3D,YAAY,kBAAkB;AAAA,UAClC;AAAA,QACJ,OACK;AACD,iBAAO;AAAA,YACH,WAAW;AAAA,YACX,YAAY;AAAA,UAChB;AAAA,QACJ;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,UAAI,gBAAgB,YAAY,KAAK;AACrC,UAAI,KAAK,iBAAiB,SAAS,cAAc,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;AACxF,UAAI,KAAK,iBAAiB,SAAS,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AACtE,UAAI,KAAK,iBAAiB,SAAS,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC;AACnF,UAAI,KAAK,iBAAiB,SAAS,uBAAuB,SAAS,GAAG,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAC5G,UAAI,eAAe,iBAAiB,OAAO,CAAC,CAAC;AAC7C,UAAI,KAAK,iBAAiB,SAAS,uBAAuB,UAAU,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AAC/G,UAAI,0BAA0B,WAAY;AAAE,eAAO;AAAA,MAAM;AACzD,UAAI,mBAAmB,iBAAiB,YAAY,WAAY;AAAE,eAAO,MAAM,IAAI,kBAAkB,KAAK;AAAA,MAAyB,GAAG,CAAC,MAAM,IAAI,kBAAkB,CAAC,CAAC;AACrK,UAAI,kBAAkB,SAAU,WAAW;AACvC,qBAAa,SAAS;AACtB,YAAI,SAAS,SAAS,GAAG;AACrB,oBAAU,UAAU,WAAW,UAAU;AAAA,QAC7C;AAAA,MACJ;AACA,UAAI,mBAAmB,SAAU,WAAW;AACxC,sBAAc,SAAS;AACvB,YAAI,SAAS,SAAS,GAAG;AACrB,oBAAU,UAAU,WAAW,SAAS;AAAA,QAC5C;AAAA,MACJ;AACA,UAAI,cAAc,SAAU,OAAO;AAC/B,YAAI,aAAa,MAAM;AACvB,YAAI,SAAS,WAAW,KAAK,eAAe,GAAG;AAC3C,iBAAO;AAAA,QACX;AACA,YAAI,kBAAkB,UAAU,aAAa,IAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,YAAY,KAAK,CAAC;AAC5F,wBAAgB,eAAe;AAC/B,eAAO,iBAAiB,MAAM,kBAAkB,CAAC,CAAC;AAAA,MACtD;AACA,UAAI,sBAAsB,WAAY;AAAE,eAAO,YAAY,eAAe,CAAC;AAAA,MAAG;AAC9E,UAAI,kBAAkB,WAAY;AAAE,eAAO,YAAY,eAAe,CAAC;AAAA,MAAG;AAC1E,UAAI,eAAe,WAAY;AAC3B,cAAM,OAAO,WAAW,CAAC,oBAAoB,CAAC;AAC9C,mBAAW,EAAE;AACb,wBAAgB,CAAC;AACjB,iBAAS,CAAC,CAAC;AACX,qBAAa,KAAK;AAClB,sBAAc,KAAK;AAAA,MACvB;AACA,UAAI,SAAS,WAAY;AAAE,eAAO,UAAU,UAAU,WAAW,UAAU;AAAA,MAAG;AAC9E,UAAI,aAAa,SAAU,SAAS;AAAE,eAAO,YAAY,YAAY,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;AAAA,MAAG;AAC3F,UAAI,iBAAiB,SAAUC,mBAAkB;AAC7C,cAAM,OAAO,oBAAoBA,iBAAgB;AAAA,MACrD;AACA,UAAI,kBAAkB,WAAY;AAC9B,YAAI,aAAa,cAAc;AAC/B,YAAI,CAAC,YAAY;AACb,iBAAO,QAAQ,QAAQ,CAAC,CAAC;AAAA,QAC7B;AACA,YAAI,WAAW,MAAM,WAAW,QAAQ,EACnC,KAAK,CAAC,EACN,IAAI,SAAU,GAAG,WAAW;AAC7B,iBAAO,KAAK,QAAQ,YAAY,SAAS,EACpC,KAAK,SAAU,MAAM;AACtB,mBAAO,KAAK,eAAe;AAAA,UAC/B,CAAC,EACI,KAAK,SAAU,SAAS;AACzB,gBAAI,cAAc,QAAQ,MAAM,IAAI,SAAU,MAAM;AAAE,qBAAO,KAAK,OAAO;AAAA,YAAI,CAAC,EAAE,KAAK,EAAE;AACvF,mBAAO,QAAQ,QAAQ;AAAA,cACnB;AAAA,cACA;AAAA,YACJ,CAAC;AAAA,UACL,CAAC;AAAA,QACL,CAAC;AACD,eAAO,QAAQ,IAAI,QAAQ,EAAE,KAAK,SAAU,MAAM;AAC9C,eAAK,KAAK,SAAU,GAAG,GAAG;AAAE,mBAAO,EAAE,YAAY,EAAE;AAAA,UAAW,CAAC;AAC/D,iBAAO,QAAQ,QAAQ,KAAK,IAAI,SAAU,MAAM;AAAE,mBAAO,KAAK;AAAA,UAAa,CAAC,CAAC;AAAA,QACjF,CAAC;AAAA,MACL;AACA,UAAI,mBAAmB,SAAU,OAAO;AACpC,YAAI,aAAa,MAAM,IAAI,YAAY;AACvC,YAAI,YAAY;AACZ,qBAAW,MAAM,SAAS;AAAA,QAC9B;AACA,cAAM,OAAO,iBAAiB;AAAA,UAC1B,YAAY,MAAM;AAAA,UAClB,WAAW,MAAM;AAAA,QACrB,CAAC;AACD,eAAO;AAAA,MACX;AACA,UAAI,mBAAmB,SAAU,SAAS;AACtC,YAAI,mBAAmB,QAAQ;AAC3B,iBAAO,QAAQ;AAAA,QACnB;AACA,YAAI,OAAO,YAAY,UAAU;AAC7B,iBAAO;AAAA,QACX;AACA,eAAO,QAAQ;AAAA,MACnB;AACA,UAAI,YAAY,SAAU,cAAc,gBAAgB,iBAAiB;AACrE,YAAI,aAAa,cAAc;AAC/B,YAAI,CAAC,YAAY;AACb,iBAAO,QAAQ,QAAQ,CAAC,CAAC;AAAA,QAC7B;AACA,YAAI,WAAW,WAAW;AAC1B,YAAIC,YAAW,aAAa,IAAI,SAAU,GAAG;AAAE,iBAAO,uBAAuB,GAAG,gBAAgB,eAAe;AAAA,QAAG,CAAC;AACnH,cAAM,OAAO,WAAWA,SAAQ;AAChC,wBAAgB,CAAC;AACjB,iBAAS,CAAC,CAAC;AACX,eAAO,IAAI,QAAQ,SAAU,SAAS,GAAG;AACrC,cAAI,iBAAiB,aAAa,QAAQ,WAAW,IAC/C,gBAAgB,EAAE,KAAK,SAAU,UAAU;AACzC,yBAAa,UAAU;AACvB,mBAAO,QAAQ,QAAQ,QAAQ;AAAA,UACnC,CAAC,IACC,QAAQ,QAAQ,aAAa,OAAO;AAC1C,yBAAe,KAAK,SAAU,UAAU;AACpC,gBAAI,MAAM,CAAC;AACX,qBAAS,QAAQ,SAAU,UAAU,WAAW;AAC5C,kBAAI,iBAAiB,EAAE,EAAE,WAAsB,SAAmB,CAAC,GAAG;AAClE,gBAAAA,UAAS,QAAQ,SAAU,SAAS;AAChC,sBAAI,aAAa;AACjB,sBAAI;AACJ,0BAAQ,UAAU,QAAQ,OAAO,KAAK,QAAQ,OAAO,MAAM;AACvD,wBAAI,KAAK;AAAA,sBACL,SAAS,QAAQ;AAAA,sBACjB;AAAA,sBACA;AAAA,sBACA;AAAA,sBACA,YAAY,QAAQ;AAAA,sBACpB,UAAU,QAAQ,OAAO;AAAA,oBAC7B,CAAC;AACD;AAAA,kBACJ;AAAA,gBACJ,CAAC;AAAA,cACL;AAAA,YACJ,CAAC;AACD,qBAAS,GAAG;AACZ,gBAAI,IAAI,SAAS,GAAG;AAChB,8BAAgB,CAAC;AACjB,+BAAiB,IAAI,CAAC,CAAC;AAAA,YAC3B;AACA,oBAAQ,GAAG;AAAA,UACf,CAAC;AAAA,QACL,CAAC;AAAA,MACL;AACA,uBAAiB,UAAU,WAAY;AACnC,qBAAa,UAAU,CAAC;AAAA,MAC5B,GAAG,CAAC,cAAc,OAAO,CAAC;AAC1B,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,iBAAiB,MAAM;AAAA,QACvB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,SAAS,WAAW,IAAI,KAAK,iBAAiB,SAAS,CAAC,CAAC;AAAA,QAClE;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,SAAS,SAAU,IAAI;AACvB,UAAI,WAAW,GAAG,UAAU,QAAQ,GAAG;AACvC,UAAI,SAAS,UAAU,KAAK;AAC5B,UAAI,KAAK,iBAAiB,SAAS,KAAK,GAAG,mBAAmB,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC;AAC7F,UAAI,wBAAwB,SAAU,GAAG;AAAE,eAAO,kBAAkB,IAAI;AAAA,MAAG;AAC3E,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,OAAO,qBAAqB;AAC5C,eAAO,WAAY;AACf,gBAAM,YAAY,OAAO,qBAAqB;AAAA,QAClD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO,SAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,iBAAmC,CAAC,CAAC;AAAA,IAC1F;AAEA,QAAI,kBAAkB,SAAU,IAAI;AAChC,UAAI,eAAe,GAAG,cAAc,QAAQ,GAAG;AAC/C,UAAI,mBAAmB,iBAAiB,OAAO,KAAK;AACpD,UAAI,mBAAmB,WAAY;AAC/B,yBAAiB,UAAU;AAAA,MAC/B;AACA,UAAI,mBAAmB,WAAY;AAC/B,yBAAiB,UAAU;AAAA,MAC/B;AACA,UAAI,gBAAgB,SAAU,GAAG;AAC7B,YAAI,eAAe,aAAa;AAChC,YAAI,CAAC,cAAc;AACf;AAAA,QACJ;AACA,YAAI,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,KAAK;AACzC;AAAA,QACJ;AACA,YAAI,mBAAmB,KAAK,MAAM,IAAI,EAAE,WAAW,CAAC,EAAE,UAAU,EAAE;AAClE,YAAI,CAAC,kBAAkB;AACnB;AAAA,QACJ;AACA,YAAI,iBAAiB,WAAY,SAAS,iBAAiB,aAAa,SAAS,SAAS,aAAa,GAAI;AACvG,YAAE,eAAe;AACjB,gBAAM,OAAO,uBAAuB,IAAI;AAAA,QAC5C;AAAA,MACJ;AACA,uBAAiB,UAAU,WAAY;AACnC,YAAI,eAAe,aAAa;AAChC,YAAI,CAAC,cAAc;AACf;AAAA,QACJ;AACA,iBAAS,iBAAiB,WAAW,aAAa;AAClD,qBAAa,iBAAiB,cAAc,gBAAgB;AAC5D,qBAAa,iBAAiB,cAAc,gBAAgB;AAC5D,eAAO,WAAY;AACf,mBAAS,oBAAoB,WAAW,aAAa;AACrD,uBAAa,oBAAoB,cAAc,gBAAgB;AAC/D,uBAAa,oBAAoB,cAAc,gBAAgB;AAAA,QACnE;AAAA,MACJ,GAAG,CAAC,aAAa,OAAO,CAAC;AACzB,aAAO,iBAAiB,cAAc,iBAAiB,UAAU,IAAI;AAAA,IACzE;AAEA,QAAI,kBAAkB,EAAE,MAAM,GAAG,KAAK,EAAE;AACxC,QAAI,gBAAgB,SAAU,IAAI;AAC9B,UAAI,QAAQ,GAAG,OAAO,WAAW,GAAG;AACpC,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,YAAY,iBAAiB,WAAW,KAAK,YAAY,EAAE;AAC/D,UAAI,KAAK,iBAAiB,SAAS,KAAK,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AACnF,UAAI,KAAK,iBAAiB,SAAS,KAAK,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AACnF,UAAI,QAAQ,cAAc,KAAK,cAAc;AAC7C,UAAI,KAAK,UAAU,KAAK,GAAG,eAAe,GAAG,cAAc,kBAAkB,GAAG,iBAAiB,mBAAmB,GAAG,kBAAkB,eAAe,GAAG,cAAc,kBAAkB,GAAG,iBAAiB,sBAAsB,GAAG,qBAAqB,UAAU,GAAG,SAAS,YAAY,GAAG,WAAW,kBAAkB,GAAG,iBAAiB,aAAa,GAAG,YAAY,SAAS,GAAG,QAAQ,aAAa,GAAG;AACnZ,UAAI,gBAAgB,SAAU,IAAI;AAC9B,sBAAc,IAAI;AAClB,eAAO,EAAE,KAAK,SAAU,GAAG;AACvB,wBAAc,KAAK;AACnB,wBAAc,IAAI;AAClB,gBAAM,GAAG;AAAA,QACb,CAAC;AAAA,MACL;AACA,UAAI,kBAAkB,SAAU,GAAG;AAC/B,YAAI,EAAE,QAAQ,WAAW,SAAS;AAC9B,uBAAa,gBAAgB,IAAI,cAAc;AAAA,QACnD;AAAA,MACJ;AACA,UAAI,oBAAoB,SAAU,GAAG;AACjC,sBAAc,KAAK;AACnB,wBAAgB,EAAE,OAAO,OAAO;AAAA,MACpC;AACA,UAAI,qBAAqB,SAAU,GAAG;AAClC,sBAAc,KAAK;AACnB,yBAAiB,EAAE,OAAO,OAAO;AAAA,MACrC;AACA,UAAI,UAAU,WAAY;AACtB,iBAAS;AACT,qBAAa;AAAA,MACjB;AACA,UAAI,kBAAkB,SAAU,OAAO;AACnC,sBAAc,KAAK;AACnB,mBAAW,KAAK;AAAA,MACpB;AACA,uBAAiB,UAAU,WAAY;AACnC,YAAI,iBAAiB,MAAM,IAAI,gBAAgB;AAC/C,YAAI,kBAAkB,eAAe,WAAW,KAAK,SAAS;AAC1D,wBAAc,WAAY;AACtB,kBAAM,OAAO,kBAAkB,CAAC,CAAC;AAAA,UACrC,CAAC;AAAA,QACL;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,UAAI,cAAc,QAAQ,KAAK,SAAS,KAAK,OAAO,gBAAgB;AACpE,UAAI,qBAAqB,QAAQ,KAAK,SAAS,KAAK,OAAO,gBAAgB;AAC3E,UAAI,iBAAiB,QAAQ,KAAK,SAAS,KAAK,OAAO,YAAY;AACnE,UAAI,mBAAmB,QAAQ,KAAK,SAAS,KAAK,OAAO,QAAQ;AACjE,aAAQ,iBAAiB;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,sBAAsB;AAAA,QAC7E,iBAAiB;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,oCAAoC;AAAA,UACnF,iBAAiB,cAAc,KAAK,SAAS,EAAE,WAAW,aAAa,WAAW,MAAM,aAAa,aAAa,MAAM,QAAQ,OAAO,SAAS,UAAU,iBAAiB,WAAW,gBAAgB,CAAC;AAAA,UACvM,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,KAAK,WAAW;AAAA,cAC3D,+BAA+B;AAAA,cAC/B,oCAAoC,CAAC;AAAA,cACrC,oCAAoC;AAAA,YACxC,CAAC,EAAE;AAAA,YACH,cAAc,iBAAiB,cAAc,KAAK,SAAS,EAAE,QAAQ,6BAA6B,MAAM,OAAO,CAAC;AAAA,YAChH,CAAC,cAAe,iBAAiB;AAAA,cAAc;AAAA,cAAQ,EAAE,eAAe,8BAA8B;AAAA,cAClG;AAAA,cACA;AAAA,cACA;AAAA,YAAe;AAAA,UAAE;AAAA,QAAC;AAAA,QAC9B,iBAAiB;AAAA,UAAc;AAAA,UAAS,EAAE,WAAW,4BAA4B;AAAA,UAC7E,iBAAiB,cAAc,SAAS,EAAE,WAAW,sCAAsC,eAAe,8BAA8B,SAAS,WAAW,MAAM,YAAY,UAAU,kBAAkB,CAAC;AAAA,UAC3M;AAAA,UACA,QAAQ,KAAK,SAAS,KAAK,OAAO,YAAY;AAAA,QAAY;AAAA,QAC9D,iBAAiB;AAAA,UAAc;AAAA,UAAS,EAAE,WAAW,4BAA4B;AAAA,UAC7E,iBAAiB,cAAc,SAAS,EAAE,WAAW,sCAAsC,SAAS,YAAY,eAAe,+BAA+B,MAAM,YAAY,UAAU,mBAAmB,CAAC;AAAA,UAC9M;AAAA,UACA,QAAQ,KAAK,SAAS,KAAK,OAAO,aAAa;AAAA,QAAa;AAAA,QAChE,iBAAiB;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,6BAA6B;AAAA,UAC5E,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,kCAAkC;AAAA,YACjF,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,yBAAyB,UAAU,QAAQ,KAAK,SAAS,cAAc,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,cAAc,KAAK;AAAA,cAAe,EAAE,WAAW,oBAAoB,YAAY,gBAAgB,GAAG,SAAS,oBAAoB;AAAA,cACtT,iBAAiB,cAAc,cAAc,IAAI;AAAA,YAAC,GAAG,SAAS,WAAY;AAAE,qBAAO;AAAA,YAAoB,GAAG,QAAQ,gBAAgB,CAAC;AAAA,UAAC;AAAA,UAChJ,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,kCAAkC;AAAA,YACjF,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,qBAAqB,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,cAAc,KAAK;AAAA,cAAe,EAAE,WAAW,gBAAgB,YAAY,eAAe,kBAAkB,GAAG,SAAS,gBAAgB;AAAA,cACvR,iBAAiB,cAAc,UAAU,IAAI;AAAA,YAAC,GAAG,SAAS,WAAY;AAAE,qBAAO;AAAA,YAAgB,GAAG,QAAQ,gBAAgB,CAAC;AAAA,UAAC;AAAA,UACxI,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,KAAK,WAAW;AAAA,cAC3D,qCAAqC;AAAA,cACrC,0CAA0C,CAAC;AAAA,cAC3C,0CAA0C;AAAA,YAC9C,CAAC,EAAE;AAAA,YACH,iBAAiB,cAAc,KAAK,QAAQ,EAAE,SAAS,QAAQ,GAAG,gBAAgB;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IACrG;AAEA,QAAI,6BAA6B,SAAU,IAAI;AAC3C,UAAI,WAAW,GAAG,UAAU,UAAU,GAAG;AACzC,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,SAAS,KAAK,OAAO,SAAS;AACvD,UAAI,OAAO,iBAAiB,cAAc,YAAY,IAAI;AAC1D,aAAO,SAAS,EAAE,MAAY,OAAc,QAAiB,CAAC;AAAA,IAClE;AAEA,QAAI,iBAAiB,EAAE,MAAM,GAAG,KAAK,EAAE;AACvC,QAAI,0BAA0B,SAAU,IAAI;AACxC,UAAI,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG,OAAO,UAAU,GAAG;AACzE,UAAI,mBAAmB,kBAAmB,KAAK,MAAM,IAAI,WAAW,WAAY;AAChF,UAAI,yBAAyB,SAAU,qBAAqB;AACxD,YAAI,qBAAqB;AACrB,kBAAQ;AAAA,QACZ;AAAA,MACJ;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,uBAAuB,sBAAsB;AAC7D,eAAO,WAAY;AACf,gBAAM,YAAY,uBAAuB,sBAAsB;AAAA,QACnE;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAQ,iBAAiB,cAAc,4BAA4B,EAAE,QAAiB,GAAG,SAAU,GAAG;AAAE,eAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,kBAAkB,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB,cAAc,KAAK,eAAe,EAAE,kBAAoC,WAAW,EAAE,OAAO,QAAQ,0BAA0B,QAAiB,GAAG,EAAE,IAAI,GAAG,SAAS,WAAY;AAAE,iBAAO,EAAE;AAAA,QAAO,GAAG,QAAQ,eAAe,CAAC;AAAA,MAAI,CAAC;AAAA,IAC1e;AAEA,QAAI,gBAAgB,EAAE,MAAM,GAAG,KAAK,EAAE;AACtC,QAAI,oBAAoB,SAAU,IAAI;AAClC,UAAI,WAAW,GAAG,UAAU,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG;AAC7E,UAAI,YAAY,iBAAiB,WAAW,KAAK,YAAY,EAAE;AAC/D,UAAI,iBAAiB,cAAc,KAAK,cAAc,cAAc,KAAK,SAAS,cAAc,KAAK,SAAS;AAC9G,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,yBAAyB,SAAS,EAAE,iBAAkC,MAAa,GAAG,KAAK,CAAC;AAAA,MAAI;AAChL,UAAI,SAAS,YAAY;AACzB,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,UAAU,YAAY,OAAO,UAAU,gBAAgB,QAAQ,SAAU,QAAQ;AACpJ,eAAO,OAAO;AAAA,UACV,SAAS;AAAA,QACb,CAAC;AAAA,MACL,GAAG,SAAS,SAAU,QAAQ;AAAE,eAAO,iBAAiB,cAAc,eAAe,EAAE,OAAc,UAAU,OAAO,CAAC;AAAA,MAAG,GAAG,QAAQ,eAAe,qBAAqB,OAAO,eAAe,KAAK,CAAC;AAAA,IAC7M;AAEA,QAAI,oBAAoB,SAAU,SAAS;AACvC,aAAO,MAAM,QAAQ,OAAO,IAAI,QAAQ,IAAI,SAAU,GAAG;AAAE,eAAO,uBAAuB,CAAC;AAAA,MAAG,CAAC,IAAI,CAAC,uBAAuB,OAAO,CAAC;AAAA,IACtI;AACA,QAAI,eAAe,SAAU,OAAO;AAChC,UAAI,oBAAoB,iBAAiB,QAAQ,WAAY;AAAE,eAAO,OAAO,OAAO,CAAC,GAAG,EAAE,iBAAiB,MAAM,oBAAoB,WAAY;AAAA,QAAE,EAAE,GAAG,KAAK;AAAA,MAAG,GAAG,CAAC,CAAC;AACrK,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAC7C,eAAO,KAAK,YAAY;AAAA,UACpB,gBAAgB,SAAS,MAAM,UAAW,MAAM,QAAQ,MAAM,OAAO,IAAI,MAAM,UAAU,CAAC,MAAM,OAAO,IAAK,CAAC;AAAA,UAC7G,SAAS,SAAS,MAAM,UAAU,kBAAkB,MAAM,OAAO,IAAI,CAAC,oBAAoB;AAAA,UAC1F,eAAe;AAAA,YACX,YAAY;AAAA,YACZ,WAAW;AAAA,UACf;AAAA,UACA,cAAc,oBAAI,IAAI;AAAA,QAC1B,CAAC;AAAA,MACL,GAAG,CAAC,CAAC;AACL,UAAI,KAAK,UAAU,KAAK,GAAG,eAAe,GAAG,cAAc,cAAc,GAAG,aAAa,kBAAkB,GAAG,iBAAiB,sBAAsB,GAAG,qBAAqB,YAAY,GAAG,WAAW,cAAc,GAAG,aAAa,iBAAiB,GAAG;AACzP,UAAI,kBAAkB,SAAUC,QAAO;AAAE,eAAO,iBAAiB,cAAc,QAAQ,SAAS,CAAC,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AAC/H,UAAIC,8BAA6B,SAAUD,QAAO;AAAE,eAAQ,iBAAiB,cAAc,mBAAmB,SAAS,EAAE,iBAAiB,kBAAkB,gBAAgB,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAI;AAC3M,UAAI,mCAAmC,WAAY;AAAE,eAAQ,iBAAiB,cAAcC,6BAA4B,MAAM,SAAUD,QAAO;AAAE,iBAAQ,iBAAiB,cAAc,yBAAyB,SAAS,EAAE,iBAAiB,kBAAkB,iBAAiB,MAAa,GAAGA,MAAK,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAC/S,UAAI,eAAe,SAAU,mBAAmB;AAC5C,YAAI,cAAc,kBAAkB;AACpC,YAAI,YAAY,SAAS;AACrB,sBAAY,QAAQ,WAAY,iBAAiB;AAAA,YAAc,iBAAiB;AAAA,YAAU;AAAA,YACtF,kBAAkB,mBAAoB,iBAAiB,cAAc,iBAAiB,EAAE,cAAc,kBAAkB,cAAc,MAAa,CAAC;AAAA,YACpJ,YAAY,QAAQ;AAAA,UAAQ;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AACA,UAAI,kBAAkB,SAAU,aAAa;AAAE,eAAQ,iBAAiB,cAAc,YAAY,EAAE,KAAK,YAAY,WAAW,UAAU,YAAY,IAAI,UAAU,WAAW,YAAY,WAAW,kBAAkB,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM,kBAAkB,OAAc,oBAAoB,kBAAkB,mBAAmB,CAAC;AAAA,MAAI;AAC1W,aAAO;AAAA,QACH,SAAS,SAAU,iBAAiB;AAChC,cAAI,iBAAiB,SAAS,MAAM,UAAW,MAAM,QAAQ,MAAM,OAAO,IAAI,MAAM,UAAU,CAAC,MAAM,OAAO,IAAK,CAAC;AAClH,cAAI,UAAU,SAAS,MAAM,UAAU,kBAAkB,MAAM,OAAO,IAAI,CAAC,oBAAoB;AAC/F,gBAAM,OAAO,kBAAkB,cAAc;AAC7C,gBAAM,OAAO,qBAAqB,gBAAgB,iBAAiB;AACnE,gBAAM,OAAO,cAAc,gBAAgB,UAAU;AACrD,gBAAM,OAAO,WAAW,OAAO;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW,SAAUA,QAAO;AACxB,cAAI,eAAe,MAAM,IAAI,cAAc;AAC3C,cAAI,cAAc;AACd,yBAAa,MAAM;AAAA,UACvB;AAAA,QACJ;AAAA,QACA,gBAAgB,SAAUA,QAAO;AAC7B,gBAAM,OAAO,OAAOA,OAAM,GAAG;AAAA,QACjC;AAAA,QACA,mBAAmB,SAAUA,QAAO;AAChC,cAAI,eAAe,MAAM,IAAI,cAAc;AAC3C,cAAI,cAAc;AACd,2BAAe,aAAa,IAAIA,OAAM,WAAWA,MAAK;AACtD,kBAAM,OAAO,gBAAgB,YAAY;AAAA,UAC7C;AAAA,QACJ;AAAA,QACA,QAAQ;AAAA,QACR,mBAAmBC;AAAA,QACnB,yBAAyB;AAAA,QACzB,iBAAiB,WAAY;AACzB,uBAAa;AAAA,QACjB;AAAA,QACA,WAAW,SAAU,SAAS;AAC1B,cAAI,WAAW,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAC1D,sBAAY,QAAQ;AACpB,iBAAO,UAAU,QAAQ;AAAA,QAC7B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAEA,YAAQ,WAAW;AACnB,YAAQ,eAAe;AACvB,YAAQ,aAAa;AACrB,YAAQ,eAAe;AAAA;AAAA;;;AC/0BvB,IAAAC,gBAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,WAAW,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QACtF,iBAAiB,cAAc,QAAQ,EAAE,GAAG,yPAAyP,CAAC;AAAA,QACtS,iBAAiB,cAAc,QAAQ,EAAE,GAAG,+CAA+C,CAAC;AAAA,MAAC;AAAA,IAAI;AAErG,QAAI,YAAY,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QACvF,iBAAiB,cAAc,QAAQ,EAAE,GAAG,4PAA4P,CAAC;AAAA,QACzS,iBAAiB,cAAc,QAAQ,EAAE,GAAG,sCAAsC,CAAC;AAAA,MAAC;AAAA,IAAI;AAiB5F,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,iBAAiB,EAAE,MAAM,GAAG,KAAK,EAAE;AACvC,QAAI,oBAAoB,SAAU,IAAI;AAClC,UAAI,UAAU,GAAG;AACjB,UAAI,QAAQ,iBAAiB,WAAW,KAAK,YAAY;AACzD,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,cAAc,MAAM,iBAAiB;AACzC,UAAI,QAAQ,QAAQ,KAAK,QACnB,cACI,KAAK,MAAM,mBACX,KAAK,MAAM,kBACf,cACI,8BACA;AACV,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,gBAAgB,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB,cAAc,KAAK,eAAe,EAAE,WAAW,OAAO,QAAQ,wBAAwB,QAAiB,GAAG,cAAc,iBAAiB,cAAc,WAAW,IAAI,IAAI,iBAAiB,cAAc,UAAU,IAAI,CAAC,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAO,GAAG,QAAQ,eAAe,CAAC;AAAA,IACzb;AAEA,QAAI,cAAc,SAAU,IAAI;AAC5B,UAAI,WAAW,GAAG;AAClB,UAAI,QAAQ,iBAAiB,WAAW,KAAK,YAAY;AACzD,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAO,iBAAiB,cAAc,mBAAmB,EAAE,SAAS,MAAM,QAAQ,CAAC;AAAA,MAAG;AAC/H,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV,SAAS,WAAY;AAAE,iBAAO,MAAM,gBAAgB,MAAM,iBAAiB,SAAS,UAAU,MAAM;AAAA,QAAG;AAAA,MAC3G,CAAC;AAAA,IACL;AAEA,QAAI,sBAAsB,SAAU,IAAI;AACpC,UAAI,UAAU,GAAG;AACjB,UAAI,QAAQ,iBAAiB,WAAW,KAAK,YAAY;AACzD,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,cAAc,MAAM,iBAAiB;AACzC,UAAI,QAAQ,QAAQ,KAAK,QACnB,cACI,KAAK,MAAM,mBACX,KAAK,MAAM,kBACf,cACI,8BACA;AACV,aAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,MAAM,cAAc,iBAAiB,cAAc,WAAW,IAAI,IAAI,iBAAiB,cAAc,UAAU,IAAI,GAAG,QAAQ,sBAAsB,QAAiB,GAAG,KAAK;AAAA,IACzO;AAEA,QAAI,cAAc,WAAY;AAC1B,UAAI,uBAAuB,SAAU,OAAO;AAAE,eAAO,iBAAiB,cAAc,aAAa,SAAS,CAAC,GAAG,KAAK,CAAC;AAAA,MAAG;AACvH,UAAI,6BAA6B,WAAY;AAAE,eAAQ,iBAAiB,cAAc,sBAAsB,MAAM,SAAU,OAAO;AAAE,iBAAO,iBAAiB,cAAc,mBAAmB,SAAS,CAAC,GAAG,KAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MAAI;AACzN,UAAI,+BAA+B,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,sBAAsB,MAAM,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,qBAAqB,EAAE,SAAS,WAAY;AACnN,cAAE,QAAQ;AACV,kBAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,aAAO;AAAA,QACH,aAAa;AAAA,QACb,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACzB;AAAA,IACJ;AAEA,YAAQ,WAAW;AACnB,YAAQ,YAAY;AACpB,YAAQ,cAAc;AAAA;AAAA;;;ACnHtB,IAAAC,gBAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AAEZ,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,aAAa,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,iBAAiB,MAAM,MAAM,GAAG;AAAA,QAC/G,iBAAiB,cAAc,QAAQ,EAAE,GAAG,0MAA0M,CAAC;AAAA,MAAC;AAAA,IAAI;AAEhQ,QAAI,cAAc,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,iBAAiB,MAAM,MAAM,GAAG;AAAA,QAChH,iBAAiB,cAAc,QAAQ,EAAE,GAAG,8KAA8K,CAAC;AAAA,MAAC;AAAA,IAAI;AAiBpO,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,UAAU,SAAU,OAAO;AAC3B,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,OAAO,KAAK,CAAC,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC;AAC3F,UAAI,qBAAqB,SAAU,cAAc;AAC7C,iBAAS,YAAY;AAAA,MACzB;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,SAAS,kBAAkB;AAC3C,eAAO,WAAY;AACf,gBAAM,YAAY,SAAS,kBAAkB;AAAA,QACjD;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO,EAAE,MAAa;AAAA,IAC1B;AAEA,QAAI,eAAe,SAAU,IAAI;AAC7B,UAAI,WAAW,GAAG,UAAU,QAAQ,GAAG;AACvC,UAAI,QAAQ,QAAQ,KAAK,EAAE;AAC3B,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAO,iBAAiB,cAAc,iBAAiB,UAAU,MAAM,GAAG,OAAO,KAAK,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,CAAC;AAAA,MAAG;AAChK,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO,EAAE,MAAa,CAAC;AAAA,IAClC;AAEA,QAAI,sBAAsB;AAAA,MACtB,SAAS;AAAA,IACb;AACA,QAAI,aAAa;AACjB,QAAI,mBAAmB,WAAY;AAC/B,aAAO,eAAe,aAAa,SAAS,gBAAgB,8BAA8B,KAAK;AAAA,IACnG;AACA,QAAI,YAAY,SAAU,IAAI;AAC1B,UAAI,oBAAoB,GAAG,mBAAmB,QAAQ,GAAG;AACzD,UAAI,SAAS,KAAK,oBAAoB,SAAU,OAAO;AACnD,YAAI,OAAO,MAAM,IAAI,MAAM;AAC3B,YAAI,MAAM;AACN,eAAK,KAAK;AAAA,QACd;AAAA,MACJ,GAAG,EAAE;AACL,UAAI,mBAAmB,SAAU,GAAG;AAChC,YAAI,CAAC,EAAE,SAAS;AACZ;AAAA,QACJ;AACA,UAAE,eAAe;AACjB,YAAI,SAAS,EAAE;AACf,YAAI,OAAO,OAAO,sBAAsB;AACxC,YAAI,YAAY,IAAI,EAAE,SAAS;AAC/B,YAAI,UAAU,EAAE,UAAU,KAAK;AAC/B,YAAI,UAAU,EAAE,UAAU,KAAK;AAC/B,YAAI,eAAe,MAAM,IAAI,OAAO;AACpC,YAAI,SAAS,iBAAiB,EACzB,gBAAgB,EAChB,UAAU,SAAS,OAAO,EAC1B,MAAM,SAAS,EACf,UAAU,CAAC,SAAS,CAAC,OAAO,EAC5B,MAAM,YAAY;AACvB,eAAO,OAAO,CAAC;AAAA,MACnB;AACA,WAAK,0BAA0B,WAAY;AACvC,YAAI,iBAAiB,kBAAkB;AACvC,YAAI,CAAC,gBAAgB;AACjB;AAAA,QACJ;AACA,uBAAe,iBAAiB,SAAS,kBAAkB,mBAAmB;AAC9E,eAAO,WAAY;AACf,yBAAe,oBAAoB,SAAS,gBAAgB;AAAA,QAChE;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,aAAO,iBAAiB,cAAc,iBAAiB,UAAU,IAAI;AAAA,IACzE;AAEA,QAAI,SAAS;AAAA,MACT;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAG;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAC5G;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,MAAK;AAAA,IACvC;AACA,QAAI,WAAW,SAAU,cAAc;AACnC,UAAI,QAAQ,OAAO,KAAK,SAAU,MAAM;AAAE,eAAO,OAAO;AAAA,MAAc,CAAC;AACvE,aAAO,SAAS;AAAA,IACpB;AACA,QAAI,WAAW,SAAU,cAAc;AACnC,UAAI,QAAQ,OAAO,UAAU,SAAU,MAAM;AAAE,eAAO,QAAQ;AAAA,MAAc,CAAC;AAC7E,aAAO,UAAU,MAAM,UAAU,IAAI,eAAe,OAAO,QAAQ,CAAC;AAAA,IACxE;AAEA,QAAI,kBAAkB,SAAU,IAAI;AAChC,UAAI,eAAe,GAAG,cAAc,QAAQ,GAAG;AAC/C,UAAI,iBAAiB,SAAU,GAAG;AAC9B,YAAI,EAAE,YAAY,EAAE,QAAQ;AACxB;AAAA,QACJ;AACA,YAAI,mBAAmB,KAAK,MAAM,IAAI,EAAE,UAAU,EAAE;AACpD,YAAI,CAAC,kBAAkB;AACnB;AAAA,QACJ;AACA,YAAI,eAAe,aAAa;AAChC,YAAI,CAAC,gBAAgB,CAAC,SAAS,iBAAiB,CAAC,aAAa,SAAS,SAAS,aAAa,GAAG;AAC5F;AAAA,QACJ;AACA,YAAI,OAAO,MAAM,IAAI,MAAM;AAC3B,YAAI,CAAC,MAAM;AACP;AAAA,QACJ;AACA,YAAI,QAAQ,MAAM,IAAI,OAAO,KAAK;AAClC,YAAI,WAAW;AACf,gBAAQ,EAAE,KAAK;AAAA,UACX,KAAK;AACD,uBAAW,SAAS,KAAK;AACzB;AAAA,UACJ,KAAK;AACD,uBAAW,SAAS,KAAK;AACzB;AAAA,UACJ,KAAK;AACD,uBAAW;AACX;AAAA,UACJ;AACI,uBAAW;AACX;AAAA,QACR;AACA,YAAI,aAAa,OAAO;AACpB,YAAE,eAAe;AACjB,eAAK,QAAQ;AAAA,QACjB;AAAA,MACJ;AACA,uBAAiB,UAAU,WAAY;AACnC,YAAI,eAAe,aAAa;AAChC,YAAI,CAAC,cAAc;AACf;AAAA,QACJ;AACA,iBAAS,iBAAiB,WAAW,cAAc;AACnD,eAAO,WAAY;AACf,mBAAS,oBAAoB,WAAW,cAAc;AAAA,QAC1D;AAAA,MACJ,GAAG,CAAC,aAAa,OAAO,CAAC;AACzB,aAAO,iBAAiB,cAAc,iBAAiB,UAAU,IAAI;AAAA,IACzE;AAEA,QAAI,iBAAiB,CAAC,KAAK,MAAM,GAAG,MAAM,KAAK,GAAG,GAAG,CAAC;AACtD,QAAI,gBAAgB,EAAE,MAAM,GAAG,KAAK,EAAE;AACtC,QAAI,cAAc,SAAU,IAAI;AAC5B,UAAI,KAAK,GAAG,QAAQ,SAAS,OAAO,SAAS,iBAAiB,IAAI,QAAQ,GAAG,OAAO,SAAS,GAAG;AAChG,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,YAAY,iBAAiB,WAAW,KAAK,YAAY,EAAE;AAC/D,UAAI,QAAQ,cAAc,KAAK,cAAc;AAC7C,UAAI,sBAAsB,SAAU,OAAO;AACvC,gBAAQ,OAAO;AAAA,UACX,KAAK,KAAK,iBAAiB;AACvB,mBAAO,QAAQ,KAAK,OAAO,KAAK,KAAK,aAAa;AAAA,UACtD,KAAK,KAAK,iBAAiB;AACvB,mBAAO,QAAQ,KAAK,OAAO,KAAK,KAAK,UAAU;AAAA,UACnD,KAAK,KAAK,iBAAiB;AACvB,mBAAO,QAAQ,KAAK,OAAO,KAAK,KAAK,YAAY;AAAA,QACzD;AAAA,MACJ;AACA,UAAI,oBAAoB,QAAQ,KAAK,OAAO,KAAK,KAAK,eAAe;AACrE,UAAI,eAAe,SAAU,QAAQ;AACjC,YAAI,QAAQ,WAAY;AACpB,iBAAO;AAAA,QACX;AACA,eAAQ,iBAAiB;AAAA,UAAc,KAAK;AAAA,UAAe,EAAE,WAAW,mBAAmB,QAAQ,wBAAwB,SAAS,MAAM;AAAA,UACtI,iBAAiB;AAAA,YAAc;AAAA,YAAQ,EAAE,WAAW,2BAA2B;AAAA,YAC3E,iBAAiB;AAAA,cAAc;AAAA,cAAQ,EAAE,eAAe,8BAA8B,WAAW,KAAK,WAAW;AAAA,gBACzG,kCAAkC;AAAA,gBAClC,uCAAuC,CAAC;AAAA,gBACxC,uCAAuC;AAAA,cAC3C,CAAC,EAAE;AAAA,cACH,KAAK,MAAM,QAAQ,GAAG;AAAA,cACtB;AAAA,YAAG;AAAA,YACP,iBAAiB,cAAc,QAAQ,EAAE,WAAW,iCAAiC,CAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MACpG;AACA,UAAI,gBAAgB,SAAU,QAAQ;AAAE,eAAQ,iBAAiB;AAAA,UAAc,KAAK;AAAA,UAAM;AAAA,UACtF,OAAO,KAAK,KAAK,gBAAgB,EAAE,IAAI,SAAU,GAAG;AAChD,gBAAI,QAAQ;AACZ,gBAAI,gBAAgB,WAAY;AAC5B,qBAAO;AACP,qBAAO,KAAK;AAAA,YAChB;AACA,mBAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,KAAK,OAAO,SAAS,cAAc,GAAG,oBAAoB,KAAK,CAAC;AAAA,UAC5H,CAAC;AAAA,UACD,iBAAiB,cAAc,KAAK,aAAa,IAAI;AAAA,UACrD,OAAO,IAAI,SAAU,OAAO;AACxB,gBAAI,gBAAgB,WAAY;AAC5B,qBAAO;AACP,qBAAO,KAAK;AAAA,YAChB;AACA,mBAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,KAAK,OAAO,SAAS,cAAc,GAAG,GAAG,OAAO,KAAK,MAAM,QAAQ,GAAG,GAAG,GAAG,CAAC;AAAA,UACzI,CAAC;AAAA,QAAC;AAAA,MAAI;AACV,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,QAAQ,cAAc,QAAQ,UAAU,KAAK,SAAS,cAAc,QAAQ,cAAc,SAAS,eAAe,QAAQ,eAAe,qBAAqB,MAAM,eAAe,KAAK,CAAC;AAAA,IACxQ;AAEA,QAAI,OAAO,SAAU,IAAI;AACrB,UAAI,WAAW,GAAG,UAAU,SAAS,GAAG,QAAQ,QAAQ,GAAG;AAC3D,UAAI,QAAQ,QAAQ,KAAK,EAAE;AAC3B,UAAI,SAAS,SAAU,UAAU;AAC7B,YAAI,OAAO,MAAM,IAAI,MAAM;AAC3B,YAAI,MAAM;AACN,eAAK,QAAQ;AAAA,QACjB;AAAA,MACJ;AACA,UAAI,kBAAkB,SAAU,OAAO;AAAE,eAAQ,iBAAiB,cAAc,aAAa,EAAE,QAAgB,OAAO,MAAM,OAAO,QAAQ,MAAM,OAAO,CAAC;AAAA,MAAI;AAC7J,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,MACZ,CAAC;AAAA,IACL;AAEA,QAAI,mBAAmB,EAAE,MAAM,GAAG,KAAK,EAAE;AACzC,QAAI,eAAe,SAAU,IAAI;AAC7B,UAAI,kBAAkB,GAAG,iBAAiB,UAAU,GAAG;AACvD,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,OAAO,KAAK,KAAK,SAAS;AACnD,UAAI,mBAAmB,kBAAmB,KAAK,MAAM,IAAI,WAAW,WAAY;AAChF,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,WAAW,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAe,EAAE,kBAAoC,WAAW,OAAO,QAAQ,mBAAmB,QAAiB;AAAA,QACnR,iBAAiB,cAAc,YAAY,IAAI;AAAA,MAAC,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAO,GAAG,QAAQ,iBAAiB,CAAC;AAAA,IACjI;AAEA,QAAI,SAAS,SAAU,IAAI;AACvB,UAAI,WAAW,GAAG,UAAU,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG;AAC7E,UAAI,QAAQ,QAAQ,KAAK,EAAE;AAC3B,UAAI,SAAS,WAAY;AACrB,YAAI,OAAO,MAAM,IAAI,MAAM;AAC3B,YAAI,MAAM;AACN,cAAI,WAAW,SAAS,KAAK;AAC7B,eAAK,QAAQ;AAAA,QACjB;AAAA,MACJ;AACA,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV;AAAA,QACA,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AAEA,QAAI,iBAAiB,SAAU,IAAI;AAC/B,UAAI,UAAU,GAAG;AACjB,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,OAAO,KAAK,KAAK,SAAS;AACnD,aAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,MAAM,iBAAiB,cAAc,YAAY,IAAI,GAAG,QAAQ,iBAAiB,QAAiB,GAAG,KAAK;AAAA,IACtK;AAEA,QAAI,iBAAiB,EAAE,MAAM,GAAG,KAAK,EAAE;AACvC,QAAI,gBAAgB,SAAU,IAAI;AAC9B,UAAI,kBAAkB,GAAG,iBAAiB,UAAU,GAAG;AACvD,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,OAAO,KAAK,KAAK,UAAU;AACpD,UAAI,mBAAmB,kBAAmB,KAAK,MAAM,IAAI,WAAW,WAAY;AAChF,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,YAAY,UAAU,KAAK,SAAS,cAAc,QAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAe,EAAE,kBAAoC,WAAW,OAAO,QAAQ,oBAAoB,QAAiB;AAAA,QACrR,iBAAiB,cAAc,aAAa,IAAI;AAAA,MAAC,GAAG,SAAS,WAAY;AAAE,eAAO;AAAA,MAAO,GAAG,QAAQ,eAAe,CAAC;AAAA,IAChI;AAEA,QAAI,UAAU,SAAU,IAAI;AACxB,UAAI,WAAW,GAAG,UAAU,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG;AAC7E,UAAI,QAAQ,QAAQ,KAAK,EAAE;AAC3B,UAAI,SAAS,WAAY;AACrB,YAAI,OAAO,MAAM,IAAI,MAAM;AAC3B,YAAI,MAAM;AACN,cAAI,WAAW,SAAS,KAAK;AAC7B,eAAK,QAAQ;AAAA,QACjB;AAAA,MACJ;AACA,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO;AAAA,QACV;AAAA,QACA,SAAS;AAAA,MACb,CAAC;AAAA,IACL;AAEA,QAAI,kBAAkB,SAAU,IAAI;AAChC,UAAI,UAAU,GAAG;AACjB,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,QAAQ,QAAQ,KAAK,OAAO,KAAK,KAAK,UAAU;AACpD,aAAQ,iBAAiB,cAAc,KAAK,UAAU,EAAE,MAAM,iBAAiB,cAAc,aAAa,IAAI,GAAG,QAAQ,kBAAkB,QAAiB,GAAG,KAAK;AAAA,IACxK;AAEA,QAAI,aAAa,SAAU,OAAO;AAC9B,UAAI,kBAAkB,iBAAiB,QAAQ,WAAY;AAAE,eAAO,OAAO,OAAO,CAAC,GAAG,EAAE,iBAAiB,KAAK,GAAG,KAAK;AAAA,MAAG,GAAG,CAAC,CAAC;AAC9H,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAAE,eAAO,KAAK,YAAY,CAAC,CAAC;AAAA,MAAG,GAAG,CAAC,CAAC;AACrF,UAAI,wBAAwB,SAAUC,QAAO;AAAE,eAAO,iBAAiB,cAAc,cAAc,SAAS,CAAC,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AAC3I,UAAI,kBAAkB,SAAUA,QAAO;AAAE,eAAQ,iBAAiB,cAAc,QAAQ,SAAS,EAAE,iBAAiB,gBAAgB,gBAAgB,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAI;AACnL,UAAI,wBAAwB,WAAY;AAAE,eAAO,iBAAiB,cAAc,iBAAiB,MAAM,SAAUA,QAAO;AAAE,iBAAO,iBAAiB,cAAc,cAAc,SAAS,CAAC,GAAGA,MAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AACxM,UAAI,0BAA0B,SAAUA,QAAO;AAAE,eAAQ,iBAAiB,cAAc,iBAAiB,MAAM,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,gBAAgB,EAAE,SAAS,WAAY;AACpM,cAAE,QAAQ;AACV,YAAAA,OAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,mBAAmB,SAAUA,QAAO;AAAE,eAAQ,iBAAiB,cAAc,SAAS,SAAS,EAAE,iBAAiB,gBAAgB,gBAAgB,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAI;AACrL,UAAI,yBAAyB,WAAY;AAAE,eAAO,iBAAiB,cAAc,kBAAkB,MAAM,SAAUA,QAAO;AAAE,iBAAO,iBAAiB,cAAc,eAAe,SAAS,CAAC,GAAGA,MAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MAAG;AAC3M,UAAI,2BAA2B,SAAUA,QAAO;AAAE,eAAQ,iBAAiB,cAAc,kBAAkB,MAAM,SAAU,GAAG;AAAE,iBAAQ,iBAAiB,cAAc,iBAAiB,EAAE,SAAS,WAAY;AACvM,cAAE,QAAQ;AACV,YAAAA,OAAM,QAAQ;AAAA,UAClB,EAAE,CAAC;AAAA,QAAI,CAAC;AAAA,MAAI;AAChB,UAAI,gBAAgB,SAAUA,QAAO;AAAE,eAAO,iBAAiB,cAAc,MAAM,SAAS,CAAC,GAAGA,QAAO,EAAE,MAAa,CAAC,CAAC;AAAA,MAAG;AAC3H,UAAI,uBAAuB,SAAU,iBAAiB;AAAE,eAAQ,iBAAiB,cAAc,eAAe,MAAM,SAAUA,QAAO;AAAE,iBAAO,iBAAiB,cAAc,aAAa,SAAS,EAAE,QAAQ,oBAAoB,QAAQ,oBAAoB,SAAS,SAAS,gBAAgB,OAAO,GAAGA,MAAK,CAAC;AAAA,QAAG,CAAC;AAAA,MAAI;AACvT,UAAI,eAAe,SAAUA,QAAO;AAChC,YAAI,OAAOA,OAAM;AACjB,YAAI,CAAC,gBAAgB,iBAAiB;AAClC,iBAAO;AAAA,QACX;AACA,YAAI,aAAa;AAAA,UACb,UAAW,iBAAiB;AAAA,YAAc,iBAAiB;AAAA,YAAU;AAAA,YACjE,iBAAiB,cAAc,iBAAiB,EAAE,cAAcA,OAAM,cAAc,MAAa,CAAC;AAAA,YAClG,iBAAiB,cAAc,WAAW,EAAE,mBAAmBA,OAAM,mBAAmB,MAAa,CAAC;AAAA,YACtG,KAAK;AAAA,UAAQ;AAAA,QACrB;AACA,eAAO,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,UAAU;AAAA,MAClD;AACA,aAAO;AAAA,QACH;AAAA,QACA,SAAS,SAAU,iBAAiB;AAChC,gBAAM,OAAO,QAAQ,gBAAgB,IAAI;AAAA,QAC7C;AAAA,QACA,qBAAqB,SAAU,aAAa;AACxC,gBAAM,OAAO,SAAS,YAAY,KAAK;AACvC,iBAAO;AAAA,QACX;AAAA,QACA,QAAQ,SAAU,OAAO;AACrB,cAAI,OAAO,MAAM,IAAI,MAAM;AAC3B,cAAI,MAAM;AACN,iBAAK,KAAK;AAAA,UACd;AAAA,QACJ;AAAA,QACA,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,SAAS;AAAA,QACT,eAAe;AAAA,QACf,iBAAiB;AAAA,QACjB,MAAM;AAAA,QACN,aAAa;AAAA,MACjB;AAAA,IACJ;AAEA,YAAQ,aAAa;AACrB,YAAQ,cAAc;AACtB,YAAQ,aAAa;AAAA;AAAA;;;ACpYrB,IAAAC,gBAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,gBAAgB;AACpB,QAAI,QAAQ;AACZ,QAAI,aAAa;AACjB,QAAI,UAAU;AACd,QAAI,OAAO;AACX,QAAI,iBAAiB;AACrB,QAAI,QAAQ;AACZ,QAAI,aAAa;AACjB,QAAI,SAAS;AACb,QAAI,aAAa;AACjB,QAAI,SAAS;AACb,QAAI,QAAQ;AACZ,QAAI,OAAO;AAEX,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,WAAW,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QACtF,iBAAiB,cAAc,QAAQ,EAAE,GAAG,sUAAsU,CAAC;AAAA,MAAC;AAAA,IAAI;AAE5X,QAAI,gBAAgB,EAAE,MAAM,GAAG,KAAK,EAAE;AACtC,QAAI,qBAAqB,SAAU,IAAI;AACnC,UAAI,cAAc,GAAG;AACrB,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,YAAY,iBAAiB,WAAW,KAAK,YAAY,EAAE;AAC/D,UAAI,iBAAiB,cAAc,KAAK,cAAc,cAAc,KAAK,SAAS,aAAa,KAAK,SAAS;AAC7G,UAAI,mBAAmB,YAAY,kBAAkB,0BAA0B,YAAY,yBAAyB,wBAAwB,YAAY,uBAAuB,uBAAuB,YAAY,sBAAsB,uBAAuB,YAAY,sBAAsB,2BAA2B,YAAY,0BAA0B,eAAe,YAAY,cAAc,gBAAgB,YAAY,eAAe,yBAAyB,YAAY,wBAAwB,wBAAwB,YAAY,uBAAuB,yBAAyB,YAAY,wBAAwB,2BAA2B,YAAY,0BAA0B,8BAA8B,YAAY,6BAA6B,yBAAyB,YAAY,wBAAwB,sBAAsB,YAAY;AACl1B,UAAI,eAAe,SAAU,QAAQ,QAAQ;AACzC,YAAI,QAAQ,QAAQ,KAAK,UAAU,KAAK,QAAQ,cAAc;AAC9D,eAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,wBAAwB,UAAU,gBAAgB,QAAQ,iBAAiB;AAAA,UAAc,KAAK;AAAA,UAAe,EAAE,WAAW,OAAO,YAAY,QAAQ,QAAQ,wCAAwC,SAAS,OAAO;AAAA,UACxR,iBAAiB,cAAc,UAAU,IAAI;AAAA,QAAC,GAAG,SAAS,WAAY;AAAE,iBAAO;AAAA,QAAO,GAAG,QAAQ,cAAc,CAAC;AAAA,MAC5H;AACA,UAAI,gBAAgB,SAAU,QAAQ;AAClC,eAAQ,iBAAiB;AAAA,UAAc,KAAK;AAAA,UAAM;AAAA,UAC9C,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,4DAA4D;AAAA,YAC3G,iBAAiB,cAAc,qBAAqB,EAAE,SAAS,OAAO,CAAC;AAAA,UAAC;AAAA,UAC5E,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,4DAA4D;AAAA,YAC3G,iBAAiB,cAAc,yBAAyB,EAAE,SAAS,OAAO,CAAC;AAAA,UAAC;AAAA,UAChF,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,4DAA4D;AAAA,YAC3G,iBAAiB,cAAc,cAAc,IAAI;AAAA,UAAC;AAAA,UACtD,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,4DAA4D;AAAA,YAC3G,iBAAiB,cAAc,eAAe,EAAE,SAAS,OAAO,CAAC;AAAA,UAAC;AAAA,UACtE,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,4DAA4D;AAAA,YAC3G,iBAAiB,cAAc,kBAAkB,EAAE,SAAS,OAAO,CAAC;AAAA,UAAC;AAAA,UACzE,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,4DAA4D;AAAA,YAC3G,iBAAiB,cAAc,KAAK,aAAa,IAAI;AAAA,UAAC;AAAA,UAC1D,iBAAiB,cAAc,uBAAuB,EAAE,SAAS,OAAO,CAAC;AAAA,UACzE,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,4DAA4D;AAAA,YAC3G,iBAAiB,cAAc,0BAA0B,EAAE,SAAS,OAAO,CAAC;AAAA,UAAC;AAAA,UACjF,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,4DAA4D;AAAA,YAC3G,iBAAiB,cAAc,sBAAsB,EAAE,SAAS,OAAO,CAAC;AAAA,UAAC;AAAA,UAC7E,iBAAiB,cAAc,sBAAsB,EAAE,SAAS,OAAO,CAAC;AAAA,UACxE,iBAAiB,cAAc,KAAK,aAAa,IAAI;AAAA,UACrD,iBAAiB,cAAc,uBAAuB,EAAE,SAAS,OAAO,CAAC;AAAA,UACzE,iBAAiB,cAAc,wBAAwB,EAAE,SAAS,OAAO,CAAC;AAAA,UAC1E,iBAAiB,cAAc,KAAK,aAAa,IAAI;AAAA,UACrD,iBAAiB,cAAc,6BAA6B,EAAE,MAAM,cAAc,cAAc,MAAM,SAAS,OAAO,CAAC;AAAA,UACvH,iBAAiB,cAAc,6BAA6B,EAAE,MAAM,cAAc,cAAc,MAAM,SAAS,OAAO,CAAC;AAAA,UACvH,iBAAiB,cAAc,KAAK,aAAa,IAAI;AAAA,UACrD,iBAAiB,cAAc,0BAA0B,EAAE,MAAM,KAAK,WAAW,MAAM,SAAS,OAAO,CAAC;AAAA,UACxG,iBAAiB,cAAc,0BAA0B,EAAE,MAAM,KAAK,WAAW,UAAU,SAAS,OAAO,CAAC;AAAA,UAC5G,iBAAiB,cAAc,0BAA0B,EAAE,MAAM,KAAK,WAAW,YAAY,SAAS,OAAO,CAAC;AAAA,UAC9G,iBAAiB,cAAc,0BAA0B,EAAE,MAAM,KAAK,WAAW,SAAS,SAAS,OAAO,CAAC;AAAA,UAC3G,iBAAiB,cAAc,KAAK,aAAa,IAAI;AAAA,UACrD,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,2DAA2D;AAAA,YAC1G,iBAAiB,cAAc,wBAAwB,EAAE,MAAM,KAAK,SAAS,YAAY,SAAS,OAAO,CAAC;AAAA,UAAC;AAAA,UAC/G,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,2DAA2D;AAAA,YAC1G,iBAAiB,cAAc,wBAAwB,EAAE,MAAM,KAAK,SAAS,UAAU,SAAS,OAAO,CAAC;AAAA,UAAC;AAAA,UAC7G,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,2DAA2D;AAAA,YAC1G,iBAAiB,cAAc,wBAAwB,EAAE,MAAM,KAAK,SAAS,mBAAmB,SAAS,OAAO,CAAC;AAAA,UAAC;AAAA,UACtH,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,2DAA2D;AAAA,YAC1G,iBAAiB,cAAc,KAAK,aAAa,IAAI;AAAA,UAAC;AAAA,UAC1D,iBAAiB,cAAc,wBAAwB,EAAE,SAAS,OAAO,CAAC;AAAA,QAAC;AAAA,MACnF;AACA,aAAQ,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,wBAAwB,cAAc,QAAQ,UAAU,gBAAgB,QAAQ,cAAc,SAAS,eAAe,QAAQ,eAAe,qBAAqB,MAAM,eAAe,KAAK,CAAC;AAAA,IAC5Q;AAiBA,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,uBAAuB,SAAU,sBAAsB;AACvD,aAAO,SAAU,oBAAoB;AACjC,YAAI,cAAc,iBAAiB,QAAQ,WAAY;AAAE,iBAAO,qBAAqB,kBAAkB;AAAA,QAAG,GAAG,CAAC,CAAC;AAC/G,YAAI,YAAY,iBAAiB,WAAW,KAAK,YAAY,EAAE;AAC/D,YAAI,QAAQ,cAAc,KAAK,cAAc;AAC7C,YAAI,mBAAmB,YAAY,kBAAkB,WAAW,YAAY,UAAU,kBAAkB,YAAY,iBAAiB,eAAe,YAAY,cAAc,mBAAmB,YAAY,kBAAkB,gBAAgB,YAAY,eAAe,OAAO,YAAY,MAAM,QAAQ,YAAY,OAAO,oBAAoB,YAAY,mBAAmB,cAAc,YAAY,aAAa,OAAO,YAAY,MAAM,SAAS,YAAY,QAAQ,UAAU,YAAY;AACpe,eAAQ,iBAAiB;AAAA,UAAc;AAAA,UAAO,EAAE,eAAe,WAAW,WAAW,KAAK,WAAW;AAAA,YAC7F,eAAe;AAAA,YACf,oBAAoB;AAAA,UACxB,CAAC,GAAG,MAAM,WAAW,oBAAoB,aAAa;AAAA,UACtD,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,oBAAoB;AAAA,YACnE,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,oBAAoB;AAAA,cACnE,iBAAiB,cAAc,mBAAmB,IAAI;AAAA,YAAC;AAAA,YAC3D,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,2DAA2D;AAAA,cAC1G,iBAAiB;AAAA,gBAAc;AAAA,gBAAO,EAAE,WAAW,oBAAoB;AAAA,gBACnE,iBAAiB,cAAc,kBAAkB,IAAI;AAAA,cAAC;AAAA,YAAC;AAAA,YAC/D,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,oBAAoB;AAAA,cACnE,iBAAiB,cAAc,kBAAkB,IAAI;AAAA,cACrD,iBAAiB;AAAA,gBAAc;AAAA,gBAAQ,EAAE,WAAW,qBAAqB;AAAA,gBACrE,iBAAiB,cAAc,eAAe,IAAI;AAAA,cAAC;AAAA,YAAC;AAAA,YAC5D,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,2DAA2D;AAAA,cAC1G,iBAAiB;AAAA,gBAAc;AAAA,gBAAO,EAAE,WAAW,oBAAoB;AAAA,gBACnE,iBAAiB,cAAc,cAAc,IAAI;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC;AAAA,UAChE,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,sBAAsB;AAAA,YACrE,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,oBAAoB;AAAA,cACnE,iBAAiB,cAAc,SAAS,IAAI;AAAA,YAAC;AAAA,YACjD,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,2DAA2D;AAAA,cAC1G,iBAAiB;AAAA,gBAAc;AAAA,gBAAO,EAAE,WAAW,oBAAoB;AAAA,gBACnE,iBAAiB,cAAc,MAAM,IAAI;AAAA,cAAC;AAAA,YAAC;AAAA,YACnD,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,oBAAoB;AAAA,cACnE,iBAAiB,cAAc,QAAQ,IAAI;AAAA,YAAC;AAAA,UAAC;AAAA,UACrD,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,qBAAqB;AAAA,YACpE,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,4DAA4D;AAAA,cAC3G,iBAAiB;AAAA,gBAAc;AAAA,gBAAO,EAAE,WAAW,oBAAoB;AAAA,gBACnE,iBAAiB,cAAc,aAAa,IAAI;AAAA,cAAC;AAAA,YAAC;AAAA,YAC1D,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,4DAA4D;AAAA,cAC3G,iBAAiB;AAAA,gBAAc;AAAA,gBAAO,EAAE,WAAW,oBAAoB;AAAA,gBACnE,iBAAiB,cAAc,iBAAiB,IAAI;AAAA,cAAC;AAAA,YAAC;AAAA,YAC9D,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,4DAA4D;AAAA,cAC3G,iBAAiB;AAAA,gBAAc;AAAA,gBAAO,EAAE,WAAW,oBAAoB;AAAA,gBACnE,iBAAiB,cAAc,MAAM,IAAI;AAAA,cAAC;AAAA,YAAC;AAAA,YACnD,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,4DAA4D;AAAA,cAC3G,iBAAiB;AAAA,gBAAc;AAAA,gBAAO,EAAE,WAAW,oBAAoB;AAAA,gBACnE,iBAAiB,cAAc,UAAU,IAAI;AAAA,cAAC;AAAA,YAAC;AAAA,YACvD,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,4DAA4D;AAAA,cAC3G,iBAAiB;AAAA,gBAAc;AAAA,gBAAO,EAAE,WAAW,oBAAoB;AAAA,gBACnE,iBAAiB,cAAc,OAAO,IAAI;AAAA,cAAC;AAAA,YAAC;AAAA,YACpD,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,WAAW,oBAAoB;AAAA,cACnE,iBAAiB,cAAc,oBAAoB,EAAE,YAAyB,CAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAClG;AAAA,IACJ;AAEA,QAAI,mBAAmB,SAAU,MAAM;AACnC,UAAI,gBAAgB,KAAK;AACzB,aAAO,OAAO,OAAO,CAAC,GAAG,MAAM;AAAA,QAC3B,eAAe,WAAY;AAAE,iBAAQ,iBAAiB;AAAA,YAAc,iBAAiB;AAAA,YAAU;AAAA,YAC3F;AAAA,YACA,iBAAiB,cAAc,eAAe,IAAI;AAAA,UAAC;AAAA,QAAI;AAAA,MAC/D,CAAC;AAAA,IACL;AACA,QAAI,gBAAgB,SAAU,aAAa;AACvC,aAAO,qBAAqB,gBAAgB,EAAE,WAAW;AAAA,IAC7D;AAEA,QAAI,UAAU,SAAU,IAAI;AACxB,UAAI,WAAW,GAAG,UAAU,OAAO,GAAG;AACtC,UAAI,SAAS,YAAY;AACzB,aAAO,OAAO,IAAI;AAAA,IACtB;AAEA,QAAI,gBAAgB,SAAU,OAAO;AACjC,UAAI,2BAA2B,WAAW,iBAAiB,QAAQ,MAAM,mBAAmB,CAAC,CAAC;AAC9F,UAAI,wBAAwB,QAAQ,cAAc,QAAQ,MAAM,gBAAgB,CAAC,CAAC;AAClF,UAAI,qBAAqB,KAAK,WAAW,QAAQ,MAAM,aAAa,CAAC,CAAC;AACtE,UAAI,+BAA+B,eAAe,qBAAqB,QAAQ,MAAM,uBAAuB,CAAC,CAAC;AAC9G,UAAI,sBAAsB,MAAM,YAAY,QAAQ,MAAM,cAAc,CAAC,CAAC;AAC1E,UAAI,2BAA2B,WAAW,iBAAiB;AAC3D,UAAI,uBAAuB,OAAO,aAAa;AAC/C,UAAI,2BAA2B,WAAW,iBAAiB;AAC3D,UAAI,uBAAuB,OAAO,aAAa,QAAQ,MAAM,eAAe,CAAC,CAAC;AAC9E,UAAI,8BAA8B,cAAc,oBAAoB,QAAQ,MAAM,sBAAsB,CAAC,CAAC;AAC1G,UAAI,sBAAsB,MAAM,YAAY;AAC5C,UAAI,qBAAqB,KAAK,WAAW,QAAQ,MAAM,aAAa,CAAC,CAAC;AACtE,UAAI,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AACA,UAAI,mBAAmB,iBAAiB,YAAY,SAAUC,QAAO;AACjE,YAAI,kBAAkB,yBAAyB,iBAAiB,0BAA0B,yBAAyB;AACnH,YAAI,WAAW,sBAAsB,UAAU,mBAAmB,sBAAsB;AACxF,YAAI,OAAO,mBAAmB,MAAM,eAAe,mBAAmB;AACtE,YAAI,mBAAmB,6BAA6B,kBAAkB,mBAAmB,6BAA6B,kBAAkB,gBAAgB,6BAA6B,eAAe,wBAAwB,6BAA6B,uBAAuB,eAAe,6BAA6B,cAAc,uBAAuB,6BAA6B,sBAAsB,eAAe,6BAA6B,cAAc,uBAAuB,6BAA6B,sBAAsB,mBAAmB,6BAA6B,kBAAkB,2BAA2B,6BAA6B,0BAA0B,gBAAgB,6BAA6B;AACztB,YAAI,QAAQ,oBAAoB,OAAO,gBAAgB,oBAAoB;AAC3E,YAAI,iBAAiB,yBAAyB,gBAAgB,yBAAyB,yBAAyB;AAChH,YAAI,SAAS,qBAAqB,QAAQ,yBAAyB,qBAAqB,wBAAwB,wBAAwB,qBAAqB;AAC7J,YAAI,mBAAmB,yBAAyB,kBAAkB,2BAA2B,yBAAyB,0BAA0B,iBAAiB,yBAAyB,gBAAgB,yBAAyB,yBAAyB;AAC5P,YAAI,SAAS,qBAAqB,QAAQ,oBAAoB,qBAAqB;AACnF,YAAI,sBAAsB,4BAA4B,qBAAqB,8BAA8B,4BAA4B;AACrI,YAAI,cAAc,oBAAoB,aAAa,sBAAsB,oBAAoB;AAC7F,YAAI,eAAe,mBAAmB,cAAc,OAAO,mBAAmB,MAAM,SAAS,mBAAmB,QAAQ,iBAAiB,mBAAmB,gBAAgB,UAAU,mBAAmB,SAAS,kBAAkB,mBAAmB;AACvP,eAAQ,iBAAiB,cAAc,SAAS,SAAS,CAAC,GAAGA,QAAO,EAAE,MAAM;AAAA,UACpE;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACJ,EAAE,CAAC,CAAC;AAAA,MACZ,GAAG,CAAC,CAAC;AACL,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,SAAU,iBAAiB;AAChC,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,SAAS;AAChB,qBAAO,QAAQ,eAAe;AAAA,YAClC;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,QACA,iBAAiB,SAAU,aAAa;AAAE,iBAAQ,iBAAiB,cAAc,iBAAiB,UAAU,MAAM,QAAQ,IAAI,SAAU,QAAQ,KAAK;AACjJ,mBAAO,OAAO,kBAAmB,iBAAiB,cAAc,iBAAiB,UAAU,EAAE,KAAK,IAAI,GAAG,OAAO,gBAAgB,WAAW,CAAC,IAAM,iBAAiB,cAAc,iBAAiB,UAAU,EAAE,KAAK,IAAI,CAAC;AAAA,UAC5N,CAAC,CAAC;AAAA,QAAI;AAAA,QACN,cAAc,SAAUA,QAAO;AAC3B,cAAI,OAAOA,OAAM;AACjB,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,cAAc;AACrB,qBAAO,OAAO,aAAa,SAAS,SAAS,CAAC,GAAGA,MAAK,GAAG,EAAE,KAAW,CAAC,CAAC;AAAA,YAC5E;AAAA,UACJ,CAAC;AACD,iBAAO;AAAA,QACX;AAAA,QACA,WAAW,SAAU,iBAAiB;AAClC,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,WAAW;AAClB,qBAAO,UAAU,eAAe;AAAA,YACpC;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,QACA,gBAAgB,SAAUA,QAAO;AAC7B,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,gBAAgB;AACvB,qBAAO,eAAeA,MAAK;AAAA,YAC/B;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,QACA,yBAAyB,SAAUA,QAAO;AACtC,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,yBAAyB;AAChC,qBAAO,wBAAwBA,MAAK;AAAA,YACxC;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,QACA,mBAAmB,SAAUA,QAAO;AAChC,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,mBAAmB;AAC1B,qBAAO,kBAAkBA,MAAK;AAAA,YAClC;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,QACA,qBAAqB,SAAU,aAAa;AACxC,cAAI,WAAW;AACf,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,qBAAqB;AAC5B,yBAAW,OAAO,oBAAoB,QAAQ;AAAA,YAClD;AAAA,UACJ,CAAC;AACD,iBAAO;AAAA,QACX;AAAA,QACA;AAAA,QACA,SAAS;AAAA,MACb;AAAA,IACJ;AAEA,YAAQ,qBAAqB;AAC7B,YAAQ,WAAW;AACnB,YAAQ,gBAAgB;AAAA;AAAA;;;AC/VxB,IAAAC,gBAAA;AAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,QAAQ;AACZ,QAAI,aAAa;AACjB,QAAI,WAAW;AACf,QAAI,YAAY;AAChB,QAAI,UAAU;AAEd,aAAS,yBAAyB,GAAG;AACjC,UAAI,IAAI,uBAAO,OAAO,IAAI;AAC1B,UAAI,GAAG;AACH,eAAO,KAAK,CAAC,EAAE,QAAQ,SAAU,GAAG;AAChC,cAAI,MAAM,WAAW;AACjB,gBAAI,IAAI,OAAO,yBAAyB,GAAG,CAAC;AAC5C,mBAAO,eAAe,GAAG,GAAG,EAAE,MAAM,IAAI;AAAA,cACpC,YAAY;AAAA,cACZ,KAAK,WAAY;AAAE,uBAAO,EAAE,CAAC;AAAA,cAAG;AAAA,YACpC,CAAC;AAAA,UACL;AAAA,QACJ,CAAC;AAAA,MACL;AACA,QAAE,UAAU;AACZ,aAAO,OAAO,OAAO,CAAC;AAAA,IAC1B;AAEA,QAAI,mBAAgC,yBAAyB,KAAK;AAElE,QAAI,eAAe,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAC1F,iBAAiB,cAAc,QAAQ,EAAE,GAAG,iZAAiZ,CAAC;AAAA,MAAC;AAAA,IAAI;AAiBvc,QAAI,WAAW,WAAW;AACtB,iBAAW,OAAO,UAAU,SAASC,UAAS,GAAG;AAC7C,iBAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,cAAI,UAAU,CAAC;AACf,mBAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,QAC/E;AACA,eAAO;AAAA,MACX;AACA,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IACzC;AAEA,QAAI,WAAW,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QACtF,iBAAiB,cAAc,QAAQ,EAAE,GAAG,wTAAwT,CAAC;AAAA,MAAC;AAAA,IAAI;AAE9W,QAAI,gBAAgB,WAAY;AAAE,aAAQ,iBAAiB;AAAA,QAAc,KAAK;AAAA,QAAM,EAAE,MAAM,GAAG;AAAA,QAC3F,iBAAiB,cAAc,QAAQ,EAAE,GAAG,+cAA+c,CAAC;AAAA,MAAC;AAAA,IAAI;AAErgB,QAAI,qBAAqB,EAAE,MAAM,GAAG,KAAK,EAAE;AAC3C,QAAI,qBAAqB,EAAE,MAAM,IAAI,KAAK,EAAE;AAC5C,QAAI,UAAU,SAAU,IAAI;AACxB,UAAI,uBAAuB,GAAG,sBAAsB,qBAAqB,GAAG,oBAAoB,QAAQ,GAAG,OAAO,sBAAsB,GAAG,qBAAqB,OAAO,GAAG;AAC1K,UAAI,eAAe,iBAAiB,OAAO;AAC3C,UAAI,OAAO,iBAAiB,WAAW,KAAK,mBAAmB,EAAE;AACjE,UAAI,KAAK,iBAAiB,SAAS,MAAM,IAAI,oBAAoB,KAAK,KAAK,GAAG,SAAS,GAAG,CAAC,GAAG,YAAY,GAAG,CAAC;AAC9G,UAAI,KAAK,iBAAiB,SAAS,KAAK,IAAI,MAAM,IAAI,YAAY,KAAK,GAAG,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC;AACvH,UAAI,YAAY,iBAAiB,WAAW,KAAK,YAAY,EAAE;AAC/D,UAAI,QAAQ,cAAc,KAAK,cAAc;AAC7C,UAAI,kBAAkB,SAAU,MAAM;AAAE,eAAO,KAAK,uBAAuB,MAAM,KAAK,uBAAuB;AAAA,MAAI;AACjH,UAAI,cAAc;AAAA,QACd;AAAA,UACI,SAAS;AAAA,UACT,MAAM,iBAAiB,cAAc,eAAe,IAAI;AAAA,UACxD,OAAO,QAAQ,KAAK,gBACd,KAAK,cAAc,YACnB;AAAA,QACV;AAAA,QACA;AAAA,UACI,SAAS;AAAA,UACT,MAAM,iBAAiB,cAAc,cAAc,IAAI;AAAA,UACvD,OAAO,QAAQ,KAAK,gBAAgB,KAAK,cAAc,WAAW;AAAA,QACtE;AAAA,QACA;AAAA,UACI,SAAS;AAAA,UACT,MAAM,iBAAiB,cAAc,UAAU,IAAI;AAAA,UACnD,OAAO,QAAQ,KAAK,gBACd,KAAK,cAAc,aACnB;AAAA,QACV;AAAA,MACJ;AACA,UAAI,WAAW,OAAO,KAAK,WAAW,IAAI;AAC1C,UAAI,YAAY,SAAU,OAAO;AAC7B,YAAI,eAAe,OAAO;AACtB,gBAAM,OAAO,sBAAsB,CAAC,MAAM,IAAI,oBAAoB,CAAC;AACnE,cAAI,YAAY,aAAa;AAC7B,cAAI,WAAW;AACX,gBAAI,QAAQ,UAAU,MAAM;AAC5B,gBAAI,OAAO;AACP,wBAAU,MAAM,eAAe,OAAO;AAAA,YAC1C;AAAA,UACJ;AAAA,QACJ,OACK;AACD,gBAAM,OAAO,cAAc,KAAK;AAAA,QACpC;AAAA,MACJ;AACA,UAAI,cAAc,SAAU,OAAO;AAC/B,YAAI,SAAS,KAAK,SAAS,SAAS,SAAS,GAAG;AAC5C,gBAAM,OAAO,sBAAsB,IAAI;AACvC,wBAAc,KAAK;AAAA,QACvB;AAAA,MACJ;AACA,UAAI,yBAAyB,SAAUC,SAAQ;AAC3C,kBAAUA,OAAM;AAAA,MACpB;AACA,uBAAiB,UAAU,WAAY;AACnC,cAAM,UAAU,cAAc,WAAW;AACzC,cAAM,UAAU,sBAAsB,sBAAsB;AAC5D,eAAO,WAAY;AACf,gBAAM,YAAY,cAAc,WAAW;AAC3C,gBAAM,YAAY,sBAAsB,sBAAsB;AAAA,QAClE;AAAA,MACJ,GAAG,CAAC,CAAC;AACL,UAAI,SAAS,WAAW,GAAG;AACvB,eAAO,iBAAiB,cAAc,iBAAiB,UAAU,IAAI;AAAA,MACzE;AACA,aAAQ,iBAAiB;AAAA,QAAc,iBAAiB;AAAA,QAAU;AAAA,QAC9D,iBAAiB;AAAA,UAAc;AAAA,UAAO,EAAE,eAAe,2BAA2B,WAAW,KAAK,WAAW;AAAA,YACrG,+BAA+B;AAAA,YAC/B,uCAAuC;AAAA,YACvC,oCAAoC,CAAC;AAAA,YACrC,oCAAoC;AAAA,UACxC,CAAC,GAAG,KAAK,aAAa;AAAA,UACtB,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,mCAAmC;AAAA,YAClF,iBAAiB,cAAc,OAAO,EAAE,WAAW,uCAAuC,MAAM,WAAW,oBAAoB,WAAW,GAAG,SAAS,IAAI,SAAU,KAAK,OAAO;AAAE,qBAAQ,iBAAiB;AAAA,gBAAc;AAAA,gBAAO,EAAE,iBAAiB,uCAAuC,iBAAiB,eAAe,OAAO,KAAK,OAAO,WAAW,sCAAsC,IAAI,mCAAmC,OAAO,KAAK,GAAG,MAAM,MAAM;AAAA,gBAC5b,iBAAiB,cAAc,KAAK,SAAS,EAAE,oBAAoB,8BAA8B,OAAO,KAAK,GAAG,UAAU,QAAQ,KAAK,SAAS,aAAa,KAAK,SAAS,aAAa,QAAQ,iBAAiB,cAAc,KAAK,eAAe,EAAE,WAAW,IAAI,OAAO,YAAY,eAAe,OAAO,SAAS,WAAY;AAAE,yBAAO,UAAU,KAAK;AAAA,gBAAG,EAAE,GAAG,IAAI,IAAI,GAAG,SAAS,WAAY;AAAE,yBAAO,IAAI;AAAA,gBAAO,GAAG,QAAQ,QAAQ,qBAAqB,mBAAmB,CAAC;AAAA,cAAC;AAAA,YAAI,CAAC,CAAC;AAAA,YAC7d,iBAAiB,cAAc,OAAO,EAAE,mBAAmB,mCAAmC,OAAO,UAAU,GAAG,IAAI,uCAAuC,WAAW,KAAK,WAAW;AAAA,cAChL,uCAAuC;AAAA,cACvC,+CAA+C;AAAA,cAC/C,4CAA4C,CAAC;AAAA,cAC7C,4CAA4C;AAAA,YAChD,CAAC,GAAG,MAAM,YAAY,UAAU,GAAG,GAAG,SAAS,UAAU,EAAE,OAAO;AAAA,UAAC;AAAA,QAAC;AAAA,QAChF,UAAU,iBAAiB,cAAc,KAAK,UAAU,EAAE,WAAW,gBAAgB,CAAC;AAAA,MAAC;AAAA,IAC/F;AAEA,QAAI,sBAAsB,SAAU,OAAO;AACvC,UAAI,QAAQ,iBAAiB,QAAQ,WAAY;AAC7C,eAAO,KAAK,YAAY;AAAA,UACpB,oBAAoB;AAAA,UACpB,YAAY;AAAA,QAChB,CAAC;AAAA,MACL,GAAG,CAAC,CAAC;AACL,UAAI,2BAA2B,WAAW,iBAAiB;AAC3D,UAAI,yBAAyB,SAAS,eAAe;AACrD,UAAI,0BAA0B,UAAU,gBAAgB,QAAQ,MAAM,kBAAkB,CAAC,CAAC;AAC1F,UAAI,wBAAwB,QAAQ,cAAc,QAAQ,MAAM,gBAAgB,CAAC,CAAC;AAClF,UAAI,cAAc,yBAAyB;AAC3C,UAAI,YAAY,uBAAuB;AACvC,UAAI,aAAa,wBAAwB;AACzC,UAAI,UAAU,sBAAsB;AACpC,UAAI,cAAc,QAAQ,MAAM,cAAc,SAAU,aAAa;AAAE,eAAO;AAAA,MAAa;AAC3F,UAAI,UAAU,CAAC,0BAA0B,wBAAwB,yBAAyB,qBAAqB;AAC/G,aAAO;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa,SAAU,OAAO;AAC1B,gBAAM,OAAO,cAAc,KAAK;AAAA,QACpC;AAAA,QACA,WAAW,SAAU,OAAO;AACxB,cAAI,aAAa,MAAM,IAAI,YAAY;AACvC,gBAAM,OAAO,sBAAsB,CAAC,MAAM,IAAI,oBAAoB,CAAC;AACnE,cAAI,eAAe,OAAO;AACtB,kBAAM,OAAO,cAAc,KAAK;AAAA,UACpC;AAAA,QACJ;AAAA,QACA,SAAS,SAAU,iBAAiB;AAChC,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,SAAS;AAChB,qBAAO,QAAQ,eAAe;AAAA,YAClC;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,QACA,iBAAiB,SAAU,aAAa;AAAE,iBAAQ,iBAAiB,cAAc,iBAAiB,UAAU,MAAM,QAAQ,IAAI,SAAU,QAAQ,KAAK;AACjJ,mBAAO,OAAO,kBAAmB,iBAAiB,cAAc,iBAAiB,UAAU,EAAE,KAAK,IAAI,GAAG,OAAO,gBAAgB,WAAW,CAAC,IAAM,iBAAiB;AAAA,cAAc,iBAAiB;AAAA,cAAU,EAAE,KAAK,IAAI;AAAA,cACnN,iBAAiB,cAAc,iBAAiB,UAAU,IAAI;AAAA,YAAC;AAAA,UACvE,CAAC,CAAC;AAAA,QAAI;AAAA,QACN,cAAc,SAAU,aAAa;AACjC,cAAI,OAAO,YAAY;AACvB,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,cAAc;AACrB,qBAAO,OAAO,aAAa,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG,EAAE,KAAW,CAAC,CAAC;AAAA,YAClF;AAAA,UACJ,CAAC;AACD,cAAI,eAAe,KAAK,WAAW,KAAK,QAAQ,QAC1C;AAAA,YACE,WAAW,KAAK,QAAQ,MAAM;AAAA,YAC9B,eAAe,KAAK,QAAQ,MAAM,aAAa;AAAA,YAC/C,KAAK,KAAK,QAAQ,MAAM;AAAA,YACxB,OAAO,KAAK,QAAQ,MAAM;AAAA,UAC9B,IACE,CAAC;AACP,eAAK,WAAY,iBAAiB;AAAA,YAAc;AAAA,YAAO,EAAE,WAAW,gCAAgC;AAAA,YAChG,iBAAiB;AAAA,cAAc;AAAA,cAAO,EAAE,eAAe,wBAAwB,WAAW,KAAK,WAAW;AAAA,gBAClG,4BAA4B;AAAA,gBAC5B,iCAAiC,YAAY,aAAa,cAAc,KAAK,cAAc;AAAA,cAC/F,CAAC,EAAE;AAAA,cACH,iBAAiB,cAAc,SAAS,EAAE,sBAAsB,iBAAiB,cAAc,aAAa,IAAI,GAAG,oBAAoB,iBAAiB,cAAc,WAAW,IAAI,GAAG,OAAc,qBAAqB,iBAAiB,cAAc,YAAY,IAAI,GAAG,MAAM,YAAY,CAAC;AAAA,cAChS,iBAAiB;AAAA,gBAAc;AAAA,gBAAO,EAAE,WAAW,4BAA4B,eAAe,uBAAuB;AAAA,gBACjH,iBAAiB,cAAc,OAAO,EAAE,WAAW,8BAA8B,GAAG,SAAS,MAAM,gBAAgB,MAAM,cAAc,OAAO,IAAI,iBAAiB,cAAc,SAAS,IAAI,CAAC;AAAA,gBAC/L,iBAAiB,cAAc,OAAO,SAAS,CAAC,GAAG,YAAY,GAAG,KAAK,QAAQ,QAAQ;AAAA,cAAC;AAAA,YAAC;AAAA,YACjG,KAAK;AAAA,UAAQ;AACjB,eAAK,QAAQ,QAAQ,CAAC;AACtB,eAAK,QAAQ,WAAW,iBAAiB,cAAc,iBAAiB,UAAU,IAAI;AACtF,iBAAO;AAAA,QACX;AAAA,QACA,WAAW,SAAU,iBAAiB;AAClC,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,WAAW;AAClB,qBAAO,UAAU,eAAe;AAAA,YACpC;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,QACA,gBAAgB,SAAU,mBAAmB;AACzC,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,gBAAgB;AACvB,qBAAO,eAAe,iBAAiB;AAAA,YAC3C;AAAA,UACJ,CAAC;AACD,cAAI,SAAS,MAAM,eAAe;AAC9B,kBAAM,cAAc,kBAAkB,GAAG,EAAE,KAAK,SAAU,YAAY;AAClE,oBAAM,OAAO,cAAc,UAAU;AACrC,oBAAM,OAAO,sBAAsB,IAAI;AAAA,YAC3C,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,QACA,yBAAyB,SAAUC,QAAO;AACtC,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,yBAAyB;AAChC,qBAAO,wBAAwBA,MAAK;AAAA,YACxC;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,QACA,mBAAmB,SAAUA,QAAO;AAChC,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,mBAAmB;AAC1B,qBAAO,kBAAkBA,MAAK;AAAA,YAClC;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,QACA,qBAAqB,SAAU,aAAa;AACxC,cAAI,WAAW;AACf,kBAAQ,QAAQ,SAAU,QAAQ;AAC9B,gBAAI,OAAO,qBAAqB;AAC5B,yBAAW,OAAO,oBAAoB,QAAQ;AAAA,YAClD;AAAA,UACJ,CAAC;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,4BAA4B,SAAU,KAAK;AAC3C,aAAO,IAAI,QAAQ,SAAU,SAAS,GAAG;AACrC,YAAI,YAAY,EAAE,KAAK,SAAU,UAAU;AACvC,cAAI,CAAC,UAAU;AACX,oBAAQ,EAAE;AAAA,UACd,OACK;AACD,oBAAQ,UAAU;AAAA,cACd,KAAK,KAAK,SAAS;AACf,wBAAQ,CAAC;AACT;AAAA,cACJ,KAAK,KAAK,SAAS;AACf,wBAAQ,CAAC;AACT;AAAA,cACJ,KAAK,KAAK,SAAS;AACf,wBAAQ,CAAC;AACT;AAAA,cACJ;AACI,wBAAQ,EAAE;AACV;AAAA,YACR;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AAEA,YAAQ,eAAe;AACvB,YAAQ,WAAW;AACnB,YAAQ,gBAAgB;AACxB,YAAQ,sBAAsB;AAC9B,YAAQ,4BAA4B;AAAA;AAAA;;;AC1SpC,IAAAC,gBAAA;AAAA;AAUA,QAAI,OAAuC;AACvC,aAAO,UAAU;AAAA,IACrB,OAAO;AACH,aAAO,UAAU;AAAA,IACrB;AAAA;AAAA;", "names": ["require_lib", "__assign", "Toggle", "toggle", "require_lib", "__assign", "pageRotation", "_a", "direction", "rotatedPage", "viewMode", "require_lib", "__assign", "SwitchSelectionModeDecorator", "props", "require_lib", "__assign", "fullScreenMode", "props", "require_lib", "__assign", "download", "props", "require_lib", "__assign", "props", "require_lib", "__assign", "jumpToNextDestination", "jumpToPreviousDestination", "props", "require_lib", "__assign", "PrintStatus", "props", "require_lib", "__assign", "require_lib", "__assign", "require_lib", "__assign", "SwitchScrollModeDecorator", "SwitchViewModeDecorator", "require_lib", "__assign", "item", "_a", "targetPageFilter", "keywords", "props", "ShowSearchPopoverDecorator", "require_lib", "__assign", "require_lib", "__assign", "props", "require_lib", "__assign", "props", "require_lib", "__assign", "opened", "props", "require_lib"]}
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { UnsortedSegmentSum, util } from '@tensorflow/tfjs-core';
import { assertNotComplex } from '../cpu_util';
import { cast } from './Cast';
import { equal } from './Equal';
import { expandDims } from './ExpandDims';
import { multiply } from './Multiply';
import { pack } from './Pack';
import { sum } from './Sum';
export function unsortedSegmentSum(args) {
    const { inputs, backend, attrs } = args;
    const { x, segmentIds } = inputs;
    const { numSegments } = attrs;
    assertNotComplex(x, 'unsortedSegmentSum');
    const xRank = x.shape.length;
    const segmentIdsRank = segmentIds.shape.length;
    const res = [];
    const intermediates = [];
    // Reshape the segment id's so that they can be broadcast with
    // x. The new shape should be [segmentIds.shape, 1, ..., 1]
    const numIters = xRank - segmentIdsRank;
    let $segmentIds = segmentIds;
    for (let i = 0; i < numIters; ++i) {
        const expanded = expandDims({ inputs: { input: $segmentIds }, backend, attrs: { dim: i + 1 } });
        $segmentIds = expanded;
        intermediates.push(expanded);
    }
    for (let i = 0; i < numSegments; ++i) {
        const scalarValue = util.createScalarValue(i, 'int32');
        const segmentId = backend.makeTensorInfo([], 'int32', scalarValue);
        const mask = equal({ inputs: { a: segmentId, b: $segmentIds }, backend });
        const maskCasted = cast({ inputs: { x: mask }, backend, attrs: { dtype: 'float32' } });
        const mul = multiply({ inputs: { a: maskCasted, b: x }, backend });
        const sumTensorInfo = sum({ inputs: { x: mul }, backend, attrs: { axis: 0, keepDims: false } });
        res.push(sumTensorInfo);
        intermediates.push(segmentId);
        intermediates.push(mask);
        intermediates.push(maskCasted);
        intermediates.push(mul);
        intermediates.push(sumTensorInfo);
    }
    const result = pack({ inputs: res, backend, attrs: { axis: 0 } });
    intermediates.forEach(t => backend.disposeIntermediateTensorInfo(t));
    return result;
}
export const unsortedSegmentSumConfig = {
    kernelName: UnsortedSegmentSum,
    backendName: 'cpu',
    kernelFunc: unsortedSegmentSum
};
//# sourceMappingURL=data:application/json;base64,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
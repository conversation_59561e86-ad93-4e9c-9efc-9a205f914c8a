/**
 * @license
 * Copyright 2022 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import{util as e,kernel_impls as t,KernelBackend as n,DataStorage as a,engine as s,env as r,backend_util as o,buffer as i,Abs as l,Complex as d,Identity as c,Real as p,Cast as u,Add as h,Ceil as f,Equal as m,Exp as k,Expm1 as g,Floor as I,Greater as b,GreaterEqual as y,Less as N,LessEqual as T,Log as x,Maximum as S,Minimum as v,Multiply as F,Neg as w,NotEqual as M,Transpose as A,upcastType as D,Prod as E,tidy as z,reshape as W,broadcastTo as R,Rsqrt as P,Sigmoid as H,slice_util as C,Slice as $,Sqrt as O,SquaredDifference as V,Sub as _,TensorBuffer as G,registerBackend as B,Elu as L,LeakyRelu as q,Prelu as U,Relu as Z,Relu6 as K,Reshape as j,BatchMatMul as Y,broadcast_util as J,_FusedMatMul as Q,Acos as X,Acosh as ee,AddN as te,All as ne,Any as ae,ArgMax as se,ArgMin as re,Asin as oe,Asinh as ie,Atan as le,Atan2 as de,Atanh as ce,AvgPool as pe,AvgPool3D as ue,AvgPool3DGrad as he,AvgPoolGrad as fe,FusedBatchNorm as me,BatchToSpaceND as ke,Bincount as ge,BroadcastArgs as Ie,ClipByValue as be,ComplexAbs as ye,Imag as Ne,Concat as Te,Conv2D as xe,Conv2DBackpropFilter as Se,Conv2DBackpropInput as ve,Conv3D as Fe,Conv3DBackpropFilterV2 as we,Conv3DBackpropInputV2 as Me,Cos as Ae,Cosh as De,CropAndResize as Ee,Cumprod as ze,Cumsum as We,DenseBincount as Re,DepthToSpace as Pe,DepthwiseConv2dNative as He,DepthwiseConv2dNativeBackpropFilter as Ce,DepthwiseConv2dNativeBackpropInput as $e,Diag as Oe,Dilation2D as Ve,Dilation2DBackpropFilter as _e,Dilation2DBackpropInput as Ge,Sum as Be,Einsum as Le,EluGrad as qe,Erf as Ue,ExpandDims as Ze,RealDiv as Ke,FFT as je,Fill as Ye,FlipLeftRight as Je,FloorDiv as Qe,FusedConv2D as Xe,FusedDepthwiseConv2D as et,GatherNd as tt,GatherV2 as nt,IFFT as at,IsFinite as st,IsInf as rt,IsNan as ot,LinSpace as it,Log1p as lt,LogicalAnd as dt,LogicalNot as ct,LogicalOr as pt,LRN as ut,LRNGrad as ht,Max as ft,MaxPool as mt,MaxPool3D as kt,MaxPool3DGrad as gt,MaxPoolGrad as It,MaxPoolWithArgmax as bt,Mean as yt,Min as Nt,MirrorPad as Tt,Mod as xt,Softmax as St,Multinomial as vt,NonMaxSuppressionV3 as Ft,NonMaxSuppressionV4 as wt,NonMaxSuppressionV5 as Mt,OneHot as At,ZerosLike as Dt,OnesLike as Et,Pack as zt,PadV2 as Wt,Pow as Rt,RaggedGather as Pt,RaggedTensorToTensor as Ht,Range as Ct,Reciprocal as $t,ResizeBilinear as Ot,ResizeBilinearGrad as Vt,ResizeNearestNeighbor as _t,ResizeNearestNeighborGrad as Gt,Reverse as Bt,RotateWithOffset as Lt,Round as qt,ScatterNd as Ut,SearchSorted as Zt,Select as Kt,Selu as jt,Sign as Yt,Sin as Jt,Sinh as Qt,Softplus as Xt,SpaceToBatchND as en,SparseFillEmptyRows as tn,SparseReshape as nn,SparseSegmentMean as an,SparseSegmentSum as sn,SparseToDense as rn,SplitV as on,Square as ln,Step as dn,StridedSlice as cn,StringNGrams as pn,StringSplit as un,StringToHashBucketFast as hn,Tan as fn,Tanh as mn,Tile as kn,TopK as gn,Transform as In,Unique as bn,Unpack as yn,UnsortedSegmentSum as Nn,registerKernel as Tn}from"@tensorflow/tfjs-core";import*as xn from"seedrandom";function Sn(t,n){Array.isArray(t)||(t=[t]),t.forEach((t=>{null!=t&&e.assert("complex64"!==t.dtype,(()=>`${n} does not support complex64 tensors in the CPU backend.`))}))}const vn=t.whereImpl;class Fn extends n{constructor(){super(),this.blockSize=48,this.firstUse=!0,this.data=new a(this,s())}nextDataId(){return Fn.nextDataId++}write(e,t,n){this.firstUse&&(this.firstUse=!1,r().get("IS_NODE")&&o.warn("\n============================\nHi, looks like you are running TensorFlow.js in Node.js. To speed things up dramatically, install our node backend, visit https://github.com/tensorflow/tfjs-node for more details. \n============================"));const a={id:this.nextDataId()};return this.data.set(a,{values:e,dtype:n,refCount:1}),a}makeTensorInfo(t,n,a){let s;if("string"===n&&null!=a&&a.length>0&&e.isString(a[0])){const r=a.map((t=>e.encodeString(t)));s=this.write(r,t,n)}else s=this.write(a,t,n);return{dataId:s,shape:t,dtype:n}}refCount(e){if(this.data.has(e)){return this.data.get(e).refCount}return 0}incRef(e){this.data.get(e).refCount++}decRef(e){if(this.data.has(e)){this.data.get(e).refCount--}}move(e,t,n,a,s){this.data.set(e,{values:t,dtype:a,refCount:s})}numDataIds(){return this.data.numDataIds()}async read(e){return this.readSync(e)}readSync(e){const{dtype:t,complexTensorInfos:n}=this.data.get(e);if("complex64"===t){const e=this.readSync(n.real.dataId),t=this.readSync(n.imag.dataId);return o.mergeRealAndImagArrays(e,t)}return this.data.get(e).values}bufferSync(t){const n=this.readSync(t.dataId);if("string"===t.dtype)try{const a=n.map((t=>e.decodeString(t)));return i(t.shape,t.dtype,a)}catch(e){throw new Error("Failed to decode encoded string bytes into utf-8")}return i(t.shape,t.dtype,n)}makeOutput(e,t,n){return s().makeTensorFromTensorInfo(this.makeTensorInfo(t,n,e),this)}disposeData(e,t=!1){if(this.data.has(e)){if(this.data.get(e).refCount--,!t&&this.data.get(e).refCount>0)return!1;const{complexTensorInfos:n}=this.data.get(e);null!=n&&(this.disposeData(n.real.dataId,!0),this.disposeData(n.imag.dataId,!0)),this.data.delete(e)}return!0}disposeIntermediateTensorInfo(e){this.disposeData(e.dataId)}async time(t){const n=e.now();t();return{kernelMs:e.now()-n}}memory(){return{unreliable:!0,reasons:["The reported memory is an upper bound. Due to automatic garbage collection, the true allocated memory may be less."]}}where(e){Sn([e],"where");const t=this.readSync(e.dataId);return vn(e.shape,t)}dispose(){}floatPrecision(){return 32}epsilon(){return super.epsilon()}}function wn(e){const t=new Float32Array(e.length);for(let n=0;n<e.length;++n)t[n]=Math.abs(e[n]);return t}Fn.nextDataId=0;const Mn={kernelName:l,backendName:"cpu",kernelFunc:t=>{const{x:n}=t.inputs,a=t.backend;Sn(n,"abs");let s=new Float32Array(e.sizeFromShape(n.shape));return s=wn(a.data.get(n.dataId).values),a.makeOutput(s,n.shape,n.dtype)}};function An(t){return(n,a,s,r,i)=>{const l=o.assertAndGetBroadcastShape(n,a),d=l.length,c=e.computeStrides(l),p=e.sizeFromShape(l),u=e.getTypedArrayFromDType(i,p),h=n.length,f=a.length,m=e.computeStrides(n),k=e.computeStrides(a),g=o.getBroadcastDims(n,l),I=o.getBroadcastDims(a,l);if(g.length+I.length===0)for(let e=0;e<u.length;++e)u[e]=t(s[e%s.length],r[e%r.length]);else for(let n=0;n<u.length;++n){const a=e.indexToLoc(n,d,c),o=a.slice(-h);g.forEach((e=>o[e]=0));const i=e.locToIndex(o,h,m),l=a.slice(-f);I.forEach((e=>l[e]=0));const p=e.locToIndex(l,f,k);u[n]=t(s[i],r[p])}return[u,l]}}function Dn(e){const{inputs:t,backend:n}=e,{real:a,imag:s}=t,r=n.data.get(a.dataId).values,o=n.data.get(s.dataId).values,i=n.makeTensorInfo(a.shape,"complex64");return n.data.get(i.dataId).complexTensorInfos={real:n.makeTensorInfo(a.shape,"float32",r),imag:n.makeTensorInfo(s.shape,"float32",o)},i}const En={kernelName:d,backendName:"cpu",kernelFunc:Dn};function zn(t,n,a="float32"){if("complex64"===a){return Dn({inputs:{real:zn(t,n,"float32"),imag:zn(t,n,"float32")},backend:t})}const s=e.makeZerosTypedArray(e.sizeFromShape(n),a);return t.makeTensorInfo(n,a,s)}function Wn(e){const{inputs:t,backend:n}=e,{x:a}=t;return n.incRef(a.dataId),{dataId:a.dataId,shape:a.shape,dtype:a.dtype}}const Rn={kernelName:c,backendName:"cpu",kernelFunc:Wn};function Pn(e){const{inputs:t,backend:n}=e,{input:a}=t,s=n.data.get(a.dataId).complexTensorInfos.real,r=n.data.get(s.dataId).values;return n.makeTensorInfo(s.shape,s.dtype,r)}const Hn={kernelName:p,backendName:"cpu",kernelFunc:Pn};function Cn(t,n,a,s){if("int32"===s){return[n,"int32",Int32Array.from(t)]}if("bool"===s){const s=e.toTypedArray([0],a),[r,o]=An(((e,t)=>e!==t?1:0))(n,[],t,s,"bool");return[o,"bool",r]}throw new Error(`Error in Cast: failed to cast ${a} to ${s}`)}function $n(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{dtype:o}=s;if("complex64"===o){if("complex64"===r.dtype)return Wn({inputs:{x:r},backend:a});const e=zn(a,r.shape,r.dtype),t=$n({inputs:{x:r},backend:a,attrs:{dtype:"float32"}}),n=Dn({inputs:{real:t,imag:e},backend:a});return a.disposeIntermediateTensorInfo(e),a.disposeIntermediateTensorInfo(t),n}if("complex64"===r.dtype){const e=Pn({inputs:{input:r},backend:a}),t=$n({inputs:{x:e},backend:a,attrs:{dtype:o}});return a.disposeIntermediateTensorInfo(e),t}if(!e.hasEncodingLoss(r.dtype,o)){const e=Wn({inputs:{x:r},backend:a});return{dataId:e.dataId,shape:e.shape,dtype:o}}const i=a.data.get(r.dataId).values,[l,d,c]=Cn(i,r.shape,r.dtype,o);return a.makeTensorInfo(l,d,c)}const On={kernelName:u,backendName:"cpu",kernelFunc:$n};function Vn(e,t,n,a){return null==n?({inputs:n,backend:s})=>{const{a:r,b:i}=n,l=s;Sn([r,i],e);const d=l.data.get(r.dataId).values,c=l.data.get(i.dataId).values,p="string"===r.dtype?o.fromUint8ToStringArray(d):d,u="string"===r.dtype?o.fromUint8ToStringArray(c):c,h=a||r.dtype,[f,m]=t(r.shape,i.shape,p,u,h);return l.makeTensorInfo(m,h,f)}:({inputs:e,backend:s})=>{const{a:r,b:o}=e,i=s;if("complex64"===r.dtype||"complex64"===o.dtype){const e=$n({inputs:{x:r},backend:i,attrs:{dtype:"complex64"}}),t=i.data.get(e.dataId),a=t.complexTensorInfos.real,s=t.complexTensorInfos.imag,l=i.data.get(a.dataId).values,d=i.data.get(s.dataId).values,c=$n({inputs:{x:o},backend:i,attrs:{dtype:"complex64"}}),p=i.data.get(c.dataId),u=p.complexTensorInfos.real,h=p.complexTensorInfos.imag,f=i.data.get(u.dataId).values,m=i.data.get(h.dataId).values,[k,g,I]=n(r.shape,o.shape,l,d,f,m),b=i.makeTensorInfo(I,"float32",k),y=i.makeTensorInfo(I,"float32",g),N=Dn({inputs:{real:b,imag:y},backend:i});return i.disposeIntermediateTensorInfo(e),i.disposeIntermediateTensorInfo(c),i.disposeIntermediateTensorInfo(b),i.disposeIntermediateTensorInfo(y),N}{const e=i.data.get(r.dataId).values,n=i.data.get(o.dataId).values,s=a||r.dtype,[l,d]=t(r.shape,o.shape,e,n,s);return i.makeTensorInfo(d,s,l)}}}function _n(t){return(n,a,s,r,i,l)=>{const d=o.assertAndGetBroadcastShape(n,a),c=e.sizeFromShape(d),p=d.length,u=e.computeStrides(d),h=e.getTypedArrayFromDType("float32",c),f=e.getTypedArrayFromDType("float32",c),m=o.getBroadcastDims(n,d),k=o.getBroadcastDims(a,d),g=o.mergeRealAndImagArrays(s,r),I=o.mergeRealAndImagArrays(i,l),b=n.length,y=e.computeStrides(n),N=a.length,T=e.computeStrides(a);if(m.length+k.length===0)for(let e=0;e<h.length;e++){const n=e%g.length,a=e%I.length,s=t(g[2*n],g[2*n+1],I[2*a],I[2*a+1]);h[e]=s.real,f[e]=s.imag}else for(let n=0;n<h.length;n++){const a=e.indexToLoc(n,p,u),s=a.slice(-b);m.forEach((e=>s[e]=0));const r=e.locToIndex(s,b,y),o=a.slice(-N);k.forEach((e=>o[e]=0));const i=e.locToIndex(o,N,T),l=t(g[2*r],g[2*r+1],I[2*i],I[2*i+1]);h[n]=l.real,f[n]=l.imag}return[h,f,d]}}const Gn=An(((e,t)=>e+t)),Bn=Vn(h,Gn,_n(((e,t,n,a)=>({real:e+n,imag:t+a})))),Ln={kernelName:h,backendName:"cpu",kernelFunc:Bn};function qn(t,n,a,s,r){const o=e.sizeFromShape(s),i=e.makeZerosTypedArray(r,a);for(let e=0;e<t.length;e++){const a=t[e];if(a<0)throw new Error("Input x must be non-negative!");a>=r||(i[a]+=o>0?n[e]:1)}return i}function Un(e,t,n,a=!1){const s=e.shape[0],r=e.shape[1],o=i([s,n],t.dtype);for(let i=0;i<s;i++)for(let s=0;s<r;s++){const r=e.get(i,s);if(r<0)throw new Error("Input x must be non-negative!");r>=n||(a?o.set(1,i,r):t.size>0?o.set(o.get(i,r)+t.get(i,s),i,r):o.set(o.get(i,r)+1,i,r))}return o}function Zn(t){return(n,a,s)=>{const r=e.getTypedArrayFromDType(a,n.length);for(let e=0;e<n.length;++e)r[e]=t(n[e],s);return r}}function Kn(t,n,a){return({inputs:s,attrs:r,backend:o})=>{const{x:i}=s;if(Sn(i,t),"string"===i.dtype||"string"===a)throw new Error("unaryKernelFunc does not support string input/output");const l=o,d=l.data.get(i.dataId).values,c=e.sizeFromShape(i.shape),p=a||i.dtype,u=e.getArrayFromDType(p,c);for(let e=0;e<c;++e)u[e]=n(d[e],r);return l.makeTensorInfo(i.shape,p,u)}}function jn(e,t,n){return({inputs:a,attrs:s,backend:r})=>{const{x:o}=a;if(Sn(o,e),"string"===o.dtype||"string"===n)throw new Error("unaryKernelFunc does not support string input/output");const i=r,l=i.data.get(o.dataId).values,d=n||o.dtype,c=t(l,d,s);return i.makeTensorInfo(o.shape,d,c)}}const Yn=Zn((e=>Math.ceil(e))),Jn={kernelName:f,backendName:"cpu",kernelFunc:jn(f,Yn)};function Qn(t,n,a,s){const r=e.getArrayFromDType(a,e.sizeFromShape(n));if(s&&"string"!==a){let n=0;t.forEach((t=>{const a=e.sizeFromShape(t.shape);r.set(t.vals,n),n+=a}))}else{let e=0;t.forEach((t=>{const s="string"===a?o.fromUint8ToStringArray(t.vals):t.vals;let i=0;for(let a=0;a<t.shape[0];++a){const o=a*n[1]+e;for(let e=0;e<t.shape[1];++e)r[o+e]=s[i++]}e+=t.shape[1]}))}return r}const Xn=An(((e,t)=>e===t?1:0)),ea=Vn(m,Xn,null,"bool"),ta={kernelName:m,backendName:"cpu",kernelFunc:ea},na=Zn((e=>Math.exp(e))),aa=jn(k,na,"float32"),sa={kernelName:k,backendName:"cpu",kernelFunc:aa},ra=Zn((e=>Math.expm1(e))),oa={kernelName:g,backendName:"cpu",kernelFunc:jn(g,ra)},ia=Zn((e=>Math.floor(e))),la={kernelName:I,backendName:"cpu",kernelFunc:jn(I,ia)};function da(e,t,n,a,s,r,o,l,d){const c=i([a,r],n);for(let n=0;n<a;n++){const a=[];let i=0;for(let t=0;t<s;t++){const r=e[n*s+t];i+=r*o[t],a.push(r)}if(i<0||i>=d/r)throw new Error(`Invalid indices: ${a} does not index into ${l}`);for(let e=0;e<r;e++)c.values[n*r+e]=t.get(...t.indexToLoc(i*r+e))}return c}function ca(e,t,n){const a=i(n,e.dtype);for(let n=0;n<a.size;++n){const s=a.indexToLoc(n).slice(),r=s[0],o=s[2],i=t.locToIndex([r,o]);s[2]=t.values[i];const l=e.locToIndex(s);0<=l&&l<e.values.length&&(a.values[n]=e.values[l])}return a}const pa=An(((e,t)=>e>t?1:0)),ua={kernelName:b,backendName:"cpu",kernelFunc:Vn(b,pa,null,"bool")},ha=An(((e,t)=>e>=t?1:0)),fa={kernelName:y,backendName:"cpu",kernelFunc:Vn(y,ha,null,"bool")},ma=An(((e,t)=>e<t?1:0)),ka={kernelName:N,backendName:"cpu",kernelFunc:Vn(N,ma,null,"bool")},ga=An(((e,t)=>e<=t?1:0)),Ia={kernelName:T,backendName:"cpu",kernelFunc:Vn(T,ga,null,"bool")};function ba(t,n,a){const s=(n-t)/(a-1),r=e.makeZerosTypedArray(a,"float32");r[0]=t;for(let e=1;e<r.length;e++)r[e]=r[e-1]+s;return r}const ya=Zn((e=>Math.log(e))),Na={kernelName:x,backendName:"cpu",kernelFunc:jn(x,ya)};function Ta(t,n,a,s){const r=e.getTypedArrayFromDType(s,e.sizeFromShape(a));for(let e=0;e<r.length;++e){const a=e*n;let s=t[a];for(let e=0;e<n;++e){const n=t[a+e];(Number.isNaN(n)||n>s)&&(s=n)}r[e]=s}return r}const xa=An(((e,t)=>Math.max(e,t))),Sa={kernelName:S,backendName:"cpu",kernelFunc:Vn(S,xa)},va=An(((e,t)=>Math.min(e,t))),Fa={kernelName:v,backendName:"cpu",kernelFunc:Vn(v,va)},wa=An(((e,t)=>e*t)),Ma=_n(((e,t,n,a)=>({real:e*n-t*a,imag:e*a+t*n}))),Aa=Vn(F,wa,Ma),Da={kernelName:F,backendName:"cpu",kernelFunc:Aa};function Ea(t,n,a){const s=e.createScalarValue(-1,a);return wa([],n,s,t,a)}const za={kernelName:w,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{x:a}=t;Sn(a,"neg");const s=n.data.get(a.dataId).values,[r,o]=Ea(s,a.shape,a.dtype);return n.makeTensorInfo(o,a.dtype,r)}},Wa=An(((e,t)=>e!==t?1:0)),Ra={kernelName:M,backendName:"cpu",kernelFunc:Vn(M,Wa,null,"bool")};function Pa(t,n,a,s,r){const o=n.length,i=e.sizeFromShape(n),l=e.computeStrides(n),d=e.computeStrides(r),c=e.getTypedArrayFromDType(a,e.sizeFromShape(r));for(let n=0;n<i;++n){const a=e.indexToLoc(n,o,l),r=new Array(a.length);for(let e=0;e<r.length;e++)r[e]=a[s[e]];c[e.locToIndex(r,o,d)]=t[n]}return c}function Ha(e){const{inputs:t,attrs:n,backend:a}=e,{x:s}=t,{perm:r}=n;Sn(s,"transpose");const o=s.shape.length,i=new Array(o);for(let e=0;e<i.length;e++)i[e]=s.shape[r[e]];const l=Pa(a.data.get(s.dataId).values,s.shape,s.dtype,r,i);return{dataId:a.write(l,i,s.dtype),shape:i,dtype:s.dtype}}const Ca={kernelName:A,backendName:"cpu",kernelFunc:Ha};function $a(t,n,a,s){const[r,i]=o.computeOutAndReduceShapes(t,s),l=D(n,"int32"),d=e.makeZerosTypedArray(e.sizeFromShape(r),l),c=e.sizeFromShape(i);for(let e=0;e<d.length;++e){const t=e*c;let n=1;for(let e=0;e<c;++e)n*=a[t+e];d[e]=n}return{outVals:d,outShape:r,outDtype:l}}const Oa={kernelName:E,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,keepDims:l}=s;Sn(r,"prod");const d=r.shape.length,c=e.parseAxisParam(i,r.shape),p=o.getAxesPermutation(c,d);let u=c,h=r;const f=[];null!=p&&(h=Ha({inputs:{x:r},backend:a,attrs:{perm:p}}),f.push(h),u=o.getInnerMostAxes(u.length,d));const m=a.data.get(h.dataId).values,{outVals:k,outShape:g,outDtype:I}=$a(h.shape,h.dtype,m,u);let b=g;return l&&(b=o.expandShapeToKeepDim(g,c)),f.forEach((e=>a.disposeIntermediateTensorInfo(e))),a.makeTensorInfo(b,I,k)}};function Va(e,t,n,a){const s=[];let r=0;const o=t.length-1+n.length,i=new Array(o).fill(null).map((()=>[0]));!function(e,t){for(let n=0;n<e.length;++n){const a=e[n],s=n===e.length-1?t:e[n+1].length;if(0===a.length)throw new Error("Ragged splits may not be empty");if(a[0]<0)throw new Error("Ragged splits must be non-negative");if(a[a.length-1]>s)throw new Error("Ragged splits must not point past values");for(let e=1;e<a.length;++e)if(a[e-1]>a[e])throw new Error("Ragged splits must be sorted in ascending order")}}(n,a);let l=1;for(let e=0;e<t.length-1;++e){l*=t[e];const n=t[e+1];for(let t=1;t<l+1;++t)i[e].push(t*n)}for(let a=0;a<e.length;++a){let o=e[a],l=e[a]+1;for(let e=0;e<n.length;++e){const a=n[e],s=e+t.length-1;if(s>=0){const e=i[s],t=e[e.length-1]-a[o];for(let e=o;e<l;++e)i[s].push(a[e+1]+t)}o=a[o],l=a[l]}l!==o&&(s.push([o,l]),r+=l-o)}return{outSplits:i,valueSlices:s,numValues:r}}function _a(e,t){const n=e.slice(0,t);for(;n.length<t;)n.push(1);for(let a=t;a<e.length;a++)n[t-1]*=e[a];return n}function Ga(t,n,a,s,r){const o=n.slice();o[0]=r;const i=e.getArrayFromDType(a,e.sizeFromShape(o)),l=t.length;return function(e,t,n,a,s,r){const o=_a(t,2)[1],i=_a(r,2)[1];let l=0;for(const t of n)for(let n=t[0];n<t[1];++n){for(let t=0;t<a;++t)s[l*i+t]=e[n*o+t];++l}}(t,n,s,0===l?0:l/n[0],i,o),[i,o]}function Ba(t,n,a,s,r,o,i,l){if(0===t.length)throw new Error("paramsNestedSplits must be non empty");if(0===n[0].length)throw new Error("Split tensors must not be scalars");if(function(t,n,a){t.forEach(((t,s)=>{if(t<0||t>=a){const r=e.indexToLoc(s,n.length,e.computeStrides(n)).join(",");throw new Error(`indices[${r}] = ${t} is not in [0, ${a})`)}}))}(o,i,n[0][0]-1),0===s.length)throw new Error("params.rank must be nonzero");const d=s[0],{outSplits:c,valueSlices:p,numValues:u}=Va(o,i,t,d),h=function(t){const n=[];for(let a=0;a<t.length;++a){const s=t[a].length,r=e.getArrayFromDType("int32",s);n.push(r),t[a].forEach(((e,t)=>r[t]=e))}return n}(c),f=Ga(a,s,r,p,u);return[h,f[0],f[1]]}var La=o.RowPartitionType;class qa{constructor(e,t,n,a,s,r,i,l,d,c){this.shape=e,this.shapeShape=t,this.values=n,this.valuesShape=a,this.valuesDType=s,this.defaultValue=r,this.defaultValueShape=i,this.rowPartitionValues=l,this.rowPartitionValuesShapes=d,this.rowPartitionTypes=o.getRowPartitionTypesHelper(c),this.raggedRank=o.getRaggedRank(this.rowPartitionTypes)}getRowPartitionTypeByDimension(e){return this.rowPartitionTypes[0]===La.FIRST_DIM_SIZE?this.rowPartitionTypes[e+1]:this.rowPartitionTypes[e]}getRowPartitionTensor(e){return this.rowPartitionTypes[0]===La.FIRST_DIM_SIZE?this.rowPartitionValues[e+1]:this.rowPartitionValues[e]}getMaxWidth(e){const t=this.getRowPartitionTensor(e-1);switch(this.getRowPartitionTypeByDimension(e-1)){case La.VALUE_ROWIDS:return qa.getMaxWidthValueRowID(t);case La.ROW_SPLITS:return qa.getMaxWidthRowSplit(t);default:throw new Error(`Cannot handle partition type ${La[this.getRowPartitionTypeByDimension(e-1)]}`)}}static getMaxWidthRowSplit(e){const t=e.length;if(0===t||1===t)return 0;let n=0;for(let a=0;a<t-1;++a){const t=e[a+1]-e[a];t>n&&(n=t)}return n}static getMaxWidthValueRowID(e){const t=e.length;if(0===t)return 0;let n=0,a=e[0],s=0;for(let r=1;r<t;++r){const t=e[r];t!==a&&(a=t,s=Math.max(r-n,s),n=r)}return Math.max(t-n,s)}tensorShapeFromTensor(e,t,n=!0){if(0===t.length){if(-1===e[0])return[];throw new Error("The only valid scalar shape tensor is the fully unknown shape specified as -1.")}return Za(e,n)}calculateOutputSize(e){const t=this.valuesShape,n=this.defaultValueShape;o.validateDefaultValueShape(n,t);const a=this.tensorShapeFromTensor(this.shape,this.shapeShape),s=o.combineRaggedTensorToTensorShapes(this.raggedRank,a,t);s[0]<0&&(s[0]=e);for(let e=1;e<=this.raggedRank;++e)s[e]<0&&(s[e]=this.getMaxWidth(e));return s}calculateFirstParentOutputIndex(t,n,a){const s=Math.min(t,a),r=[];let o=0;for(let e=0;e<s;++e,o+=n)r.push(o);for(let e=s;e<t;++e)r.push(-1);return e.assert(r.length===t,(()=>"Final length of result must be equal to firstDimension.")),r}calculateOutputIndexRowSplit(e,t,n,a){const s=e.length,r=[];for(let o=0;o<s-1;++o){const s=e[o+1]-e[o];let i=Math.min(a,s),l=t[o];-1===l&&(i=0);for(let e=0;e<i;++e)r.push(l),l+=n;for(let e=0;e<s-i;++e)r.push(-1)}if(s>0&&r.length!==e[s-1])throw new Error("Invalid row split size.");return r}calculateOutputIndexValueRowID(e,t,n,a){const s=e.length,r=[];if(0===s)return[];let o=0,i=e[0];if(i>=t.length)throw new Error(`Got currentValueRowId=${i}, which is not less than ${t.length}`);let l=t[i];r.push(l);for(let d=1;d<s;++d){const s=e[d];if(s===i)l>=0&&(++o,o<a?l+=n:l=-1);else{if(o=0,i=s,s>=t.length)throw new Error(`Got nextValueRowId=${s} which is not less than ${t.length}`);l=t[s]}r.push(l)}if(r.length!==e.length)throw new Error("Invalid row ids.");return r}calculateOutputIndex(e,t,n,a){const s=this.getRowPartitionTensor(e),r=this.getRowPartitionTypeByDimension(e);switch(r){case La.VALUE_ROWIDS:return this.calculateOutputIndexValueRowID(s,t,n,a);case La.ROW_SPLITS:if(s.length-1>t.length)throw new Error(`Row partition size is greater than output size: ${s.length-1} > ${t.length}`);return this.calculateOutputIndexRowSplit(s,t,n,a);default:throw new Error(`Unsupported partition type: ${La[r]}`)}}getFirstDimensionSize(){const e=this.rowPartitionValues[0];if(0===this.rowPartitionTypes.length)throw new Error("No row_partition_types given.");const t=this.rowPartitionTypes[0];switch(t){case La.FIRST_DIM_SIZE:return e[0];case La.VALUE_ROWIDS:throw new Error("Cannot handle VALUE_ROWIDS in first dimension.");case La.ROW_SPLITS:return this.rowPartitionValuesShapes[0][0]-1;default:throw new Error(`Cannot handle type ${La[t]}`)}}compute(){if(this.rowPartitionValues[0].length<=0)throw new Error("Invalid first partition input. Tensor requires at least one element.");const t=this.getFirstDimensionSize(),n=this.calculateOutputSize(t),a=new Array(this.raggedRank+1);a[a.length-1]=1;for(let e=a.length-2;e>=0;--e)a[e]=a[e+1]*n[e+1];const s=Za(n,!1),r=e.getArrayFromDType(this.valuesDType,e.sizeFromShape(s));if(a[0]*n[0]>0){let e=this.calculateFirstParentOutputIndex(t,a[0],n[0]);for(let t=1;t<=this.raggedRank;++t){e=this.calculateOutputIndex(t-1,e,a[t],n[t])}this.setOutput(this.raggedRank,e,r,s)}return[s,r]}setOutput(t,n,a,s){if(0===a.length)return;const r=this.values,o=a;let i=s.slice();i=i.slice(t+1);const l=e.sizeFromShape(i),d=n.length;let c=this.defaultValue;if(c.length!==l&&1!==c.length){const e=this.defaultValueShape;z((()=>{const t=W(c,e),n=R(t,i);c=n.dataSync()}))}let p=0,u=0,h=0;for(let e=0;e<=d;++e){let t=e<d?n[e]:-1;if(t!==h){if(u<h){const e=r.subarray(p*l);Ua(o.subarray(u*l),e,(h-u)*l)}if(e>=d){const e=a.length;t=Math.floor(e/l)}if(t>h)if(1===this.defaultValue.length)o.subarray(h*l,t*l).fill(this.defaultValue[0]),h=t;else for(;t>h;){Ua(o.slice(h*l),c,l),++h}t<0?(p=e+1,u=h):(p=e,u=h,h=u+1)}else++h}}}function Ua(e,t,n){for(let a=0;a<n;a++)e[a]=t[a]}function Za(e,t){const n=[];for(let a of e){if(a<0){if(!t)throw new Error(`Dimension ${a} must be >= 0`);if(a<-1)throw new Error(`Dimension ${a} must be >= -1`);a=-1}n.push(a)}return n}function Ka(e,t,n,a,s,r,o,i,l,d){return new qa(e,t,n,a,s,r,o,i,l,d).compute()}function ja(t,n,a,s){if(t===n||t<n&&a<0||n<t&&a>1)return e.makeZerosTypedArray(0,s);const r=Math.abs(Math.ceil((n-t)/a)),o=e.makeZerosTypedArray(r,s);n<t&&1===a&&(a=-1),o[0]=t;for(let e=1;e<o.length;e++)o[e]=o[e-1]+a;return o}const Ya=Zn((e=>1/Math.sqrt(e))),Ja={kernelName:P,backendName:"cpu",kernelFunc:jn(P,Ya)};function Qa(e,t,n,a,s,r,o,l,d,c){const p=[a/s,s],u=e.values,h=t.values;if(0===a)return i(n,t.dtype);const f=i(p,t.dtype);"string"==typeof d||"number"==typeof d?f.values.fill(d):"boolean"==typeof d&&f.values.fill(+d);for(let e=0;e<r;e++){const r=[];let i=0;for(let t=0;t<o;t++){const n=u[e*o+t];r.push(n),i+=n*l[t]}if(i<0||i>=a/s)throw new Error(`Invalid indices: ${r} does not index into ${n}`);for(let n=0;n<s;n++)c?f.values[i*s+n]+=h[e*s+n]:f.values[i*s+n]=0===t.rank?h[0]:h[e*s+n]}return f}const Xa=Zn((e=>1/(1+Math.exp(-e)))),es=Kn(H,(e=>1/(1+Math.exp(-e)))),ts={kernelName:H,backendName:"cpu",kernelFunc:es};function ns(t,n,a,s,r){const l=C.isSliceContinous(s,n,a),d=e.sizeFromShape(a),c=e.computeStrides(s);if(l){const e=C.computeFlatOffset(n,c);return"string"===r?t.slice(e,e+d):t.subarray(e,e+d)}const p="string"===r?o.fromUint8ToStringArray(t):t,u=i(s,r,p),h=i(a,r);for(let e=0;e<h.size;++e){const t=h.indexToLoc(e),a=t.map(((e,t)=>e+n[t]));h.set(u.get(...a),...t)}return"string"===r?o.fromStringArrayToUint8(h.values):h.values}function as(e){const{inputs:t,backend:n,attrs:a}=e,{x:s}=t,{begin:r,size:o}=a;Sn(s,"slice");const[i,l]=C.parseSliceParams(s,r,o);C.assertParamsValid(s,i,l);const d=ns(n.data.get(s.dataId).values,i,l,s.shape,s.dtype);return n.makeTensorInfo(l,s.dtype,d)}const ss={kernelName:$,backendName:"cpu",kernelFunc:as};function rs(t,n,a,s,r,i,l){const d=n[0],c=i[0],p=new Array(c),u=new Array(d),h=n[1];if(0===c){if(0!==d)throw new Error(o.getSparseFillEmptyRowsIndicesDenseShapeMismatch(d));return[e.getArrayFromDType(a,0),[0,h],e.getArrayFromDType(r,0),p,u]}let f=!0,m=0;const k=new Array(c).fill(0);for(let e=0;e<d;++e){const n=t[e*h];if(n<0)throw new Error(o.getSparseFillEmptyRowsNegativeIndexErrorMessage(e,n));if(n>=c)throw new Error(o.getSparseFillEmptyRowsOutOfRangeIndexErrorMessage(e,n,c));++k[n],f=f&&n>=m,m=n}let g=!0;for(let e=0;e<c;++e){const t=0===k[e];p[e]=t,g=g&&!t,k[e]=Math.max(k[e],1),e>0&&(k[e]+=k[e-1])}if(g&&f){const e=t,n=s;for(let e=0;e<d;++e)u[e]=e;return[e,[d,h],n,p,u]}{const n=k[c-1],o=e.getArrayFromDType(a,n*h),i=e.getArrayFromDType(r,n),f=new Array(c).fill(0);for(let e=0;e<d;++e){const n=t[e*h],a=f[n],r=(0===n?0:k[n-1])+a;f[n]++;for(let n=0;n<h;++n)o[r*h+n]=t[e*h+n];i[r]=s[e],u[e]=r}for(let e=0;e<c;++e){if(0===f[e]){const t=0===e?0:k[e-1];o[t*h+0]=e;for(let e=1;e<h;++e)o[t*h+e]=0;i[t]=l}}return[o,[n,h],i,p,u]}}function os(t,n,a,s,r){const i=e.sizeFromShape(s),l=n[0],d=r.length,c=[];let p=1,u=-1;for(let e=0;e<d;++e){const t=r[e];if(-1===t){if(-1!==u)throw new Error(o.getSparseReshapeMultipleNegativeOneOutputDimErrorMessage(u,e));u=e,c.push(1)}else{if(t<0)throw new Error(o.getSparseReshapeNegativeOutputDimErrorMessage(e,t));p*=t,c.push(t)}}if(-1!==u){if(p<=0)throw new Error(o.getSparseReshapeEmptyTensorZeroOutputDimErrorMessage());const e=Math.trunc(i/p);if(p*e!==i)throw new Error(o.getSparseReshapeInputOutputMultipleErrorMessage(s,c));c[u]=e}if(e.sizeFromShape(c)!==i)throw new Error(o.getSparseReshapeInputOutputMismatchErrorMessage(s,c));const h=s.length,f=[];if(h>0){f[h-1]=1;for(let e=h-2;e>=0;--e)f[e]=f[e+1]*s[e+1]}const m=[];if(d>0){m[d-1]=1;for(let e=d-2;e>=0;--e)m[e]=m[e+1]*c[e+1]}const k=e.getArrayFromDType(a,l*d);for(let e=0;e<l;++e){let n=0;for(let a=0;a<h;++a)n+=t[e*h+a]*f[a];for(let t=0;t<d;++t)k[e*d+t]=Math.trunc(n/m[t]),n%=m[t]}return[k,[l,d],c]}function is(t,n,a,s,r,i=!1,l=0){const d=s.length,c=[n[0],t.length/n[0]],p=c[1],u=d>0?r[d-1]+1:0;if(u<0)throw new Error(o.getSparseSegmentReductionNegativeSegmentIdsErrorMessage());const h=n.slice();h[0]=u;const f=h.reduce(((e,t)=>e*t),1),m=e.getArrayFromDType(a,f);if(0===d)return u>0&&m.fill(l),[m,h];if(u<=0)throw new Error(o.getSparseSegmentReductionNegativeSegmentIdsErrorMessage());let k=0,g=1,I=0,b=r[k];for(;;){let e=0;if(g<d){if(e=r[g],b===e){++g;continue}if(b>=e)throw new Error(o.getSparseSegmentReductionNonIncreasingSegmentIdsErrorMessage())}if(b<0||b>=u)throw new Error(o.getSparseSegmentReductionSegmentIdOutOfRangeErrorMessage(b,u));b>I&&m.fill(l,I*p,b*p);for(let e=k;e<g;++e){const n=s[e];if(n<0||n>=c[0])throw new Error(o.getSparseSegmentReductionIndicesOutOfRangeErrorMessage(e,s[e],c[0]));for(let e=0;e<p;e++)m[b*p+e]+=t[n*p+e]}if(i)for(let e=0;e<p;e++)m[b*p+e]/=g-k;if(k=g,++g,I=b+1,b=e,g>d)break}return I<u&&m.fill(l,I*p,u*p),[m,h]}const ls=Zn((e=>Math.sqrt(e))),ds={kernelName:O,backendName:"cpu",kernelFunc:Kn(O,(e=>Math.sqrt(e)))},cs=An(((e,t)=>{const n=e-t;return n*n})),ps={kernelName:V,backendName:"cpu",kernelFunc:Vn(V,cs)};function us(e,t,n,a){const s=i(e,t.dtype);for(let e=0;e<s.size;e++){const r=s.indexToLoc(e),o=new Array(r.length);for(let e=0;e<o.length;e++)o[e]=r[e]*n[e]+a[e];s.set(t.get(...o),...r)}return s}class hs{constructor(t,n,a,s,r,o){this.separator=e.encodeString(t),this.nGramWidths=n,this.leftPad=e.encodeString(a),this.rightPad=e.encodeString(s),this.padWidth=r,this.preserveShort=o}getPadWidth(e){return Math.min(this.padWidth<0?e-1:this.padWidth,e-1)}getNumNGrams(e,t){const n=this.getPadWidth(t);return Math.max(0,e+2*n-t+1)}createNGrams(e,t,n,a,s,r){for(let o=0;o<s;++o){const i=this.getPadWidth(r),l=Math.max(0,i-o),d=Math.max(0,i-(s-(o+1))),c=r-(l+d),p=t+(l>0?0:o-i);let u=0;u+=l*this.leftPad.length;for(let t=0;t<c;++t)u+=e[p+t].length;u+=d*this.rightPad.length;u+=(l+d+c-1)*this.separator.length,n[a+o]=new Uint8Array(u);const h=n[a+o];let f=0;const m=e=>e.forEach((e=>h[f++]=e));for(let e=0;e<l;++e)m(this.leftPad),m(this.separator);for(let t=0;t<c-1;++t)m(e[p+t]),m(this.separator);if(c>0){m(e[p+c-1]);for(let e=0;e<d;++e)m(this.separator),m(this.rightPad)}else{for(let e=0;e<d-1;++e)m(this.rightPad),m(this.separator);m(this.rightPad)}}}compute(t,n){const a=t.length,s=n.length;if(s>0){let e=n[0];if(0!==e)throw new Error(`First split value must be 0, got ${e}`);for(let t=1;t<s;++t){let s=n[t]>=e;if(s=s&&n[t]<=a,!s)throw new Error(`Invalid split value ${n[t]}, must be in [${e}, ${a}]`);e=n[t]}if(e!==a)throw new Error(`Last split value must be data size. Expected ${a}, got ${e}`)}const r=s-1,o=e.getArrayFromDType("int32",s);if(0===a||0===s){const e=new Array(a);for(let e=0;e<=r;++e)o[e]=0;return[e,o]}o[0]=0;for(let e=1;e<=r;++e){const t=n[e]-n[e-1];let a=0;this.nGramWidths.forEach((e=>{a+=this.getNumNGrams(t,e)})),this.preserveShort&&t>0&&0===a&&(a=1),o[e]=o[e-1]+a}const i=new Array(o[r]);for(let e=0;e<r;++e){const a=n[e];let s=o[e];if(this.nGramWidths.forEach((r=>{const o=n[e+1]-n[e],l=this.getNumNGrams(o,r);this.createNGrams(t,a,i,s,l,r),s+=l})),this.preserveShort&&s===o[e]){const r=n[e+1]-n[e];if(0===r)continue;const o=r+2*this.padWidth,l=1;this.createNGrams(t,a,i,s,l,o)}}return[i,o]}}function fs(e,t,n,a,s,r,o,i){return new hs(n,a,s,r,o,i).compute(e,t)}function ms(e,t,n,a){if(!e.length)return;if(0===t.length){for(let t=0;t<e.length;++t)a.push(e.subarray(t,t+1));return}if(1===t.length){const s=t[0];let r=e.indexOf(s);for(;-1!==r;){const t=e.subarray(0,r);n&&0===t.length||a.push(t),r=(e=e.subarray(r+1)).indexOf(s)}return void(n&&0===e.length||a.push(e))}let s=0;for(let r=0;r<e.length+1;r++)if(r===e.length||-1!==t.indexOf(e[r])){const t=e.subarray(s,r);n&&0===t.length||a.push(t),s=r+1}}function ks(t,n,a){const s=t.length,r=[];let o=0,i=0;const l=new Array(s);for(let e=0;e<s;++e){const s=r.length;ms(t[e],n,a,r);const d=r.length-s;l[e]=d,o+=d,i=Math.max(i,d)}const d=e.getArrayFromDType("int32",2*o),c=new Array(o),p=[s,i];let u=0;for(let e=0;e<s;++e)for(let t=0;t<l[e];++t)d[2*u]=e,d[2*u+1]=t,c[u]=r[u],++u;return[d,c,p]}function gs(t,n){const a=e.getArrayFromDType("int32",t.length);for(let s=0;s<t.length;++s)a[s]=e.fingerPrint64(t[s]).modulo(n).getLowBitsUnsigned();return a}const Is=An(((e,t)=>e-t)),bs=Vn(_,Is,_n(((e,t,n,a)=>({real:e-n,imag:t-a})))),ys={kernelName:_,backendName:"cpu",kernelFunc:bs};function Ns(e,t){const n=new Array(e.rank);for(let a=0;a<n.length;a++)n[a]=e.shape[a]*t[a];const a=i(n,e.dtype);for(let t=0;t<a.values.length;++t){const n=a.indexToLoc(t),s=new Array(e.rank);for(let t=0;t<s.length;t++)s[t]=n[t]%e.shape[t];const r=e.locToIndex(s);a.values[t]=e.values[r]}return a}const Ts=(e,t)=>{const n=t.value-e.value;return 0===n?e.index-t.index:n};function xs(t,n,a=0,s=t.length-1){for(;s>a;){if(s-a>600){const e=s-a+1,r=n-a+1,o=Math.log(e),i=.5*Math.exp(2*o/3),l=.5*Math.sqrt(o*i*(e-i)/e)*Math.sign(r-e/2);xs(t,n,Math.max(a,Math.floor(n-r*i/e+l)),Math.min(s,Math.floor(n+(e-r)*i/e+l)))}const r=t[n];let o=a,i=s;for(e.swap(t,a,n),Ts(t[s],r)>0&&e.swap(t,a,s);o<i;){for(e.swap(t,o,i),o++,i--;Ts(t[o],r)<0;)o+=1;for(;Ts(t[i],r)>0;)i-=1}0===Ts(t[a],r)?e.swap(t,a,i):(i+=1,e.swap(t,i,s)),i<=n&&(a=i+1),n<=i&&(s=i-1)}}function Ss(t,n,a,s,r){const o=n[n.length-1],[l,d]=[t.length/o,o],c=e.getTypedArrayFromDType(a,l*s),p=e.getTypedArrayFromDType("int32",l*s);for(let e=0;e<l;e++){const n=e*d,a=t.subarray(n,n+d);let o=new Array(a.length);a.forEach(((e,t)=>o[t]={value:e,index:t})),s<o.length&&(xs(o,s),o=o.slice(0,s)),r&&o.sort(Ts);const i=e*s,l=c.subarray(i,i+s),u=p.subarray(i,i+s);for(let e=0;e<s;e++)l[e]=o[e].value,u[e]=o[e].index}const u=n.slice();return u[u.length-1]=s,[i(u,a,c),i(u,"int32",p)]}function vs(t,n,a,s){const r=e.parseAxisParam(n,a)[0],o=[1,a[0],1];for(let e=0;e<r;e++)o[0]*=a[e];o[1]=a[r];for(let e=r+1;e<a.length;e++)o[2]*=a[e];const i={},l=new Int32Array(a[r]),d=new G(o,s,t),c=[],p=1===o[0]&&1===o[2];for(let e=0;e<a[r];e++){let n;if(p)n=t[e].toString();else{const t=[];for(let n=0;n<o[0];n++)for(let a=0;a<o[2];a++)t.push(d.get(n,e,a));n=t.join(",")}if(void 0!==i[n])l[e]=i[n];else{const t=Object.keys(i).length;i[n]=t,l[e]=t,c.push(e)}}const u=o.slice();u[1]=Object.keys(i).length;const h=new G(u,s);c.forEach(((e,t)=>{for(let n=0;n<o[0];n++)for(let a=0;a<o[2];a++)h.set(d.get(n,e,a),n,t,a)}));const f=a.slice();return f[r]=u[1],{outputValues:h.values,outputShape:f,indices:l}}var Fs={__proto__:null,simpleAbsImpl:wn,addImpl:Gn,bincountImpl:qn,bincountReduceImpl:Un,castImpl:Cn,ceilImpl:Yn,concatImpl:Qn,equalImpl:Xn,expImpl:na,expm1Impl:ra,floorImpl:ia,gatherNdImpl:da,gatherV2Impl:ca,greaterImpl:pa,greaterEqualImpl:ha,lessImpl:ma,lessEqualImpl:ga,linSpaceImpl:ba,logImpl:ya,maxImpl:Ta,maximumImpl:xa,minimumImpl:va,multiplyImpl:wa,negImpl:Ea,notEqualImpl:Wa,prodImpl:$a,raggedGatherImpl:Ba,raggedTensorToTensorImpl:Ka,rangeImpl:ja,rsqrtImpl:Ya,scatterImpl:Qa,sigmoidImpl:Xa,sliceImpl:ns,sparseFillEmptyRowsImpl:rs,sparseReshapeImpl:os,sparseSegmentReductionImpl:is,sqrtImpl:ls,squaredDifferenceImpl:cs,stridedSliceImpl:us,stringNGramsImpl:fs,stringSplitImpl:ks,stringToHashBucketFastImpl:gs,subImpl:Is,tileImpl:Ns,topKImpl:Ss,transposeImpl:Pa,uniqueImpl:vs};const ws="3.21.0";B("cpu",(()=>new Fn),1);const Ms=Kn(L,(e=>e>=0?e:Math.exp(e)-1)),As={kernelName:L,backendName:"cpu",kernelFunc:Ms};function Ds(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{alpha:o}=s;Sn([r],"leakyRelu");const i=e.sizeFromShape(r.shape),l=a.data.get(r.dataId).values,d=e.getTypedArrayFromDType("float32",i);for(let e=0;e<l.length;e++)d[e]=l[e]<0?o*l[e]:l[e];return a.makeTensorInfo(r.shape,"float32",d)}const Es={kernelName:q,backendName:"cpu",kernelFunc:Ds},zs=An(((e,t)=>e<0?t*e:e));function Ws(e){const{inputs:t,backend:n}=e,{x:a,alpha:s}=t;Sn([a,s],"prelu");const r=n.data.get(a.dataId).values,o=n.data.get(s.dataId).values,[i,l]=zs(a.shape,s.shape,r,o,"float32");return n.makeTensorInfo(l,"float32",i)}const Rs={kernelName:U,backendName:"cpu",kernelFunc:Ws},Ps=Kn(Z,(e=>Math.max(0,e))),Hs={kernelName:Z,backendName:"cpu",kernelFunc:Ps},Cs=Kn(K,(e=>Math.min(Math.max(0,e),6))),$s={kernelName:K,backendName:"cpu",kernelFunc:Cs};function Os(e,t,n,a,s){if("linear"===n)return Wn({inputs:{x:t},backend:e});if("relu"===n)return Ps({inputs:{x:t},backend:e});if("elu"===n)return Ms({inputs:{x:t},backend:e});if("relu6"===n)return Cs({inputs:{x:t},backend:e});if("prelu"===n)return Ws({inputs:{x:t,alpha:a},backend:e});if("leakyrelu"===n)return Ds({inputs:{x:t},backend:e,attrs:{alpha:s}});if("sigmoid"===n)return es({inputs:{x:t},backend:e});throw new Error(`Activation ${n} has not been implemented for the CPU backend.`)}function Vs(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{shape:o}=s,i=e.sizeFromShape(r.shape),l=e.inferFromImplicitShape(o,i),d=e.sizeFromShape(l);e.assert(i===d,(()=>`The new shape (${l}) has ${d} elements and the old shape (${r.shape}) has ${i} elements. The new shape and old shape must have the same number of elements.`)),a.incRef(r.dataId);const c=a.data.get(r.dataId);if(null!=c.complexTensorInfos){const e=c.complexTensorInfos.real,t=c.complexTensorInfos.imag;e.shape=l,t.shape=l}return{dataId:r.dataId,shape:l,dtype:r.dtype}}const _s={kernelName:j,backendName:"cpu",kernelFunc:Vs};function Gs(t){const{inputs:n,backend:a,attrs:s}=t,{a:r,b:o}=n,{transposeA:l,transposeB:d}=s;Sn([r,o],"matMul");const c=r.shape.length,p=o.shape.length,u=l?r.shape[c-2]:r.shape[c-1],h=d?o.shape[p-1]:o.shape[p-2],f=l?r.shape[c-1]:r.shape[c-2],m=d?o.shape[p-2]:o.shape[p-1],k=r.shape.slice(0,-2),g=o.shape.slice(0,-2),I=e.sizeFromShape(k),b=e.sizeFromShape(g),y=J.assertAndGetBroadcastShape(r.shape.slice(0,-2),o.shape.slice(0,-2)).concat([f,m]);e.assert(u===h,(()=>`Error in matMul: inner shapes (${u}) and (${h}) of Tensors with shapes ${r.shape} and ${o.shape} and transposeA=${l} and transposeB=${d} must match.`));const N=d?[b,m,h]:[b,h,m],T=Vs({inputs:{x:r},backend:a,attrs:{shape:l?[I,u,f]:[I,f,u]}}),x=Vs({inputs:{x:o},backend:a,attrs:{shape:N}}),S=l?T.shape[1]:T.shape[2],v=l?T.shape[2]:T.shape[1],F=d?x.shape[1]:x.shape[2],w=Math.max(I,b),M=a.data.get(T.dataId).values,A=a.data.get(x.dataId).values,D=e.computeStrides(T.shape),E=e.computeStrides(x.shape),[z,W,R]=l?[D[0],1,D[1]]:[D[0],D[1],1],[P,H,C]=d?[1,E[1],E[0]]:[E[1],1,E[0]],$=v*F,O=i([w,v,F],T.dtype),V=O.values,_=a.blockSize;for(let e=0;e<w;e++)for(let t=0;t<v;t+=_)for(let n=0;n<F;n+=_)for(let a=0;a<S;a+=_){const s=Math.min(t+_,v),r=Math.min(n+_,F),o=Math.min(a+_,S);for(let i=t;i<s;i++)for(let t=n;t<r;t++){let n=0;for(let s=a;s<o;s++){const a=Math.min(e,I-1)*z,r=Math.min(e,b-1)*C;n+=M[a+i*W+s*R]*A[s*P+t*H+r]}V[e*$+(i*F+t)]+=n}}return a.disposeIntermediateTensorInfo(T),a.disposeIntermediateTensorInfo(x),a.makeTensorInfo(y,O.dtype,O.values)}const Bs={kernelName:Y,backendName:"cpu",kernelFunc:Gs};const Ls={kernelName:Q,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{a:s,b:r,bias:o,preluActivationWeights:i}=t,{transposeA:l,transposeB:d,activation:c,leakyreluAlpha:p}=a;let u,h,f;const m=[];u=Gs({inputs:{a:s,b:r},attrs:{transposeA:l,transposeB:d},backend:n}),o&&(h=Bn({inputs:{a:u,b:o},backend:n}),m.push(u),u=h),c&&(f=Os(n,u,c,i,p),m.push(u),u=f);for(const e of m)n.disposeIntermediateTensorInfo(e);return u}},qs={kernelName:X,backendName:"cpu",kernelFunc:Kn(X,(e=>Math.acos(e)))},Us={kernelName:ee,backendName:"cpu",kernelFunc:Kn(ee,(e=>Math.acosh(e)))};const Zs={kernelName:te,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,a=t;Sn(t,"addN");const s=a.map((e=>n.data.get(e.dataId).values)),r=i(a[0].shape,a[0].dtype),o=r.values;for(let e=0;e<a.length;e++){const t=s[e];for(let e=0;e<o.length;e++)o[e]+=t[e]}return n.makeTensorInfo(r.shape,r.dtype,r.values)}};const Ks={kernelName:ne,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,keepDims:l}=s;Sn(r,"all");const d=e.parseAxisParam(i,r.shape);let c=d;const p=o.getAxesPermutation(c,r.shape.length);let u=r;null!=p&&(u=Ha({inputs:{x:r},backend:a,attrs:{perm:p}}),c=o.getInnerMostAxes(c.length,r.shape.length)),o.assertAxesAreInnerMostDims("all",c,u.shape.length);const[h,f]=o.computeOutAndReduceShapes(u.shape,c),m=e.sizeFromShape(f),k=e.makeZerosTypedArray(e.sizeFromShape(h),u.dtype),g=a.data.get(u.dataId).values;for(let e=0;e<k.length;++e){const t=e*m;let n=g[t];for(let e=0;e<m;++e){const a=g[t+e];n=n&&a}k[e]=n}null!=p&&a.disposeIntermediateTensorInfo(u);const I=a.makeTensorInfo(h,u.dtype,k);if(l){const e=Vs({inputs:{x:I},backend:a,attrs:{shape:o.expandShapeToKeepDim(h,d)}});return a.disposeIntermediateTensorInfo(I),e}return I}};const js={kernelName:ae,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,keepDims:l}=s;Sn(r,"any");const d=e.parseAxisParam(i,r.shape);let c=d;const p=o.getAxesPermutation(c,r.shape.length);let u=r;null!=p&&(u=Ha({inputs:{x:r},backend:a,attrs:{perm:p}}),c=o.getInnerMostAxes(c.length,r.shape.length)),o.assertAxesAreInnerMostDims("any",c,u.shape.length);const[h,f]=o.computeOutAndReduceShapes(u.shape,c),m=e.sizeFromShape(f),k=e.makeZerosTypedArray(e.sizeFromShape(h),u.dtype),g=a.data.get(u.dataId).values;for(let e=0;e<k.length;++e){const t=e*m;let n=g[t];for(let e=0;e<m;++e){const a=g[t+e];n=n||a}k[e]=n}null!=p&&a.disposeIntermediateTensorInfo(u);const I=a.makeTensorInfo(h,u.dtype,k);if(l){const e=Vs({inputs:{x:I},backend:a,attrs:{shape:o.expandShapeToKeepDim(h,d)}});return a.disposeIntermediateTensorInfo(I),e}return I}};const Ys={kernelName:se,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i}=s;Sn(r,"argMax");let l=e.parseAxisParam(i,r.shape);const d=o.getAxesPermutation(l,r.shape.length);let c=r;const p=[];null!=d&&(c=Ha({inputs:{x:r},backend:a,attrs:{perm:d}}),p.push(c),l=o.getInnerMostAxes(l.length,c.shape.length)),l=[l[0]],o.assertAxesAreInnerMostDims("argMax",l,c.shape.length);const[u,h]=o.computeOutAndReduceShapes(c.shape,l),f=e.sizeFromShape(u),m=e.makeZerosTypedArray(f,"int32"),k=e.sizeFromShape(h),g=a.data.get(c.dataId).values;for(let e=0;e<m.length;++e){const t=e*k;let n=g[t],a=0;for(let e=0;e<k;++e){const s=g[t+e];s>n&&(n=s,a=e)}m[e]=a}return p.forEach((e=>a.disposeIntermediateTensorInfo(e))),a.makeTensorInfo(u,"int32",m)}};const Js={kernelName:re,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i}=s;Sn(r,"argMin");let l=e.parseAxisParam(i,r.shape);const d=o.getAxesPermutation(l,r.shape.length);let c=r;const p=[];null!=d&&(c=Ha({inputs:{x:r},backend:a,attrs:{perm:d}}),p.push(c),l=o.getInnerMostAxes(l.length,c.shape.length)),l=[l[0]],o.assertAxesAreInnerMostDims("argMin",l,c.shape.length);const[u,h]=o.computeOutAndReduceShapes(c.shape,l),f=e.sizeFromShape(u),m=e.makeZerosTypedArray(f,"int32"),k=e.sizeFromShape(h),g=a.data.get(c.dataId).values;for(let e=0;e<m.length;++e){const t=e*k;let n=g[t],a=0;for(let e=0;e<k;++e){const s=g[t+e];s<n&&(n=s,a=e)}m[e]=a}return p.forEach((e=>a.disposeIntermediateTensorInfo(e))),a.makeTensorInfo(u,"int32",m)}},Qs={kernelName:oe,backendName:"cpu",kernelFunc:Kn(oe,(e=>Math.asin(e)))},Xs={kernelName:ie,backendName:"cpu",kernelFunc:Kn(ie,(e=>Math.asinh(e)))},er={kernelName:le,backendName:"cpu",kernelFunc:Kn(le,(e=>Math.atan(e)))},tr={kernelName:de,backendName:"cpu",kernelFunc:Vn(de,An(((e,t)=>Math.atan2(e,t))))},nr={kernelName:ce,backendName:"cpu",kernelFunc:Kn(ce,(e=>Math.atanh(e)))};function ar(e,t,n,a,s,r){const o=s.strideHeight,l=s.strideWidth,d=s.dilationHeight,c=s.dilationWidth,p=s.effectiveFilterHeight,u=s.effectiveFilterWidth,h=s.padInfo.top,f=s.padInfo.left,m="max"===r?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,k=i(s.outShape,n),g=k.values,I=s.outShape[1]*s.outShape[2]*s.outShape[3],b=s.outShape[2]*s.outShape[3],y=s.outShape[3];for(let t=0;t<s.batchSize;++t){const n=t*I,i=t*a[0];for(let t=0;t<s.inChannels;++t)for(let k=0;k<s.outHeight;++k){const I=k*o-h,N=Math.max(0,I),T=Math.min(s.inHeight,p+I),x=n+k*b;for(let n=0;n<s.outWidth;++n){const o=n*l-f,p=Math.max(0,o),h=Math.min(s.inWidth,u+o);let k=m,I=0,b=0;for(let n=N;n<T;n+=d){const s=i+n*a[1];for(let n=p;n<h;n+=c){const o=e[s+n*a[2]+t];"max"===r&&o>k?k=o:"avg"===r&&(I+=o,b++)}if(isNaN(k))break}g[x+n*y+t]="avg"===r?I/b:k}}}return k}function sr(e,t,n,a,s=!1,r=!1){const o=i(a.outShape,"int32"),l=a.strideHeight,d=a.strideWidth,c=a.dilationHeight,p=a.dilationWidth,u=a.effectiveFilterHeight,h=a.effectiveFilterWidth,f=a.padInfo.top,m=a.padInfo.left,k=i(t,n,e);for(let e=0;e<a.batchSize;++e)for(let t=0;t<a.inChannels;++t)for(let n=0;n<a.outHeight;++n){const i=n*l-f;let g=i;for(;g<0;)g+=c;const I=Math.min(a.inHeight,u+i);for(let l=0;l<a.outWidth;++l){const u=l*d-m;let f=u;for(;f<0;)f+=p;const b=Math.min(a.inWidth,h+u);let y=Number.NEGATIVE_INFINITY,N=-1;for(let n=g;n<I;n+=c){const o=n-i;for(let i=f;i<b;i+=p){const l=i-u,d=k.get(e,n,i,t);d>y&&(y=d,N=s?r?((e*a.inHeight+n)*a.inWidth+i)*a.inChannels+t:(n*a.inWidth+i)*a.inChannels+t:o*h+l)}}o.set(N,e,n,l,t)}}return o}function rr(e,t,n,a,s,r){const o=s.strideDepth,l=s.strideHeight,d=s.strideWidth,c=s.dilationDepth,p=s.dilationHeight,u=s.dilationWidth,h=s.effectiveFilterDepth,f=s.effectiveFilterHeight,m=s.effectiveFilterWidth,k=s.padInfo.front,g=s.padInfo.top,I=s.padInfo.left,b="max"===r?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,y=i(s.outShape,n),N=y.values,T=s.outShape[1]*s.outShape[2]*s.outShape[3]*s.outShape[4],x=s.outShape[2]*s.outShape[3]*s.outShape[4],S=s.outShape[3]*s.outShape[4],v=s.outShape[4];for(let t=0;t<s.batchSize;++t){const n=t*T,i=t*a[0];for(let t=0;t<s.inChannels;++t)for(let y=0;y<s.outDepth;++y){const T=y*o-k;let F=T;for(;F<0;)F+=c;const w=Math.min(s.inDepth,h+T),M=n+y*x;for(let n=0;n<s.outHeight;++n){const o=n*l-g;let h=o;for(;h<0;)h+=p;const k=Math.min(s.inHeight,f+o),y=M+n*S;for(let n=0;n<s.outWidth;++n){const o=n*d-I;let l=o;for(;l<0;)l+=u;const f=Math.min(s.inWidth,m+o),g=y+n*v;let T=b,x=0,S=0;for(let n=F;n<w;n+=c){const s=i+n*a[1];for(let n=h;n<k;n+=p){const o=s+n*a[2];for(let n=l;n<f;n+=u){const s=e[o+n*a[3]+t];if("max"===r&&s>T?T=s:"avg"===r&&(x+=s,S++),isNaN(T))break}if(isNaN(T))break}if(isNaN(T))break}N[g+t]="avg"===r?x/S:T}}}}return y}const or={kernelName:pe,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n;Sn(r,"avgPool");const{filterSize:i,strides:l,pad:d,dimRoundingMode:c}=s;e.assert(o.eitherStridesOrDilationsAreOne(l,1),(()=>`Error in avgPool: Either strides or dilations must be 1. Got strides ${l} and dilations '1'`));const p=o.computePool2DInfo(r.shape,i,l,1,d,c);let u;if(1===p.filterWidth&&1===p.filterHeight&&e.arraysEqual(p.inShape,p.outShape))u=Wn({inputs:{x:r},backend:a});else{const t=a.data.get(r.dataId).values,n=e.computeStrides(r.shape),s=ar(t,r.shape,r.dtype,n,p,"avg");u=a.makeTensorInfo(p.outShape,r.dtype,s.values)}return u}};const ir={kernelName:ue,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{filterSize:i,strides:l,pad:d,dimRoundingMode:c,dataFormat:p}=s;Sn(r,"avgPool3d");const u=o.computePool3DInfo(r.shape,i,l,1,d,c,p),h=rr(a.data.get(r.dataId).values,r.shape,r.dtype,e.computeStrides(r.shape),u,"avg");return a.makeTensorInfo(h.shape,"float32",h.values)}};const lr={kernelName:he,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:s,input:r}=t,{filterSize:l,strides:d,pad:c,dimRoundingMode:p}=a;Sn([s,r],"avgPool3DGrad");const u=o.computePool3DInfo(r.shape,l,d,1,c,p),h=u.strideDepth,f=u.strideHeight,m=u.strideWidth,k=u.filterDepth,g=u.filterHeight,I=u.filterWidth,b=u.dilationDepth,y=u.dilationHeight,N=u.dilationWidth,T=u.effectiveFilterDepth,x=u.effectiveFilterHeight,S=u.effectiveFilterWidth,v=T-1-u.padInfo.front,F=S-1-u.padInfo.left,w=x-1-u.padInfo.top,M=i(r.shape,"float32"),A=1/(k*g*I),D=n.bufferSync(s);for(let e=0;e<u.batchSize;++e)for(let t=0;t<u.inChannels;++t)for(let n=0;n<u.inDepth;++n)for(let a=0;a<u.inHeight;++a)for(let s=0;s<u.inWidth;++s){const r=n-v,o=a-w,i=s-F;let l=0;for(let n=0;n<T;n+=b){const a=(r+n)/h;if(!(a<0||a>=u.outDepth||Math.floor(a)!==a))for(let n=0;n<x;n+=y){const s=(o+n)/f;if(!(s<0||s>=u.outHeight||Math.floor(s)!==s))for(let n=0;n<S;n+=N){const r=(i+n)/m;if(r<0||r>=u.outWidth||Math.floor(r)!==r)continue;l+=D.get(e,a,s,r,t)}}}M.set(l*A,e,n,a,s,t)}return n.makeTensorInfo(M.shape,M.dtype,M.values)}};const dr={kernelName:fe,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:s,input:r}=t,l=r;Sn([s,r],"avgPoolGrad");const{filterSize:d,strides:c,pad:p}=a,u=o.computePool2DInfo(l.shape,d,c,1,p),h=u.strideHeight,f=u.strideWidth,m=u.filterHeight,k=u.filterWidth,g=u.dilationHeight,I=u.dilationWidth,b=u.effectiveFilterHeight,y=u.effectiveFilterWidth,N=y-1-u.padInfo.left,T=b-1-u.padInfo.top,x=i(l.shape,"float32"),S=1/(m*k),v=n.data.get(s.dataId).values,F=i(s.shape,"float32",v);for(let e=0;e<u.batchSize;++e)for(let t=0;t<u.inChannels;++t)for(let n=0;n<u.inHeight;++n)for(let a=0;a<u.inWidth;++a){const s=n-T,r=a-N;let o=0;for(let n=0;n<b;n+=g){const a=(s+n)/h;if(!(a<0||a>=u.outHeight||Math.floor(a)!==a))for(let n=0;n<y;n+=I){const s=(r+n)/f;if(s<0||s>=u.outWidth||Math.floor(s)!==s)continue;o+=F.get(e,a,s,t)}}x.set(o*S,e,n,a,t)}return n.makeTensorInfo(x.shape,x.dtype,x.values)}};const cr={kernelName:me,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,scale:o,offset:i,mean:l,variance:d}=n;e.assert(l.shape.length===d.shape.length,(()=>"Batch normalization gradient requires mean and variance to have equal ranks.")),e.assert(null==i||l.shape.length===i.shape.length,(()=>"Batch normalization gradient requires mean and offset to have equal ranks.")),e.assert(null==o||l.shape.length===o.shape.length,(()=>"Batch normalization gradient requires mean and scale to have equal ranks.")),Sn([r,l,d,o,i],"batchNorm");let{varianceEpsilon:c}=s;null==c&&(c=.001);const p=a.data.get(r.dataId).values,u=a.data.get(l.dataId).values,h=a.data.get(d.dataId).values,f=o?a.data.get(o.dataId).values:new Float32Array([1]),m=i?a.data.get(i.dataId).values:new Float32Array([0]),k=new Float32Array(p.length),g=m.length,I=f.length,b=h.length,y=u.length;let N=0,T=0,x=0,S=0;for(let e=0;e<p.length;++e)k[e]=m[N++]+(p[e]-u[T++])*f[x++]/Math.sqrt(h[S++]+c),N>=g&&(N=0),T>=y&&(T=0),x>=I&&(x=0),S>=b&&(S=0);return a.makeTensorInfo(r.shape,r.dtype,k)}};const pr={kernelName:ke,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s}=t,{blockShape:r,crops:i}=a;Sn([s],"batchToSpaceND");const l=r.reduce(((e,t)=>e*t)),d=o.getReshaped(s.shape,r,l),c=o.getPermuted(d.length,r.length),p=o.getReshapedPermuted(s.shape,r,l),u=o.getSliceBeginCoords(i,r.length),h=o.getSliceSize(p,i,r.length),f=Vs({inputs:{x:s},backend:n,attrs:{shape:d}}),m=Ha({inputs:{x:f},backend:n,attrs:{perm:c}}),k=Vs({inputs:{x:m},backend:n,attrs:{shape:p}}),g=as({inputs:{x:k},backend:n,attrs:{begin:u,size:h}});return n.disposeIntermediateTensorInfo(f),n.disposeIntermediateTensorInfo(m),n.disposeIntermediateTensorInfo(k),g}};const ur={kernelName:ge,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,weights:r}=t,{size:o}=a,i=qn(n.data.get(s.dataId).values,n.data.get(r.dataId).values,r.dtype,r.shape,o);return n.makeTensorInfo([o],r.dtype,i)}};const hr={kernelName:Ie,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{s0:a,s1:s}=t,r=n.data.get(a.dataId).values,i=n.data.get(s.dataId).values,l=o.assertAndGetBroadcastShape(Array.from(r),Array.from(i));return n.makeTensorInfo([l.length],"int32",Int32Array.from(l))}},fr={kernelName:be,backendName:"cpu",kernelFunc:Kn(be,((e,t)=>{const n=t;return e>n.clipValueMax?n.clipValueMax:e<n.clipValueMin?n.clipValueMin:e}))},mr={kernelName:ye,backendName:"cpu",kernelFunc:t=>{const{x:n}=t.inputs,a=t.backend,s=new Float32Array(e.sizeFromShape(n.shape)),r=a.data.get(n.dataId),o=r.complexTensorInfos.real,i=r.complexTensorInfos.imag,l=a.data.get(o.dataId).values,d=a.data.get(i.dataId).values;for(let e=0;e<l.length;e++){const t=l[e],n=d[e];s[e]=Math.hypot(t,n)}return a.makeOutput(s,n.shape,"float32")}};function kr(e){const{inputs:t,backend:n}=e,{input:a}=t,s=n.data.get(a.dataId).complexTensorInfos.imag,r=n.data.get(s.dataId).values;return n.makeTensorInfo(s.shape,s.dtype,r)}const gr={kernelName:Ne,backendName:"cpu",kernelFunc:kr};function Ir(t){const{inputs:n,backend:a,attrs:s}=t,{axis:r}=s,i=e.parseAxisParam(r,n[0].shape)[0],l=n.map((e=>e.shape));o.assertParamsConsistent(l,i);let d=o.computeOutShape(n.map((e=>e.shape)),i);if(0===e.sizeFromShape(d))return a.makeTensorInfo(d,n[0].dtype,[]);const c=n.filter((t=>e.sizeFromShape(t.shape)>0));if(1===c.length)return Wn({inputs:{x:c[0]},backend:a});if("complex64"===c[0].dtype){const e=c.map((e=>Pn({inputs:{input:e},backend:a}))),t=c.map((e=>kr({inputs:{input:e},backend:a}))),n=Ir({inputs:e,backend:a,attrs:{axis:i}}),s=Ir({inputs:t,backend:a,attrs:{axis:i}}),r=Dn({inputs:{real:n,imag:s},backend:a});return e.forEach((e=>a.disposeIntermediateTensorInfo(e))),t.forEach((e=>a.disposeIntermediateTensorInfo(e))),a.disposeIntermediateTensorInfo(n),a.disposeIntermediateTensorInfo(s),r}const p=c.map((t=>{const n=e.sizeFromShape(t.shape.slice(i));return Vs({inputs:{x:t},backend:a,attrs:{shape:[-1,n]}})})),u=p.map((e=>({vals:a.data.get(e.dataId).values,shape:e.shape})));d=o.computeOutShape(p.map((e=>e.shape)),1);const h=1===p[0].shape[0],f=Qn(u,d,n[0].dtype,h),m=o.computeOutShape(c.map((e=>e.shape)),i),k=a.makeTensorInfo(m,n[0].dtype,f);return p.forEach((e=>a.disposeIntermediateTensorInfo(e))),k}const br={kernelName:Te,backendName:"cpu",kernelFunc:Ir};function yr(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,filter:i}=n,{strides:l,pad:d,dataFormat:c,dilations:p,dimRoundingMode:u}=s;Sn([r,i],"conv2d");const h=o.convertConv2DDataFormat(c),f=o.computeConv2DInfo(r.shape,i.shape,l,p,d,u,!1,h),m=f.filterHeight,k=f.filterWidth,g=f.dilationHeight,I=f.dilationWidth,b=f.padInfo.left,y=f.padInfo.top,N="channelsLast"===f.dataFormat,T=new G(f.outShape,r.dtype),x=e.computeStrides(r.shape),S=e.computeStrides(i.shape),v=x[0],F=N?x[1]:x[2],w=N?x[2]:1,M=N?1:x[1],A=T.strides[0],D=N?T.strides[1]:T.strides[2],E=N?T.strides[2]:1,z=N?1:T.strides[1],W=a.data.get(r.dataId).values,R=a.data.get(i.dataId).values,P=T.values;for(let e=0;e<f.batchSize;++e){const t=e*v,n=e*A;for(let e=0;e<f.outHeight;++e){const a=n+e*D,s=e*f.strideHeight-y;for(let e=0;e<m;++e){const n=s+e*g;if(n<0||n>=f.inHeight)continue;const r=e*S[0],o=t+n*F;for(let e=0;e<f.outWidth;++e){const t=a+e*E,n=e*f.strideWidth-b;for(let e=0;e<k;++e){const a=n+e*I;if(a<0||a>=f.inWidth)continue;const s=o+a*w;let i=r+e*S[1];for(let e=0;e<f.inChannels;++e){const n=W[s+e*M];for(let e=0;e<f.outChannels;++e)P[t+e*z]+=n*R[i+e];i+=f.outChannels}}}}}}return a.makeTensorInfo(T.shape,T.dtype,P)}const Nr={kernelName:xe,backendName:"cpu",kernelFunc:yr};const Tr={kernelName:Se,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,dy:r}=t,{strides:i,pad:l,dataFormat:d,dimRoundingMode:c,filterShape:p}=a;Sn([s,r],"conv2dBackpropFilter");const u=o.convertConv2DDataFormat(d),h=o.computeConv2DInfo(s.shape,p,i,1,l,c,!1,u),{strideHeight:f,strideWidth:m,filterHeight:k,filterWidth:g}=h,I="channelsLast"===h.dataFormat,b=new G(h.filterShape,"float32"),y=h.padInfo.left,N=h.padInfo.top,T=n.data.get(s.dataId).values,x=n.data.get(r.dataId).values,S=new G(s.shape,s.dtype,T),v=new G(r.shape,r.dtype,x);for(let e=0;e<k;++e){const t=Math.max(0,Math.ceil((N-e)/f)),n=Math.min(h.outHeight,(h.inHeight+N-e)/f);for(let a=0;a<g;++a){const s=Math.max(0,Math.ceil((y-a)/m)),r=Math.min(h.outWidth,(h.inWidth+y-a)/m);for(let o=0;o<h.inChannels;++o)for(let i=0;i<h.outChannels;++i){let l=0;for(let d=0;d<h.batchSize;++d)for(let c=t;c<n;++c){const t=e+c*f-N;for(let e=s;e<r;++e){const n=a+e*m-y;l+=I?S.get(d,t,n,o)*v.get(d,c,e,i):S.get(d,o,t,n)*v.get(d,i,c,e)}}b.set(l,e,a,o,i)}}}return n.makeTensorInfo(b.shape,b.dtype,b.values)}};const xr={kernelName:ve,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{dy:r,filter:i}=n,{inputShape:l,strides:d,pad:c,dataFormat:p,dimRoundingMode:u}=s;Sn([r,i],"conv2dBackpropInput");const h=e.computeStrides(i.shape),f=e.computeStrides(r.shape);let m=o.convertConv2DDataFormat(p);const k=o.computeConv2DInfo(l,i.shape,d,1,c,u,!1,m),g=new G(k.inShape,"float32"),I=g.values,b=a.data.get(r.dataId).values,y=a.data.get(i.dataId).values,[N,T,x]=h,{batchSize:S,filterHeight:v,filterWidth:F,inChannels:w,inHeight:M,inWidth:A,outChannels:D,outHeight:E,outWidth:z,strideHeight:W,strideWidth:R}=k;m=k.dataFormat;const P=v-1-k.padInfo.top,H=F-1-k.padInfo.left,C="channelsLast"===m,$=g.strides[0],O=C?g.strides[1]:g.strides[2],V=C?g.strides[2]:1,_=C?1:g.strides[1],B=f[0],L=C?f[1]:f[2],q=C?f[2]:1,U=C?1:f[1];for(let e=0;e<S;++e)for(let t=0;t<w;++t)for(let n=0;n<M;++n){const a=n-P,s=Math.max(0,Math.ceil(a/W)),r=Math.min(E,(v+a)/W);for(let o=0;o<A;++o){const i=o-H,l=Math.max(0,Math.ceil(i/R)),d=Math.min(z,(F+i)/R);let c=0;for(let n=s;n<r;++n){const s=n*W-a;for(let a=l;a<d;++a){const r=B*e+L*n+q*a,o=N*(v-1-s)+T*(F-1-(a*R-i))+x*t;for(let e=0;e<D;++e){c+=b[r+U*e]*y[o+e]}}}I[$*e+O*n+V*o+_*t]=c}}return a.makeTensorInfo(g.shape,g.dtype,g.values)}};const Sr={kernelName:Fe,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,filter:i}=n,{strides:l,pad:d,dilations:c}=s;Sn([r,i],"conv3d");const p=o.computeConv3DInfo(r.shape,i.shape,l,c,d),{filterDepth:u,filterHeight:h,filterWidth:f,dilationDepth:m,dilationHeight:k,dilationWidth:g,padInfo:I}=p,b=I.front,y=I.left,N=I.top,T=new G(p.outShape,r.dtype),x=a.data.get(r.dataId).values,S=a.data.get(i.dataId).values,v=T.values,F=e.computeStrides(r.shape),w=e.computeStrides(i.shape);for(let e=0;e<p.batchSize;++e){const t=e*F[0],n=e*T.strides[0];for(let e=0;e<p.outDepth;++e){const a=n+e*T.strides[1],s=e*p.strideDepth-b;for(let e=0;e<u;++e){const n=s+e*m;if(n<0||n>=p.inDepth)continue;const r=e*w[0],o=t+n*F[1];for(let e=0;e<p.outHeight;++e){const t=a+e*T.strides[2],n=e*p.strideHeight-N;for(let e=0;e<h;++e){const a=n+e*k;if(a<0||a>=p.inHeight)continue;const s=r+e*w[1],i=o+a*F[2];for(let e=0;e<p.outWidth;++e){const n=t+e*p.outChannels,a=e*p.strideWidth-y;for(let e=0;e<f;++e){const t=a+e*g;if(t<0||t>=p.inWidth)continue;const r=s+e*w[2],o=i+t*p.inChannels;let l=r;for(let e=0;e<p.inChannels;++e){const t=x[o+e];for(let e=0;e<p.outChannels;++e)v[n+e]+=t*S[l+e];l+=p.outChannels}}}}}}}}return a.makeTensorInfo(T.shape,T.dtype,T.values)}};const vr={kernelName:we,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,dy:i}=n,{strides:l,pad:d,filterShape:c}=s;Sn([r,i],"conv3dBackpropFilterV2");const p=e.computeStrides(r.shape),u=e.computeStrides(i.shape),h=o.computeConv3DInfo(r.shape,c,l,1,d),f=h.strideDepth,m=h.strideHeight,k=h.strideWidth,g=h.filterDepth,I=h.filterHeight,b=h.filterWidth,y=new G(h.filterShape,"float32"),N=y.values,[T,x,S,v]=y.strides,F=a.data.get(i.dataId).values,[w,M,A,D]=u,E=a.data.get(r.dataId).values,[z,W,R,P]=p,H=h.padInfo.front,C=h.padInfo.left,$=h.padInfo.top;for(let e=0;e<g;++e){const t=Math.max(0,Math.ceil((H-e)/f)),n=Math.min(h.outDepth,(h.inDepth+H-e)/f),a=e*T;for(let s=0;s<I;++s){const r=Math.max(0,Math.ceil(($-s)/m)),o=Math.min(h.outHeight,(h.inHeight+$-s)/m),i=s*x+a;for(let a=0;a<b;++a){const l=Math.max(0,Math.ceil((C-a)/k)),d=Math.min(h.outWidth,(h.inWidth+C-a)/k),c=a*S+i;for(let i=0;i<h.inChannels;++i){const p=i*v+c;for(let c=0;c<h.outChannels;++c){let u=0;for(let p=0;p<h.batchSize;++p){const h=p*z,g=p*w;for(let p=t;p<n;++p){const t=(e+p*f-H)*W+h,n=p*M+g;for(let e=r;e<o;++e){const r=(s+e*m-$)*R+t,o=e*A+n;for(let e=l;e<d;++e){const t=e*D+o;u+=E[(a+e*k-C)*P+r+i]*F[t+c]}}}}N[p+c]=u}}}}}return a.makeTensorInfo(y.shape,y.dtype,y.values)}};const Fr={kernelName:Me,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{dy:r,filter:i}=n,{pad:l,strides:d,inputShape:c}=s;Sn([r],"conv3dBackpropInputV2");const p=e.computeStrides(r.shape),u=e.computeStrides(i.shape),h=o.computeConv3DInfo(c,i.shape,d,1,l),f=new G(h.inShape,"float32"),m=f.values,[k,g,I,b]=f.strides,y=a.data.get(r.dataId).values,[N,T,x,S]=p,v=a.data.get(i.dataId).values,[F,w,M,A]=u,{batchSize:D,filterDepth:E,filterHeight:z,filterWidth:W,inChannels:R,inDepth:P,inHeight:H,inWidth:C,outChannels:$,outDepth:O,outHeight:V,outWidth:_,strideDepth:B,strideHeight:L,strideWidth:q}=h,U=E-1-h.padInfo.front,Z=z-1-h.padInfo.top,K=W-1-h.padInfo.left;for(let e=0;e<D;++e)for(let t=0;t<R;++t)for(let n=0;n<P;++n){const a=n-U,s=Math.max(0,Math.ceil(a/B)),r=Math.min(O,(E+a)/B);for(let o=0;o<H;++o){const i=o-Z,l=Math.max(0,Math.ceil(i/L)),d=Math.min(V,(z+i)/L);for(let c=0;c<C;++c){const p=c-K,u=Math.max(0,Math.ceil(p/q)),h=Math.min(_,(W+p)/q);let f=0;for(let n=s;n<r;++n){const s=n*B-a;for(let a=l;a<d;++a){const r=a*L-i;for(let o=u;o<h;++o){const i=N*e+T*n+x*a+S*o,l=F*(E-1-s)+w*(z-1-r)+M*(W-1-(o*q-p))+A*t;for(let e=0;e<$;++e){f+=y[i+e]*v[l+e]}}}}m[k*e+g*n+I*o+b*c+t]=f}}}return a.makeTensorInfo(f.shape,f.dtype,f.values)}},wr={kernelName:Ae,backendName:"cpu",kernelFunc:Kn(Ae,(e=>Math.cos(e)))},Mr={kernelName:De,backendName:"cpu",kernelFunc:Kn(De,(e=>Math.cosh(e)))};const Ar={kernelName:Ee,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{image:r,boxes:o,boxInd:l}=n,{cropSize:d,method:c,extrapolationValue:p}=s,[u,h,f,m]=r.shape,k=o.shape[0],[g,I]=d,b=i([k,g,I,m],"float32"),y=a.data.get(o.dataId).values,N=a.data.get(l.dataId).values,T=a.data.get(r.dataId).values,x=e.computeStrides(r.shape),S=e.computeStrides(b.shape);for(let e=0;e<k;e++){const t=4*e,n=y[t],a=y[t+1],s=y[t+2],r=y[t+3],o=N[e];if(o>=u)continue;const i=g>1?(s-n)*(h-1)/(g-1):0,l=I>1?(r-a)*(f-1)/(I-1):0;for(let t=0;t<g;t++){const d=g>1?n*(h-1)+t*i:.5*(n+s)*(h-1);if(d<0||d>h-1)for(let n=0;n<I;n++)for(let a=0;a<m;a++){const s=a+n*S[2]+t*S[1]+e*S[0];b.values[s]=p}else if("bilinear"===c){const n=Math.floor(d),s=Math.ceil(d),i=d-n;for(let d=0;d<I;d++){const c=I>1?a*(f-1)+d*l:.5*(a+r)*(f-1);if(c<0||c>f-1){for(let n=0;n<m;n++){const a=n+d*S[2]+t*S[1]+e*S[0];b.values[a]=p}continue}const u=Math.floor(c),h=Math.ceil(c),k=c-u;for(let a=0;a<m;a++){let r=a+u*x[2]+n*x[1]+o*x[0];const l=T[r];r=a+h*x[2]+n*x[1]+o*x[0];const c=T[r];r=a+u*x[2]+s*x[1]+o*x[0];const p=T[r];r=a+h*x[2]+s*x[1]+o*x[0];const f=l+(c-l)*k,m=p+(T[r]-p)*k;r=a+d*S[2]+t*S[1]+e*S[0],b.values[r]=f+(m-f)*i}}}else for(let n=0;n<I;++n){const s=I>1?a*(f-1)+n*l:.5*(a+r)*(f-1);if(s<0||s>f-1){for(let a=0;a<m;a++){const s=a+n*S[2]+t*S[1]+e*S[0];b.values[s]=p}continue}const i=Math.round(s),c=Math.round(d);for(let a=0;a<m;a++){const s=a+i*x[2]+c*x[1]+o*x[0],r=a+n*S[2]+t*S[1]+e*S[0];b.values[r]=T[s]}}}}return a.makeTensorInfo(b.shape,b.dtype,b.values)}};const Dr={kernelName:ze,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,exclusive:l,reverse:d}=s;Sn(r,"cumprod");const c=o.getAxesPermutation([i],r.shape.length);let p=r;null!=c&&(p=Ha({inputs:{x:r},backend:a,attrs:{perm:c}}));const u=o.getInnerMostAxes(1,r.shape.length)[0];if(u!==p.shape.length-1)throw new Error(`backend.cumprod in CPU expects an inner-most axis=${p.shape.length-1} but got axis=${u}`);const h=D(p.dtype,"int32"),f=e.makeOnesTypedArray(e.sizeFromShape(p.shape),h),m=a.data.get(p.dataId).values,k=p.shape[p.shape.length-1],g=d?(e,t)=>e+k-t-1:(e,t)=>e+t;for(let e=0;e<m.length;e+=k)for(let t=0;t<k;t++){const n=g(e,t);if(0===t)f[n]=l?1:m[n];else{const a=g(e,t-1);f[n]=l?m[a]*f[a]:m[n]*f[a]}}const I=a.makeTensorInfo(p.shape,h,f);if(null!=c){const e=Ha({inputs:{x:I},backend:a,attrs:{perm:o.getUndoAxesPermutation(c)}});return a.disposeIntermediateTensorInfo(I),a.disposeIntermediateTensorInfo(p),e}return I}};const Er={kernelName:We,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,exclusive:l,reverse:d}=s;Sn(r,"cumsum");const c=o.getAxesPermutation([i],r.shape.length);let p=r;null!=c&&(p=Ha({inputs:{x:r},backend:a,attrs:{perm:c}}));const u=o.getInnerMostAxes(1,r.shape.length)[0];if(u!==p.shape.length-1)throw new Error(`backend.cumsum in CPU expects an inner-most axis=${p.shape.length-1} but got axis=${u}`);const h=D(p.dtype,"int32"),f=e.makeZerosTypedArray(e.sizeFromShape(p.shape),h),m=a.data.get(p.dataId).values,k=p.shape[p.shape.length-1],g=d?(e,t)=>e+k-t-1:(e,t)=>e+t;for(let e=0;e<m.length;e+=k)for(let t=0;t<k;t++){const n=g(e,t);if(0===t)f[n]=l?0:m[n];else{const a=g(e,t-1);f[n]=l?m[a]+f[a]:m[n]+f[a]}}const I=a.makeTensorInfo(p.shape,h,f);if(null!=c){const e=Ha({inputs:{x:I},backend:a,attrs:{perm:o.getUndoAxesPermutation(c)}});return a.disposeIntermediateTensorInfo(I),a.disposeIntermediateTensorInfo(p),e}return I}};const zr={kernelName:Re,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,weights:r}=t,{size:o,binaryOutput:i}=a;if(1===s.shape.length){const e=qn(n.data.get(s.dataId).values,n.data.get(r.dataId).values,r.dtype,r.shape,o);return n.makeTensorInfo([o],r.dtype,e)}if(2===s.shape.length){const e=Un(n.bufferSync(s),n.bufferSync(r),o,i);return n.makeTensorInfo(e.shape,r.dtype,e.values)}throw new Error(`Error in denseBincount: input must be at most rank 2, but got rank${s.shape.length}.`)}};const Wr={kernelName:Pe,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{blockSize:o,dataFormat:i}=s;e.assert("NHWC"===i,(()=>`Only NHWC dataFormat supported on CPU for depthToSpace. Got ${i}`));const l=r.shape[0],d=r.shape[1],c=r.shape[2],p=r.shape[3],u=d*o,h=c*o,f=p/(o*o),m=a.data.get(r.dataId).values,k=new Float32Array(l*u*h*f);let g=0;for(let e=0;e<l;++e)for(let t=0;t<u;++t){const n=Math.floor(t/o),a=t%o;for(let t=0;t<h;++t){const s=Math.floor(t/o),r=(a*o+t%o)*f;for(let t=0;t<f;++t){const a=t+r+p*(s+c*(n+d*e));k[g++]=m[a]}}}return a.makeTensorInfo([l,u,h,f],r.dtype,k)}};function Rr(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,filter:i}=n,{strides:l,pad:d,dilations:c,dimRoundingMode:p}=s;Sn([r,i],"depthwiseConv2DNative");const u=e.computeStrides(r.shape),h=e.computeStrides(i.shape);let f=c;null==f&&(f=[1,1]),e.assert(o.eitherStridesOrDilationsAreOne(l,f),(()=>`Error in depthwiseConv2d: Either strides or dilations must be 1. Got strides ${l} and dilations '${f}'`));const m=o.computeConv2DInfo(r.shape,i.shape,l,f,d,p,!0),{filterHeight:k,filterWidth:g,dilationHeight:I,dilationWidth:b,padInfo:y}=m,N=y.left,T=y.top,x=m.outChannels/m.inChannels,S=new G(m.outShape,r.dtype),v=a.data.get(r.dataId).values,F=a.data.get(i.dataId).values,w=S.values;for(let e=0;e<m.batchSize;++e){const t=e*u[0],n=e*S.strides[0];for(let e=0;e<m.outHeight;++e){const a=n+e*S.strides[1],s=e*m.strideHeight-T;for(let e=0;e<k;++e){const n=s+e*I;if(n<0||n>=m.inHeight)continue;const r=e*h[0],o=t+n*u[1];for(let e=0;e<m.outWidth;++e){const t=a+e*S.strides[2],n=e*m.strideWidth-N;for(let e=0;e<g;++e){const a=n+e*b;if(a<0||a>=m.inWidth)continue;const s=r+e*h[1],i=o+a*m.inChannels;let l=t,d=s;for(let e=0;e<m.inChannels;++e){const t=v[i+e];for(let e=0;e<x;++e)w[l+e]+=t*F[d+e];l+=x,d+=x}}}}}}return a.makeTensorInfo(S.shape,S.dtype,S.values)}const Pr={kernelName:He,backendName:"cpu",kernelFunc:Rr};const Hr={kernelName:Ce,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,dy:r}=t,{strides:i,dilations:l,pad:d,dimRoundingMode:c,filterShape:p}=a;Sn([s,r],"depthwiseConv2dNativeBackpropFilter");const u=o.computeConv2DInfo(s.shape,p,i,l,d,c,!0),{strideHeight:h,strideWidth:f,filterHeight:m,filterWidth:k}=u,g=new G(u.filterShape,"float32"),I=u.padInfo.left,b=u.padInfo.top,y=u.outChannels/u.inChannels,N=n.data.get(s.dataId).values,T=new G(s.shape,s.dtype,N),x=n.data.get(r.dataId).values,S=new G(r.shape,r.dtype,x);for(let e=0;e<m;++e){const t=Math.max(0,Math.ceil((b-e)/h)),n=Math.min(u.outHeight,(u.inHeight+b-e)/h);for(let a=0;a<k;++a){const s=Math.max(0,Math.ceil((I-a)/f)),r=Math.min(u.outWidth,(u.inWidth+I-a)/f);for(let o=0;o<u.outChannels;++o){const i=Math.trunc(o/y),l=o%y;let d=0;for(let l=0;l<u.batchSize;++l)for(let c=t;c<n;++c){const t=e+c*h-b;for(let e=s;e<r;++e){const n=a+e*f-I;d+=T.get(l,t,n,i)*S.get(l,c,e,o)}}g.set(d,e,a,i,l)}}}return n.makeTensorInfo(g.shape,g.dtype,g.values)}};const Cr={kernelName:$e,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{dy:r,filter:i}=n,{strides:l,dilations:d,pad:c,dimRoundingMode:p,inputShape:u}=s;Sn([r,i],"depthwiseConv2DNativeBackpropInput");const h=e.computeStrides(r.shape),f=e.computeStrides(i.shape),m=o.computeConv2DInfo(u,i.shape,l,d,c,p,!0),k=new G(m.inShape,"float32"),g=k.values,[I,b,y]=k.strides,N=a.data.get(r.dataId).values,[T,x,S]=h,v=a.data.get(i.dataId).values,[F,w,M]=f,{batchSize:A,filterHeight:D,filterWidth:E,inChannels:z,inHeight:W,inWidth:R,outChannels:P,outHeight:H,outWidth:C,strideHeight:$,strideWidth:O}=m,V=D-1-m.padInfo.top,_=E-1-m.padInfo.left,B=P/z;for(let e=0;e<A;++e)for(let t=0;t<z;++t)for(let n=0;n<W;++n){const a=n-V,s=Math.max(0,Math.ceil(a/$)),r=Math.min(H,(D+a)/$);for(let o=0;o<R;++o){const i=o-_,l=Math.max(0,Math.ceil(i/O)),d=Math.min(C,(E+i)/O);let c=0;for(let n=s;n<r;++n){const s=n*$-a;for(let a=l;a<d;++a){const r=T*e+x*n+S*a,o=F*(D-1-s)+w*(E-1-(a*O-i))+M*t;for(let e=0;e<B;++e){c+=N[r+(t*B+e)]*v[o+e]}}}g[I*e+b*n+y*o+t]=c}}return a.makeTensorInfo(k.shape,k.dtype,k.values)}};const $r={kernelName:Oe,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a}=t,{x:s}=n,r=e.sizeFromShape(s.shape),o=a.data.get(s.dataId).values,l=i([r,r],s.dtype),d=l.values;for(let e=0;e<o.length;e++)d[e*r+e]=o[e];const c=[...s.shape,...s.shape];return a.makeTensorInfo(c,l.dtype,l.values)}},Or={kernelName:Ve,backendName:"cpu",kernelFunc:({inputs:t,backend:n,attrs:a})=>{const{x:s,filter:r}=t,{strides:i,pad:l,dilations:d}=a,c=n,p=c.data.get(s.dataId).values,u=s.shape.length,h=c.data.get(r.dataId).values,f=r.shape.length,{batchSize:m,inHeight:k,inWidth:g,inChannels:I,outHeight:b,outWidth:y,padInfo:N,strideHeight:T,strideWidth:x,filterHeight:S,filterWidth:v,dilationHeight:F,dilationWidth:w,outShape:M}=o.computeDilation2DInfo(s.shape,r.shape,i,l,"NHWC",d),A=e.sizeFromShape(M),D=M.length,E=e.getArrayFromDType(s.dtype,A);for(let t=0;t<m;++t)for(let n=0;n<b;++n){const a=n*T-N.top;for(let o=0;o<y;++o){const i=o*x-N.left;for(let l=0;l<I;++l){let d=Number.MIN_SAFE_INTEGER;for(let n=0;n<S;++n){const o=a+n*F;if(o>=0&&o<k)for(let a=0;a<v;++a){const c=i+a*w;if(c>=0&&c<g){const i=e.locToIndex([t,o,c,l],u,e.computeStrides(s.shape)),m=e.locToIndex([n,a,l],f,e.computeStrides(r.shape)),k=p[i]+h[m];k>d&&(d=k)}}}E[e.locToIndex([t,n,o,l],D,e.computeStrides(M))]=d}}}return{dataId:c.write(e.toTypedArray(E,s.dtype),M,s.dtype),shape:M,dtype:s.dtype}}},Vr={kernelName:_e,backendName:"cpu",kernelFunc:({inputs:t,backend:n,attrs:a})=>{const{x:s,filter:r,dy:i}=t,{strides:l,pad:d,dilations:c}=a,p=n,u=e.toNestedArray(s.shape,p.data.get(s.dataId).values),h=e.toNestedArray(r.shape,p.data.get(r.dataId).values),{batchSize:f,inHeight:m,inWidth:k,inChannels:g,outHeight:I,outWidth:b,padInfo:y,strideHeight:N,strideWidth:T,filterHeight:x,filterWidth:S,dilationHeight:v,dilationWidth:F,outShape:w}=o.computeDilation2DInfo(s.shape,r.shape,l,d,"NHWC",c);e.assert(i.rank===w.length,(()=>`Error in ${_e}, dy must have the same rank as output ${w.length}, but got ${i.rank}`));const M=e.toNestedArray(w,p.data.get(i.dataId).values),A=e.makeZerosNestedTypedArray(r.shape,r.dtype);for(let e=0;e<f;++e)for(let t=0;t<I;++t){const n=t*N-y.top;for(let a=0;a<b;++a){const s=a*T-y.left;for(let r=0;r<g;++r){let o=Number.MIN_SAFE_INTEGER,i=0,l=0;for(let t=0;t<x;++t){const a=n+t*v;if(a>=0&&a<m)for(let n=0;n<S;++n){const d=s+n*F;if(d>=0&&d<k){const s=u[e][a][d][r]+h[t][n][r];s>o&&(o=s,i=t,l=n)}}}A[i][l][r]+=M[e][t][a][r]}}}return{dataId:p.write(e.toTypedArray(A,s.dtype),r.shape,r.dtype),shape:r.shape,dtype:r.dtype}}},_r={kernelName:Ge,backendName:"cpu",kernelFunc:({inputs:t,backend:n,attrs:a})=>{const{x:s,filter:r,dy:i}=t,{strides:l,pad:d,dilations:c}=a,p=n,u=e.toNestedArray(s.shape,p.data.get(s.dataId).values),h=e.toNestedArray(r.shape,p.data.get(r.dataId).values),{batchSize:f,inHeight:m,inWidth:k,inChannels:g,outHeight:I,outWidth:b,padInfo:y,strideHeight:N,strideWidth:T,filterHeight:x,filterWidth:S,dilationHeight:v,dilationWidth:F,outShape:w}=o.computeDilation2DInfo(s.shape,r.shape,l,d,"NHWC",c);e.assert(i.rank===w.length,(()=>`Error in ${Ge}, dy must have the same rank as output ${w.length}, but got ${i.rank}`));const M=e.toNestedArray(w,p.data.get(i.dataId).values),A=e.makeZerosNestedTypedArray(s.shape,s.dtype);for(let e=0;e<f;++e)for(let t=0;t<I;++t){const n=t*N-y.top;for(let a=0;a<b;++a){const s=a*T-y.left;for(let r=0;r<g;++r){let o=Number.MIN_SAFE_INTEGER,i=n<0?0:n,l=s<0?0:s;for(let t=0;t<x;++t){const a=n+t*v;if(a>=0&&a<m)for(let n=0;n<S;++n){const d=s+n*F;if(d>=0&&d<k){const s=u[e][a][d][r]+h[t][n][r];s>o&&(o=s,i=a,l=d)}}}A[e][i][l][r]+=M[e][t][a][r]}}}return{dataId:p.write(e.toTypedArray(A,s.dtype),s.shape,s.dtype),shape:s.shape,dtype:s.dtype}}};function Gr(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,keepDims:l}=s;let d;Sn(r,"sum"),d="bool"===r.dtype?$n({inputs:{x:r},backend:a,attrs:{dtype:"int32"}}):Wn({inputs:{x:r},backend:a});const c=d.shape.length,p=e.parseAxisParam(i,d.shape),u=o.getAxesPermutation(p,c);let h=p,f=d;null!=u&&(f=Ha({inputs:{x:d},backend:a,attrs:{perm:u}}),h=o.getInnerMostAxes(h.length,c)),o.assertAxesAreInnerMostDims("sum",h,f.shape.length);const[m,k]=o.computeOutAndReduceShapes(f.shape,h);let g=zn(a,m,o.upcastType(f.dtype,"int32"));const I=e.sizeFromShape(k),b=a.data.get(g.dataId).values,y=a.data.get(f.dataId).values;for(let e=0;e<b.length;++e){const t=e*I;let n=0;for(let e=0;e<I;++e)n+=y[t+e];b[e]=n}if(l){const e=g;g=Vs({inputs:{x:g},backend:a,attrs:{shape:o.expandShapeToKeepDim(g.shape,p)}}),a.disposeIntermediateTensorInfo(e)}return a.disposeIntermediateTensorInfo(d),null!=u&&a.disposeIntermediateTensorInfo(f),g}const Br={kernelName:Be,backendName:"cpu",kernelFunc:Gr};const Lr={kernelName:Le,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{equation:r}=s,i=n,{allDims:l,summedDims:d,idDims:c}=o.decodeEinsumEquation(r,i.length);o.checkEinsumDimSizes(l.length,c,i);const{path:p,steps:u}=o.getEinsumComputePath(d,c),h=u.length;let f=null,m=l.length;const k=[];for(let t=0;t<h;++t){for(const n of u[t]){const{permutationIndices:t,expandDims:s}=o.getEinsumPermutation(m,c[n]);let r;o.isIdentityPermutation(t)?r=i[n]:(r=Ha({inputs:{x:i[n]},backend:a,attrs:{perm:t}}),k.push(r));const l=r.shape.slice();for(let e=0;e<s.length;++e)l.splice(s[e],0,1);e.arraysEqual(r.shape,l)||(r=Vs({inputs:{x:r},backend:a,attrs:{shape:l}}),k.push(r)),null===f?f=r:(f=Aa({inputs:{a:r,b:f},backend:a}),k.push(f))}t<h-1&&(p[t]>=0&&(f=Gr({inputs:{x:f},backend:a,attrs:{axis:p[t]-(l.length-m),keepDims:!1}}),k.push(f)),m--)}for(const e of k)e!==f&&a.disposeIntermediateTensorInfo(e);return f}};const qr={kernelName:qe,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a}=t,{dy:s,y:r}=n;Sn([s,r],"eluGrad");const o=new Float32Array(e.sizeFromShape(r.shape)),i=a.data.get(r.dataId).values,l=a.data.get(s.dataId).values;for(let e=0;e<i.length;++e){const t=i[e];o[e]=t>=1?l[e]:l[e]*(t+1)}return a.makeTensorInfo(r.shape,"float32",o)}},Ur=o.ERF_P,Zr=o.ERF_A1,Kr=o.ERF_A2,jr=o.ERF_A3,Yr=o.ERF_A4,Jr=o.ERF_A5,Qr=Kn(Ue,(e=>{const t=Math.sign(e),n=Math.abs(e),a=1/(1+Ur*n);return t*(1-((((Jr*a+Yr)*a+jr)*a+Kr)*a+Zr)*a*Math.exp(-n*n))})),Xr={kernelName:Ue,backendName:"cpu",kernelFunc:Qr};function eo(t){const{inputs:n,backend:a,attrs:s}=t,{input:r}=n,{dim:o}=s,i=r.shape.length,l=r.shape.slice();let d=o;return o<0&&(e.assert(-(i+1)<=o,(()=>`Axis must be in the interval [${-(i+1)}, ${i}]`)),d=i+o+1),l.splice(d,0,1),Vs({inputs:{x:r},backend:a,attrs:{shape:l}})}const to={kernelName:Ze,backendName:"cpu",kernelFunc:eo},no=Vn(Ke,An(((e,t)=>e/t))),ao={kernelName:Ke,backendName:"cpu",kernelFunc:no};function so(t,n,a){const s=t.shape,r=s[0],i=s[1],l=a.data.get(t.dataId),d=l.complexTensorInfos.real,c=l.complexTensorInfos.imag,p=[r,i],u=e.sizeFromShape(p),h=e.getTypedArrayFromDType("float32",u),f=e.getTypedArrayFromDType("float32",u);for(let e=0;e<r;e++){const t=as({inputs:{x:d},backend:a,attrs:{begin:[e,0],size:[1,i]}}),s=as({inputs:{x:c},backend:a,attrs:{begin:[e,0],size:[1,i]}}),r=Dn({inputs:{real:t,imag:s},backend:a}),{real:l,imag:p}=ro(r,n,a),u=o.mergeRealAndImagArrays(l,p);for(let t=0;t<i;t++){const n=o.getComplexWithIndex(u,t);h[e*i+t]=n.real,f[e*i+t]=n.imag}a.disposeIntermediateTensorInfo(t),a.disposeIntermediateTensorInfo(s),a.disposeIntermediateTensorInfo(r)}const m=a.makeTensorInfo(p,"float32",h),k=a.makeTensorInfo(p,"float32",f),g=Dn({inputs:{real:m,imag:k},backend:a});return a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(k),g}function ro(t,n,a){const s=e.sizeFromShape(t.shape),r=a.data.get(t.dataId),i=a.data.get(r.complexTensorInfos.real.dataId).values,l=a.data.get(r.complexTensorInfos.imag.dataId).values;if(0==((d=s)&d-1)){const r=oo(i,l,s,n,a),o=[t.shape[0],t.shape[1]];if(n){const t=a.makeTensorInfo(o,"float32",r.real),n=a.makeTensorInfo(o,"float32",r.imag),i=a.makeTensorInfo([],"float32",e.createScalarValue(s,"float32")),l=Wn({inputs:{x:i},backend:a}),d=ao.kernelFunc({inputs:{a:t,b:i},backend:a}),c=ao.kernelFunc({inputs:{a:n,b:l},backend:a}),p=a.data.get(d.dataId).values,u=a.data.get(c.dataId).values;return a.disposeIntermediateTensorInfo(t),a.disposeIntermediateTensorInfo(n),a.disposeIntermediateTensorInfo(i),a.disposeIntermediateTensorInfo(l),a.disposeIntermediateTensorInfo(d),a.disposeIntermediateTensorInfo(c),{real:p,imag:u}}return r}{const e=function(e,t,n){const a=new Float32Array(2*t);for(let s=0;s<t;s++){let r=0,i=0;for(let a=0;a<t;a++){const l=o.exponent(s*a,t,n),d=o.getComplexWithIndex(e,a);r+=d.real*l.real-d.imag*l.imag,i+=d.real*l.imag+d.imag*l.real}n&&(r/=t,i/=t),o.assignToTypedArray(a,r,i,s)}return a}(o.mergeRealAndImagArrays(i,l),s,n);return o.splitRealAndImagArrays(e)}var d}function oo(e,t,n,a,s){if(1===n)return{real:e,imag:t};const r=o.mergeRealAndImagArrays(e,t),i=n/2,l=o.complexWithEvenIndex(r),d=l.real,c=l.imag,p=[d.length],u=s.makeTensorInfo(p,"float32",d),h=s.makeTensorInfo(p,"float32",c),f=Dn({inputs:{real:u,imag:h},backend:s}),m=o.complexWithOddIndex(r),k=m.real,g=m.imag,I=[k.length],b=s.makeTensorInfo(I,"float32",k),y=s.makeTensorInfo(I,"float32",g),N=Dn({inputs:{real:b,imag:y},backend:s}),T=oo(d,c,i,a,s),x=T.real,S=T.imag,v=[x.length],F=s.makeTensorInfo(v,"float32",x),w=s.makeTensorInfo(v,"float32",S),M=Dn({inputs:{real:F,imag:w},backend:s}),A=oo(k,g,i,a,s),D=A.real,E=A.imag,z=[D.length],W=s.makeTensorInfo(z,"float32",D),R=s.makeTensorInfo(z,"float32",E),P=Dn({inputs:{real:W,imag:R},backend:s}),H=o.exponents(n,a),C=[H.real.length],$=s.makeTensorInfo(C,"float32",H.real),O=s.makeTensorInfo(C,"float32",H.imag),V=Dn({inputs:{real:$,imag:O},backend:s}),_=Aa({inputs:{a:V,b:P},backend:s}),G=Bn({inputs:{a:M,b:_},backend:s}),B=bs({inputs:{a:M,b:_},backend:s}),L=Pn({inputs:{input:G},backend:s}),q=Pn({inputs:{input:B},backend:s}),U=kr({inputs:{input:G},backend:s}),Z=kr({inputs:{input:B},backend:s}),K=Ir({inputs:[L,q],backend:s,attrs:{axis:0}}),j=Ir({inputs:[U,Z],backend:s,attrs:{axis:0}}),Y=s.data.get(K.dataId).values,J=s.data.get(j.dataId).values;return s.disposeIntermediateTensorInfo(u),s.disposeIntermediateTensorInfo(h),s.disposeIntermediateTensorInfo(f),s.disposeIntermediateTensorInfo(b),s.disposeIntermediateTensorInfo(y),s.disposeIntermediateTensorInfo(N),s.disposeIntermediateTensorInfo(F),s.disposeIntermediateTensorInfo(w),s.disposeIntermediateTensorInfo(M),s.disposeIntermediateTensorInfo(W),s.disposeIntermediateTensorInfo(R),s.disposeIntermediateTensorInfo(P),s.disposeIntermediateTensorInfo($),s.disposeIntermediateTensorInfo(O),s.disposeIntermediateTensorInfo(V),s.disposeIntermediateTensorInfo(_),s.disposeIntermediateTensorInfo(G),s.disposeIntermediateTensorInfo(B),s.disposeIntermediateTensorInfo(L),s.disposeIntermediateTensorInfo(U),s.disposeIntermediateTensorInfo(q),s.disposeIntermediateTensorInfo(Z),s.disposeIntermediateTensorInfo(K),s.disposeIntermediateTensorInfo(j),{real:Y,imag:J}}const io={kernelName:je,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a}=t,{input:s}=n,r=e.sizeFromShape(s.shape),o=s.shape[s.shape.length-1],i=Vs({inputs:{x:s},backend:a,attrs:{shape:[r/o,o]}}),l=so(i,!1,a),d=Vs({inputs:{x:l},backend:a,attrs:{shape:s.shape}});return a.disposeIntermediateTensorInfo(i),a.disposeIntermediateTensorInfo(l),d}};function lo(t){const{backend:n,attrs:a}=t,{shape:s,value:r,dtype:o}=a,i=o||e.inferDtype(r),l=e.getArrayFromDType(i,e.sizeFromShape(s));return function(e,t,n){e.fill(t)}(l,r),n.makeTensorInfo(s,i,l)}const co={kernelName:Ye,backendName:"cpu",kernelFunc:lo};const po={kernelName:Je,backendName:"cpu",kernelFunc:({inputs:t,attrs:n,backend:a})=>{const{image:s}=t,r=a,o=e.getTypedArrayFromDType(s.dtype,e.sizeFromShape(s.shape)),[i,l,d,c]=s.shape,p=r.data.get(s.dataId).values;for(let e=0;e<i;e++){const t=e*d*l*c;for(let e=0;e<l;e++){const n=e*(d*c);for(let e=0;e<d;e++){const a=e*c;for(let s=0;s<c;s++){const r=Math.round(d-e-1),i=t+n+a+s;let l=p[i];if(r>=0&&r<d){l=p[t+n+r*c+s]}o[i]=l}}}}return{dataId:r.write(o,s.shape,s.dtype),shape:s.shape,dtype:s.dtype}}},uo={kernelName:Qe,backendName:"cpu",kernelFunc:Vn(Qe,An(((e,t)=>Math.floor(e/t))),null,"int32")};const ho={kernelName:Xe,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,filter:r,bias:o,preluActivationWeights:i}=t,{strides:l,pad:d,dataFormat:c,dilations:p,dimRoundingMode:u,activation:h,leakyreluAlpha:f}=a;let m=yr({inputs:{x:s,filter:r},backend:n,attrs:{strides:l,pad:d,dataFormat:c,dilations:p,dimRoundingMode:u}});if(o){const e=m;if("NCHW"===c&&1===o.shape.length&&1!==o.shape[0]){const e=Vs({inputs:{x:o},backend:n,attrs:{shape:[o.shape[0],1,1]}});m=Bn({inputs:{a:m,b:e},backend:n}),n.disposeIntermediateTensorInfo(e)}else m=Bn({inputs:{a:m,b:o},backend:n});n.disposeIntermediateTensorInfo(e)}if(h){const e=m;if("NCHW"===c&&"prelu"===h&&1===i.shape.length&&1!==i.shape[0]){const e=Vs({inputs:{x:i},backend:n,attrs:{shape:[i.shape[0],1,1]}});m=Os(n,m,h,e,f),n.disposeIntermediateTensorInfo(e)}else m=Os(n,m,h,i,f);n.disposeIntermediateTensorInfo(e)}return m}};const fo={kernelName:et,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s,filter:r,bias:o,preluActivationWeights:i}=t,{strides:l,pad:d,dataFormat:c,dilations:p,dimRoundingMode:u,activation:h,leakyreluAlpha:f}=a;let m=Rr({inputs:{x:s,filter:r},backend:n,attrs:{strides:l,pad:d,dataFormat:c,dilations:p,dimRoundingMode:u}});if(o){const e=m;m=Bn({inputs:{a:m,b:o},backend:n}),n.disposeIntermediateTensorInfo(e)}if(h){const e=m;m=Os(n,m,h,i,f),n.disposeIntermediateTensorInfo(e)}return m}};const mo={kernelName:tt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a}=t,{params:s,indices:r}=n,i=e.sizeFromShape(s.shape),l=r.shape,d=l[l.length-1],[c,p,u,h]=o.prepareAndValidate(s,r);if(0===p)return a.makeTensorInfo(c,s.dtype,[]);const f=da(a.data.get(r.dataId).values,a.bufferSync(s),s.dtype,p,d,u,h,s.shape,i);return a.makeTensorInfo(c,s.dtype,f.values)}};const ko={kernelName:nt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,indices:i}=n,{axis:l,batchDims:d}=s;Sn([r,i],"gatherV2");const c=e.parseAxisParam(l,r.shape)[0],p=a.data.get(i.dataId).values,u=r.shape[c];for(let t=0;t<p.length;++t){const n=p[t];e.assert(n<=u-1&&n>=0,(()=>`GatherV2: the index value ${n} is not in [0, ${u-1}]`))}let h=d;null==d&&(h=0);const f=e.sizeFromShape(i.shape),m=o.segment_util.collectGatherOpShapeInfo(r,i,c,h),k=Vs({inputs:{x:r},backend:a,attrs:{shape:[m.batchSize,m.outerSize,m.dimSize,m.sliceSize]}}),g=Vs({inputs:{x:i},backend:a,attrs:{shape:[m.batchSize,f/m.batchSize]}}),I=[m.batchSize,m.outerSize,f/m.batchSize,m.sliceSize],b=a.bufferSync(g),y=ca(a.bufferSync(k),b,I);return a.disposeIntermediateTensorInfo(k),a.disposeIntermediateTensorInfo(g),a.makeTensorInfo(m.outputShape,y.dtype,y.values)}};const go={kernelName:at,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a}=t,{input:s}=n,r=e.sizeFromShape(s.shape),o=s.shape[s.shape.length-1],i=Vs({inputs:{x:s},backend:a,attrs:{shape:[r/o,o]}}),l=so(i,!0,a),d=Vs({inputs:{x:l},backend:a,attrs:{shape:s.shape}});return a.disposeIntermediateTensorInfo(i),a.disposeIntermediateTensorInfo(l),d}},Io={kernelName:st,backendName:"cpu",kernelFunc:Kn(st,(e=>Number.isFinite(e)?1:0),"bool")},bo={kernelName:rt,backendName:"cpu",kernelFunc:Kn(rt,(e=>Math.abs(e)===1/0?1:0),"bool")},yo={kernelName:ot,backendName:"cpu",kernelFunc:Kn(ot,(e=>Number.isNaN(e)?1:0),"bool")};const No={kernelName:it,backendName:"cpu",kernelFunc:function(e){const{backend:t,attrs:n}=e,{start:a,stop:s,num:r}=n,o=ba(a,s,r);return t.makeTensorInfo([o.length],"float32",o)}},To={kernelName:lt,backendName:"cpu",kernelFunc:Kn(lt,(e=>Math.log1p(e)))},xo={kernelName:dt,backendName:"cpu",kernelFunc:Vn(dt,An(((e,t)=>e&&t)),null,"bool")},So={kernelName:ct,backendName:"cpu",kernelFunc:Kn(ct,(e=>e?0:1),"bool")},vo={kernelName:pt,backendName:"cpu",kernelFunc:Vn(pt,An(((e,t)=>e||t)),null,"bool")};const Fo={kernelName:ut,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{depthRadius:o,bias:i,alpha:l,beta:d}=s;Sn(r,"LRN");const c=r.shape[3],p=c-1,u=a.data.get(r.dataId).values,h=e.sizeFromShape(r.shape),f=new Float32Array(h);function m(e){const t=e%c;let n=e-t+Math.max(0,t-o);const a=e-t+Math.min(t+o,p);let s=0;for(;n<=a;n++){const e=u[n];s+=e*e}return s}for(let e=0;e<h;e++){const t=m(e),n=u[e]*Math.pow(i+l*t,-d);f[e]=n}return a.makeTensorInfo(r.shape,r.dtype,f)}};const wo={kernelName:ht,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,y:o,dy:i}=n,{depthRadius:l,bias:d,alpha:c,beta:p}=s;Sn(i,"LRNGrad");const u=e.sizeFromShape(i.shape),h=i.shape[3],f=a.data.get(i.dataId).values,m=a.data.get(r.dataId).values,k=a.data.get(o.dataId).values,g=new Float32Array(u),I=u;for(let e=0;e<I;e++){const t=e%h,n=e-t+Math.max(0,t-l),a=e-t+Math.min(h,t+l+1);let s=0;for(let e=n;e<a;e++)s+=Math.pow(m[e],2);s=c*s+d;for(let t=n;t<a;t++){let n=-2*c*p*m[t]*k[e]/s;e===t&&(n+=Math.pow(s,-p)),n*=f[e],g[t]+=n}}return a.makeTensorInfo(i.shape,r.dtype,g)}};function Mo(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{reductionIndices:i,keepDims:l}=s,d=a;let c=r.shape;const p=c.length,u=e.parseAxisParam(i,c);let h=u;const f=o.getAxesPermutation(h,p);let m=d.data.get(r.dataId).values;if(null!=f){const e=new Array(p);for(let t=0;t<e.length;t++)e[t]=c[f[t]];m=Pa(m,c,r.dtype,f,e),h=o.getInnerMostAxes(h.length,p),c=e}Sn(r,"max"),o.assertAxesAreInnerMostDims("max",h,p);const[k,g]=o.computeOutAndReduceShapes(c,h),I=Ta(m,e.sizeFromShape(g),k,r.dtype),b=d.write(I,k,r.dtype);let y=k;if(l){y=o.expandShapeToKeepDim(k,u)}return{dataId:b,shape:y,dtype:r.dtype}}const Ao={kernelName:ft,backendName:"cpu",kernelFunc:Mo};const Do={kernelName:mt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n;Sn(r,"maxPool");const{filterSize:i,strides:l,pad:d,dimRoundingMode:c}=s;e.assert(o.eitherStridesOrDilationsAreOne(l,1),(()=>`Error in maxPool: Either strides or dilations must be 1. Got strides ${l} and dilations '1'`));const p=o.computePool2DInfo(r.shape,i,l,1,d,c);let u;if(1===p.filterWidth&&1===p.filterHeight&&e.arraysEqual(p.inShape,p.outShape))u=Wn({inputs:{x:r},backend:a});else{const t=a.data.get(r.dataId).values,n=e.computeStrides(r.shape),s=ar(t,r.shape,r.dtype,n,p,"max");u=a.makeTensorInfo(p.outShape,r.dtype,s.values)}return u}};const Eo={kernelName:kt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{filterSize:i,strides:l,pad:d,dimRoundingMode:c,dataFormat:p}=s;Sn(r,"maxPool3d");const u=o.computePool3DInfo(r.shape,i,l,1,d,c,p),h=rr(a.data.get(r.dataId).values,r.shape,r.dtype,e.computeStrides(r.shape),u,"max");return a.makeTensorInfo(h.shape,"float32",h.values)}};const zo={kernelName:gt,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:s,input:r}=t,{filterSize:l,strides:d,pad:c,dimRoundingMode:p}=a;Sn([s,r],"maxPool3DGrad");const u=o.computePool3DInfo(r.shape,l,d,1,c,p),h=function(e,t){const n=i(t.outShape,"int32"),a=t.strideDepth,s=t.strideHeight,r=t.strideWidth,o=t.dilationDepth,l=t.dilationHeight,d=t.dilationWidth,c=t.effectiveFilterDepth,p=t.effectiveFilterHeight,u=t.effectiveFilterWidth,h=t.padInfo.front,f=t.padInfo.top,m=t.padInfo.left;for(let i=0;i<t.batchSize;++i)for(let k=0;k<t.inChannels;++k)for(let g=0;g<t.outDepth;++g){const I=g*a-h;let b=I;for(;b<0;)b+=o;const y=Math.min(t.inDepth,c+I);for(let a=0;a<t.outHeight;++a){const c=a*s-f;let h=c;for(;h<0;)h+=l;const N=Math.min(t.inHeight,p+c);for(let s=0;s<t.outWidth;++s){const f=s*r-m;let T=f;for(;T<0;)T+=d;const x=Math.min(t.inWidth,u+f);let S=Number.NEGATIVE_INFINITY,v=-1;for(let t=b;t<y;t+=o){const n=t-I;for(let a=h;a<N;a+=l){const s=a-c;for(let r=T;r<x;r+=d){const o=r-f,l=e.get(i,t,a,r,k);l>=S&&(S=l,v=n*p*u+s*p+o)}}}n.set(v,i,g,a,s,k)}}}return n}(n.bufferSync(r),u),f=u.strideDepth,m=u.strideHeight,k=u.strideWidth,g=u.dilationDepth,I=u.dilationHeight,b=u.dilationWidth,y=u.effectiveFilterDepth,N=u.effectiveFilterHeight,T=u.effectiveFilterWidth,x=y-1-u.padInfo.front,S=T-1-u.padInfo.left,v=N-1-u.padInfo.top,F=i(r.shape,"float32"),w=n.bufferSync(s);for(let e=0;e<u.batchSize;++e)for(let t=0;t<u.inChannels;++t)for(let n=0;n<u.inDepth;++n)for(let a=0;a<u.inHeight;++a)for(let s=0;s<u.inWidth;++s){const r=n-x,o=a-v,i=s-S;let l=0;for(let n=0;n<y;n+=g){const a=(r+n)/f;if(!(a<0||a>=u.outDepth||Math.floor(a)!==a))for(let s=0;s<N;s+=I){const r=(o+s)/m;if(!(r<0||r>=u.outHeight||Math.floor(r)!==r))for(let o=0;o<T;o+=b){const d=(i+o)/k;if(d<0||d>=u.outWidth||Math.floor(d)!==d)continue;const c=y*N*T-1-h.get(e,a,r,d,t)===n*N*T+s*T+o?1:0;if(0===c)continue;l+=w.get(e,a,r,d,t)*c}}}F.set(l,e,n,a,s,t)}return n.makeTensorInfo(F.shape,F.dtype,F.values)}};const Wo={kernelName:It,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{dy:s,input:r,output:l}=t,d=r;Sn([r,l],"maxPoolGrad");const{filterSize:c,strides:p,pad:u,dimRoundingMode:h}=a,f=o.computePool2DInfo(d.shape,c,p,1,u,h),m=n.data.get(d.dataId).values,k=i(f.outShape,d.dtype,sr(m,d.shape,d.dtype,f).values),g=f.strideHeight,I=f.strideWidth,b=f.dilationHeight,y=f.dilationWidth,N=f.effectiveFilterHeight,T=f.effectiveFilterWidth,x=T-1-f.padInfo.left,S=N-1-f.padInfo.top,v=i(d.shape,"float32"),F=n.data.get(s.dataId).values,w=i(s.shape,"float32",F);for(let e=0;e<f.batchSize;++e)for(let t=0;t<f.inChannels;++t)for(let n=0;n<f.inHeight;++n)for(let a=0;a<f.inWidth;++a){const s=n-S,r=a-x;let o=0;for(let n=0;n<N;n+=b){const a=(s+n)/g;if(!(a<0||a>=f.outHeight||Math.floor(a)!==a))for(let s=0;s<T;s+=y){const i=(r+s)/I;if(i<0||i>=f.outWidth||Math.floor(i)!==i)continue;const l=N*T-1-k.get(e,a,i,t)===n*T+s?1:0;if(0===l)continue;o+=w.get(e,a,i,t)*l}}v.set(o,e,n,a,t)}return n.makeTensorInfo(v.shape,v.dtype,v.values)}};const Ro={kernelName:bt,backendName:"cpu",kernelFunc:({inputs:t,attrs:n,backend:a})=>{const{x:s}=t,{filterSize:r,strides:i,pad:l,includeBatchInIndex:d}=n,c=a;Sn(s,"MaxPoolWithArgmax");const p=c.data.get(s.dataId).values,u=o.computePool2DInfo(s.shape,r,i,[1,1],l),[h,f]=function(t,n,a,s,r){const o=ar(t,0,a,e.computeStrides(n),r,"max"),i=sr(t,n,a,r,!0,s);return[o.values,i.values]}(p,s.shape,s.dtype,d,u),m=c.write(h,u.outShape,s.dtype),k=c.write(f,u.outShape,s.dtype);return[{dataId:m,shape:u.outShape,dtype:s.dtype},{dataId:k,shape:u.outShape,dtype:"int32"}]}};const Po={kernelName:yt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,keepDims:l}=s,d=e.parseAxisParam(i,r.shape),c=o.computeOutAndReduceShapes(r.shape,d)[1],p=e.sizeFromShape(c),u=[],h=a.makeTensorInfo([],"float32",new Float32Array([p]));u.push(h);const f=$n({inputs:{x:r},backend:a,attrs:{dtype:"float32"}});u.push(f);const m=no({inputs:{a:f,b:h},backend:a});u.push(m);const k=Gr({inputs:{x:m},backend:a,attrs:{axis:i,keepDims:l}});return u.forEach((e=>a.disposeIntermediateTensorInfo(e))),k}};const Ho={kernelName:Nt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{axis:i,keepDims:l}=s;Sn(r,"min");const d=e.parseAxisParam(i,r.shape);let c=d;const p=o.getAxesPermutation(c,r.shape.length);let u=r;null!=p&&(u=Ha({inputs:{x:r},backend:a,attrs:{perm:p}}),c=o.getInnerMostAxes(c.length,r.shape.length)),o.assertAxesAreInnerMostDims("min",c,u.shape.length);const[h,f]=o.computeOutAndReduceShapes(u.shape,c),m=e.sizeFromShape(f),k=e.makeZerosTypedArray(e.sizeFromShape(h),u.dtype),g=a.data.get(u.dataId).values;for(let e=0;e<k.length;++e){const t=e*m;let n=g[t];for(let e=0;e<m;++e){const a=g[t+e];(Number.isNaN(a)||a<n)&&(n=a)}k[e]=n}null!=p&&a.disposeIntermediateTensorInfo(u);const I=a.makeTensorInfo(h,u.dtype,k);if(l){const e=Vs({inputs:{x:I},backend:a,attrs:{shape:o.expandShapeToKeepDim(h,d)}});return a.disposeIntermediateTensorInfo(I),e}return I}};const Co={kernelName:Tt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{paddings:o,mode:i}=s;Sn(r,"mirrorPad");const l=o.map(((e,t)=>e[0]+r.shape[t]+e[1])),d=o.map((e=>e[0])),c=o.map(((e,t)=>e[0]+r.shape[t])),p="reflect"===i?0:1,u=a.data.get(r.dataId).values,h=r.shape.length,f=e.computeStrides(r.shape),m=e.sizeFromShape(l),k=l.length,g=e.computeStrides(l),I=e.getTypedArrayFromDType(r.dtype,m);for(let t=0;t<m;t++){let n=e.indexToLoc(t,k,g);for(let e=0;e<k;e++)n[e]<d[e]?n[e]=2*d[e]-n[e]-p:n[e]>=c[e]&&(n[e]=2*(c[e]-1)-n[e]+p);n=n.map(((e,t)=>e-d[t]));const a=e.locToIndex(n,h,f);I[t]=u[a]}return{dataId:a.write(I,l,r.dtype),shape:l,dtype:r.dtype}}},$o={kernelName:xt,backendName:"cpu",kernelFunc:Vn(xt,An(((e,t)=>{const n=e%t;return e<0&&t<0||e>=0&&t>=0?n:(n+t)%t})))};function Oo(t){const{inputs:n,backend:a,attrs:s}=t,{logits:r}=n,{dim:i}=s,l=r.shape.length;let d=i;if(-1===d&&(d=l-1),d!==l-1)throw Error(`Softmax along a non-last dimension is not yet supported. Logits was rank ${l} and dim was ${d}`);const c=e.parseAxisParam([d],r.shape),p=Mo({inputs:{x:r},backend:a,attrs:{reductionIndices:c,keepDims:!1}}),u=o.expandShapeToKeepDim(p.shape,c),h=Vs({inputs:{x:p},backend:a,attrs:{shape:u}}),f=bs({inputs:{a:r,b:h},backend:a}),m=aa({inputs:{x:f},backend:a}),k=Gr({inputs:{x:m},backend:a,attrs:{axis:c,keepDims:!1}}),g=Vs({inputs:{x:k},backend:a,attrs:{shape:u}}),I=no({inputs:{a:m,b:g},backend:a});return a.disposeIntermediateTensorInfo(p),a.disposeIntermediateTensorInfo(h),a.disposeIntermediateTensorInfo(f),a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(k),a.disposeIntermediateTensorInfo(g),I}const Vo={kernelName:St,backendName:"cpu",kernelFunc:Oo};const _o={kernelName:vt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{logits:r}=n,{numSamples:o,seed:i,normalized:l}=s;Sn(r,"multinomial");const d=l?r:Oo({inputs:{logits:r},backend:a,attrs:{dim:-1}}),c=d.shape[0],p=d.shape[1],u=a.data.get(d.dataId).values,h=[c,o],f=e.makeZerosTypedArray(e.sizeFromShape(h),"int32");for(let e=0;e<c;++e){const t=e*p,n=new Float32Array(p-1);n[0]=u[t];for(let e=1;e<n.length;++e)n[e]=n[e-1]+u[t+e];const a=xn.alea(i.toString()),s=e*o;for(let e=0;e<o;++e){const t=a();f[s+e]=n.length;for(let a=0;a<n.length;a++)if(t<n[a]){f[s+e]=a;break}}}return l||a.disposeIntermediateTensorInfo(d),a.makeTensorInfo(h,"int32",f)}},Go=t.nonMaxSuppressionV3Impl;const Bo={kernelName:Ft,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{boxes:s,scores:r}=t,{maxOutputSize:o,iouThreshold:i,scoreThreshold:l}=a;Sn(s,"NonMaxSuppression");const d=n.data.get(s.dataId).values,c=n.data.get(r.dataId).values,{selectedIndices:p}=Go(d,c,o,i,l);return n.makeTensorInfo([p.length],"int32",new Int32Array(p))}},Lo=t.nonMaxSuppressionV4Impl;const qo={kernelName:wt,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{boxes:s,scores:r}=t,{maxOutputSize:o,iouThreshold:i,scoreThreshold:l,padToMaxOutputSize:d}=a;Sn(s,"NonMaxSuppressionPadded");const c=n.data.get(s.dataId).values,p=n.data.get(r.dataId).values,{selectedIndices:u,validOutputs:h}=Lo(c,p,o,i,l,d);return[n.makeTensorInfo([u.length],"int32",new Int32Array(u)),n.makeTensorInfo([],"int32",new Int32Array([h]))]}},Uo=t.nonMaxSuppressionV5Impl;const Zo={kernelName:Mt,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{boxes:s,scores:r}=t,{maxOutputSize:o,iouThreshold:i,scoreThreshold:l,softNmsSigma:d}=a;Sn(s,"NonMaxSuppressionWithScore");const c=n.data.get(s.dataId).values,p=n.data.get(r.dataId).values,u=o,h=i,f=l,m=d,{selectedIndices:k,selectedScores:g}=Uo(c,p,u,h,f,m);return[n.makeTensorInfo([k.length],"int32",new Int32Array(k)),n.makeTensorInfo([g.length],"float32",new Float32Array(g))]}};const Ko={kernelName:At,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{indices:r}=n,{dtype:o,depth:i,onValue:l,offValue:d}=s;Sn(r,"oneHot");const c=e.sizeFromShape(r.shape),p=new Float32Array(c*i);p.fill(d);const u=a.data.get(r.dataId).values;for(let e=0;e<c;++e)u[e]>=0&&u[e]<i&&(p[e*i+u[e]]=l);return a.makeTensorInfo([...r.shape,i],o,p)}};function jo(e){const{inputs:t,backend:n}=e,{x:a}=t;if("string"===a.dtype)throw new Error("zerosLike is not supported for string tensors");if("complex64"===a.dtype){const e=Pn({inputs:{input:a},backend:n}),t=jo({inputs:{x:e},backend:n}),s=kr({inputs:{input:a},backend:n}),r=jo({inputs:{x:s},backend:n}),o=Dn({inputs:{real:t,imag:r},backend:n});return n.disposeIntermediateTensorInfo(e),n.disposeIntermediateTensorInfo(t),n.disposeIntermediateTensorInfo(s),n.disposeIntermediateTensorInfo(r),o}return lo({backend:n,attrs:{shape:a.shape,value:0,dtype:a.dtype}})}const Yo={kernelName:Dt,backendName:"cpu",kernelFunc:jo};const Jo={kernelName:Et,backendName:"cpu",kernelFunc:function e(t){const{inputs:n,backend:a}=t,{x:s}=n;if("string"===s.dtype)throw new Error("onesLike is not supported for string tensors");if("complex64"===s.dtype){const t=Pn({inputs:{input:s},backend:a}),n=e({inputs:{x:t},backend:a}),r=kr({inputs:{input:s},backend:a}),o=jo({inputs:{x:r},backend:a}),i=Dn({inputs:{real:n,imag:o},backend:a});return a.disposeIntermediateTensorInfo(t),a.disposeIntermediateTensorInfo(n),a.disposeIntermediateTensorInfo(r),a.disposeIntermediateTensorInfo(o),i}return lo({backend:a,attrs:{shape:s.shape,value:1,dtype:s.dtype}})}};function Qo(t){const{inputs:n,backend:a,attrs:s}=t,{axis:r}=s;if(1===n.length)return eo({inputs:{input:n[0]},backend:a,attrs:{dim:r}});const o=n[0].shape,i=n[0].dtype;n.forEach((t=>{e.assertShapesMatch(o,t.shape,"All tensors passed to stack must have matching shapes"),e.assert(i===t.dtype,(()=>"All tensors passed to stack must have matching dtypes"))}));const l=[],d=Ir({inputs:n.map((e=>{const t=eo({inputs:{input:e},backend:a,attrs:{dim:r}});return l.push(t),t})),backend:a,attrs:{axis:r}});return l.forEach((e=>a.disposeIntermediateTensorInfo(e))),d}const Xo={kernelName:zt,backendName:"cpu",kernelFunc:Qo};const ei={kernelName:Wt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{paddings:o,constantValue:i}=s;Sn(r,"pad");const l=o.map(((e,t)=>e[0]+r.shape[t]+e[1])),d=o.map((e=>e[0])),c=a.data.get(r.dataId).values,p=e.sizeFromShape(r.shape),u=r.shape.length,h=e.computeStrides(r.shape),f=e.sizeFromShape(l),m=l.length,k=e.computeStrides(l),g=e.getTypedArrayFromDType(r.dtype,f);0!==i&&g.fill(i);for(let t=0;t<p;t++){const n=e.indexToLoc(t,u,h).map(((e,t)=>e+d[t]));g[e.locToIndex(n,m,k)]=c[t]}return{dataId:a.write(g,l,r.dtype),shape:l,dtype:r.dtype}}},ti={kernelName:Rt,backendName:"cpu",kernelFunc:Vn(Rt,An(((e,t)=>Math.pow(e,t))))};const ni={kernelName:Pt,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{paramsNestedSplits:s,paramsDenseValues:r,indices:o}=t,i=s.map((e=>n.data.get(e.dataId).values)),l=s.map((e=>e.shape)),d=n.data.get(r.dataId).values,c=n.data.get(o.dataId).values,[p,u,h]=Ba(i,l,d,r.shape,r.dtype,c,o.shape),f=p.map((e=>n.makeTensorInfo([e.length],"int32",e))),m=n.makeTensorInfo(h,r.dtype,u);return f.concat([m])}};const ai={kernelName:Ht,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{shape:s,values:r,defaultValue:o,rowPartitionTensors:i}=t,{rowPartitionTypes:l}=a,d=n.data.get(s.dataId).values,c=n.data.get(r.dataId).values,p=n.data.get(o.dataId).values,u=i.map((e=>n.data.get(e.dataId).values)),h=i.map((e=>e.shape)),[f,m]=Ka(d,s.shape,c,r.shape,r.dtype,p,o.shape,u,h,l);return n.makeTensorInfo(f,r.dtype,m)}};const si={kernelName:Ct,backendName:"cpu",kernelFunc:function(e){const{backend:t,attrs:n}=e,{start:a,stop:s,dtype:r,step:o}=n,i=ja(a,s,o,r);return t.makeTensorInfo([i.length],r,i)}},ri={kernelName:$t,backendName:"cpu",kernelFunc:Kn($t,(e=>1/e))};const oi={kernelName:Ot,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{images:r}=n,{alignCorners:o,halfPixelCenters:i,size:l}=s;Sn(r,"resizeBilinear");const d=e.computeStrides(r.shape),[c,p]=l,[u,h,f,m]=r.shape,k=a.data.get(r.dataId).values,g=new Float32Array(e.sizeFromShape([u,c,p,m])),I=[o&&c>1?h-1:h,o&&p>1?f-1:f],b=[o&&c>1?c-1:c,o&&p>1?p-1:p];let y=0;const N=I[0]/b[0],T=I[1]/b[1];for(let e=0;e<u;e++)for(let t=0;t<c;t++){let n;n=i?N*(t+.5)-.5:N*t;const a=Math.max(0,Math.floor(n)),s=n-a,r=Math.min(h-1,Math.ceil(n)),o=e*d[0]+a*d[1],l=e*d[0]+r*d[1];for(let e=0;e<p;e++){let t;t=i?T*(e+.5)-.5:T*e;const n=Math.max(0,Math.floor(t)),a=t-n,r=Math.min(f-1,Math.ceil(t)),c=o+n*d[2],p=l+n*d[2],u=o+r*d[2],h=l+r*d[2];for(let e=0;e<m;e++){const t=k[c+e],n=k[p+e],r=t+(k[u+e]-t)*a,o=r+(n+(k[h+e]-n)*a-r)*s;g[y++]=o}}}return a.makeTensorInfo([u,c,p,m],"float32",g)}};const ii={kernelName:Vt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{images:r,dy:o}=n,{alignCorners:i}=s;Sn([o,r],"resizeBilinearGrad");const l=e.computeStrides(r.shape),[d,c,p,u]=r.shape,[,h,f]=o.shape,m=new Float32Array(d*c*p*u),k=[i&&h>1?c-1:c,i&&f>1?p-1:p],g=[i&&h>1?h-1:h,i&&f>1?f-1:f],I=k[0]/g[0],b=k[1]/g[1],y=a.data.get(o.dataId).values;let N=0;for(let e=0;e<d;e++){const t=e*l[0];for(let e=0;e<h;e++){const n=e*I,a=Math.floor(n),s=Math.min(Math.ceil(n),c-1),r=t+a*l[1],o=t+s*l[1],i=n-a,d=1-i;for(let e=0;e<f;e++){const t=e*b,n=Math.floor(t),a=Math.min(Math.ceil(t),p-1),s=t-n,c=1-s,h=r+n*l[2],f=r+a*l[2],k=o+n*l[2],g=o+a*l[2],I=d*c,T=d*s,x=i*c,S=i*s;for(let e=0;e<u;e++){const t=y[N++];m[h+e]+=t*I,m[f+e]+=t*T,m[k+e]+=t*x,m[g+e]+=t*S}}}}return a.makeTensorInfo([d,p,c,u],"float32",m)}};const li={kernelName:_t,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{images:r}=n,{alignCorners:o,halfPixelCenters:i,size:l}=s;Sn(r,"resizeNearestNeighbor");const d=e.computeStrides(r.shape),[c,p]=l,[u,h,f,m]=r.shape,k=a.data.get(r.dataId).values,g=new Float32Array(u*c*p*m),I=[o&&c>1?h-1:h,o&&p>1?f-1:f],b=[o&&c>1?c-1:c,o&&p>1?p-1:p],y=I[0]/b[0],N=I[1]/b[1];let T=0;for(let e=0;e<u;e++){const t=e*d[0];for(let e=0;e<c;e++){const n=i?y*(e+.5):y*e;let a=Math.min(h-1,o?Math.round(n):Math.floor(n));i&&(a=Math.max(0,a));const s=t+a*d[1];for(let e=0;e<p;e++){const t=i?N*(e+.5):N*e;let n=Math.min(f-1,o?Math.round(t):Math.floor(t));i&&(n=Math.max(0,n));const a=s+n*d[2];for(let e=0;e<m;e++){const t=k[a+e];g[T++]=t}}}}return a.makeTensorInfo([u,c,p,m],r.dtype,g)}};const di={kernelName:Gt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{images:r,dy:o}=n,{alignCorners:i}=s;Sn([o,r],"resizeNearestNeighborGrad");const l=e.computeStrides(r.shape),d=e.computeStrides(o.shape),[c,p,u,h]=r.shape,[,f,m]=o.shape,k=new Float32Array(c*p*u*h),g=a.data.get(o.dataId).values,I=[i&&f>1?p-1:p,i&&m>1?u-1:u],b=[i&&f>1?f-1:f,i&&m>1?m-1:m],y=I[0]/b[0],N=I[1]/b[1],T=1/y,x=1/N,S=2*Math.ceil(T)+2,v=2*Math.ceil(x)+2;for(let e=0;e<c;e++){const t=e*l[0];for(let e=0;e<p;e++){const n=t+e*l[1],a=Math.floor(e*T),s=Math.floor(a-S/2);for(let a=0;a<u;a++){const r=n+a*l[2],o=Math.floor(a*x),c=Math.floor(o-v/2);for(let n=0;n<h;n++){let o=0;for(let r=0;r<S;r++){const l=r+s;if(l<0||l>=f)continue;const h=t+l*d[1],k=l*y;if(e===Math.min(p-1,i?Math.round(k):Math.floor(k)))for(let e=0;e<v;e++){const t=e+c;if(t<0||t>=m)continue;const s=h+t*d[2],r=t*N;a===Math.min(u-1,i?Math.round(r):Math.floor(r))&&(o+=g[s+n])}}k[r+n]=o}}}}return a.makeTensorInfo(r.shape,r.dtype,k)}};const ci={kernelName:Bt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{dims:o}=s;Sn(r,"reverse");const i=r.shape.length,l=e.parseAxisParam(o,r.shape);if(0===i)return Wn({inputs:{x:r},backend:a});const d=new G(r.shape,r.dtype),c=a.bufferSync(r);for(let e=0;e<d.size;e++){const t=d.indexToLoc(e),n=t.slice();l.forEach((e=>n[e]=r.shape[e]-1-n[e])),d.set(c.get(...n),...t)}return a.makeTensorInfo(d.shape,d.dtype,d.values)}},pi={kernelName:Lt,backendName:"cpu",kernelFunc:({inputs:t,attrs:n,backend:a})=>{const{image:s}=t,{radians:r,fillValue:i,center:l}=n,d=a,c=e.getTypedArrayFromDType(s.dtype,e.sizeFromShape(s.shape)),[p,u,h,f]=s.shape,[m,k]=o.getImageCenter(l,u,h),g=Math.sin(r),I=Math.cos(r),b=d.data.get(s.dataId).values;for(let e=0;e<p;e++){const t=e*h*u*f;for(let e=0;e<u;e++){const n=e*(h*f);for(let a=0;a<h;a++){const s=a*f;for(let r=0;r<f;r++){const o=[p,e,a,r],l=o[2],d=o[1];let y=(l-m)*I-(d-k)*g,N=(l-m)*g+(d-k)*I;y=Math.round(y+m),N=Math.round(N+k);let T=i;if("number"!=typeof i&&(T=3===r?255:i[r]),y>=0&&y<h&&N>=0&&N<u){T=b[t+N*(h*f)+y*f+r]}c[t+n+s+r]=T}}}}return{dataId:d.write(c,s.shape,s.dtype),shape:s.shape,dtype:s.dtype}}},ui={kernelName:qt,backendName:"cpu",kernelFunc:Kn(qt,(e=>{const t=Math.floor(e);return e-t<.5?Math.floor(e):e-t>.5?Math.ceil(e):t%2==0?t:t+1}))};const hi={kernelName:Ut,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{indices:s,updates:r}=t,{shape:i}=a,{sliceRank:l,numUpdates:d,sliceSize:c,strides:p,outputSize:u}=o.calculateShapes(r,s,i),h=Qa(n.bufferSync(s),n.bufferSync(r),i,u,c,d,l,p,0,!0);return n.makeTensorInfo(i,h.dtype,h.values)}};function fi(e,t){let n=0,a=e.length,s=0;for(;n<a;)s=Math.floor((n+a)/2),e[s]<t?n=s+1:a=s;return a}function mi(e,t){let n=0,a=e.length,s=0;for(;n<a;)s=Math.floor((n+a)/2),e[s]<=t?n=s+1:a=s;return a}const ki={kernelName:Zt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{sortedSequence:r,values:o}=n,{side:i}=s,l=function(t,n,a,s,r,o){const i=e.getArrayFromDType("int32",a*r);for(let e=0;e<a;++e){const a=t.slice(e*s,(e+1)*s),l=e*r;for(let e=0;e<r;++e)i[l+e]="left"===o?fi(a,n[e+l]):mi(a,n[e+l])}return i}(a.data.get(r.dataId).values,a.data.get(o.dataId).values,r.shape[0],r.shape[1],o.shape[1],i);return a.makeTensorInfo(o.shape,"int32",l)}};const gi={kernelName:Kt,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a}=t,{condition:s,t:r,e:o}=n;Sn([s,r,o],"select");const i=s.shape.length,l=a.data.get(s.dataId).values,d=a.data.get(r.dataId).values,c=a.data.get(o.dataId).values,p=D(r.dtype,o.dtype),u=e.makeZerosTypedArray(e.sizeFromShape(r.shape),p);let h=0;const f=0===i||i>1||1===r.shape.length?1:e.sizeFromShape(r.shape.slice(1));for(let e=0;e<l.length;e++)for(let t=0;t<f;t++)1===l[e]?u[h++]=d[e]:u[h++]=c[e];return a.makeTensorInfo(r.shape,p,u)}},Ii=o.SELU_SCALEALPHA,bi=o.SELU_SCALE,yi={kernelName:jt,backendName:"cpu",kernelFunc:Kn(jt,(e=>e>=0?bi*e:Ii*(Math.exp(e)-1)))},Ni={kernelName:Yt,backendName:"cpu",kernelFunc:Kn(Yt,(e=>e<0?-1:e>0?1:0))},Ti={kernelName:Jt,backendName:"cpu",kernelFunc:Kn(Jt,(e=>Math.sin(e)))},xi={kernelName:Qt,backendName:"cpu",kernelFunc:Kn(Qt,(e=>Math.sinh(e)))},Si=Math.log(1.1920928955078125e-7)+2,vi={kernelName:Xt,backendName:"cpu",kernelFunc:Kn(Xt,(e=>{const t=e>-Si,n=e<Si,a=Math.exp(e);let s;return s=n?a:t?e:Math.log(1+a),s}))};const Fi={kernelName:en,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{blockShape:i,paddings:l}=s;Sn([r],"spaceToBatchND");const d=e.sizeFromShape(i),c=[[0,0]];c.push(...l);for(let e=1+i.length;e<r.shape.length;++e)c.push([0,0]);const p=ei.kernelFunc({inputs:{x:r},backend:a,attrs:{paddings:c,constantValue:0}}),u=o.getReshaped(p.shape,i,d,!1),h=o.getPermuted(u.length,i.length,!1),f=o.getReshapedPermuted(p.shape,i,d,!1),m=Vs({inputs:{x:p},backend:a,attrs:{shape:u}}),k=Ha({inputs:{x:m},backend:a,attrs:{perm:h}}),g=Vs({inputs:{x:k},backend:a,attrs:{shape:f}});return a.disposeIntermediateTensorInfo(p),a.disposeIntermediateTensorInfo(m),a.disposeIntermediateTensorInfo(k),g}};const wi={kernelName:tn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{indices:a,values:s,denseShape:r,defaultValue:o}=t;if(1!==r.shape.length)throw new Error(`Dense shape must be a vector, saw:\n        ${r.shape}`);if(2!==a.shape.length)throw new Error(`Indices must be a matrix, saw:\n        ${a.shape}`);if(1!==s.shape.length)throw new Error(`Values must be a vector, saw:\n        ${s.shape}`);if(0!==o.shape.length)throw new Error(`Default value must be a scalar, saw:\n        ${o.shape}`);const i=n.data.get(a.dataId).values,l=n.data.get(s.dataId).values,d=n.data.get(r.dataId).values,c=n.data.get(o.dataId).values[0],[p,u,h,f,m]=rs(i,a.shape,a.dtype,l,s.dtype,d,c);return[n.makeTensorInfo(u,a.dtype,p),n.makeTensorInfo([u[0]],s.dtype,h),n.makeTensorInfo([f.length],"bool",new Uint8Array(f.map((e=>Number(e))))),n.makeTensorInfo([m.length],a.dtype,new Int32Array(m))]}};const Mi={kernelName:nn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{inputIndices:a,inputShape:s,newShape:r}=t;if(2!==a.shape.length)throw new Error(`Input indices should be a matrix but received shape\n        ${a.shape}`);if(1!==s.shape.length)throw new Error(`Input shape should be a vector but received shape\n        ${s.shape}`);if(1!==r.shape.length)throw new Error(`Target shape should be a vector but received shape ${r.shape}`);const o=Array.from(n.data.get(s.dataId).values),i=n.data.get(a.dataId).values,l=Array.from(n.data.get(r.dataId).values),[d,c,p]=os(i,a.shape,a.dtype,o,l);return[n.makeTensorInfo(c,a.dtype,d),n.makeTensorInfo([p.length],r.dtype,new Int32Array(p))]}};const Ai={kernelName:an,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{data:a,indices:s,segmentIds:r}=t;if(a.shape.length<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==s.shape.length)throw new Error(`Indices should be a vector but received shape\n          ${s.shape}`);if(1!==r.shape.length)throw new Error(`Segment ids should be a vector but received shape\n          ${r.shape}`);if(s.shape[0]!==r.shape[0])throw new Error("segmentIds and indices should have same size.");const o=n.data.get(a.dataId).values,i=n.data.get(s.dataId).values,l=n.data.get(r.dataId).values,[d,c]=is(o,a.shape,a.dtype,i,l,!0);return n.makeTensorInfo(c,a.dtype,d)}};const Di={kernelName:sn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n}=e,{data:a,indices:s,segmentIds:r}=t;if(a.shape.length<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==s.shape.length)throw new Error(`Indices should be a vector but received shape\n         ${s.shape}`);if(1!==r.shape.length)throw new Error(`Segment ids should be a vector but received shape\n         ${r.shape}`);if(s.shape[0]!==r.shape[0])throw new Error("segmentIds and indices should have same size.");const o=n.data.get(a.dataId).values,i=n.data.get(s.dataId).values,l=n.data.get(r.dataId).values,[d,c]=is(o,a.shape,a.dtype,i,l);return n.makeTensorInfo(c,a.dtype,d)}};const Ei={kernelName:rn,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{sparseIndices:r,sparseValues:i,defaultValue:l}=n,{outputShape:d}=s,{sliceRank:c,numUpdates:p,sliceSize:u,strides:h,outputSize:f}=o.calculateShapes(i,r,d),m=!1,k=a.bufferSync(r);let g;switch(i.dtype){case"bool":g=Qa(k,a.bufferSync(i),d,f,u,p,c,h,Boolean(a.data.get(l.dataId).values[0]),m);break;case"float32":g=Qa(k,a.bufferSync(i),d,f,u,p,c,h,a.data.get(l.dataId).values[0],m);break;case"int32":g=Qa(k,a.bufferSync(i),d,f,u,p,c,h,a.data.get(l.dataId).values[0],m);break;case"string":g=Qa(k,a.bufferSync(i),d,f,u,p,c,h,e.decodeString(a.data.get(l.dataId).values[0]),m);break;default:throw new Error(`Unsupported type ${i.dtype}`)}return a.makeTensorInfo(d,g.dtype,g.values)}};const zi={kernelName:on,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{numOrSizeSplits:i,axis:l}=s,d=e.parseAxisParam(l,r.shape)[0],c=o.prepareSplitSize(r,i,d),p=new Array(r.shape.length).fill(0),u=r.shape.slice();return c.map((e=>{const t=[...u];t[d]=e;const n=as({inputs:{x:r},backend:a,attrs:{begin:p,size:t}});return p[d]+=e,n}))}},Wi={kernelName:ln,backendName:"cpu",kernelFunc:({inputs:e,backend:t})=>{const{x:n}=e,a=t;Sn(n,"square");const s=a.data.get(n.dataId).values,r=new Float32Array(s.length);for(let e=0;e<s.length;++e){const t=s[e];r[e]=t*t}return{dataId:a.write(r,n.shape,n.dtype),shape:n.shape,dtype:n.dtype}}},Ri={kernelName:dn,backendName:"cpu",kernelFunc:Kn(dn,((e,t)=>{const n=t;return isNaN(e)?NaN:e>0?1:n.alpha}))};const Pi={kernelName:cn,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r}=n,{begin:o,end:i,strides:l,beginMask:d,endMask:c,ellipsisMask:p,newAxisMask:u,shrinkAxisMask:h}=s;Sn(r,"stridedSlice");const{finalShapeSparse:f,finalShape:m,isIdentity:k,sliceDim0:g,isSimpleSlice:I,begin:b,end:y,strides:N}=C.sliceInfo(r.shape,o,i,l,d,c,p,u,h);let T;if(k)T=Vs({inputs:{x:r},backend:a,attrs:{shape:m}});else if(g||I){e.assert(r.shape.length>=1,(()=>`Input must have rank at least 1, got: ${r.shape.length}`));const t=C.computeOutShape(b,y,N),n=as({inputs:{x:r},backend:a,attrs:{begin:b,size:t}});T=Vs({inputs:{x:n},backend:a,attrs:{shape:m}}),a.disposeIntermediateTensorInfo(n)}else{const e=us(f,a.bufferSync(r),N,b);T=a.makeTensorInfo(m,e.dtype,e.values)}return T}};const Hi={kernelName:pn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{separator:s,nGramWidths:r,leftPad:o,rightPad:i,padWidth:l,preserveShortSequences:d}=a,{data:c,dataSplits:p}=t,u=n.data.get(c.dataId).values,h=n.data.get(p.dataId).values,[f,m]=fs(u,h,s,r,o,i,l,d);return[n.makeTensorInfo([f.length],"string",f),n.makeTensorInfo(p.shape,"int32",m)]}};const Ci={kernelName:un,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{skipEmpty:s}=a,{input:r,delimiter:o}=t;if("string"!==r.dtype)throw new Error("Input must be of datatype string");if(1!==r.shape.length)throw new Error(`Input must be a vector, got shape: ${r.shape}`);if(0!==o.shape.length)throw new Error(`Delimiter must be a scalar, got shape: ${o.shape}`);const i=n.data.get(r.dataId).values,l=n.data.get(o.dataId).values[0],[d,c,p]=ks(i,l,s),u=c.length;return[n.makeTensorInfo([u,2],"int32",d),n.makeTensorInfo([u],"string",c),n.makeTensorInfo([2],"int32",new Int32Array(p))]}};const $i={kernelName:hn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{numBuckets:s}=a,{input:r}=t;if("string"!==r.dtype)throw new Error("Input must be of datatype string");if(s<=0)throw new Error("Number of buckets must be at least 1");const o=gs(n.data.get(r.dataId).values,s);return n.makeTensorInfo(r.shape,"int32",o)}},Oi={kernelName:fn,backendName:"cpu",kernelFunc:Kn(fn,(e=>Math.tan(e)))},Vi=Kn(mn,(e=>Math.tanh(e)));const _i={kernelName:In,backendName:"cpu",kernelFunc:function(t){const{inputs:n,attrs:a,backend:s}=t,{image:r,transforms:o}=n,{interpolation:i,fillMode:l,fillValue:d,outputShape:c}=a,[p,u,h,f]=r.shape,[m,k]=null!=c?c:[u,h],g=[p,m,k,f],I=e.computeStrides(r.shape),b=I[0],y=I[1],N=I[2],T=e.computeStrides(g),x=T[0],S=T[1],v=T[2],F=e.getTypedArrayFromDType(r.dtype,e.sizeFromShape(g));F.fill(d);const w=s.data.get(r.dataId).values,M=s.data.get(o.dataId).values;for(let e=0;e<p;++e){const t=1===o.shape[0]?M:M.subarray(8*e,8*e+8);for(let n=0;n<m;++n)for(let a=0;a<k;++a)for(let s=0;s<f;++s){let r;const o=t[6]*a+t[7]*n+1;if(0===o)continue;const c=(t[0]*a+t[1]*n+t[2])/o,p=(t[3]*a+t[4]*n+t[5])/o,f=Gi(c,h,l),m=Gi(p,u,l);switch(i){case"nearest":r=Li(w,u,h,b,y,N,e,m,f,s,d);break;case"bilinear":r=qi(w,u,h,b,y,N,e,m,f,s,d);break;default:throw new Error(`Error in Transform: Expect 'nearest' or 'bilinear', but got ${i}`)}F[e*x+n*S+a*v+s]=r}return s.makeTensorInfo(g,r.dtype,F)}return{dataId:s.write(F,g,r.dtype),shape:r.shape,dtype:r.dtype}}};function Gi(t,n,a){switch(a){case"reflect":return function(t,n){let a=t;if(a<0)if(n<=1)a=0;else{const e=2*n;a<e&&(a=e*Math.trunc(-a/e)+a),a=a<-n?a+e:-a-1}else if(a>n-1)if(n<=1)a=0;else{const e=2*n;a-=e*Math.trunc(a/e),a>=n&&(a=e-a-1)}return e.clamp(0,a,n-1)}(t,n);case"wrap":return function(t,n){let a=t;if(a<0)if(n<=1)a=0;else{const e=n-1;a+=n*(Math.trunc(-a/e)+1)}else if(a>n-1)if(n<=1)a=0;else{const e=n-1;a-=n*Math.trunc(a/e)}return e.clamp(0,a,n-1)}(t,n);case"nearest":return function(t,n){return e.clamp(0,t,n-1)}(t,n);default:return function(e,t){return e}(t)}}function Bi(e,t,n,a,s,r,o,i,l,d,c){return 0<=i&&i<t&&0<=l&&l<n?e[o*a+i*s+l*r+d]:c}function Li(e,t,n,a,s,r,o,i,l,d,c){return Bi(e,t,n,a,s,r,o,Math.round(i),Math.round(l),d,c)}function qi(e,t,n,a,s,r,o,i,l,d,c){const p=Math.floor(i),u=Math.floor(l),h=p+1,f=u+1;return(h-i)*((f-l)*Bi(e,t,n,a,s,r,o,p,u,d,c)+(l-u)*Bi(e,t,n,a,s,r,o,p,f,d,c))+(i-p)*((f-l)*Bi(e,t,n,a,s,r,o,h,u,d,c)+(l-u)*Bi(e,t,n,a,s,r,o,h,f,d,c))}const Ui=[Ls,Mn,qs,Us,Ln,Zs,Ks,js,Ys,Js,Qs,Xs,er,tr,nr,or,ir,lr,dr,Bs,cr,pr,ur,hr,On,Jn,fr,En,mr,br,Nr,Tr,xr,Sr,vr,Fr,wr,Mr,Ar,Dr,Er,zr,Wr,Pr,Hr,Cr,$r,Or,Vr,_r,Lr,As,qr,ta,Xr,sa,to,oa,io,co,po,la,uo,ho,fo,mo,ko,ua,fa,Rn,go,gr,Io,bo,yo,Es,ka,Ia,No,Na,To,xo,So,vo,Fo,wo,Ao,Sa,Do,Eo,zo,Wo,Ro,Po,Ho,Fa,Co,$o,_o,Da,za,Bo,qo,Zo,Ra,Ko,Jo,Xo,ei,ti,Rs,Oa,ni,ai,si,Hn,ao,ri,Hs,$s,_s,oi,ii,li,di,ci,pi,ui,Ja,hi,ki,gi,yi,ts,Ni,Ti,xi,ss,Vo,vi,Fi,wi,Mi,Ai,Di,Ei,zi,ds,Wi,ps,Ri,Pi,Hi,Ci,$i,ys,Br,Oi,{kernelName:mn,backendName:"cpu",kernelFunc:Vi},{kernelName:kn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s}=t,{reps:r}=a;Sn(s,"tile");const o=Ns(n.bufferSync(s),r);return n.makeTensorInfo(o.shape,o.dtype,o.values)}},{kernelName:gn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{x:s}=t,{k:r,sorted:o}=a;Sn(s,"topk");const i=n.data.get(s.dataId).values,[l,d]=Ss(i,s.shape,s.dtype,r,o);return[n.makeTensorInfo(l.shape,l.dtype,l.values),n.makeTensorInfo(d.shape,d.dtype,d.values)]}},_i,Ca,{kernelName:bn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,attrs:n,backend:a}=e,{axis:s}=n,{x:r}=t;Sn(r,"unique");const o=a.data.get(r.dataId).values,{outputValues:i,outputShape:l,indices:d}=vs(o,s,r.shape,r.dtype);return[a.makeTensorInfo(l,r.dtype,i),a.makeTensorInfo([d.length],"int32",d)]}},{kernelName:yn,backendName:"cpu",kernelFunc:function(e){const{inputs:t,backend:n,attrs:a}=e,{value:s}=t;let{axis:r}=a;r<0&&(r+=s.shape.length);const o=s.shape.length,i=s.shape[r],l=new Array(o-1);let d=0;for(let e=0;e<o;e++)e!==r&&(l[d++]=s.shape[e]);const c=new Array(o).fill(0),p=s.shape.slice();p[r]=1;const u=new Array(i);for(let e=0;e<u.length;e++){c[r]=e;const t=as({inputs:{x:s},backend:n,attrs:{begin:c,size:p}});u[e]=Vs({inputs:{x:t},backend:n,attrs:{shape:l}}),n.disposeIntermediateTensorInfo(t)}return u}},{kernelName:Nn,backendName:"cpu",kernelFunc:function(t){const{inputs:n,backend:a,attrs:s}=t,{x:r,segmentIds:o}=n,{numSegments:i}=s;Sn(r,"unsortedSegmentSum");const l=[],d=[],c=r.shape.length-o.shape.length;let p=o;for(let e=0;e<c;++e){const t=eo({inputs:{input:p},backend:a,attrs:{dim:e+1}});p=t,d.push(t)}for(let t=0;t<i;++t){const n=e.createScalarValue(t,"int32"),s=a.makeTensorInfo([],"int32",n),o=ea({inputs:{a:s,b:p},backend:a}),i=$n({inputs:{x:o},backend:a,attrs:{dtype:"float32"}}),c=Aa({inputs:{a:i,b:r},backend:a}),u=Gr({inputs:{x:c},backend:a,attrs:{axis:0,keepDims:!1}});l.push(u),d.push(s),d.push(o),d.push(i),d.push(c),d.push(u)}const u=Qo({inputs:l,backend:a,attrs:{axis:0}});return d.forEach((e=>a.disposeIntermediateTensorInfo(e))),u}},Yo];for(const e of Ui)Tn(e);export{Fn as MathBackendCPU,Fs as shared,ws as version_cpu};
//# sourceMappingURL=tf-backend-cpu.fesm.min.js.map

{"name": "@tensorflow/tfjs-backend-webgl", "version": "3.21.0", "description": "GPU accelerated WebGL backend for TensorFlow.js", "private": false, "main": "dist/tf-backend-webgl.node.js", "jsdelivr": "dist/tf-backend-webgl.min.js", "unpkg": "dist/tf-backend-webgl.min.js", "types": "dist/index.d.ts", "jsnext:main": "dist/index.js", "module": "dist/index.js", "miniprogram": "dist/miniprogram", "engines": {"yarn": ">= 1.3.2"}, "repository": {"type": "git", "url": "https://github.com/tensorflow/tfjs.git"}, "license": "Apache-2.0", "devDependencies": {"@babel/polyfill": "^7.8.7", "@bazel/bazelisk": "^1.12.0"}, "scripts": {"build-ci": "yarn build", "build": "bazel build :tfjs-backend-webgl_pkg", "bundle": "bazel build :tfjs-backend-webgl_pkg", "bundle-ci": "yarn bundle", "build-link-package": "cd ../link-package && yarn build", "build-deps": "yarn build-link-package", "build-deps-ci": "yarn build-deps", "build-npm": "bazel build :tfjs-backend-webgl_pkg", "link-local": "yalc link", "publish-npm": "bazel run :tfjs-backend-webgl_pkg.publish", "test": "yarn test-dev", "test-dev": "bazel test :tests --test_output=streamed", "run-browserstack": "bazel test :browserstack_bs_chrome_mac_tfjs-backend-webgl2_test"}, "dependencies": {"@tensorflow/tfjs-backend-cpu": "3.21.0", "@types/offscreencanvas": "~2019.3.0", "@types/seedrandom": "^2.4.28", "@types/webgl-ext": "0.0.30", "@types/webgl2": "0.0.6", "seedrandom": "^3.0.5"}, "peerDependencies": {"@tensorflow/tfjs-core": "3.21.0"}, "browser": {"util": false, "crypto": false}, "sideEffects": ["./dist/register_all_kernels.js", "./dist/flags_webgl.js", "./dist/base.js", "./dist/index.js", "./dist/register_all_kernels.mjs", "./dist/flags_webgl.mjs", "./dist/base.mjs", "./dist/index.mjs", "./src/register_all_kernels.mjs", "./src/flags_webgl.mjs", "./src/base.mjs", "./src/index.mjs"]}
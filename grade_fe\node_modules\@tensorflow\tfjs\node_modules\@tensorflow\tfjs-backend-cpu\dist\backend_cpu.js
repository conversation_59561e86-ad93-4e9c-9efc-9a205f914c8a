/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { backend_util, buffer, DataStorage, engine, env, kernel_impls, KernelBackend, util } from '@tensorflow/tfjs-core';
const whereImpl = kernel_impls.whereImpl;
import { assertNotComplex } from './cpu_util';
export class MathBackendCPU extends KernelBackend {
    constructor() {
        super();
        this.blockSize = 48;
        this.firstUse = true;
        this.data = new DataStorage(this, engine());
    }
    nextDataId() {
        return MathBackendCPU.nextDataId++;
    }
    write(values, shape, dtype) {
        if (this.firstUse) {
            this.firstUse = false;
            if (env().get('IS_NODE')) {
                backend_util.warn('\n============================\n' +
                    'Hi, looks like you are running TensorFlow.js in ' +
                    'Node.js. To speed things up dramatically, install our node ' +
                    'backend, visit https://github.com/tensorflow/tfjs-node for more details. ' +
                    '\n============================');
            }
        }
        const dataId = { id: this.nextDataId() };
        this.data.set(dataId, { values, dtype, refCount: 1 });
        return dataId;
    }
    /**
     * Create a data bucket in cpu backend.
     * @param shape Shape of the `TensorInfo`.
     * @param dtype DType of the `TensorInfo`.
     * @param values The value of the `TensorInfo` stored as a flattened array.
     */
    makeTensorInfo(shape, dtype, values) {
        let outId;
        if (dtype === 'string' && values != null && values.length > 0 &&
            util.isString(values[0])) {
            const encodedValues = values.map(d => util.encodeString(d));
            outId = this.write(encodedValues, shape, dtype);
        }
        else {
            outId = this.write(values, shape, dtype);
        }
        return { dataId: outId, shape, dtype };
    }
    /** Return refCount of a `TensorData`. */
    refCount(dataId) {
        if (this.data.has(dataId)) {
            const tensorData = this.data.get(dataId);
            return tensorData.refCount;
        }
        return 0;
    }
    /** Increase refCount of a `TensorData`. */
    incRef(dataId) {
        const tensorData = this.data.get(dataId);
        tensorData.refCount++;
    }
    /** Decrease refCount of a `TensorData`. */
    decRef(dataId) {
        if (this.data.has(dataId)) {
            const tensorData = this.data.get(dataId);
            tensorData.refCount--;
        }
    }
    move(dataId, values, shape, dtype, refCount) {
        this.data.set(dataId, { values, dtype, refCount });
    }
    numDataIds() {
        return this.data.numDataIds();
    }
    async read(dataId) {
        return this.readSync(dataId);
    }
    readSync(dataId) {
        const { dtype, complexTensorInfos } = this.data.get(dataId);
        if (dtype === 'complex64') {
            const realValues = this.readSync(complexTensorInfos.real.dataId);
            const imagValues = this.readSync(complexTensorInfos.imag.dataId);
            return backend_util.mergeRealAndImagArrays(realValues, imagValues);
        }
        return this.data.get(dataId).values;
    }
    bufferSync(t) {
        const data = this.readSync(t.dataId);
        if (t.dtype === 'string') {
            try {
                // Decode the bytes into string.
                const strings = data.map(d => util.decodeString(d));
                return buffer(t.shape, t.dtype, strings);
            }
            catch (_a) {
                throw new Error('Failed to decode encoded string bytes into utf-8');
            }
        }
        return buffer(t.shape, t.dtype, data);
    }
    makeOutput(values, shape, dtype) {
        return engine().makeTensorFromTensorInfo(this.makeTensorInfo(shape, dtype, values), this);
    }
    /**
     * Dispose the memory if the dataId has 0 refCount. Return true if the memory
     * is released or memory is not managed in this backend, false if memory is
     * not cleared.
     * @param dataId
     * @oaram force Optional, remove the data regardless of refCount
     */
    disposeData(dataId, force = false) {
        if (this.data.has(dataId)) {
            this.data.get(dataId).refCount--;
            if (!force && this.data.get(dataId).refCount > 0) {
                return false;
            }
            const { complexTensorInfos } = this.data.get(dataId);
            if (complexTensorInfos != null) {
                this.disposeData(complexTensorInfos.real.dataId, true);
                this.disposeData(complexTensorInfos.imag.dataId, true);
            }
            this.data.delete(dataId);
        }
        return true;
    }
    disposeIntermediateTensorInfo(tensorInfo) {
        this.disposeData(tensorInfo.dataId);
    }
    async time(f) {
        const start = util.now();
        f();
        const kernelMs = util.now() - start;
        return { kernelMs };
    }
    memory() {
        return {
            // Unreliable due to automatic gc. The numbers above are cumulative.
            unreliable: true,
            reasons: ['The reported memory is an upper bound. Due to automatic garbage ' +
                    'collection, the true allocated memory may be less.']
        };
    }
    where(condition) {
        assertNotComplex([condition], 'where');
        const condVals = this.readSync(condition.dataId);
        return whereImpl(condition.shape, condVals);
    }
    dispose() { }
    floatPrecision() {
        return 32;
    }
    /** Returns the smallest representable number.  */
    epsilon() {
        return super.epsilon();
    }
}
MathBackendCPU.nextDataId = 0;
//# sourceMappingURL=data:application/json;base64,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
/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { util } from '@tensorflow/tfjs-core';
import { Im2ColPackedProgram } from '../im2col_packed_gpu';
import { mapActivationToShaderProgram } from '../kernel_utils/kernel_funcs_utils';
import { MatMulPackedProgram } from '../mulmat_packed_gpu';
import * as webgl_util from '../webgl_util';
import { batchMatMulImpl, MATMUL_SHARED_DIM_THRESHOLD } from './BatchMatMul_impl';
import { identity } from './Identity';
import { reshape } from './Reshape';
// Both conv2dByMatMul and conv2dWithIm2Row fuse height and width into one
// dimension to compute batchMatMul, so bias and activation weights are also
// supposed to fuse the two dimensions into one.
//
// This function computes the target shape for fusing height and width
// dimensions. Returning null means the shape is already compatible.
//
// Even though the bias is not supposed to be a 3-D or a 4-D (including
// batch) tensor and PReLU activiation weights is not supposed to be a 4-D
// tensor, we still need to support them, because we haven't disabled
// them for NHWC format.
// https://github.com/tensorflow/tfjs/blob/b53bd47e880367ae57493f0ea628abaf08db2d5d/tfjs-core/src/ops/fused/conv2d.ts#L181-L196
function getShapeForBatchMatMul(shape, isChannelsLast) {
    const length = shape.length;
    if (length >= 3) {
        return isChannelsLast ?
            [
                ...shape.slice(0, -3) /* batch */,
                shape[length - 3] * shape[length - 2] /* height * width */,
                shape[length - 1] /* channel */
            ] :
            [
                ...shape.slice(0, -3) /* batch */, shape[length - 3] /* channel */,
                shape[length - 2] * shape[length - 1] /* height * width */
            ];
    }
    else if (!isChannelsLast && length === 1 && shape[0] > 1) {
        return [shape[0], 1];
    }
    else {
        return null;
    }
}
// For 1x1 kernels that iterate through every point in the input, convolution
// can be expressed as matrix multiplication (without need for memory
// remapping).
export function conv2dByMatMul({ x, filter, convInfo, backend, bias = null, preluActivationWeights = null, leakyreluAlpha = 0, activation = null }) {
    // Reshapes conv2D input to 2D tensors, uses matMul and then reshape the
    // result from 2D to 4D.
    const xShape = x.shape;
    const xTexData = backend.texData.get(x.dataId);
    const sharedMatMulDim = convInfo.inChannels;
    const outerShapeX = xShape[0] * xShape[1] * xShape[2];
    const outerShapeFilter = convInfo.outChannels;
    const isChannelsLast = convInfo.dataFormat === 'channelsLast';
    const transposeA = false;
    const transposeB = false;
    let out;
    const intermediates = [];
    if (preluActivationWeights != null) {
        const targetShape = getShapeForBatchMatMul(preluActivationWeights.shape, isChannelsLast);
        if (targetShape != null) {
            preluActivationWeights = reshape({
                inputs: { x: preluActivationWeights },
                backend,
                attrs: { shape: targetShape }
            });
            intermediates.push(preluActivationWeights);
        }
    }
    if (bias != null) {
        const targetShape = getShapeForBatchMatMul(bias.shape, isChannelsLast);
        if (targetShape != null) {
            bias = reshape({ inputs: { x: bias }, backend, attrs: { shape: targetShape } });
            intermediates.push(bias);
        }
    }
    // TODO: Once reduction ops are packed, batchMatMul will always be packed
    // and we can remove this condition.
    const batchMatMulWillBeUnpacked = (outerShapeX === 1 || outerShapeFilter === 1) &&
        sharedMatMulDim > MATMUL_SHARED_DIM_THRESHOLD;
    // The algorithm in the if condition assumes (1) the output will be packed,
    // (2) x is packed, (3) x isChannelsLast, (4)  x's packed texture is already
    // on GPU, (5) col is odd, (6) the width, height and inChannels are the same
    // for xTexData.shape and xShape.
    const canOptimize = !batchMatMulWillBeUnpacked && xTexData.isPacked &&
        isChannelsLast && xTexData.texture != null && xShape[2] % 2 !== 0 &&
        util.arraysEqual(xTexData.shape.slice(-3), xShape.slice(-3));
    if (canOptimize) {
        // We avoid expensive packed 2x2 reshape by padding col count to next,
        // even number. When col is odd, the result of packed batchMatMul is
        // the same (has the same texture layout and and values in the texture) as
        // it is for next even col. We make the odd-cols tensor to look like
        // even-cols tensor before the operation and, after the batchMatMul,
        // fix the even-cols result to have odd number of cols.
        const targetShape = xShape[0] * xShape[1] * (xShape[2] + 1);
        const xReshaped = {
            dataId: x.dataId,
            shape: [1, targetShape, convInfo.inChannels],
            dtype: x.dtype
        };
        // xTexData.shape gets referenced from GPGPUBinary.inShapeInfos.
        // Decrementing col count, after batchMatMul->...->compileProgram leads to
        // invalid col count within the reference in GPGPUBinary.inShapeInfos.
        // Alternative fix would be to provide a copy to GPGPUBinary.inShapeInfos
        // in compileProgram method, but that would affect compilation of all
        // programs - instead, provide a copy here, with even col count, before
        // calling batchMatMul->...->compileProgram and after that, the original
        // xTexData.shape is restored.
        const originalXTexDataShape = xTexData.shape;
        xTexData.shape = xTexData.shape.slice();
        xTexData.shape[xTexData.shape.length - 2]++;
        util.assert(webgl_util.isReshapeFree(xTexData.shape, xReshaped.shape), () => `packed reshape ${xTexData.shape} to ${xReshaped.shape} isn't free`);
        const filterReshaped = reshape({
            inputs: { x: filter },
            backend,
            attrs: { shape: [1, convInfo.inChannels, convInfo.outChannels] }
        });
        intermediates.push(filterReshaped);
        const pointwiseConv = batchMatMulImpl({
            a: xReshaped,
            b: filterReshaped,
            backend,
            transposeA,
            transposeB,
            bias,
            activation,
            preluActivationWeights,
            leakyreluAlpha
        });
        const pointwiseConvTexData = backend.texData.get(pointwiseConv.dataId);
        util.assert(pointwiseConvTexData.isPacked, () => 'batchMatMul result is expected to be packed');
        // Restore the input shape to original.
        xTexData.shape = originalXTexDataShape;
        // Set the output shape - there is no need for expensive reshape as data
        // layout is already correct.
        pointwiseConvTexData.shape = convInfo.outShape;
        out = identity({ inputs: { x: pointwiseConv }, backend });
        out.shape = convInfo.outShape;
        intermediates.push(pointwiseConv);
    }
    else {
        const numCols = convInfo.outHeight * convInfo.outWidth;
        const xReshaped = reshape({
            inputs: { x },
            backend,
            attrs: {
                shape: isChannelsLast ?
                    [convInfo.batchSize, numCols, convInfo.inChannels] :
                    [convInfo.batchSize, convInfo.inChannels, numCols]
            }
        });
        const filterReshaped = reshape({
            inputs: { x: filter },
            backend,
            attrs: { shape: [1, convInfo.inChannels, convInfo.outChannels] }
        });
        const result = batchMatMulImpl({
            a: isChannelsLast ? xReshaped : filterReshaped,
            b: isChannelsLast ? filterReshaped : xReshaped,
            transposeA: !isChannelsLast,
            transposeB,
            backend,
            bias,
            activation,
            preluActivationWeights,
            leakyreluAlpha
        });
        out = reshape({ inputs: { x: result }, backend, attrs: { shape: convInfo.outShape } });
        intermediates.push(xReshaped);
        intermediates.push(filterReshaped);
        intermediates.push(result);
    }
    for (const i of intermediates) {
        backend.disposeIntermediateTensorInfo(i);
    }
    return out;
}
// Implements the im2row algorithm as outlined in "High Performance
// Convolutional Neural Networks for Document Processing" (Suvisoft, 2006)
export function conv2dWithIm2Row({ x, filter, convInfo, backend, bias = null, preluActivationWeights = null, leakyreluAlpha = 0, activation = null }) {
    // Rearranges conv2d input so each block to be convolved over forms the
    // column of a new matrix with shape [filterWidth * filterHeight *
    // inChannels, outHeight * outWidth]. The filter is also rearranged so each
    // output channel forms a row of a new matrix with shape [outChannels,
    // filterWidth * filterHeight * inChannels]. The convolution is then
    // computed by multiplying these matrices and reshaping the result.
    const { filterWidth, filterHeight, inChannels, outWidth, outHeight, dataFormat } = convInfo;
    const isChannelsLast = dataFormat === 'channelsLast';
    const sharedDim = filterWidth * filterHeight * inChannels;
    const numCols = outHeight * outWidth;
    const x2ColShape = [convInfo.batchSize, sharedDim, numCols];
    const transposeA = true;
    const transposeB = false;
    const intermediates = [];
    if (preluActivationWeights != null) {
        const targetShape = getShapeForBatchMatMul(preluActivationWeights.shape, isChannelsLast);
        if (targetShape != null) {
            preluActivationWeights = reshape({
                inputs: { x: preluActivationWeights },
                backend,
                attrs: { shape: targetShape }
            });
            intermediates.push(preluActivationWeights);
        }
    }
    if (bias != null) {
        const targetShape = getShapeForBatchMatMul(bias.shape, isChannelsLast);
        if (targetShape != null) {
            bias = reshape({ inputs: { x: bias }, backend, attrs: { shape: targetShape } });
            intermediates.push(bias);
        }
    }
    const w2Row = reshape({
        inputs: { x: filter },
        backend,
        attrs: { shape: [1, sharedDim, util.sizeFromShape(filter.shape) / sharedDim] }
    });
    intermediates.push(w2Row);
    const im2ColProgram = new Im2ColPackedProgram(x2ColShape, convInfo);
    const customValues = [
        x.shape, [convInfo.padInfo.top, convInfo.padInfo.left],
        [convInfo.strideHeight, convInfo.strideWidth],
        [convInfo.dilationHeight, convInfo.dilationWidth], [convInfo.inChannels],
        [convInfo.filterWidth * convInfo.inChannels], [convInfo.outWidth]
    ];
    const im2Col = backend.runWebGLProgram(im2ColProgram, [x], 'float32', customValues);
    const im2ColReshaped = reshape({ inputs: { x: im2Col }, backend, attrs: { shape: x2ColShape } });
    intermediates.push(im2Col);
    intermediates.push(im2ColReshaped);
    const hasBias = bias != null;
    const hasPreluActivationWeights = preluActivationWeights != null;
    const hasLeakyreluAlpha = activation === 'leakyrelu';
    const fusedActivation = activation ? mapActivationToShaderProgram(activation, true) : null;
    const matmulProgram = new MatMulPackedProgram(isChannelsLast ? im2ColReshaped.shape :
        w2Row.shape, isChannelsLast ? w2Row.shape :
        im2ColReshaped.shape, isChannelsLast ? [convInfo.batchSize, numCols, convInfo.outChannels] :
        [convInfo.batchSize, convInfo.outChannels, numCols], transposeA, transposeB, hasBias, fusedActivation, hasPreluActivationWeights, hasLeakyreluAlpha);
    const inputs = isChannelsLast ? [im2ColReshaped, w2Row] : [w2Row, im2ColReshaped];
    if (bias) {
        inputs.push(bias);
    }
    if (hasPreluActivationWeights) {
        inputs.push(preluActivationWeights);
    }
    if (hasLeakyreluAlpha) {
        const $leakyreluAlpha = backend.makeTensorInfo([], 'float32', util.createScalarValue(leakyreluAlpha, 'float32'));
        inputs.push($leakyreluAlpha);
        intermediates.push($leakyreluAlpha);
    }
    const product = backend.runWebGLProgram(matmulProgram, inputs, 'float32');
    const out = reshape({ inputs: { x: product }, backend, attrs: { shape: convInfo.outShape } });
    intermediates.push(product);
    for (const i of intermediates) {
        backend.disposeIntermediateTensorInfo(i);
    }
    return out;
}
//# sourceMappingURL=data:application/json;base64,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